import { Component, OnInit } from '@angular/core';
import { PaymentHistoryService } from '@platform/app/core/services/payment-history.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { EMPTY, catchError, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { format } from 'date-fns';
import { vi_VN } from 'ng-zorro-antd/i18n';

@Component({
  selector: 'app-payment-history',
  templateUrl: './payment-history.component.html',
  styleUrls: ['./payment-history.component.scss'],
})
export class PaymentHistoryComponent implements OnInit {
  readonly NzLocaleDatePicker = vi_VN.DatePicker;
  startDate = new Date();
  endDate = new Date();

  readonly statuses = [
    {
      label: 'Tất cả',
      value: -1,
    },
    {
      label: 'Thành công',
      value: 1,
    },
    {
      label: 'Không thành công',
      value: 2,
    },
  ];
  status: {
    label: string;
    value: number;
  } = this.statuses[0];
  propertiesSort: string = 'orderDate';
  sort: 'ASC' | 'DESC' = 'ASC';
  countPayment: number = 0;
  totalPages: number = 0;
  pageIndex: number = 1;
  pageSize: number = 5;
  listPayment: any[] = [];
  formatAmount = this.utilsService.formatAmount;

  constructor(
    private paymentHistoryService: PaymentHistoryService,
    private utilsService: UtilsService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.fetchPaymentHistory(this.pageIndex);
  }

  handleFilterBtnClick() {
    this.pageIndex = 1;
    this.fetchPaymentHistory(1);
  }

  fetchPaymentHistory(pageIndex) {
    this.paymentHistoryService
      .fetchPaymentHistory({
        startDate: format(this.startDate, 'dd/MM/yyyy'),
        endDate: format(this.endDate, 'dd/MM/yyyy'),
        orderStatus: this.status.value,
        page: pageIndex,
        propertiesSort: 'orderDate',
        sort: 'ASC',
        maxSize: this.pageSize,
      })
      .pipe(
        tap(({ object }) => {
          this.listPayment = object.data;
          this.pageSize = object.maxSize;
          this.countPayment = object.totalElement;
          this.totalPages = object.totalPages;
        }),
        catchError(() => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại.');
          return EMPTY;
        })
      )
      .subscribe();
  }

  onQueryParamsChange(params: NzTableQueryParams): void {
    const { pageIndex } = params;
    this.fetchPaymentHistory(pageIndex);
  }

  handleSelectStatus(status) {
    this.status = status;
  }

  getOrderType(type) {
    switch (parseInt(type)) {
      case 1:
        return 'Đăng ký';
      case 2:
        return 'Gia hạn';
      case 3:
        return 'Thanh toán hủy trả sau';
      case 4:
        return 'Thêm data';
    }
    return '';
  }

  getOrderStatus(status) {
    switch (parseInt(status)) {
      case 1:
        return 'Thành công';
      case 2:
        return 'Không thành công';
      default:
        return 'Giao dịch bất thường';
    }
  }
}
