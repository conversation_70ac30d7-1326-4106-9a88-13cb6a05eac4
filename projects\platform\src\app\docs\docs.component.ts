import {
  Component,
  ElementRef,
  Inject,
  ViewChild,
  ChangeDetectorRef,
  OnDestroy
} from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { docsConfigList, headerPrefix } from './docs';
import { BehaviorSubject, distinctUntilChanged, tap } from 'rxjs';
import { cloneDeep } from 'lodash';

@Component({
  selector: 'app-docs',
  templateUrl: './docs.component.html',
  styleUrls: ['./docs.component.scss']
})
export class DocsComponent implements OnDestroy {
  readonly SelectedHeadingTagForToc = ['h1', 'h2', 'h3' /* 'h4', 'h5', 'h6' */];
  readonly docsConfigList = docsConfigList;
  selectedDocsIndex = 0;
  get docsConfig() {
    return this.docsConfigList[this.selectedDocsIndex];
  }
  @ViewChild('docsToc') docsToc!: ElementRef;
  @ViewChild('docsCtn') docsCtn!: ElementRef;
  /* all header elem queried from DOM and set once markdown being rendered into html */
  extractedHeaderElems: Element[];
  /* all header info */
  extractedHeaders: {
    level: number;
    text: string;
    id: string;
    href: string;
  }[];
  /* observing the extractedHeaderElems to see which are scrolled into view */
  private intersectionObserver: IntersectionObserver;
  /* keep a list of headers being scrolled into view */
  private inViewHeaderElemsSubject = new BehaviorSubject<Element[]>([]);
  /* the current highlight header in docs-toc, is calculated from the inViewHeaderElemsSubject, and recalculate everytime inViewHeaderElemsSubject.value changed */
  currentHeaderElem;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    private cdr: ChangeDetectorRef
  ) {
    this.activatedRoute.paramMap.subscribe((e) => {
      /* reset state from previous docs page */
      this.extractedHeaders = [];
      this.currentHeaderElem = null;
      this.inViewHeaderElemsSubject.next([]);
      this.extractedHeaderElems?.forEach((header) =>
        this.intersectionObserver.unobserve(header)
      );
      this.extractedHeaderElems = null;

      /* find next docs page based on docs-key */
      const foundIndex = docsConfigList.findIndex(
        (docs) => docs.key === this.activatedRoute.snapshot.paramMap.get('docs-key')
      );
      /* if not found, use the first docs listed in docsConfigList */
      this.selectedDocsIndex = foundIndex !== -1 ? foundIndex : 0;
      /* navigate on invalid docs-key */
      if (foundIndex === -1) this.router.navigate(['/', 'docs', this.docsConfig.key]);

      /* go top on changing into new docs page */
      setTimeout(() => {
        /* set timeout to let docsCtn being init first before scroll top */
        this.docsCtn?.nativeElement?.scrollTo({ top: 0 });
      }, 50);
    });

    // Create an Intersection Observer
    this.intersectionObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        let inViewHeaderElems = cloneDeep(this.inViewHeaderElemsSubject.value);
        if (entry.isIntersecting) {
          if (!inViewHeaderElems.find((item) => item.id === entry.target.id))
            inViewHeaderElems.push(entry.target);
        } else {
          inViewHeaderElems = inViewHeaderElems.filter(
            (item) => item.id !== entry.target.id
          );
        }
        this.inViewHeaderElemsSubject.next(
          inViewHeaderElems.sort((a, b) => a['offsetTop'] - b['offsetTop'])
        );
      });
    });

    /* recalculate currentHeaderElem */
    this.inViewHeaderElemsSubject
      .asObservable()
      .pipe(
        distinctUntilChanged(),
        tap((list) => {
          if (list[0]) {
            if (this.currentHeaderElem) {
              const oldLink = document.querySelector(
                `#docs-toc a[href="${window.location.pathname}#${this.currentHeaderElem.id}"]`
              );
              oldLink.classList.remove('!border-brand-1');
            }
            const newLink = document.querySelector(
              `#docs-toc a[href="${window.location.pathname}#${list[0].id}"]`
            );

            newLink.classList.add('!border-brand-1');
            this.currentHeaderElem = list[0];
          }
          /* if there is no list[0] => kept the currentHeaderElem */
        })
      )
      .subscribe();
  }

  handleMarkdownReady() {
    // find all heading tag with id start with 'headerPrefix'
    const extractedHeaderElems = [];
    const extractedHeaders = [];
    this.document
      .querySelectorAll(
        `${this.SelectedHeadingTagForToc.join(',')}[id^="${headerPrefix}"]`
      )
      .forEach((header) => {
        extractedHeaderElems.push(header);
        extractedHeaders.push({
          level:
            this.SelectedHeadingTagForToc.findIndex(
              (tag) => tag.toLowerCase() === header.tagName.toLowerCase()
            ) + 1,
          text: header.textContent,
          id: header.id,
          href: window.location.pathname + '#' + header.id
        });
      });
    this.extractedHeaderElems = extractedHeaderElems;
    this.extractedHeaders = extractedHeaders;

    this.extractedHeaderElems.forEach((header) =>
      this.intersectionObserver.observe(header)
    );

    this.scrollTo(this.activatedRoute.snapshot.fragment);
  }

  scrollTo(id) {
    if (!id) return;
    this.router.navigate([], { fragment: id });
    setTimeout(() => {
      this.document.getElementById(id)?.scrollIntoView({ behavior: 'smooth' });
    }, 50);
  }

  ngOnDestroy(): void {
    this.intersectionObserver.disconnect();
    this.inViewHeaderElemsSubject.complete();
  }
}
