$text1: #273266;
$bg1: #E9ECFB;
$neutral3: #A1A5BA;
$primary1: #2140D2;
$neutral2: #666C8A;

.wrapper {
	color: $text1;
	// display: flex;
	// align-items: start;
	// justify-content: space-between;
	display: grid;
	grid-template-columns: minmax(300px, 350px) 1fr;
	gap: 40px;
	padding: 28px 40px;

	.left {
		padding: 16px;
		// max-width: 350px;
		flex: 1;
		background: #FFF;
		box-shadow: 0px 0px 25px 0px rgba(0, 207, 137, 0.06);


		.input-preview {
			display: flex;
			align-items: center;
			justify-content: left;
			gap: 12px;
			padding: 16px;
			border-radius: 8px;
			border: 1px solid $bg1;
			background: rgba(161, 165, 186, 0.15);
			height: 96px;

			img {
				border-radius: 8px;
				width: 64px;
				height: 64px;
				object-fit: cover;
				aspect-ratio: 1 / 1;
			}

			.input-name {
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				font-size: 14px;
				font-weight: 500;
				line-height: 21px;
			}

			.input-size {
				color: $neutral2;
				font-size: 12px;
				font-weight: 400;
				line-height: 22px;
			}


			&.empty {
				padding: 28px;
				gap: 16px;
				font-size: 18px;
				font-weight: 500;
				line-height: 26px;
				color: $neutral3;
			}
		}

		.actions {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 16px;
			margin: 12px 0;

			button {
				border: none;
				flex: 1;
				height: 48px;
				font-size: 14px;
				font-style: normal;
				font-weight: 700;
				line-height: 24px;
			}

			.refresh {
				background-color: #fff;
				color: $primary1;
				border-radius: 8px;
				border: 1px solid $primary1;
			}

			.ocr {
				color: #fff;
				border-radius: 8px;
				background: $primary1;
			}
		}

		hr {
			border-radius: 8px;
			background: #E4E6F3;
		}

		.upload-file {
			height: 110px;
			border-radius: 8px;
			border: 1px dashed $neutral3;
			font-size: 12px;
			font-weight: 400;
			line-height: 22px;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
			text-align: center;
			padding: 12px;
			margin-bottom: 16px;
			position: relative;

			input[type="file"] {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				overflow: hidden;
				cursor: pointer;
				font-size: 0;
				opacity: 0;
			}
		}

		.sample-list {
			display: grid;
			grid-template-columns: repeat(3, minmax(0, 1fr));
			row-gap: 18px;
			column-gap: 16px;
			max-height: 450px;
			overflow-y: auto;

			img {
				cursor: pointer;
				width: 100%;
				object-fit: cover;
				aspect-ratio: 1 / 1;
				border-radius: 8px;
			}
		}
	}

	.right {
		padding: 16px;
		// flex: 1;
		background: #FFF;
		box-shadow: 0px 0px 25px 0px rgba(0, 207, 137, 0.06);
		display: flex;
		flex-direction: column;

		.output {
			flex: 1;
			display: flex;

			.preview {
				flex: 1;

				img {
					width: 100%;
					border-radius: 8px;
				}
			}

			.border {
				border: 1px solid #E4E6F3;
				margin: 0 16px;
			}

			.info {
				flex: 1;
				max-width: 300px;

				.image-snip {
					margin-bottom: 24px;
					width: 100%;
					border-radius: 8px;
					border: 1px solid #E4E6F3;

					&.placeholder {
						height: 55px;
						background: rgba(161, 165, 186, 0.15);
					}
				}

				.title {
					font-size: 14px;
					font-style: normal;
					font-weight: 700;
					line-height: 24px;
					margin-bottom: 12px;
				}

				.water-num {
					display: flex;
					justify-content: space-between;
					gap: 12px;
					padding-bottom: 24px;

					div {
						background-color: $bg1;
						border-radius: 4px;
						text-align: center;
						font-size: 28px;
						font-weight: 600;
						line-height: 50px;
						width: 38px;
					}
				}

				.accuracy {
					height: 42px;
					font-size: 16px;
					font-weight: 500;
					line-height: 22px;
					border-radius: 8px;
					border: 1px solid #E4E6F3;
					background: #F2F4FA;
					padding: 9px 12px;
				}

			}
		}

	}
}

.title {
	font-size: 18px;
	font-style: normal;
	font-weight: 700;
	line-height: 26px;
	margin-bottom: 16px;
}

.custom-file-label {
	border-radius: 8px;

	&::after {
		content: url('~projects/platform/src/assets/img/rpa/ocr-experience/upload.svg');
		background-color: #2140D2;
		border-radius: 8px;
	}
}