:host {
  .dashboard-rpa {
    padding: 1rem 2rem;
  }

  .title-upgrade {
    font-weight: bold;
    font-size: 24px;
    line-height: 35px;
  }

  .select-subscription-length {
    display: inline-block;
    background: #eeeeee;
    border-radius: 29.5px;
    padding: 5px;

    span {
      display: inline-block;
      padding: 0 40px;
      cursor: pointer;
      border-radius: 118px;
      color: #545454;

      &.active {
        color: #ffffff;
        font-weight: bold;
        background: #0f67ce;
        padding: 10px 25px;
      }
    }
  }

  .subscription-grid {
    display: grid;
    grid-template-columns: 1fr;

    @media (min-width: 1440px) {
      grid-template-columns: repeat(4, minmax(210px, 1fr));

      // [ngStyle]="{'grid-template-columns': selectedSubscription === 1 ? 'repeat(6, minmax(210px, 1fr))' : 'repeat(4, minmax(210px, 1fr))'}"
      &.one-month {
        grid-template-columns: repeat(6, minmax(210px, 1fr));
      }

    }
  }

  .subscription-col {
    grid-column: 1 / -1;

    @media (min-width: 1440px) {
      grid-column: span 1 / span 1;
    }

    border-radius: 4px;
    border: 1px solid #eeeeee;
    border-top: 6px solid #0f67ce;

    h1 {
      font-weight: 600;
      color: #0f2b3b;
      font-size: 28px;
    }

    .subscription-title {
      h5 {
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 0.25px;
        line-height: 30px;
        color: #111127;
      }
    }

    .odd-row {
      border-bottom: 1px solid #eeeeee;
      border-top: 1px solid #eeeeee;
      background: #f6f6f6;
    }

    .quota-number {
      font-size: 16px !important;
      line-height: 24px;
      letter-spacing: 0.01em;
      color: #111127;
      font-weight: 700;
    }

    .btn-register {
      padding: 12px 0;
      width: 100%;
      background-color: #0f67ce;
      font-size: 16px;
      color: #fff;
      font-weight: 600;
      letter-spacing: 1px;
    }
  }
}