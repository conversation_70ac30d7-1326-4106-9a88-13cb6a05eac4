/* You can add global styles to this file, and also import other style files */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import 'ngx-toastr/toastr';
@import 'node_modules/ngx-spinner/animations/ball-clip-rotate.css';
@import 'node_modules/ngx-spinner/animations/ball-scale-multiple.css';
@import 'node_modules/ngx-spinner/animations/ball-pulse-sync.css';
@import 'node_modules/prismjs/themes/prism-okaidia.css';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

html,
body {
  font-family: 'Inter', sans-serif;

  // font-size: 14px;
  app-root {
    display: block;
    min-height: 100vh; // take up all parent body height
  }
}

// @import "ng-zorro-antd/ng-zorro-antd.min.css"; /* added bunch bunched of unnecessary styles like a:hover */
@import 'node_modules/ng-zorro-antd/style/index.min.css'; /* ONLY Import base styles */
/* Import only used component's styles */
@import "node_modules/ng-zorro-antd/modal/style/index.min.css";
@import "node_modules/ng-zorro-antd/tooltip/style/index.min.css";
@import "node_modules/ng-zorro-antd/form/style/index.min.css";
@import "node_modules/ng-zorro-antd/input/style/index.min.css";
@import "node_modules/ng-zorro-antd/typography/style/index.min.css";
@import "node_modules/ng-zorro-antd/select/style/index.min.css";
@import "node_modules/ng-zorro-antd/date-picker/style/index.min.css";
@import "node_modules/ng-zorro-antd/table/style/index.min.css";
@import "node_modules/ng-zorro-antd/tabs/style/index.min.css";
@import "node_modules/ng-zorro-antd/dropdown/style/index.min.css";
@import "node_modules/ng-zorro-antd/button/style/index.min.css";
@import "node_modules/ng-zorro-antd/switch/style/index.min.css";
@import "node_modules/ng-zorro-antd/collapse/style/index.min.css";
@import "node_modules/ng-zorro-antd/drawer/style/index.min.css";
@import 'node_modules/ng-zorro-antd/radio/style/index.min.css';
@import 'node_modules/ng-zorro-antd/avatar/style/index.min.css';
@import 'node_modules/ng-zorro-antd/upload/style/index.min.css';
@import 'node_modules/ng-zorro-antd/pagination/style/index.min.css';
@import 'node_modules/ng-zorro-antd/tag/style/index.min.css';
@import 'node_modules/ng-zorro-antd/resizable/style/index.min.css';
@import 'node_modules/ng-zorro-antd/skeleton/style/index.min.css';
@import 'node_modules/ng-zorro-antd/slider/style/index.min.css';
@import 'node_modules/ng-zorro-antd/progress/style/index.min.css';
@import 'node_modules/ng-zorro-antd/badge/style/index.min.css';

/* TODO: import all ng-zorro-antd.min.css but need to override any obtrusive style OR import separated component css file with a lot overlapping styles while debuging */

nz-avatar span.ant-avatar-string {
  transform: scale(1) translateX(-50%);
}

/* TODO: replace current custom class in each modal to this global class */
.custom-ant-modal-common-styles {
  .ant-modal-content {
    border-radius: 16px;
  }
}