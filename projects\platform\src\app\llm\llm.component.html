<div class="font-semibold text-xl">DEMO ỨNG DỤNG GEN AI BÓC TÁCH THÔNG TIN</div>
<div class="grid grid-cols-2 flex-auto gap-5">
  <div
    class="col-span-full xl:col-span-1 flex flex-col gap-2 overflow-auto rounded-lg border border-line p-4 bg-bg-3"
  >
    <div *ngIf="!selectedFile" class="font-semibold">Tải file bóc tách</div>
    <div
      *ngIf="!selectedFile"
      #inputFile
      class="flex relative items-center justify-center gap-3 bg-bg-1 rounded-lg p-4 border border-dashed border-line hover:border-brand-2"
      (dragover)="inputFile?.classList?.add('!border-brand-2')"
      (dragleave)="inputFile?.classList?.remove('!border-brand-2')"
      (drop)="inputFile?.classList?.remove('!border-brand-2')"
    >
      <input
        [multiple]="RULE_ACCEPT.multiple"
        class="absolute top-0 left-0 w-full h-full z-10 opacity-0 text-[0px] cursor-pointer"
        type="file"
        [accept]="RULE_ACCEPT.accept"
        #inputFileElem
        (change)="handleFileInputChange($event, inputFileElem)"
        (cancel)="(null)"
      />
      <div class="flex items-center justify-center gap-3">
        <img class="w-5" src="assets/kie/header-table/upload.svg" alt="icon-upload" />
        <div>
          <span class="font-medium text-brand-1 text-sm">Chọn</span> hoặc kéo thả file tại
          đây
          <div class="text-text-3 text-[10px]">File dạng *.pdf, *.jpeg, *.jpg, *.png</div>
        </div>
      </div>
    </div>
    <div
      *ngIf="selectedFile"
      class="w-full flex items-center gap-2 bg-bg-1 py-2 px-4 border border-line rounded-lg"
    >
      <img
        class="shrink-0"
        *ngIf="selectedFile.type === 'application/pdf'"
        src="assets/kie/header-table/pdf-icon.svg"
        alt="PDF icon"
      />
      <img
        class="shrink-0"
        *ngIf="selectedFile.type === 'image/png'"
        src="assets/kie/header-table/png-icon.svg"
        alt="PNG icon"
      />
      <img
        class="shrink-0"
        *ngIf="selectedFile.type === 'image/jpeg'"
        src="assets/kie/header-table/jpeg-icon.svg"
        alt="JPEG icon"
      />
      <div class="flex-1 truncate">
        <p
          nz-tooltip
          [nzTooltipTitle]="selectedFile.name.length > 150 ? selectedFile.name : ''"
          nzTooltipColor="#000"
          class="truncate font-semibold"
        >
          {{ selectedFile.name }}
        </p>
        <p>{{ convertBytesToMB(selectedFile.size) }}</p>
      </div>
      <button class="shrink-0" nz-button nzType="link" (click)="removeFile()">
        <i nz-icon class="text-2xl text-red-500" nz-size="large" nzType="delete"></i>
      </button>
    </div>
    <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
    <app-document-viewer
      *ngIf="selectedFileLink"
      [featureFlags]="{ ViewerFitPageWidth: false }"
      class="h-full min-h-[450px]"
      [fileLink]="selectedFileLink"
      (afterFileLinkLoaded)="(null)"
    ></app-document-viewer>
    <div
      *ngIf="!selectedFile"
      class="h-full border border-[#E7EBEF] rounded-lg bg-bg-1 flex flex-col items-center justify-center gap-3 text-icon-2 text-sm font-medium min-h-[450px]"
    >
      <ng-container [ngTemplateOutlet]="noFileIcon"></ng-container>
      <div>Chọn tài liệu</div>
    </div>
  </div>
  <div
    class="col-span-full xl:col-span-1 flex flex-col gap-2 rounded-lg border border-line p-4 bg-bg-3"
    [formGroup]="form"
  >
    <div class="font-semibold">Prompt</div>
    <nz-textarea-count [nzMaxCharacterCount]="maxPromptCount">
      <textarea
        [nzStatus]="
          form.controls.prompt.dirty && form.controls.prompt.invalid ? 'error' : ''
        "
        class="max-h-[250px] rounded-lg"
        rows="10"
        formControlName="prompt"
        nz-input
      ></textarea>
    </nz-textarea-count>
    <div class="flex items-center gap-3 m-auto">
      <button
        (click)="clearPromptAndAnswer()"
        [ngClass]="{
          'cursor-not-allowed opacity-70': !selectedFileHash
        }"
        class="flex items-center gap-2 px-4 py-2 border border-brand-1 rounded-lg text-brand-1 font-medium"
      >
        <ng-container [ngTemplateOutlet]="refreshIcon"></ng-container> Làm mới
      </button>
      <button
        [ngClass]="{
          'cursor-not-allowed opacity-70':
            !selectedFileHash || form.controls.prompt.invalid
        }"
        class="flex items-center gap-2 px-4 py-2 border border-brand-1 rounded-lg bg-brand-1 text-white font-medium"
        (click)="extractAnswer()"
      >
        <ng-container [ngTemplateOutlet]="viewResultIcon"></ng-container> Xem kết quả
      </button>
    </div>
    <div class="font-semibold">Kết quả</div>
    <div
      class="flex-auto h-[1px] min-h-[250px] overflow-auto rounded-lg bg-bg-1 border border-line p-4"
    >
      <nz-skeleton
        *ngIf="loading"
        [nzActive]="true"
        [nzParagraph]="{ rows: 8 }"
      ></nz-skeleton>
      <div *ngIf="answer">{{ answer }}</div>
    </div>
  </div>
</div>

<ng-template #refreshIcon>
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.05 21.67C19.34 20.54 22.5 16.64 22.5 12C22.5 6.48 18.06 2 12.5 2C5.83 2 2.5 7.56 2.5 7.56M2.5 7.56V3M2.5 7.56H4.51H6.94"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M2.5 12C2.5 17.52 6.98 22 12.5 22"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-dasharray="3 3"
    />
  </svg>
</ng-template>

<ng-template #viewResultIcon>
  <svg
    width="23"
    height="24"
    viewBox="0 0 23 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.4167 2V8C13.4167 8.53043 13.6186 9.03914 13.978 9.41421C14.3375 9.78929 14.825 10 15.3333 10H21.0833V20C21.0833 20.5304 20.8814 21.0391 20.522 21.4142C20.1625 21.7893 19.675 22 19.1667 22H12.6404C13.6323 20.9226 14.2382 19.5215 14.3562 18.0326C14.4742 16.5436 14.097 15.0578 13.2882 13.825C12.4794 12.5922 11.2882 11.6878 9.9153 11.264C8.54235 10.8402 7.0714 10.9228 5.75 11.498V4C5.75 3.46957 5.95193 2.96086 6.31138 2.58579C6.67082 2.21071 7.15834 2 7.66667 2H13.4167ZM14.8542 2.5V8C14.8542 8.13261 14.9047 8.25979 14.9945 8.35355C15.0844 8.44732 15.2063 8.5 15.3333 8.5H20.6042L14.8542 2.5ZM13.4167 17.5C13.4167 18.2223 13.2803 18.9375 13.0154 19.6048C12.7506 20.272 12.3623 20.8784 11.8729 21.3891C11.3834 21.8998 10.8024 22.3049 10.1629 22.5813C9.52341 22.8577 8.83801 23 8.14583 23C7.45366 23 6.76826 22.8577 6.12877 22.5813C5.48929 22.3049 4.90823 21.8998 4.41879 21.3891C3.92935 20.8784 3.5411 20.272 3.27622 19.6048C3.01133 18.9375 2.875 18.2223 2.875 17.5C2.875 16.0413 3.43032 14.6424 4.41879 13.6109C5.40726 12.5795 6.74792 12 8.14583 12C9.54375 12 10.8844 12.5795 11.8729 13.6109C12.8613 14.6424 13.4167 16.0413 13.4167 17.5V17.5ZM11.3601 15.146C11.3156 15.0994 11.2627 15.0625 11.2045 15.0373C11.1463 15.0121 11.0839 14.9991 11.0208 14.9991C10.9578 14.9991 10.8954 15.0121 10.8372 15.0373C10.779 15.0625 10.7261 15.0994 10.6816 15.146L7.1875 18.793L5.61008 17.146C5.52011 17.0521 5.39808 16.9994 5.27083 16.9994C5.14359 16.9994 5.02156 17.0521 4.93158 17.146C4.84161 17.2399 4.79106 17.3672 4.79106 17.5C4.79106 17.6328 4.84161 17.7601 4.93158 17.854L6.84825 19.854C6.89276 19.9006 6.94564 19.9375 7.00385 19.9627C7.06207 19.9879 7.12447 20.0009 7.1875 20.0009C7.25053 20.0009 7.31294 19.9879 7.37115 19.9627C7.42936 19.9375 7.48224 19.9006 7.52675 19.854L11.3601 15.854C11.4047 15.8076 11.4401 15.7524 11.4643 15.6916C11.4884 15.6309 11.5009 15.5658 11.5009 15.5C11.5009 15.4342 11.4884 15.3691 11.4643 15.3084C11.4401 15.2476 11.4047 15.1924 11.3601 15.146V15.146Z"
      fill="white"
    />
  </svg>
</ng-template>

<ng-template #noFileIcon>
  <svg
    width="140"
    height="140"
    viewBox="0 0 140 140"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M133.711 131.797H122.227C118.672 131.797 115.938 129.062 115.938 125.234V111.836C115.938 108.281 118.672 105.273 122.227 105.273H133.711C137.266 105.273 140 108.281 140 111.836V125.234C140 129.062 137.266 131.797 133.711 131.797ZM122.227 109.102C120.859 109.102 119.766 110.469 119.766 111.836V125.234C119.766 126.875 120.859 127.969 122.227 127.969H133.711C135.078 127.969 136.172 126.875 136.172 125.234V111.836C136.172 110.469 135.078 109.102 133.711 109.102H122.227Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17.7734 34.7266H6.5625C3.00781 34.7266 0 31.7188 0 28.1641V14.7656C0 11.2109 3.00781 8.20312 6.5625 8.20312H17.7734C21.3281 8.20312 24.3359 11.2109 24.3359 14.7656V28.1641C24.3359 31.7188 21.3281 34.7266 17.7734 34.7266ZM6.5625 12.0312C5.19531 12.0312 3.82812 13.3984 3.82812 14.7656V28.1641C3.82812 29.5312 5.19531 30.8984 6.5625 30.8984H17.7734C19.1406 30.8984 20.5078 29.5312 20.5078 28.1641V14.7656C20.5078 13.3984 19.1406 12.0312 17.7734 12.0312H6.5625Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17.7734 131.797H6.5625C3.00781 131.797 0 128.789 0 125.234V111.836C0 108.281 3.00781 105.273 6.5625 105.273H17.7734C21.3281 105.273 24.3359 108.281 24.3359 111.836V125.234C24.3359 128.789 21.6016 131.797 17.7734 131.797ZM6.5625 109.102C5.19531 109.102 3.82812 110.469 3.82812 111.836V125.234C3.82812 126.875 5.19531 127.969 6.5625 127.969H17.7734C19.1406 127.969 20.5078 126.875 20.5078 125.234V111.836C20.5078 110.469 19.1406 109.102 17.7734 109.102H6.5625Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M75.7422 34.7266H64.2578C60.7031 34.7266 57.9688 31.7188 57.9688 28.1641V14.7656C57.9688 11.2109 60.7031 8.20312 64.2578 8.20312H75.7422C79.2969 8.20312 82.0312 11.2109 82.0312 14.7656V28.1641C82.0312 31.7188 79.2969 34.7266 75.7422 34.7266ZM64.2578 12.0312C62.8906 12.0312 61.7969 13.3984 61.7969 14.7656V28.1641C61.7969 29.5312 62.8906 30.8984 64.2578 30.8984H75.7422C77.1094 30.8984 78.2031 29.5312 78.2031 28.1641V14.7656C78.2031 13.3984 77.1094 12.0312 75.7422 12.0312H64.2578Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M75.7422 131.797H64.2578C60.7031 131.797 57.9688 128.789 57.9688 125.234V111.836C57.9688 108.281 60.7031 105.273 64.2578 105.273H75.7422C79.2969 105.273 82.0312 108.281 82.0312 111.836V125.234C82.0312 128.789 79.2969 131.797 75.7422 131.797ZM64.2578 109.102C62.8906 109.102 61.7969 110.469 61.7969 111.836V125.234C61.7969 126.602 62.8906 127.969 64.2578 127.969H75.7422C77.1094 127.969 78.2031 126.602 78.2031 125.234V111.836C78.2031 110.469 77.1094 109.102 75.7422 109.102H64.2578Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M133.711 83.125H122.227C118.672 83.125 115.938 80.3906 115.938 76.8359V63.1641C115.938 59.6094 118.672 56.875 122.227 56.875H133.711C137.266 56.875 140 59.6094 140 63.1641V76.8359C140 80.3906 137.266 83.125 133.711 83.125ZM122.227 60.7031C120.859 60.7031 119.766 61.7969 119.766 63.1641V76.8359C119.766 78.2031 120.859 79.2969 122.227 79.2969H133.711C135.078 79.2969 136.172 78.2031 136.172 76.8359V63.1641C136.172 61.7969 135.078 60.7031 133.711 60.7031H122.227Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M101.172 96.5234H38.5547C35.2734 96.5234 32.5391 93.7891 32.5391 90.5078V49.4922C32.5391 46.2109 35.2734 43.4766 38.5547 43.4766H101.172C104.453 43.4766 106.914 46.2109 106.914 49.4922V90.5078C106.914 93.7891 104.453 96.5234 101.172 96.5234ZM38.5547 47.3047C37.4609 47.3047 36.3672 48.3984 36.3672 49.4922V90.5078C36.3672 91.6016 37.4609 92.6953 38.5547 92.6953H101.172C102.266 92.6953 103.086 91.6016 103.086 90.5078V49.4922C103.086 48.3984 102.266 47.3047 101.172 47.3047H38.5547Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M91.0549 96.5234C90.508 96.5234 90.2346 96.25 89.9611 95.9766L57.9689 70.2734L35.5471 88.0469C34.7267 88.8672 33.633 88.5938 33.0861 87.7734C32.2658 86.9531 32.5392 85.8594 33.3596 85.3125L56.8752 66.4453C57.4221 65.8984 58.5158 65.8984 59.3361 66.4453L92.1486 93.2422C92.9689 93.7891 93.2424 94.8828 92.6955 95.7031C92.1486 96.25 91.6017 96.5234 91.0549 96.5234Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M105 83.1243C104.453 83.1243 103.907 82.8508 103.633 82.5774L92.4222 70.2727L74.3753 81.7571C73.2816 82.304 72.1878 82.0305 71.641 81.2102C71.0941 80.1165 71.3675 79.0227 72.1878 78.4758L91.6019 66.1711C92.4222 65.6243 93.516 65.8977 94.0628 66.4446L106.368 79.843C107.188 80.6633 107.188 81.7571 106.368 82.5774C105.821 82.8508 105.547 83.1243 105 83.1243Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M72.4609 69.7266C67.5391 69.7266 63.7109 65.8984 63.7109 60.9766C63.7109 56.0547 67.5391 52.2266 72.4609 52.2266C77.1094 52.2266 80.9375 56.0547 80.9375 60.9766C80.9375 65.8984 77.1094 69.7266 72.4609 69.7266ZM72.4609 56.0547C69.7266 56.0547 67.5391 58.2422 67.5391 60.9766C67.5391 63.7109 69.7266 65.8984 72.4609 65.8984C74.9219 65.8984 77.1094 63.7109 77.1094 60.9766C77.1094 58.2422 74.9219 56.0547 72.4609 56.0547Z"
      fill="#6C7093"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M117.852 120.586H80.1172C79.0234 120.586 78.2031 119.766 78.2031 118.672C78.2031 117.578 79.0234 116.758 80.1172 116.758H117.852C118.945 116.758 119.766 117.578 119.766 118.672C119.766 119.766 118.945 120.586 117.852 120.586ZM59.8828 120.586H22.4219C21.3281 120.586 20.5078 119.766 20.5078 118.672C20.5078 117.578 21.3281 116.758 22.4219 116.758H59.8828C60.9766 116.758 61.7969 117.578 61.7969 118.672C61.7969 119.766 60.9766 120.586 59.8828 120.586ZM128.516 109.102C127.422 109.102 126.602 108.281 126.602 107.188V81.2109C126.602 80.1172 127.422 79.2969 128.516 79.2969C129.609 79.2969 130.43 80.1172 130.43 81.2109V107.188C130.43 108.281 129.609 109.102 128.516 109.102ZM12.0312 109.102C10.9375 109.102 10.1172 108.281 10.1172 107.188V32.8125C10.1172 31.7188 10.9375 30.8984 12.0312 30.8984C13.125 30.8984 13.9453 31.7188 13.9453 32.8125V107.188C13.9453 108.281 13.125 109.102 12.0312 109.102ZM128.516 60.7031C127.422 60.7031 126.602 59.8828 126.602 58.7891V23.2422H80.1172C79.0234 23.2422 78.2031 22.4219 78.2031 21.3281C78.2031 20.2344 79.0234 19.4141 80.1172 19.4141H128.516C129.609 19.4141 130.43 20.2344 130.43 21.3281V58.7891C130.43 59.8828 129.609 60.7031 128.516 60.7031ZM59.8828 23.2422H22.4219C21.3281 23.2422 20.5078 22.4219 20.5078 21.3281C20.5078 20.2344 21.3281 19.4141 22.4219 19.4141H59.8828C60.9766 19.4141 61.7969 20.2344 61.7969 21.3281C61.7969 22.4219 60.9766 23.2422 59.8828 23.2422Z"
      fill="#6C7093"
    />
  </svg>
</ng-template>
