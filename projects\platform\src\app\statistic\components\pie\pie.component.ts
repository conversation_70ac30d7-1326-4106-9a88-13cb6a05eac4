import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { ChartConfiguration } from 'chart.js';

@Component({
  selector: 'app-pie',
  templateUrl: './pie.component.html',
  styleUrls: ['./pie.component.scss']
})
export class PieComponent implements OnChanges {
  @Input()
  centerIcon: string = 'assets/statistic/page.svg';
  @Input()
  unit: string = 'trang';
  @Input()
  dataset: {
    key: string;
    label: string;
    value: number;
    color: string;
    tooltip?: string;
  }[];
  doughnutChartOptions: ChartConfiguration<'doughnut'>['options'] = {
    cutout: '80%',
    responsive: true,
    plugins: { legend: { display: false } }
  };
  doughnutChartData: ChartConfiguration<'doughnut'>['data'];

  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    const labels = changes['dataset'].currentValue.map((item) => item.label);
    const data = [],
      backgroundColor = [],
      hoverBackgroundColor = [],
      hoverBorderColor = [];
    changes['dataset'].currentValue.forEach((item) => {
      data.push(item.value), backgroundColor.push(item.color);
      hoverBackgroundColor.push(item.color);
      hoverBorderColor.push(item.color);
    });
    this.doughnutChartData = {
      labels,
      datasets: [
        {
          data,
          backgroundColor,
          hoverBackgroundColor,
          hoverBorderColor
        }
      ]
    };
  }

  getTotal() {
    return this.dataset.reduce((acc, curr) => acc + curr.value, 0);
  }

  getPercentage(key) {
    const val = this.dataset.find((item) => item.key === key)?.value ?? 0;
    const total = this.getTotal();

    return Math.ceil((val / total) * 100);
  }
}
