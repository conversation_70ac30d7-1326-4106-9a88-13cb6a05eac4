import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment as env } from '@platform/environment/environment';

@Injectable({ providedIn: 'root' })
export class AuthService {
  constructor(private http: HttpClient) {}

  login(body: { username: string; password: string }) {
    const url = env.backendUrl + 'auth/login';
    return this.http.post<{ accessToken: string }>(url, body);
  }

  logout() {
    localStorage.clear();
    caches.keys().then((keys) => keys.forEach((key) => caches.delete(key)));
  }
}
