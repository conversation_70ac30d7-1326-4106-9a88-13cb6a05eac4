<div class="grid grid-cols-4 gap-6 statistic-container">
  <app-pie
    class="col-span-full xl:col-span-1"
    [dataset]="userDocumentCount"
    unit="văn bản"
    centerIcon="assets/statistic/document.svg"
  ></app-pie>
  <app-chart
    class="col-span-full xl:col-span-3"
    [dataset]="userDocumentStatByTime"
    [labelKey]="'time'"
    [dataKey]="'document'"
    [chartTitle]="'Biểu đồ tổng số lượng văn bản'"
    [yAxisTitle]="'Số văn bản'"
    [xAxisTitle]="dateFilter.xAxisTitleForChart"
    [tooltipLabel]="'Văn bản'"
  ></app-chart>
  <nz-tabset [nzTabBarExtraContent]="documentTYpeFilterTmpl" class="col-span-full">
    <nz-tab nzTitle="VĂN BẢN CỦA TÔI">
      <app-table
        class="col-span-full"
        [columns]="ownedDocumentStat.columns"
        [dataSource]="ownedDocumentStat.data"
        [dateFilter]="dateFilter"
        (refetchDataEvent)="setOwnedDocumentStat()"
        [hideSearch]="true"
        [hasTotalRow]="true"
      >
      </app-table>
    </nz-tab>
    <nz-tab nzTitle="VĂN BẢN ĐƯỢC CHIA SẺ">
      <app-table
        class="col-span-full"
        [columns]="sharedDocumentStat.columns"
        [dataSource]="sharedDocumentStat.data"
        [dateFilter]="dateFilter"
        (refetchDataEvent)="setSharedDocumentStat()"
        [hideSearch]="true"
        [hasTotalRow]="true"
      >
      </app-table>
    </nz-tab>
  </nz-tabset>
  <ng-template #documentTYpeFilterTmpl>
    <nz-select
      nzSize="large"
      class="w-[200px]"
      [(ngModel)]="documentTypeFilter"
      (ngModelChange)="handleDocumentTypeFilterChanged($event)"
      nzPlaceHolder="Tất cả loại văn bản"
      nzAllowClear
    >
      <nz-option nzValue="SelfConfig" nzLabel="Văn bản tự tạo"></nz-option>
      <nz-option nzValue="SystemBased" nzLabel="Văn bản hệ thống"></nz-option>
    </nz-select>
  </ng-template>
</div>
