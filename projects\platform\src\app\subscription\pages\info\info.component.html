<div class="dashboard-rpa">
	<div class="font-bold text-xl">
		<PERSON><PERSON>g cấp g<PERSON><PERSON> c<PERSON>ớ<PERSON>
	</div>
	<div class="p-5 mt-5 mb-5 text-center bg-white rounded-lg">
		<div class="mt-2 title-upgrade">Dịch vụ bóc tách và trích xuất thông tin</div>
		<div class="mt-4 select-subscription-length">
			<span (click)="setSelectedSubscription(1)" [class.active]="selectedSubscription === 1">01 tháng</span>
			<span (click)="setSelectedSubscription(6)" [class.active]="selectedSubscription === 6">06 tháng</span>
			<span (click)="setSelectedSubscription(12)" [class.active]="selectedSubscription === 12">12 tháng</span>
		</div>
		<div class="py-3 subscription-grid" [ngClass]="{ 'one-month': selectedSubscription === 1 }">
			<div *ngFor="let item of subscriptionsInfo ; let i = index" class="subscription-col">
				<div class="text-center">
					<div class="subscription-title">
						<div class="mt-3">
							<img class="h-6 inline-block" src="{{item.img}}" alt="">
						</div>
						<div class="mt-3">
							<h5 class="mt-3">{{ item.label || item.labels[selectedSubscription] }}</h5>
						</div>
					</div>
					<div class="odd-row" style="padding: 15px">
						<span class="mb-0 quota-number">{{item.price[selectedSubscription]}}</span>
						<span *ngIf="isNumber(item.price[selectedSubscription])"> VNĐ</span>
					</div>
					<div style="padding: 15px">
						<p class="mb-0 quota-number">{{ item.quota[selectedSubscription] }}</p>
						<p class="mb-0">Trang văn bản<span *ngIf="showPerNMonth(item)">/{{selectedSubscription}} tháng</span></p>
					</div>
					<div class=" odd-row" style="padding: 15px">
						<a (click)="paymentRequest(item)"
							class="block rounded-md uppercase text-center btn-register">{{isCurrentPlan(item)
							? 'Gói cước hiện tại' :
							item.isContact ?
							'Liên hệ' : 'Đăng ký' }}</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="text-left" style="font-size: 12px; color: #333333">
		<p class="mb-1"><span style="color: red">* </span>Lưu ý:</p>
		<ul class="list-disc list-inside">
			<li class="mb-1">Giá cước chưa bao gồm VAT.</li>
			<li class="mb-1">Trường hợp khách hàng có nhu cầu gói lớn hơn hoặc có nhu cầu về bóc tách thông tin văn bản, giấy
				tờ
				theo nghiệp vụ đề nghị liên hệ với VNPT IT để xây dựng chính sách giá phù hợp</li>
		</ul>
	</div>
</div>