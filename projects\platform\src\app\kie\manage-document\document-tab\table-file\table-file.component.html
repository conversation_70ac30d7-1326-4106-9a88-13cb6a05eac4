<div class="flex flex-col gap-4 mt-4">
  <div
    *ngIf="setOfCheckedId.size > 0"
    class="bg-brand-3/10 p-[10px] rounded items-center gap-6 transition-all duration-500 flex"
  >
    <div class="flex gap-2 border-r border-icon-1 pr-4 text-sm font-medium text-text-1">
      <img
        (click)="uncheckAll()"
        class="cursor-pointer"
        src="assets/kie/header-table/cancel.svg"
        alt="cancel-icon"
      />
      <p>
        Đ<PERSON> chọn (<span class="text-brand-1">{{ setOfCheckedId.size }}</span
        >)
      </p>
    </div>
    <div
      *ngIf="checkActionPermission('delete')"
      class="flex gap-2 text-sm font-medium text-text-1"
      (click)="deleteFileMany()"
    >
      <img
        class="cursor-pointer"
        src="assets/kie/header-table/delete.svg"
        alt="delete-icon"
      />
      <p>Xóa</p>
    </div>
    <div
      class="flex gap-2 text-sm font-medium text-text-1"
      (click)="downloadExportOcrResultMany()"
    >
      <img
        class="cursor-pointer"
        src="assets/kie/header-table/download.svg"
        alt="download-icon"
      />
      <p>Tải xuống</p>
    </div>
  </div>
</div>

<nz-table
  class="mt-4"
  #rowSelectionTable
  [nzTemplateMode]="true"
  nzShowPagination="false"
  nzShowSizeChanger
  [nzFrontPagination]="false"
  [nzData]="listFiles.files"
  (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
  [nzTableLayout]="'fixed'"
>
  <thead>
    <tr>
      <th
        nzWidth="40px"
        [nzDisabled]="!rowSelectionTable.data?.length"
        [nzChecked]="checked"
        [nzIndeterminate]="indeterminate"
        (nzCheckedChange)="onAllChecked($event)"
      ></th>
      <th>Tên tài liệu</th>
      <th>Chủ sở hữu</th>
      <th nzWidth="150px">Trạng thái</th>
      <th nzWidth="150px" nzRight>Ngày tạo</th>
      <th nzWidth="170px" nzRight>Lần chỉnh sửa cuối</th>
      <th nzWidth="100px" nzRight></th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let data of rowSelectionTable.data">
      <td
        [nzChecked]="setOfCheckedId.has(data.id)"
        [nzDisabled]="data.disabled"
        (nzCheckedChange)="onItemChecked(data.id, $event)"
      ></td>
      <td>
        <div
          (click)="viewFileDetail(data.id)"
          class="text-text-1 text-sm font-medium truncate break-words hover:text-brand-2 hover:cursor-pointer"
          nz-tooltip
          [nzTooltipTitle]="data.name.length > 30 ? data.name : ''"
        >
          {{ data.name }}
        </div>
      </td>
      <td>
        <div class="flex items-center gap-2">
          <nz-avatar
            class="shrink-0 text-white bg-[#ff3355] capitalize"
            [nzText]="data.creator.name.charAt(0)"
          ></nz-avatar>
          <div class="text-brand-2 text-sm font-medium truncate break-words">
            {{ data.creator.name }}
          </div>
        </div>
      </td>
      <td>
        <div
          class="flex items-center gap-2"
          *ngIf="data.status === 'not_accepted'; else confirmed"
        >
          <div class="h-2 w-2 rounded-full bg-[#FFA100]"></div>
          <p class="text-[#FFA100] font-medium">Chờ xác nhận</p>
        </div>
        <ng-template #confirmed>
          <div class="flex items-center gap-2">
            <div class="h-2 w-2 rounded-full bg-[#07A128]"></div>
            <p class="text-[#07A128] font-medium">Đã xác nhận</p>
          </div>
        </ng-template>
      </td>
      <td>
        <p class="text-text-1 text-sm font-normal">
          {{ data.createdAt | date: 'dd/MM/yyyy' }}
        </p>
      </td>
      <td>
        <p class="text-text-1 text-sm font-normal">
          {{ data.updatedAt | date: 'dd/MM/yyyy' }}
        </p>
      </td>
      <td>
        <div class="flex gap-2">
          <app-file-preview-drawer
            [fileLink]="data.fileLink"
            [fileName]="data.name"
            [triggerTemplate]="previewBtn"
          ></app-file-preview-drawer>
          <div nz-popover nzPopoverTrigger="click" [nzPopoverContent]="actionsTemplate">
            <img
              class="cursor-pointer"
              src="assets/kie/header-table/threedot.svg"
              alt="icon-more-action"
            />
          </div>
        </div>
      </td>
      <!-- actions template -->
      <ng-template #actionsTemplate>
        <div
          class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden min-w-[200px]"
        >
          <button
            class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': !checkActionPermission('view')
            }"
            (click)="viewFileDetail(data.id)"
          >
            <img src="assets/kie/actions/details.svg" alt="icon" />
            <p>Xem chi tiết</p>
          </button>
          <button
            class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': !checkActionPermission('change-name')
            }"
            (click)="changeFileName(data)"
          >
            <img src="assets/kie/actions/rename.svg" alt="icon" />
            <p>Đổi tên</p>
          </button>
          <button
            class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': !checkActionPermission('download')
            }"
            (click)="downloadExportOcrResult(data)"
          >
            <img src="assets/kie/actions/download.svg" alt="icon" />
            <p>Tải xuống</p>
          </button>
          <button
            class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-t border-line"
            [ngClass]="{
              'opacity-50 cursor-not-allowed': !checkActionPermission('delete')
            }"
            (click)="deleteFile(data)"
          >
            <img src="assets/kie/actions/delete.svg" alt="icon" />
            <p>Xóa</p>
          </button>
        </div>
      </ng-template>
    </tr>
  </tbody>
</nz-table>
<div
  *ngIf="!listFiles.files.length"
  class="w-full min-h-[500px] flex flex-col gap-5 items-center justify-center bg-[linear-gradient(0deg,rgba(255,255,255,1)50%,rgba(251,251,252,1)100%)]"
>
  <img src="assets/kie/document/empty-table.svg" />
  <ng-container *ngTemplateOutlet="uploadBtn"></ng-container>
</div>

<nz-pagination
  class="mt-4"
  [nzShowTotal]="rangeTemplate"
  [nzTotal]="listFiles.total"
  [nzPageSize]="listFiles.limit"
  [nzPageIndex]="listFiles.page"
  (nzPageIndexChange)="onPageChange($event)"
  [nzItemRender]="renderItemTemplate"
>
</nz-pagination>
<ng-template #rangeTemplate let-range="range" let-total>
  <div>
    Số bản ghi mỗi trang
    <nz-select
      class="w-[65px]"
      [ngModel]="listFiles.limit || 10"
      (ngModelChange)="onLimitChange($event)"
    >
      <nz-option [nzValue]="10" nzLabel="10"></nz-option>
      <nz-option [nzValue]="20" nzLabel="20"></nz-option>
      <nz-option [nzValue]="30" nzLabel="30"></nz-option>
    </nz-select>
  </div>
</ng-template>
<ng-template #renderItemTemplate let-type let-page="page">
  <ng-container [ngSwitch]="type">
    <a *ngSwitchCase="'page'">{{ page }}</a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'prev'">
      <img src="assets/kie/header-table/previous_page.svg" alt="prev-icon" />
    </a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'next'">
      <img src="assets/kie/header-table/next_page.svg" alt="next-icon" />
    </a>
    <a class="btn-dot" *ngSwitchCase="'prev_5'">...</a>
    <a class="btn-dot" *ngSwitchCase="'next_5'">...</a>
  </ng-container>
</ng-template>

<!-- prviewBtn -->
<ng-template #previewBtn let-click="click" let-mouseover="mouseover" let-text="text">
  <button (click)="click ? click() : null">
    <img src="assets/kie/actions/details.svg" alt="icon" />
  </button>
</ng-template>
