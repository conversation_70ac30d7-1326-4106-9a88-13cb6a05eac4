import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-delete-access',
  templateUrl: './modal-delete-access.component.html',
  styleUrls: ['./modal-delete-access.component.scss']
})
export class ModalDeleteAccessComponent implements OnInit {
  readonly nzModalData: {
    documentId: string;
    assignees: { id: string; name?: string }[];
  } = inject(NZ_MODAL_DATA);
  documentId = this.nzModalData.documentId;
  assignees = this.nzModalData.assignees;

  get warningText() {
    if (this.assignees.length > 1)
      return `Bạn có muốn xóa quyền truy cập của ${this.assignees.length} người dùng này`;
    else
      return `Bạn có muốn xóa quyền truy cập của người dùng ${this.assignees[0]?.name ?? 'này'}`;
  }

  constructor(
    private modalRef: NzModalRef,
    private kieService: KIEService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {}

  cancel() {
    this.modalRef.close();
  }

  delete(): void {
    this.kieService
      .deleteUsersFromDocument(
        this.documentId,
        this.assignees.map((item) => item.id)
      )
      .pipe(
        tap(() => {
          this.toastr.success('Xóa quyền truy cập thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
