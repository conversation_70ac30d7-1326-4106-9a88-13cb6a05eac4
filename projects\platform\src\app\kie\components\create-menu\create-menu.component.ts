import { Component, Input, OnInit } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Folder, SystemTemplate, listSystemTemplate } from '../../kie';
import { ModalCreateDocumentComponent } from '../modal-create-document/modal-create-document.component';
import { ModalCreateFolderComponent } from '../modal-create-folder/modal-create-folder.component';
import { ModalCreateSampleDocumentComponent } from '../modal-create-sample-document/modal-create-sample-document.component';
import { cloneDeep } from 'lodash';
const AMOUNT_TEMPLATE_SUGGESTED = 4;

@Component({
  selector: 'app-create-menu',
  templateUrl: './create-menu.component.html',
  styleUrls: ['./create-menu.component.scss']
})
export class CreateMenuComponent implements OnInit {
  @Input() listFolders: Folder[] = [];
  constructor(private modal: NzModalService) {}

  readonly listSystemTemplate: SystemTemplate[] = listSystemTemplate;

  menuItems = [
    ...this.listSystemTemplate.slice(0, AMOUNT_TEMPLATE_SUGGESTED),
    {
      id: '5',
      icon: 'assets/kie/document/other-template.svg',
      title: 'Mẫu giấy tờ khác',
      submenu: this.listSystemTemplate.slice(AMOUNT_TEMPLATE_SUGGESTED)
    }
  ];
  showModalCreateFolder(): void {
    this.modal.create({
      nzTitle: 'Thư mục mới',
      nzContent: ModalCreateFolderComponent,
      nzData: { listFolders: this.listFolders },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common'
    });
  }

  showModalCreateDocument(document: SystemTemplate): void {
    this.modal.create({
      nzTitle: 'Tạo mới văn bản',
      nzData: { designatedSystemTemplateId: document.id, listFolders: this.listFolders },
      nzContent: ModalCreateDocumentComponent,
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common'
    });
  }

  showModalCreateSampleDocument(): void {
    this.modal.create({
      nzContent: ModalCreateSampleDocumentComponent,
      nzData: { listFolders: this.listFolders },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '500px' }
    });
  }

  ngOnInit(): void {}
}
