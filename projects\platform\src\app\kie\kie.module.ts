import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { ConfigFieldsComponent } from './components/config-fields/config-fields.component';
import { CreateMenuComponent } from './components/create-menu/create-menu.component';
import { ModalCreateDocumentComponent } from './components/modal-create-document/modal-create-document.component';
import { ModalCreateFolderComponent } from './components/modal-create-folder/modal-create-folder.component';
import { ModalCreateSampleDocumentComponent } from './components/modal-create-sample-document/modal-create-sample-document.component';
import { DetailLayoutComponent } from './components/detail-layout/detail-layout.component';
import { DocumentLayoutComponent } from './components/document-layout/document-layout.component';
import { DocumentViewerComponent } from '@platform/app/components/document-viewer/document-viewer.component';
import { OcrResultComponent } from './components/ocr-result/ocr-result.component';
import { ModalCreateFilesComponent } from './components/modal-create-files/modal-create-files.component';
import { ConfigTemplateComponent } from './config-template/config-template.component';
import { ExtractFileComponent } from './extract-file/extract-file.component';
import { ConfigTabComponent } from './manage-document/config-tab/config-tab.component';
import { FileTabComponent } from './manage-document/document-tab/file-tab.component';
import { TableFileComponent } from './manage-document/document-tab/table-file/table-file.component';
import { ManageDocumentComponent } from './manage-document/manage-document.component';
import { ModalInvitationComponent } from './manage-document/permission-tab/modal-invatation/modal-invatation.component';
import { PermissionTabComponent } from './manage-document/permission-tab/permission-tab.component';
import { ModalDeleteAccessComponent } from './manage-document/permission-tab/table-permission/modal-delete-access/modal-delete-access.component';
import { ModalTransferOwnershipComponent } from './manage-document/permission-tab/table-permission/modal-transfer-ownership/modal-transfer-ownership.component';
import { TablePermissionComponent } from './manage-document/permission-tab/table-permission/table-permission.component';
import { KIERoutingModule } from './kie-routing.module';
import { SharedDocumentsComponent } from './shared-documents/shared-documents.component';
import { TableSharedDocumentComponent } from './shared-documents/table-shared-document/table-shared-document.component';
import { OnboardingComponent } from './onboarding/onboarding.component';
import { FilePreviewDrawerComponent } from './components/file-preview-drawer/file-preview-drawer.component';
import { NgxSpinnerModule } from 'ngx-spinner';
import { DocumentLayoutService } from './services/document-layout.service';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NzResizableModule } from 'ng-zorro-antd/resizable';
import { UploadFilesComponent } from './components/upload-files/upload-files.component';
import { ModalChangeFileNameComponent } from './manage-document/document-tab/actions/modal-change-file-name/modal-change-file-name.component';
import { ModalDeleteFileComponent } from './manage-document/document-tab/actions/modal-delete-file/modal-delete-file.component';
import { ModalChangeDocumentNameComponent } from './components/document-layout/actions/modal-change-document-name/modal-change-document-name.component';
import { ModalDeleteDocumentComponent } from './components/document-layout/actions/modal-delete-document/modal-delete-document.component';
import { ModalMoveDocumentComponent } from './components/document-layout/actions/modal-move-document/modal-move-document.component';
import { ModalCopyDocumentComponent } from './components/document-layout/actions/modal-copy-document/modal-copy-document.component';
import { ModalDeleteFolderComponent } from './components/document-layout/actions/modal-delete-folder/modal-delete-folder.component';
import { ModalChangeFolderNameComponent } from './components/document-layout/actions/modal-change-folder-name/modal-change-folder-name.component';
import { ModalChangeDocumentTemplateFileComponent } from './manage-document/config-tab/actions/modal-change-document-template-file/modal-change-document-template-file.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { ModalConfigFiltersForFoldersListComponent } from './components/document-layout/actions/modal-config-filters-for-folders-list/modal-config-filters-for-folders-list.component';

@NgModule({
  declarations: [
    ManageDocumentComponent,
    ConfigTemplateComponent,
    ExtractFileComponent,
    DetailLayoutComponent,
    ConfigFieldsComponent,
    OcrResultComponent,
    CreateMenuComponent,
    DocumentLayoutComponent,
    SharedDocumentsComponent,
    ModalCreateFolderComponent,
    ModalCreateDocumentComponent,
    ModalCreateFilesComponent,
    ModalCreateSampleDocumentComponent,
    FileTabComponent,
    ConfigTabComponent,
    PermissionTabComponent,
    TablePermissionComponent,
    ModalDeleteAccessComponent,
    ModalTransferOwnershipComponent,
    ModalInvitationComponent,
    TableSharedDocumentComponent,
    TableFileComponent,
    OnboardingComponent,
    FilePreviewDrawerComponent,
    UploadFilesComponent,
    ModalChangeFileNameComponent,
    ModalDeleteFileComponent,
    ModalChangeDocumentNameComponent,
    ModalDeleteDocumentComponent,
    ModalMoveDocumentComponent,
    ModalCopyDocumentComponent,
    ModalDeleteFolderComponent,
    ModalChangeFolderNameComponent,
    ModalChangeDocumentTemplateFileComponent,
    ModalConfigFiltersForFoldersListComponent
  ],
  imports: [
    CommonModule,
    KIERoutingModule,
    NzButtonModule,
    NzCollapseModule,
    NzIconModule,
    NzDropDownModule,
    NzBreadCrumbModule,
    NzTabsModule,
    NzInputModule,
    NzDatePickerModule,
    NzUploadModule,
    NzTableModule,
    NzAvatarModule,
    NzPaginationModule,
    NzPopoverModule,
    NzSelectModule,
    FormsModule,
    NzSwitchModule,
    NzCheckboxModule,
    NzTagModule,
    ReactiveFormsModule,
    NzToolTipModule,
    NzDrawerModule,
    NzFormModule,
    NzRadioModule,
    NgxSpinnerModule,
    DragDropModule,
    NzResizableModule,
    ScrollingModule,
    DocumentViewerComponent
  ],
  exports: [],
  providers: [DocumentLayoutService]
})
export class KIEModule {}
