<div *ngIf="step === STEPS[0]">
  <div class="border-b border-line px-4">
    <div class="border-b border-line py-2">
      <div
        #inputFile
        class="flex relative items-center justify-center gap-3 bg-bg-1 rounded-lg p-4 border border-line"
        [ngClass]="{
          'hover:border-brand-2': imageList?.length < RULE_ACCEPT.maxFileCount,
          'cursor-not-allowed': imageList?.length >= RULE_ACCEPT.maxFileCount
        }"
        (dragover)="
          imageList?.length < RULE_ACCEPT.maxFileCount &&
            inputFile?.classList?.add('!border-brand-2')
        "
        (dragleave)="
          imageList?.length < RULE_ACCEPT.maxFileCount &&
            inputFile?.classList?.remove('!border-brand-2')
        "
        (drop)="
          imageList?.length < RULE_ACCEPT.maxFileCount &&
            inputFile?.classList?.remove('!border-brand-2')
        "
      >
        <input
          multiple
          class="absolute top-0 left-0 w-full h-full z-10 opacity-0 text-[0px] cursor-pointer"
          [ngClass]="{
            '!-z-10': imageList?.length >= RULE_ACCEPT.maxFileCount
          }"
          type="file"
          [accept]="RULE_ACCEPT.accept"
          #inputFileElem
          (change)="handleFileInputChange($event, inputFileElem)"
          (cancel)="handleFileInputCancel($event, inputFileElem)"
        />
        <div class="flex items-center justify-center gap-3">
          <img class="w-5" src="/assets/kie/header-table/upload.svg" alt="icon-upload" />
          <div>
            <div class="font-medium text-sm">
              <span class="text-brand-1">Chọn</span> hoặc kéo thả các ảnh của bạn tại đây
            </div>
            <div class="text-text-3 text-xs">
              Định dạng <b>{{ RULE_ACCEPT.typeLabel }}</b>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      *ngIf="!imageList?.length"
      class="min-h-[340px] w-full flex flex-col gap-5 items-center justify-center py-2"
    >
      <img src="assets/kie/document/empty-table.svg" />
    </div>
    <div
      *ngIf="imageList?.length"
      class="file-list h-[340px] overflow-auto py-2"
      cdkDropList
      (cdkDropListDropped)="fileDropped($event)"
    >
      <div class="text-xs font-medium mb-2">
        File được ghép theo thứ tự từ trên xuống dưới.
      </div>
      <div
        *ngFor="let file of imageList; index as i"
        cdkDrag
        class="file-item bg-[#F0F1F4] border border-line rounded-lg px-4 py-2 mt-1"
      >
        <div class="w-full flex items-center gap-2">
          <img
            cdkDragHandle
            class="hover:cursor-grab"
            src="assets/kie/document/draggable.svg"
            alt="+"
          />
          <button
            class="shrink-0"
            nz-tooltip
            [nzTooltipTitle]="'Preview ảnh'"
            (click)="showPreviewImage(file)"
          >
            <img
              *ngIf="file.type === 'image/png'"
              src="assets/kie/header-table/png-icon.svg"
              alt="PNG icon"
            />
            <img
              *ngIf="file.type === 'image/jpeg' || fileType === 'image/jpg'"
              src="assets/kie/header-table/jpeg-icon.svg"
              alt="JPEG icon"
            />
          </button>
          <div class="flex-1 truncate">
            <p
              nz-tooltip
              [nzTooltipTitle]="file.name.length > 50 ? file.name : ''"
              nzTooltipColor="#000"
              class="truncate font-semibold"
            >
              {{ file.name }}
            </p>
            <p class="text-xs">{{ convertBytesToMB(file.size) }}</p>
          </div>
          <div class="font-medium text-xs">Trang {{ i + 1 }}</div>
          <button class="shrink-0" nz-button nzType="link" (click)="removeFile(i)">
            <i nz-icon class="text-2xl text-red-500" nz-size="large" nzType="delete"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="py-6 flex items-center justify-center">
    <button
      class="py-2 bg-[#1E5FD5] text-white font-medium rounded-lg w-[125px] flex items-center justify-center gap-2"
      [ngClass]="{
        'bg-bg-1 cursor-not-allowed text-text-1':
          imageList?.length < RULE_ACCEPT.minFileCount
      }"
      (click)="mergeImagesIntoPdf()"
    >
      <ng-template *ngTemplateOutlet="mergeIcon" />
      Ghép
    </button>
  </div>
</div>

<div *ngIf="step === STEPS[1]" class="h-[300px] flex flex-col">
  <div
    class="relative flex flex-col items-center justify-end gap-3 flex-auto border-b border-line py-2 text-center"
  >
    <ngx-spinner
      [name]="'images-into-pdf-loading'"
      bdColor="rgba(0, 0, 0, 0.0)"
      size="medium"
      color="#1E5FD5"
      type="ball-pulse-sync"
      [fullScreen]="false"
    >
    </ngx-spinner>
    <div class="text-xl font-semibold text-text-1">Đang ghép...</div>
    <div class="text-text-3">
      Hệ thống đang trong quá trình xử lý ghép ảnh. <br />
      Xin bạn vui lòng chờ trong giây lát.
    </div>
  </div>
  <div class="py-6 flex items-center justify-center">
    <button
      class="py-2 border border-[#1E5FD5] text-[#1E5FD5] font-medium rounded-lg w-[125px] flex items-center justify-center gap-2"
      (click)="cancelMerge()"
    >
      Hủy
    </button>
  </div>
</div>

<div *ngIf="step === STEPS[2] && createdPdf" class="h-[300px] flex flex-col">
  <div
    class="border-b border-line flex-auto flex flex-col items-center justify-center gap-2 p-4"
  >
    <ng-template *ngTemplateOutlet="successIcon" />
    <div class="text-lg font-semibold">Đã ghép xong file</div>
    <div
      class="w-full flex items-center gap-2 bg-[#F0F1F4] border border-line rounded-lg px-4 py-2"
    >
      <img
        class="shrink-0 w-7"
        src="assets/kie/header-table/pdf-icon.svg"
        alt="PDF icon"
      />
      <div class="flex-1 truncate">
        <p
          *ngIf="step === STEPS[2] && !isEditingCreatedPdfName"
          class="truncate font-semibold leading-6"
        >
          <a target="_blank" [href]="createdPdf.url">
            {{ createdPdf.name + '.pdf' }}
          </a>
        </p>
        <input
          *ngIf="step === STEPS[2] && isEditingCreatedPdfName"
          nz-input
          placeholder="Nhập tên văn bản"
          nzSize="small"
          [(ngModel)]="createdPdf.name"
        />
        <p class="font-medium text-xs mt-[2px]">
          {{ convertBytesToMB(createdPdf.file.size) }}
        </p>
      </div>
      <button nz-tooltip="Chỉnh sửa tên" (click)="toggleEditingCreatedPdfName()">
        <i nz-icon class="text-xl text-brand-1" nzType="edit"></i>
      </button>
      <a
        class="shrink-0"
        nz-button
        nzType="link"
        nz-tooltip="Tải xuống"
        [download]="createdPdf.name + '.pdf'"
        [href]="createdPdf.url"
      >
        <i nz-icon class="text-xl text-brand-1" nzType="download"></i>
      </a>
    </div>
  </div>
  <div class="py-6 flex items-center justify-center gap-4">
    <button
      class="py-2 border border-[#1E5FD5] text-[#1E5FD5] font-medium rounded-lg w-[125px] flex items-center justify-center gap-2"
      (click)="discardCreatedPdf()"
    >
      Hủy
    </button>
    <button
      class="py-2 border border-[#1E5FD5] bg-[#1E5FD5] text-white font-medium rounded-lg w-[125px] flex items-center justify-center gap-2"
      (click)="emitCreatedPdf()"
    >
      Sử dụng
    </button>
  </div>
</div>

<ng-template #previewDrawerTmpl let-data let-drawerRef="drawerRef">
  <div class="h-full flex flex-col items-center justify-around">
    <div class="h-[90%]">
      <img class="object-contain h-full" [src]="data?.previewLink" [alt]="data?.name" />
    </div>
    <div
      class="bg-[#000000CC] rounded-[28px] px-6 py-[6px] text-center truncate max-w-full"
      *ngIf="data?.name"
    >
      {{ data?.name }}
    </div>
  </div>
</ng-template>

<ng-template #mergeIcon>
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.99984 18.3327H12.9998C17.1665 18.3327 18.8332 16.666 18.8332 12.4993V7.49935C18.8332 3.33268 17.1665 1.66602 12.9998 1.66602H7.99984C3.83317 1.66602 2.1665 3.33268 2.1665 7.49935V12.4993C2.1665 16.666 3.83317 18.3327 7.99984 18.3327Z"
      stroke="white"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M8 1.66602L12.125 18.3327"
      stroke="white"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10.1082 10.1836L2.1665 12.5003"
      stroke="white"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #successIcon>
  <svg
    width="80"
    height="80"
    viewBox="0 0 80 80"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="40" cy="40" r="40" fill="#E6FCEF" />
    <circle cx="40.0002" cy="40" r="27" fill="url(#paint0_linear_13829_54266)" />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M53.0818 29.8399C54.2748 30.9897 54.3099 32.8889 53.1601 34.0818L38.7034 49.0818C38.1379 49.6685 37.3582 50 36.5433 50C35.7285 50 34.9487 49.6686 34.3833 49.0819L26.8399 41.2552C25.6902 40.0622 25.7252 38.163 26.9181 37.0133C28.1111 35.8635 30.0103 35.8985 31.1601 37.0915L36.5433 42.6769L48.8399 29.9182C49.9897 28.7252 51.8889 28.6902 53.0818 29.8399Z"
      fill="white"
    />
    <defs>
      <linearGradient
        id="paint0_linear_13829_54266"
        x1="31.8375"
        y1="16.1395"
        x2="54.0002"
        y2="63"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#58DB37" />
        <stop offset="1" stop-color="#00AE11" />
      </linearGradient>
    </defs>
  </svg>
</ng-template>
