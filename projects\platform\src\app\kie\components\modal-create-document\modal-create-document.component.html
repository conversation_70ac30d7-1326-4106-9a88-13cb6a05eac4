<div class="flex flex-col items-center gap-4">
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="document-selection"
      >V<PERSON>n bản <span class="text-red-500">*</span></label
    >
    <nz-select
      [disabled]="!!designatedSystemTemplateId"
      nzShowSearch
      id="template-selection"
      nzSize="large"
      [(ngModel)]="selectedTemplateId"
      nzPlaceHolder="Chọn một văn bản"
    >
      <nz-option
        *ngFor="let template of listSystemTemplate"
        [nzValue]="template.value"
        [nzLabel]="template.label"
      ></nz-option>
    </nz-select>
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="name-file"
      >Tên văn bản <span class="text-red-500">*</span></label
    >
    <input
      [autofocus]="true"
      id="name-file"
      nzSize="large"
      nz-input
      [(ngModel)]="nameDocument"
      placeholder="Tên văn bản"
      class="rounded-lg"
    />
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="folder-selection"
      >Thư mục <span class="text-red-500">*</span></label
    >
    <nz-select
      id="folder-selection"
      nzSize="large"
      [(ngModel)]="selectedFolderId"
      nzPlaceHolder="Chọn một thư mục"
    >
      <nz-option
        *ngFor="let folder of listFolders"
        [nzValue]="folder.id"
        [nzLabel]="folder.name"
      ></nz-option>
    </nz-select>
  </div>

  <button
    (click)="createDocument()"
    class="rounded-lg flex items-center px-3 gap-2 py-2 bg-brand-1 text-white text-sm font-medium"
  >
    <i nz-icon nzType="plus" nzTheme="outline"></i>
    Tạo mới
  </button>
</div>
