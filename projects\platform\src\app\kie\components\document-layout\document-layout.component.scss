:host {
  @media (prefers-color-scheme: light) {
    background-size: contain;
    background-color: white;
    background-image: linear-gradient(rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.024) 5.26%,
        rgba(255, 255, 255, 0.055) 9.79%,
        rgba(255, 255, 255, 0.098) 13.69%,
        rgba(255, 255, 255, 0.145) 17.08%,
        rgba(255, 255, 255, 0.2) 20.04%,
        rgba(255, 255, 255, 0.267) 22.7%,
        rgba(255, 255, 255, 0.333) 25.16%,
        rgba(255, 255, 255, 0.408) 27.51%,
        rgba(255, 255, 255, 0.486) 29.87%,
        rgba(255, 255, 255, 0.57) 32.34%,
        rgba(255, 255, 255, 0.65) 35.02%,
        rgba(255, 255, 255, 0.737) 38.02%,
        rgba(255, 255, 255, 0.824) 41.45%,
        rgba(255, 255, 255, 0.914) 45.41%,
        rgb(255, 255, 255) 50%),
      url('~projects/platform/src/assets/kie/document/bg.png');
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #cbcbcb;
  }

  ::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }

  ::ng-deep {
    /* document-layout takes all remaining space */
    flex: auto;
    /* fix height with remaining space, make overflow children scroll inside document-layout */
    height: 1px;
    display: flex;

    .ant-collapse-borderless {
      background-color: transparent;

      .ant-collapse-item {
        border: none;

        .ant-collapse-header {
          padding: 8px 16px;
          align-items: center;
        }

        .ant-collapse-content>.ant-collapse-content-box {
          padding: 0;
        }
      }
    }
  }
}