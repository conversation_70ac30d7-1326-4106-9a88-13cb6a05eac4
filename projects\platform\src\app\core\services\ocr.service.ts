import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { environment } from '@platform/environment/environment';
import { cloneDeep, forEach, get, isEqual, pick } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import {
  BehaviorSubject,
  catchError,
  concatMap,
  defer,
  delay,
  delayWhen,
  distinctUntilChanged,
  EMPTY,
  filter,
  from,
  interval,
  map,
  of,
  repeat,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  timer,
  toArray
} from 'rxjs';
import UtilsService from './utils.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class OcrService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private readonly cacheName = 'ocr-module';
  private cacheData: {
    preprocessing: {
      fileList: {
        id: string;
        name: string;
        size: string;
        numPages?: number;
        createdAt: Date;
        pageSelectionText?: string;
      }[];
      showCanDeactivateNotice: boolean;
    };
    'in-progress': {
      fileList: {
        id: string;
        sessionId: string;
        name: string;
        lastUpdated: Date; // is set with each server response
        /* error - server response */
        errorAttemptList?: {
          errors?: string[];
          message?: string;
          status?: string;
          statusCode?: string;
        }[];
        /* progress - server response */
        progress?: {
          processedPages: number;
          remainingPages: number;
          warningMessages: string[];
          warnings: string[];
        };
      }[];
      showCanDeactivateNotice: boolean;
    };
    finished: {
      fileList: {
        id: string;
        name: string;
        exportedInJSONLink: string;
        finishedAt: Date;
      }[];
      showCanDeactivateNotice: boolean;
    };
  };
  get initialCacheData() {
    return {
      preprocessing: { fileList: [], showCanDeactivateNotice: false },
      'in-progress': { fileList: [], showCanDeactivateNotice: false },
      finished: { fileList: [], showCanDeactivateNotice: false }
    };
  }
  private cacheStorage: Cache;

  private fileListCountSubject = new BehaviorSubject({
    preprocessing: 0,
    ['in-progress']: 0,
    finished: 0
  });
  get fileListCount() {
    return this.fileListCountSubject.value;
  }
  get fileListCount$() {
    return this.fileListCountSubject.asObservable().pipe(
      takeUntil(this.destroy$),
      filter((val) => !!val),
      distinctUntilChanged(
        (prev, curr) => isEqual(prev, curr) // only emit value to subscriber if any changes
      )
    );
  }

  MaxPreprocessingFileListCount = 20;
  MaxInProgressFileListCount = 10;
  MaxErrorAttemptCount = 2;

  private preprocessingFileListSubject = new BehaviorSubject<
    ((typeof this.cacheData)['preprocessing']['fileList'][number] & {
      file: File;
      link: string;
    })[]
  >(null);
  get preprocessingFileList() {
    return this.preprocessingFileListSubject.value;
  }
  get preprocessingFileList$() {
    /* should trigger loadPreprocessingFileListFromCache here */
    /* each time any subscriber want preprocessingFileList$. load from cache first, this would ensure the data from preprocessingFileListSubject is always the latest */
    this.loadPreprocessingFileListFromCache();
    return this.preprocessingFileListSubject.asObservable().pipe(
      takeUntil(this.destroy$),
      filter((val) => !!val), // dont emit intial null value to subscriber
      distinctUntilChanged(
        (prev, curr) => isEqual(prev, curr) // only emit value to subscriber if any changes
      )
    );
  }

  private inProgressFileListSubject = new BehaviorSubject<
    ((typeof this.cacheData)['in-progress']['fileList'][number] & {
      file: File;
      link: string;
    })[]
  >(null);
  get inProgressFileList() {
    return this.inProgressFileListSubject.value;
  }
  get inProgressFileList$() {
    this.loadInProgressFileListFromCache();
    return this.inProgressFileListSubject.asObservable().pipe(
      takeUntil(this.destroy$),
      filter((val) => !!val), // dont emit intial null value to subscriber
      distinctUntilChanged(
        (prev, curr) => isEqual(prev, curr) // only emit value to subscriber if any changes
      )
    );
  }

  private finishedFileListSubject = new BehaviorSubject<
    ((typeof this.cacheData)['finished']['fileList'][number] & {
      file: File;
      link: string;
    })[]
  >(null);
  get finishedFileList() {
    return this.finishedFileListSubject.value;
  }
  get finishedFileList$() {
    this.loadFinishedFileListFromCache();
    return this.finishedFileListSubject.asObservable().pipe(
      takeUntil(this.destroy$),
      filter((val) => !!val), // dont emit intial null value to subscriber
      distinctUntilChanged(
        (prev, curr) => isEqual(prev, curr) // only emit value to subscriber if any changes
      )
    );
  }

  constructor(
    private toastr: ToastrService,
    private http: HttpClient,
    private utilsService: UtilsService,
    private router: Router
  ) {
    try {
      /* this.cacheData represents data from localStorage cache */
      const tmp = JSON.parse(localStorage.getItem(this.cacheName));
      if (!tmp) {
        this.cacheData = this.initialCacheData;
        this.saveCacheData();
      } else this.cacheData = tmp;
    } catch (error) {
      this.cacheData = this.initialCacheData;
      this.saveCacheData();
    } finally {
      /* initial population regardless saveCacheData is called */
      this.fileListCountSubject.next({
        preprocessing: this.cacheData.preprocessing.fileList.length,
        'in-progress': this.cacheData['in-progress'].fileList.length,
        finished: this.cacheData.finished.fileList.length
      });
    }

    if (!caches) {
      toastr.warning('Trình duyệt không hỗ trợ chức năng bộ nhớ đệm');
      return;
    }
    caches
      .open(this.cacheName)
      .then((cache) => {
        // console.log(`open 'ocr-module' cache sucessfully`);
        this.cacheStorage = cache;
        /* populate data into subjects after both localStorage cache and file cache storage are set */
        /* the subject helps with pushing latest data to its subscriber */
        this.loadPreprocessingFileListFromCache();
        this.loadInProgressFileListFromCache();
        this.loadFinishedFileListFromCache();

        /* 3s initial delay */
        timer(3000)
          .pipe(
            switchMap(() => {
              const fileList = this.cacheData['in-progress'].fileList.filter(
                (file) =>
                  !file.errorAttemptList ||
                  file.errorAttemptList?.length <= this.MaxErrorAttemptCount
              );
              if (!fileList.length) return EMPTY; // return EMPTY would skip delay(3000) and tap(), move straight to repeat() and timer(3000)
              return from(fileList).pipe(
                concatMap((file, index) =>
                  this.ocrScanTableAsyncResult(file.sessionId).pipe(
                    map((a) => ({ id: file.id, result: a.object })), // return progress resp
                    catchError((err) => of({ id: file.id, error: err })), // return error resp
                    delay(200) // delay 200ms between each get result request
                  )
                ),
                toArray(), // wait for all get result requests are finished
                tap((ocrResultList) => {
                  // console.log('ocrResultList', ocrResultList);

                  const finishedFileList: {
                    id: string;
                    name: string;
                    exportedInJSONLink: string;
                  }[] = [];
                  const updatingFileList: {
                    id: string;
                    error?: any;
                    progress?: any;
                  }[] = [];
                  for (const ocrResult of ocrResultList) {
                    const updatingFile = fileList.find(
                      (file) => file.id === ocrResult.id
                    );
                    if (!updatingFile) continue;

                    if (ocrResult['error'])
                      updatingFileList.push({
                        id: updatingFile.id,
                        error: get(ocrResult, 'error.error')
                      });

                    if (ocrResult['result'])
                      if (ocrResult['result']?.['link'])
                        finishedFileList.push({
                          id: updatingFile.id,
                          name: updatingFile.name,
                          exportedInJSONLink: ocrResult['result']?.['link']
                        });
                      else
                        updatingFileList.push({
                          id: updatingFile.id,
                          progress: {
                            processedPages: get(
                              ocrResult,
                              'result.num_of_processed_page'
                            ),
                            remainingPages: get(
                              ocrResult,
                              'result.num_of_remaining_pages'
                            ),
                            warningMessages: get(ocrResult, 'result.warningMessages'),
                            warnings: get(ocrResult, 'result.warnings')
                          }
                        });
                  }

                  this.updateInProgressFilesToCache(updatingFileList);
                  this.moveInProgressFilesToFinished(finishedFileList);
                }),
                switchMap(() => defer(this.loadInProgressFileListFromCache.bind(this)))
              );
            }),
            delay(3000),
            repeat(),
            takeUntil(this.destroy$)
          )
          .subscribe();
      })
      .catch((err) => {
        console.log(err);
        toastr.warning('Mở bộ nhớ đệm "ocr-module" xảy ra lỗi');
      });

    this.utilsService.onLogout$
      .asObservable()
      .pipe(
        tap(async () => {
          /* cleanup state, ensure next user login do not see previous session state */
          this.cacheData = this.initialCacheData;
          this.saveCacheData();
          this.preprocessingFileListSubject.next([]);
          this.inProgressFileListSubject.next([]);
          this.finishedFileListSubject.next([]);
          caches.delete(this.cacheName);
        })
      )
      .subscribe();
  }

  /* cache preprocessing */

  private async loadPreprocessingFileListFromCache() {
    // only load after cacheStorage is open and cacheData is set
    if (!this.cacheStorage || !this.cacheData?.preprocessing) return;

    /* FIXME: illogical */
    /* 
      if data in cacheData.preprocessing is different with current preprocessingFileListSubject
      => next data into the preprocessingFileListSubject => notify all subscriber
    */
    if (
      this.preprocessingFileList &&
      isEqual(this.cacheData.preprocessing.fileList, this.preprocessingFileList)
    )
      return;

    const fileList = [];
    const removedCachedFileIdList = [];
    for (const cachedFile of this.cacheData.preprocessing.fileList) {
      const fileResp = await this.cacheStorage.match(
        `/preprocessing/${cachedFile.id}-${cachedFile.name}`
      );

      if (!fileResp) {
        removedCachedFileIdList.push(cachedFile.id);
        continue;
      }
      const file = new File([await fileResp.arrayBuffer()], cachedFile.name, {
        type: fileResp.headers.get('Content-Type')
      });
      fileList.push({
        file,
        link: URL.createObjectURL(file),
        ...cachedFile,
        createdAt: new Date(cachedFile.createdAt)
      });
    }
    this.preprocessingFileListSubject.next(fileList);
    if (removedCachedFileIdList.length) {
      this.cacheData.preprocessing.fileList =
        this.cacheData.preprocessing.fileList.filter(
          (file) => !removedCachedFileIdList.includes(file.id)
        );
      this.saveCacheData();
    }
  }

  async addPreprocessingFilesToCache(
    files: {
      id: string;
      file: File;
      name: string;
      size: string;
      numPages?: number;
      createdAt: Date;
      pageSelectionText?: string;
    }[],
    prefix?: string,
    orderIdList?: string[]
  ) {
    if (
      files.length + this.cacheData.preprocessing.fileList.length >
      this.MaxPreprocessingFileListCount
    )
      throw new Error(
        `Maximum number of in progress file at once is ${this.MaxPreprocessingFileListCount}`
      );

    for (const file of files) {
      await this.cacheStorage.put(
        `${prefix ? prefix + '/' : ''}${file.id}-${file.name}`,
        new Response(await file.file.arrayBuffer(), {
          status: 200,
          headers: { 'Content-Type': file.file.type },
          statusText: 'OK'
        })
      );
      delete file.file;
      this.cacheData.preprocessing.fileList = [
        file,
        ...this.cacheData.preprocessing.fileList
      ];
    }
    if (orderIdList?.length === this.cacheData.preprocessing.fileList.length)
      this.cacheData.preprocessing.fileList.sort(
        (a, b) => orderIdList.indexOf(a.id) - orderIdList.indexOf(b.id)
      );
    this.saveCacheData();
  }

  async deletePreprocessingFileFromCache(file: {
    id: string;
    file: File;
    name: string;
    size: string;
    numPages?: number;
  }) {
    await this.cacheStorage.delete(`preprocessing/${file.id}-${file.name}`);
    this.cacheData.preprocessing.fileList = this.cacheData.preprocessing.fileList.filter(
      (item) => item.id !== file.id
    );
    this.saveCacheData();
  }

  updatePreprocessingFileInCache(
    fileId: string,
    updateProps: { numPages?: number; pageSelectionText?: string }
  ) {
    if (!this.cacheData) return;
    const fileIndex = this.cacheData.preprocessing.fileList.findIndex(
      (file) => file.id === fileId
    );
    if (fileIndex === -1) return;
    this.cacheData.preprocessing.fileList[fileIndex] = {
      ...this.cacheData.preprocessing.fileList[fileIndex],
      ...updateProps
    };
    this.saveCacheData();
  }

  /* cache in-progress */

  private async loadInProgressFileListFromCache() {
    if (!this.cacheStorage || !this.cacheData?.['in-progress']) return;

    /* FIXME: illogical */
    if (
      this.inProgressFileList &&
      isEqual(this.cacheData['in-progress'].fileList, this.inProgressFileList)
    )
      return;

    const fileList = [];
    const removedCachedFileIdList = [];
    for (const cachedFile of this.cacheData['in-progress'].fileList) {
      const fileResp = await this.cacheStorage.match(
        `/processed/${cachedFile.id}-${cachedFile.name}`
      );

      if (!fileResp) {
        removedCachedFileIdList.push(cachedFile.id);
        continue;
      }
      const file = new File([await fileResp.arrayBuffer()], cachedFile.name, {
        type: fileResp.headers.get('Content-Type')
      });
      fileList.push({
        file,
        link: URL.createObjectURL(file),
        ...cachedFile,
        lastUpdated: new Date(cachedFile.lastUpdated)
      });
    }
    this.inProgressFileListSubject.next(fileList);
    if (removedCachedFileIdList.length) {
      this.cacheData['in-progress'].fileList = this.cacheData[
        'in-progress'
      ].fileList.filter((file) => !removedCachedFileIdList.includes(file.id));
      this.saveCacheData();
    }
  }

  async addInProgressFilesToCache(
    files: {
      id: string;
      sessionId: string;
      file: File;
      name: string;
    }[]
  ) {
    if (
      files.length + this.cacheData['in-progress'].fileList.length >
      this.MaxInProgressFileListCount
    )
      throw new Error(
        `Maximum number of in progress file at once is ${this.MaxInProgressFileListCount}`
      );

    for (const file of files) {
      await this.cacheStorage.put(
        `processed/${file.id}-${file.name}`,
        new Response(await file.file.arrayBuffer(), {
          status: 200,
          headers: { 'Content-Type': file.file.type },
          statusText: 'OK'
        })
      );
      delete file.file;
      this.cacheData['in-progress'].fileList = [
        ...this.cacheData['in-progress'].fileList,
        { ...file, lastUpdated: new Date() }
      ];
    }
    this.saveCacheData();
  }

  updateInProgressFilesToCache(
    updatingFileList: { id: string; error?: any; progress?: any }[]
  ) {
    const evictedFileList = [];
    for (const { id, error, progress } of updatingFileList) {
      const updatingFile = this.cacheData['in-progress'].fileList.find(
        (file) => file.id === id
      );
      if (!updatingFile) continue;
      if (error) {
        delete updatingFile.progress;
        if (updatingFile['errorAttemptList']) {
          if (updatingFile['errorAttemptList'].length === this.MaxErrorAttemptCount)
            evictedFileList.push(updatingFile);
          updatingFile['errorAttemptList'].push(error);
        } else updatingFile['errorAttemptList'] = [error];
      }
      if (progress) {
        delete updatingFile.errorAttemptList;
        updatingFile['progress'] = progress;
      }
      updatingFile.lastUpdated = new Date();
    }

    if (evictedFileList.length) {
      const toast = this.toastr.error(
        `<div>Tạm dừng xử lý với các tài liệu lỗi quá ${this.MaxErrorAttemptCount} lần, vui lòng thử lại</div>
          <ul class="list-disc list-inside">
            ${evictedFileList.map((file) => '<li>' + file.name + '</li>').join('')}
          </ul>
        `,
        'Nhận dạng ký tự',
        { enableHtml: true, closeButton: true }
      );
      toast.onTap
        .pipe(
          take(1),
          tap(() => this.router.navigate(['ocr', 'in-progress']))
        )
        .subscribe();
    }
    this.saveCacheData();
  }

  cancelScanTableAsyncAndDeleteInProgressFilesFromCache(
    fileList: { id: string; name: string; sessionId: string }[],
    forceFileRemovalFromCache?: boolean
  ) {
    const failedCancelOcrScanTableAsyncResultList = [];
    const successCancelOcrScanTableAsyncResultList = [];
    return from(fileList).pipe(
      concatMap((file) =>
        this.cancelOcrScanTableAsyncResult(file.sessionId).pipe(
          tap(() => successCancelOcrScanTableAsyncResultList.push(file)),
          catchError(() => {
            if (forceFileRemovalFromCache)
              successCancelOcrScanTableAsyncResultList.push(file);
            else failedCancelOcrScanTableAsyncResultList.push(file);
            return EMPTY;
          })
        )
      ),
      toArray(), // wait for all cancel request are done
      tap(() => {
        if (failedCancelOcrScanTableAsyncResultList.length)
          this.toastr.error(
            `<div>Hủy xử lý thất bại với ${failedCancelOcrScanTableAsyncResultList.length} tài liệu:</div>
              <ul class="list-disc list-inside">
                ${failedCancelOcrScanTableAsyncResultList.map((file) => '<li>' + file.name + '</li>').join('')}
              </ul>
            `,
            'Nhận dạng ký tự',
            { enableHtml: true }
          );
      }),
      switchMap(() =>
        successCancelOcrScanTableAsyncResultList.length
          ? defer(async () => {
              await this.deleteInProgressFilesFromCache(
                successCancelOcrScanTableAsyncResultList
              );
              await this.loadInProgressFileListFromCache();
              this.toastr.info(
                `<div>Đã hủy xử lý với ${successCancelOcrScanTableAsyncResultList.length} tài liệu:</div>
                  <ul class="list-disc list-inside">
                    ${successCancelOcrScanTableAsyncResultList.map((file) => '<li>' + file.name + '</li>').join('')}
                  </ul>
                `,
                'Nhận dạng ký tự',
                { enableHtml: true }
              );
            })
          : EMPTY
      )
    );
  }

  resetErrorAttemptList(fileIds: string[]) {
    this.cacheData['in-progress'].fileList.forEach((file) => {
      if (!fileIds.includes(file.id) || !file.errorAttemptList) return;
      file.errorAttemptList = [];
    });
    this.saveCacheData();
    this.loadInProgressFileListFromCache();
  }

  private async deleteInProgressFilesFromCache(
    deletingFileList: { id: string; name: string }[]
  ) {
    for (const file of deletingFileList)
      await this.cacheStorage.delete(`processed/${file.id}-${file.name}`);

    this.cacheData['in-progress'].fileList = this.cacheData[
      'in-progress'
    ].fileList.filter(
      (file) => !deletingFileList.map((item) => item.id).includes(file.id)
    );
    this.saveCacheData();
  }

  /* cache finished */

  private async loadFinishedFileListFromCache() {
    if (!this.cacheStorage || !this.cacheData?.finished) return;

    /* FIXME: illogical */
    if (
      this.finishedFileList &&
      isEqual(this.cacheData.finished.fileList, this.finishedFileList)
    )
      return;

    const fileList = [];
    const removedCachedFileIdList = [];
    for (const cachedFile of this.cacheData.finished.fileList) {
      const fileResp = await this.cacheStorage.match(
        `/processed/${cachedFile.id}-${cachedFile.name}`
      );

      if (!fileResp) {
        removedCachedFileIdList.push(cachedFile.id);
        continue;
      }
      const file = new File([await fileResp.arrayBuffer()], cachedFile.name, {
        type: fileResp.headers.get('Content-Type')
      });
      fileList.push({
        file,
        link: URL.createObjectURL(file),
        ...cachedFile,
        finishedAt: new Date(cachedFile.finishedAt)
      });
    }
    this.finishedFileListSubject.next(fileList);
    if (removedCachedFileIdList.length) {
      this.cacheData.finished.fileList = this.cacheData.finished.fileList.filter(
        (file) => !removedCachedFileIdList.includes(file.id)
      );
      this.saveCacheData();
    }
  }

  moveInProgressFilesToFinished(
    files: { id: string; name: string; exportedInJSONLink: string }[]
  ) {
    if (!files.length) return;

    this.cacheData['in-progress'].fileList = this.cacheData[
      'in-progress'
    ].fileList.filter((file) => !files.map((item) => item.id).includes(file.id));
    this.cacheData.finished.fileList = [
      ...files.map((item) => ({ ...item, finishedAt: new Date() })),
      ...this.cacheData.finished.fileList
    ];
    this.saveCacheData();

    const toast = this.toastr.success(
      `<div>Xử lý thành công ${files.length} file:</div>
        <ul class="list-disc list-inside">
          ${files.map((file) => '<li>' + file.name + '</li>').join('')}
        </ul>
      `,
      'Nhận dạng ký tự',
      { enableHtml: true, closeButton: true }
    );
    toast.onTap
      .pipe(
        take(1),
        tap(() => this.router.navigate(['ocr', 'finished']))
      )
      .subscribe();
  }

  async deleteFinishedFilesFromCache(deletingFileList) {
    for (const file of deletingFileList)
      await this.cacheStorage.delete(`processed/${file.id}-${file.name}`);

    this.cacheData.finished.fileList = this.cacheData.finished.fileList.filter(
      (file) => !deletingFileList.map((item) => item.id).includes(file.id)
    );
    this.saveCacheData();
  }

  /* showCanDeactivateNotice */

  getShowCanDeactivateNotice(key: 'preprocessing' | 'in-progress' | 'finished') {
    return this.cacheData[key]?.showCanDeactivateNotice;
  }

  toggleShowCanDeactivateNotice(key: 'preprocessing' | 'in-progress' | 'finished') {
    this.cacheData[key].showCanDeactivateNotice =
      !this.cacheData[key].showCanDeactivateNotice;
    this.saveCacheData();
  }

  private saveCacheData() {
    /* notify the change in fileList count upon saving changes to cache */
    this.fileListCountSubject.next({
      preprocessing: this.cacheData.preprocessing.fileList.length,
      'in-progress': this.cacheData['in-progress'].fileList.length,
      finished: this.cacheData.finished.fileList.length
    });
    localStorage.setItem(this.cacheName, JSON.stringify(this.cacheData));
  }

  /* start API requests */
  ocrScanTableAsync({ hash, fileType }: { hash: string; fileType: string }) {
    return this.http.post<{
      object: {
        session_id: string;
        num_of_pages: number;
        warning_messages: string[];
        warnings: string[];
      };
      message: string;
      status: string;
      statusCode: number;
    }>(
      `${environment.backendUrl}idg-api/integration/ocr/scan-table`,
      {
        file_hash: hash,
        file_type: fileType,
        details: true,
        exporter: 'json'
      },
      { headers: this.utilsService.headersWithSkipAppLoadingSpinner }
    );
  }

  ocrScanTableAsyncResult(sessionId) {
    return this.http.post<{
      object: {
        link?: string;
        number_of_pages: number;
        num_of_processed_page?: number;
        num_of_remaining_pages?: number;
        warning_messages: string[];
        warnings: string[];
      };
      message: string;
      status: string;
      statusCode: number;
    }>(
      `${environment.backendUrl}idg-api/integration/ocr/scan-table/result`,
      { session_id: sessionId },
      { headers: this.utilsService.headersWithSkipAppLoadingSpinner }
    );
  }

  cancelOcrScanTableAsyncResult(sessionId) {
    return this.http.post<{
      object: {
        status: string;
      };
      message: string;
      status: string;
      statusCode: number;
    }>(
      `${environment.backendUrl}idg-api/integration/ocr/scan-table/cancel`,
      { session_id: sessionId },
      { headers: this.utilsService.headersWithSkipAppLoadingSpinner }
    );
  }
  /* end API requests */

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.finishedFileListSubject.complete();
    this.inProgressFileListSubject.complete();
    this.preprocessingFileListSubject.complete();
  }
}
