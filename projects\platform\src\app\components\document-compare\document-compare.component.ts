import { Component, CUSTOM_ELEMENTS_SCHEMA, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { ToastrService } from 'ngx-toastr';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';
import UtilsService from '@platform/app/core/services/utils.service';
import { isString } from 'lodash';
import { catchError, EMPTY, finalize, forkJoin, from, of, switchMap, take } from 'rxjs';
import { NgxSpinnerModule, NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-document-compare',
  standalone: true,
  imports: [CommonModule, NgxSpinnerModule],
  templateUrl: './document-compare.component.html',
  styleUrls: ['./document-compare.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DocumentCompareComponent implements OnInit {
  beforeDocumentImageList: string[];
  afterDocumentImageList: string[];
  currentPageIndex = 0;
  sharedNumPages = 0;

  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('document-compare-loading');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('document-compare-loading');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }

  @Input() set comparingDocumentPair(pair: {
    beforeDoc: File | string;
    afterDoc: File | string;
  }) {
    if (!pair.beforeDoc || !pair.afterDoc) {
      this.toastrService.error('Missing before and after document');
      return;
    }

    const sources = {
      beforeDoc: isString(pair.beforeDoc)
        ? this.utils.fetchFile(pair.beforeDoc).pipe(
            take(1),
            catchError((error) => {
              console.error('Fetch before document file failed', error);
              return EMPTY;
            })
          )
        : of(pair.beforeDoc),
      afterDoc: isString(pair.afterDoc)
        ? this.utils.fetchFile(pair.afterDoc).pipe(
            take(1),
            catchError((error) => {
              console.error('Fetch aftrer document file failed', error);
              return EMPTY;
            })
          )
        : of(pair.afterDoc)
    };
    this.loading = true;
    forkJoin(sources)
      .pipe(
        switchMap(({ beforeDoc, afterDoc }) =>
          from(
            ((beforeDoc, afterDoc) => {
              return Promise.all([
                this.processInputDocument(beforeDoc),
                this.processInputDocument(afterDoc)
              ])
                .then(([beforeDocumentImageList, afterDocumentImageList]) => {
                  this.beforeDocumentImageList = beforeDocumentImageList;
                  this.afterDocumentImageList = afterDocumentImageList;
                  if (
                    this.beforeDocumentImageList.length !==
                    this.afterDocumentImageList.length
                  )
                    this.toastrService.warning(
                      'before document and after document do not have the same number of pages'
                    );
                  this.sharedNumPages = Math.max(
                    this.beforeDocumentImageList.length,
                    this.afterDocumentImageList.length
                  );
                })
                .catch(() => {
                  /* TODO: handle error */
                });
            })(beforeDoc, afterDoc)
          )
        ),
        finalize(() => (this.loading = false))
      )
      .subscribe();
  }

  constructor(
    private pdfService: PdfService,
    private toastrService: ToastrService,
    private utils: UtilsService,
    private spinner: NgxSpinnerService
  ) {}

  ngOnInit(): void {}

  private async processInputDocument(document: File | Blob): Promise<string[]> {
    switch (document.type) {
      case 'image/png':
      case 'image/jpeg': {
        return [URL.createObjectURL(document)];
      }

      case 'application/pdf': {
        const pdfDocument = await this.pdfService.pdfjsDist.getDocument({
          data: await document.arrayBuffer()
          // cMapUrl: 'node_modules/pdfjs-dist/cmaps',
          // cMapPacked: true,
          // standardFontDataUrl: 'node_modules/pdfjs-dist/standard_fonts/'
          // canvasFactory,
        }).promise;
        const numPages = pdfDocument.numPages;

        const canvasFactory = new NodeCanvasFactory(); // create each canvas and its context for every page

        const renderedPages = new Map();
        for (const pageIndex of new Array(numPages).fill(undefined).map((_, i) => i)) {
          const page = await pdfDocument.getPage(pageIndex + 1);
          const viewport = page.getViewport({ scale: 1.0 });
          const renderScale = 3;

          const canvasAndContext = canvasFactory.create(
            viewport.width * renderScale,
            viewport.height * renderScale
          );
          const renderContext = {
            canvasContext: canvasAndContext.context,
            viewport: viewport.clone({ scale: renderScale })
          };
          const renderTask = page.render(renderContext as any);
          await renderTask.promise;

          const pageImageDataURL = canvasAndContext.canvas.toDataURL('image/png');
          const pageImageLink = URL.createObjectURL(
            new File(
              [this.utils.createBlobFromDataUrl(pageImageDataURL, 'image/png')],
              `page-${pageIndex + 1}`,
              { type: 'image/png' }
            )
          );
          renderedPages.set(pageIndex, pageImageLink);
          // Release page resources.
          canvasFactory.destroy(canvasAndContext);
          page.cleanup();
        }

        // release resource once all pages are loaded, reduce memory consumption
        pdfDocument?.cleanup();
        pdfDocument?.destroy();

        return Array.from(renderedPages.values());
      }

      default: {
        throw new Error('Invalid document file, neither an image or pdf');
      }
    }
  }

  nextPage() {
    if (this.currentPageIndex < this.sharedNumPages - 1) this.currentPageIndex++;
    else this.currentPageIndex = 0;
  }

  prevPage() {
    if (this.currentPageIndex === 0) this.currentPageIndex = this.sharedNumPages - 1;
    else this.currentPageIndex--;
  }
}
