<div class="w-[300px] shrink-0 h-full border-r-[2px] border-r-line overflow-auto bg-bg-3">
  <!-- style="background-image: url('assets/kie/document/bg-sidebar.png')"  TODO: drop background for now -->
  <div class="flex flex-col h-full">
    <div class="border-b border-line text-text-1">
      <div class="flex justify-between items-center py-2 px-4 bg-white">
        <button
          class="flex items-center cursor-pointer font-medium rounded-[4px] px-1 py-[2px] hover:bg-gray-200"
          (click)="configFolderOrderBy()"
          [nz-tooltip]="configFolderInfo"
          nzTooltipColor="#000"
        >
          <img class="mr-2" src="assets/kie/document/sort.svg" alt="icon-sort" />
          Sắp xếp
        </button>
        <ng-template #configFolderInfo>
          {{ orderByLabel || 'N/A' }}: {{ orderValueLabel || 'N/A' }}
          <br />
          <!-- TODO: Từ khóa: Test -->
        </ng-template>
        <div
          nz-dropdown
          [nzDropdownMenu]="menu"
          nzTrigger="click"
          class="flex items-center font-medium cursor-pointer rounded-[4px] px-1 py-[2px] hover:bg-gray-200"
          [ngClass]="{ '!cursor-not-allowed': !folders?.length }"
        >
          <svg
            class="mr-1"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M4.1665 10H15.8332"
              stroke="#1E5FD5"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10 15.8332V4.1665"
              stroke="#1E5FD5"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>

          <span class="text-text-1">Tạo</span>
        </div>
        <nz-dropdown-menu #menu="nzDropdownMenu">
          <!-- <app-create-menu [listFolders]="folders"></app-create-menu> -->
          <ng-container *ngIf="folders && folders.length > 0">
            <app-create-menu [listFolders]="folders"></app-create-menu>
          </ng-container>
        </nz-dropdown-menu>
      </div>
    </div>
    <a
      class="cursor-pointer flex justify-between px-4 py-2 items-center"
      [ngClass]="{
        'border-r-[3px] border-brand-1 bg-[#E6F1FE]': selectedDocumentId === 'shared'
      }"
      [routerLink]="['shared']"
    >
      <p class="flex items-center gap-2">
        <img src="assets/kie/document/shared-documents.svg" alt="icon-share" />
        <span>Văn bản được chia sẻ</span>
      </p>
      <p class="rounded-full px-2 py-1 bg-bg-1 font-semibold text-xs">
        {{ sharedDocumentsCount }}
      </p>
    </a>
    <div class="flex-1 overflow-auto" cdkScrollable id="infinite-scroll-target">
      <nz-collapse *ngIf="folders.length > 0" [nzBordered]="false">
        <nz-collapse-panel
          #p
          [nzExpandedIcon]="expandedIcon || undefined"
          *ngFor="let folder of folders; trackBy: trackBy"
          [nzHeader]="headerTemplate"
          nzShowArrow
          [(nzActive)]="folder.isActive"
        >
          <ng-template #expandedIcon let-active>
            {{ active }}
            <i
              nz-icon
              nzType="caret-right"
              class="!align-middle text-brand-1 ant-collapse-arrow"
              [nzRotate]="p.nzActive ? 90 : 0"
            ></i>
          </ng-template>
          <ng-template #headerTemplate>
            <div
              nz-tooltip
              [nzTooltipTitle]="folder.name.length > NAME_MAX_LENGTH ? folder.name : ''"
              nzTooltipColor="#000"
              class="group flex-auto flex items-center justify-between truncate"
            >
              <div class="font-semibold text-text-1 break-words truncate flex-1">
                {{ folder.name }}
              </div>
              <button
                class="shrink-0 hidden group-hover:block rounded-full w-5 h-5"
                (click)="$event.preventDefault(); $event.stopPropagation()"
                nz-popover
                nzPopoverTrigger="click"
                [nzPopoverContent]="folderActionsMenu"
                nzPopoverPlacement="rightTop"
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M7.99984 2C7.2665 2 6.6665 2.6 6.6665 3.33333C6.6665 4.06667 7.2665 4.66667 7.99984 4.66667C8.73317 4.66667 9.33317 4.06667 9.33317 3.33333C9.33317 2.6 8.73317 2 7.99984 2Z"
                    fill="#6C7093"
                  />
                  <path
                    d="M7.99984 11.3335C7.2665 11.3335 6.6665 11.9335 6.6665 12.6668C6.6665 13.4002 7.2665 14.0002 7.99984 14.0002C8.73317 14.0002 9.33317 13.4002 9.33317 12.6668C9.33317 11.9335 8.73317 11.3335 7.99984 11.3335Z"
                    fill="#6C7093"
                  />
                  <path
                    d="M7.99984 6.6665C7.2665 6.6665 6.6665 7.2665 6.6665 7.99984C6.6665 8.73317 7.2665 9.33317 7.99984 9.33317C8.73317 9.33317 9.33317 8.73317 9.33317 7.99984C9.33317 7.2665 8.73317 6.6665 7.99984 6.6665Z"
                    fill="#6C7093"
                  />
                </svg>
              </button>
            </div>
            <ng-template #folderActionsMenu>
              <div
                class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden min-w-[150px]"
              >
                <div class="px-3 py-2 border-line border-b">
                  <div class="flex gap-2 items-center justify-between">
                    <span>Tạo lúc: </span>
                    <span>{{ folder.createdAt | date: 'dd/MM/yyyy HH:mm' }}</span>
                  </div>
                  <div class="flex gap-2 items-center justify-between">
                    <span>Sửa lúc:</span>
                    <span>{{ folder.updatedAt | date: 'dd/MM/yyyy HH:mm' }}</span>
                  </div>
                </div>
                <button
                  class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
                  (click)="renameFolder(folder)"
                >
                  <img src="assets/kie/actions/rename.svg" alt="icon" />
                  <p>Đổi tên</p>
                </button>
                <button
                  class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-t border-line"
                  (click)="deleteFolder(folder)"
                >
                  <img src="assets/kie/actions/delete.svg" alt="icon" />
                  <p>Xóa</p>
                </button>
              </div>
            </ng-template>
          </ng-template>
          <ng-container *ngFor="let document of folder.documents; trackBy: trackBy">
            <ng-template
              *ngTemplateOutlet="document_item; context: { document: document }"
            ></ng-template>
          </ng-container>
        </nz-collapse-panel>
      </nz-collapse>
    </div>
    <div class="text-center font-semibold border-t border-line py-[2px]">
      Thư mục {{ folders.length }} / {{ foldersCount }}
      -
      <button
        class="hover:bg-gray-200 rounded-[4px] py-[2px] px-1"
        (click)="loadMoreFolders()"
      >
        Tải thêm
      </button>
    </div>
  </div>
</div>
<div class="flex-1 overflow-auto">
  <router-outlet></router-outlet>
</div>

<ng-template #document_item let-document="document">
  <a
    [ngClass]="{
      '!border-r-brand-1 bg-[#E6F1FE]': selectedDocumentId === document.id
    }"
    [routerLink]="document.id"
    nz-tooltip
    [nzTooltipTitle]="document.name.length >= NAME_MAX_LENGTH ? document.name : ''"
    nzTooltipColor="#000"
    class="group flex justify-between pl-12 pr-2 items-center relative py-2 border-r-[3px] border-r-transparent hover:bg-[#E6F1FE]"
  >
    <p
      *ngIf="document.isSelfConfig"
      class="absolute py-[2px] px-2 -left-1 top-1/2 -translate-y-1/2 bg-slate-500 rounded-tr-full text-text-2 rounded-br-full text-[10px] bg-gradient-to-r from-[#114DBA] to-[#007EFE]"
    >
      Tự tạo
    </p>
    <p class="break-words truncate">
      {{ document.name }}
    </p>
    <div class="w-10 flex-shrink-0 flex items-center justify-center">
      <p
        class="block group-hover:hidden rounded-full px-2 py-1 bg-bg-1 font-semibold text-xs"
      >
        {{ document.filesCount > 99 ? '99+' : document.filesCount }}
      </p>
      <button
        class="hidden group-hover:flex rounded-full w-6 h-6 items-center justify-center bg-bg-1"
        (click)="$event.preventDefault(); $event.stopPropagation()"
        nz-popover
        nzPopoverTrigger="click"
        [nzPopoverContent]="documentActionsMenu"
        nzPopoverPlacement="rightTop"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7.99984 2C7.2665 2 6.6665 2.6 6.6665 3.33333C6.6665 4.06667 7.2665 4.66667 7.99984 4.66667C8.73317 4.66667 9.33317 4.06667 9.33317 3.33333C9.33317 2.6 8.73317 2 7.99984 2Z"
            fill="#6C7093"
          />
          <path
            d="M7.99984 11.3335C7.2665 11.3335 6.6665 11.9335 6.6665 12.6668C6.6665 13.4002 7.2665 14.0002 7.99984 14.0002C8.73317 14.0002 9.33317 13.4002 9.33317 12.6668C9.33317 11.9335 8.73317 11.3335 7.99984 11.3335Z"
            fill="#6C7093"
          />
          <path
            d="M7.99984 6.6665C7.2665 6.6665 6.6665 7.2665 6.6665 7.99984C6.6665 8.73317 7.2665 9.33317 7.99984 9.33317C8.73317 9.33317 9.33317 8.73317 9.33317 7.99984C9.33317 7.2665 8.73317 6.6665 7.99984 6.6665Z"
            fill="#6C7093"
          />
        </svg>
      </button>
    </div>
  </a>
  <ng-template #documentActionsMenu>
    <div
      class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden min-w-[200px]"
    >
      <div class="px-3 py-2 border-line border-b">
        <div class="flex gap-2 items-center justify-between">
          <span>Tạo lúc: </span>
          <span>{{ document.createdAt | date: 'dd/MM/yyyy HH:mm' }}</span>
        </div>
        <div class="flex gap-2 items-center justify-between">
          <span>Sửa lúc:</span>
          <span>{{ document.updatedAt | date: 'dd/MM/yyyy HH:mm' }}</span>
        </div>
      </div>
      <button
        class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
        (click)="moveDocument(document)"
      >
        <img src="assets/kie/actions/move.svg" alt="icon" />
        <p>Chuyển vào thư mục</p>
      </button>
      <button
        class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
        (click)="renameDocument(document)"
      >
        <img src="assets/kie/actions/rename.svg" alt="icon" />
        <p>Đổi tên</p>
      </button>
      <button
        class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
        (click)="copyDocument(document)"
      >
        <img src="assets/kie/actions/copy.svg" alt="icon" />
        <p>Tạo bản sao</p>
      </button>
      <button
        class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-t border-line"
        (click)="deleteDocument(document)"
      >
        <img src="assets/kie/actions/delete.svg" alt="icon" />
        <p>Xóa</p>
      </button>
    </div>
  </ng-template>
</ng-template>
