<div class="text-center bg-white flex flex-col gap-[24px] items-center justify-center h-[calc(100vh-64px)]">
	<ng-container *ngIf="paymentResponseType === PaymentResponseType.success">
		<img src="assets/img/rpa/subscription/success.svg" alt="">
		<div class="text-[#545454]">
			<div class="text-[34px] font-bold">Cảm ơn bạn đã đăng ký!</div>
			<div>Xin chúc mừng, bạn đã đăng ký gói <span class="font-bold">{{planName}}</span> thành công !
			</div>
		</div>
		<a routerLink="/subscription"
			class="rounded-md uppercase !text-white bg-[#0F67CE] padding: py-2 px-4 font-bold">quản
			lý gói cước</a>
		<a routerLink="" class="block font-semibold">Đến <PERSON></a>
	</ng-container>
	<ng-container *ngIf="paymentResponseType === PaymentResponseType.failed">
		<img src="assets/img/rpa/subscription/error.svg" alt="">
		<div class="text-[#545454]">
			<div class="text-[34px] font-bold">Thanh toán không thành công!</div>
			<div>Giao dịch không thành công. Vui lòng thử lại</div>
		</div>
		<a routerLink="/subscription/info"
			class="rounded-md uppercase !text-white bg-[#0F67CE] padding: py-2 px-4 font-bold">THỬ LẠI</a>
		<a routerLink="/subscription" class="block font-semibold">Xem gói cước hiện tại</a>
	</ng-container>
	<ng-container *ngIf="paymentResponseType === PaymentResponseType.insufficientBalanceInWallet">
		<img src="assets/img/rpa/subscription/insufficient.svg" alt="">
		<div class="text-[#545454]">
			<div class="text-[34px] font-bold">Lỗi không đủ số dư trong ví</div>
			<div>Vui lòng nạp tiền vào ví</div>
		</div>
		<a routerLink="/subscription/info"
			class="rounded-md uppercase !text-white bg-[#0F67CE] padding: py-2 px-4 font-bold">THỬ LẠI</a>
		<a routerLink="/subscription" class="block font-semibold">Xem gói cước hiện tại</a>
	</ng-container>
	<!-- <div>Trở về màn hình <a href="#" (click)="returnUrl($event)">Gói cước</a> trong {{countdownSeconds}}s</div> -->
</div>