import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import UtilsService from '@platform/app/core/services/utils.service';
import { environment } from '@platform/environment/environment';

@Injectable({
  providedIn: 'root'
})
export class SubscriptionService {
  baseUrl = environment.backendUrl + 'idg-api/subscription';

  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  fetchSubscriptionStatus() {
    return this.http.get<{ object: any }>(this.baseUrl + '/status', {
      headers: this.utilsService.headers
    });
  }

  fetchSubscriptionCheckRegister() {
    return this.http.get<{ object: { uuidProject: { uuidProject: string } } }>(
      this.baseUrl + '/check-register',
      { headers: this.utilsService.headers }
    );
  }

  sendMessage(data) {
    return this.http.post(environment.landingPageUrl + 'api/sendMessage', data);
  }

  registerSubscription(body) {
    return this.http.post<{ object: string }>(this.baseUrl + '/register', body, {
      headers: this.utilsService.headers
    });
  }

  extendSubscription(body) {
    return this.http.post<{ object: string }>(this.baseUrl + '/extend', body, {
      headers: this.utilsService.headers
    });
  }

  getWalletDetail() {
    return this.http.get<{ object: { amount: number } }>(
      this.baseUrl + '/wallet-detail',
      { headers: this.utilsService.headers }
    );
  }
}
