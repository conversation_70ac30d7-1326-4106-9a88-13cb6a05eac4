/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin');

module.exports = {
  content: ['./projects/*/src/**/*.{html,ts}'], // apply to any project app under "projects" (projects/*)
  theme: {
    extend: {
      colors: {
        'brand-1': '#0667E1',
        'brand-2': '#0A84FF',
        'brand-3': '#278EFF',
        'brand-4': '#3634A3',
        'text-1': '#2B2D3B',
        'text-2': '#FFFFFF',
        'text-3': '#6C7093',
        'text-4': '#C4C6D4',
        'icon-1': '#989BB3',
        'icon-2': '#6C7093',
        'icon-3': '#FFFFFF',
        line: '#E7EBEF',
        'bg-1': '#F0F1F4',
        'bg-2': '#021229',
        'bg-3': '#FFFFFF',
        'bg-4': '#EEF4FF',
        'bg-5': '#222233',
        'status-error': '#FF3355',
        'status-success': '#009B4E',
        'status-process': '#FFA100',
        'blue-3': '#e6f1fe'
      }
    }
  },
  plugins: [
    plugin(function ({ addBase }) {
      addBase({ html: { fontSize: '16px', color: '#2B2D3B' } });
    })
  ]
};
