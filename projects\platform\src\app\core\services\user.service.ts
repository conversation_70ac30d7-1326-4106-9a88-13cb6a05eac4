import { Injectable } from '@angular/core';
import { environment } from '@platform/environment/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import UtilsService from '@platform/app/core/services/utils.service';
import { tap } from 'rxjs';

interface loginForm {
  username: string;
  password: string;
}

interface registerForm {
  username: string;
  password: string;
  role: string;
}

export enum AgreementCode {
  OldUserNeedsAgreement = -1,
  UserNotAgreed = 0,
  UserAgreed = 1
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  getUserInfor() {
    const url = environment.backendUrl + 'auth/profile';
    return this.http.get(url, { headers: this.utilsService.headers });
  }

  updateIdgAccount(body) {
    return this.http
      .put<{
        message: string;
        object: any;
      }>(
        environment.backendUrl + 'idg-api/account',
        { ...body, channelCode: 'SMART_RPA' },
        { headers: this.utilsService.headers }
      )
      .pipe(
        tap((result) => {
          const newCurrentUser = this.utilsService.getCurrentUser();
          newCurrentUser['account'] = result.object;
          localStorage.setItem('currentUser', JSON.stringify(newCurrentUser));
        })
      );
  }

  getIdgAccount() {
    return this.http.get<{ message: string; object: any }>(
      environment.backendUrl + 'idg-api/account',
      { headers: this.utilsService.headers }
    );
  }

  getAccountServicePermission(email: string) {
    return this.http.get<{
      message: string;
      object: { agreementCode: AgreementCode };
    }>(environment.backendUrl + 'idg-api/account-service-permission', {
      params: { email, channelCode: 'SMART_RPA' }
    });
  }

  changeAccountServicePermission() {
    return this.http.post<{
      message: string;
      object: any;
    }>(
      environment.backendUrl + 'idg-api/account-service-permission/change',
      { channelCode: 'SMART_RPA', agreementCode: 1 },
      { headers: this.utilsService.headers }
    );
  }

  createDenyServicePermission() {
    return this.http.post<{
      message: string;
      object: any;
    }>(
      environment.backendUrl + 'idg-api/account-service-permission/create-deny',
      { channelCode: 'SMART_RPA' },
      { headers: this.utilsService.headers }
    );
  }

  confirmDenyServicePermission(body: { confirmationToken: string; email: string }) {
    return this.http.post<{
      message: string;
      object: any;
    }>(environment.backendUrl + 'idg-api/account-service-permission/confirm-deny', {
      ...body,
      channelCode: 'SMART_RPA'
    });
  }

  sendRemoveDataRequest(message) {
    return this.http.post(
      environment.backendUrl + 'idg-api/remove-data-request',
      { message },
      { headers: this.utilsService.headers }
    );
  }

  changePassword(body) {
    return this.http.put(environment.backendUrl + 'idg-api/change-password', body, {
      headers: this.utilsService.headers
    });
  }

  searchUser(searchText: string) {
    const url = environment.backendUrl + 'users';
    return this.http.get<{ users: { id: string; name: string }[] }>(url, {
      headers: this.utilsService.headers,
      params: { search: searchText, page: 1, limit: 5 }
    });
  }
}
