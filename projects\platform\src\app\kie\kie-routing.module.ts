import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '@platform/app/layout/layout/layout.component';
import { ManageDocumentComponent } from './manage-document/manage-document.component';
import { ConfigTemplateComponent } from './config-template/config-template.component';
import { ExtractFileComponent } from './extract-file/extract-file.component';
import { DocumentLayoutComponent } from './components/document-layout/document-layout.component';
import { SharedDocumentsComponent } from './shared-documents/shared-documents.component';
import { OnboardingComponent } from './onboarding/onboarding.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: 'document',
        component: DocumentLayoutComponent,
        children: [
          {
            path: 'shared',
            component: SharedDocumentsComponent
          },
          {
            path: '' /* first navigation to Key Information Extractor case */,
            component: OnboardingComponent
          },
          {
            path: ':id' /* select 1 document case */,
            component: ManageDocumentComponent
          }
        ]
      },
      {
        pathMatch: 'full',
        path: '',
        redirectTo: 'document'
      }
    ]
  },
  {
    path: 'template/:id',
    component: ConfigTemplateComponent
  },
  {
    path: 'file/:id',
    component: ExtractFileComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class KIERoutingModule {}
