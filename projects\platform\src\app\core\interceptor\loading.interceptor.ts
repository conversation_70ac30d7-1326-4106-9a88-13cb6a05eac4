import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { catchError, finalize, Observable, throwError } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { get } from 'lodash';
import { Router } from '@angular/router';
import { NzModalService } from 'ng-zorro-antd/modal';
import { SubscriptionPlanWarningComponent } from '@platform/app/components/subscription-plan-warning/subscription-plan-warning.component';
import UtilsService from '../services/utils.service';
import { AuthService } from '../services/auth.service';

@Injectable()
export class LoadingInterceptor implements HttpInterceptor {
  constructor(
    private utils: UtilsService,
    private toastrService: ToastrService,
    public router: Router,
    public modal: NzModalService,
    public authService: AuthService
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: <PERSON>ttp<PERSON>and<PERSON>
  ): Observable<HttpEvent<unknown>> {
    const skipAppLoadingSpinner = !!request.headers.get(
      this.utils.SKIP_APP_LOADING_SPINNER
    );
    const clonedRequest = request.clone({
      headers: request.headers.delete(this.utils.SKIP_APP_LOADING_SPINNER)
    });
    !skipAppLoadingSpinner && this.utils.toggleAppLoadingSpinner(true);
    return next.handle(clonedRequest).pipe(
      finalize(() => !skipAppLoadingSpinner && this.utils.toggleAppLoadingSpinner(false)),
      catchError((error: HttpErrorResponse) => {
        switch (error?.status) {
          case 401: {
            if (get(error, 'error.message') === 'IDG-00000401') {
              // this.toastrService.error(get(error, 'error.error', 'IDG-00000401'));
              this.modal.create({
                nzContent: SubscriptionPlanWarningComponent,
                nzFooter: null,
                nzBodyStyle: { padding: '0' },
                nzClassName: 'custom-ant-modal-common-styles'
              });
              break;
            }

            if (this.utils.isAccessTokenExpired()) {
              this.authService.logout(); // clear local storage and cache storage
              location.reload(); // prevent services currently loaded expired token causing 401 again
            }
          }

          default:
            break;
        }
        return throwError(() => error);
      })
    );
  }
}
