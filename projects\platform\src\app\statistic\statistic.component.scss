.statistic-container {
  padding: 16px 24px;
}

/* custom antd nz-tabset component styles without bleeding styles into child component */
:host .statistic-container ::ng-deep>.ant-tabs-default>.ant-tabs-nav {
  margin-bottom: 16px;

  &::before {
    display: none;
  }

  >.ant-tabs-nav-wrap>.ant-tabs-nav-list {
    >.ant-tabs-ink-bar {
      display: none;
    }

    >.ant-tabs-tab {
      color: #0F67CE;
      background: #EBEFFA;
      border: 1px solid #0F67CE;
      border-radius: 4px;
      min-width: 150px;
      justify-content: center;
      padding: 6px 0;

      .ant-tabs-tab-btn {
        font-weight: 600;
        color: #0F67CE;
      }

      &.ant-tabs-tab-active {
        background: #0F67CE;

        .ant-tabs-tab-btn {
          color: #fff
        }
      }

    }
  }
}