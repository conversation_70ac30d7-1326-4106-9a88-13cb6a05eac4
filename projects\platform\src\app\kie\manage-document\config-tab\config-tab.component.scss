:host {
  --document-viewer-header-bg-color: #222233;
  --document-viewer-header-text-color: #fff;

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #cbcbcb;
  }

  ::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }

  display: flex;
  height: 100%;

  ::ng-deep {
    button {
      &.ant-switch {
        background-image: linear-gradient(to right,
            rgba(0, 0, 0, 0.25),
            rgba(0, 0, 0, 0.25)),
          linear-gradient(to right, #fff, #fff);
      }

      &.ant-switch-checked {
        background: #009b4e;
      }
    }
  }

  .cdk-drag-preview {
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }

  .cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }

  .field-list.cdk-drop-list-dragging .field-item:not(.cdk-drag-placeholder) {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  }
}

::ng-deep {
  .modal-config-tab-token-info {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
}