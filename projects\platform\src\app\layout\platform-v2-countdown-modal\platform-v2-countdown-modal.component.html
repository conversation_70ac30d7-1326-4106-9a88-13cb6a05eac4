<div class="flex flex-col items-center justify-between">
  <div
    class="py-4 px-6 flex justify-between items-center w-full border-b border-[#e7ebef]"
  >
    <div class="font-semibold"><PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> phiên bản mới</div>
    <svg
      (click)="close()"
      class="cursor-pointer"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 12L12 4"
        stroke="#989BB3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 12L4 4"
        stroke="#989BB3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
  <div class="p-6 flex flex-col items-center justify-between gap-3">
    <img src="assets/img/time-to-update.svg" />
    <div class="text-xl font-bold">THỜI GIAN CẬP NHẬT PHIÊN BẢN MỚI</div>
    <div class="flex gap-4 items-center justify-between">
      <ng-template
        *ngTemplateOutlet="countdown; context: { count: days, label: 'Ngày' }"
      ></ng-template>
      <div class="font-bold text-[28px] text-[#6c7093]">:</div>
      <ng-template
        *ngTemplateOutlet="countdown; context: { count: hours, label: 'GIỜ' }"
      ></ng-template>
      <div class="font-bold text-[28px] text-[#6c7093]">:</div>
      <ng-template
        *ngTemplateOutlet="countdown; context: { count: mins, label: 'PHÚT' }"
      ></ng-template>
      <div class="font-bold text-[28px] text-[#6c7093]">:</div>
      <ng-template
        *ngTemplateOutlet="countdown; context: { count: seconds, label: 'GIÂY' }"
      ></ng-template>
    </div>
    <div class="text-center">
      Xin vui lòng thông báo rằng Smart Reader <br />
      sắp có giao diện mới, hãy đón chờ!
    </div>
  </div>
  <div
    class="py-4 px-6 border-t border-[#e7ebef] flex items-center justify-between w-full"
  >
    <label
      nz-checkbox
      [(ngModel)]="platformV2Understood"
      (ngModelChange)="changePlatformV2Understood()"
      >Đã hiểu, không hiện lại trong phiên đăng nhập này
    </label>
    <div class="flex items-center gap-2 justify-between">
      <button
        (click)="openPlatformV2InfoModal()"
        class="border border-[#0667e1] bg-[#0667e1] text-white font-medium flex items-end gap-2 py-2 px-[14px] rounded-sm"
      >
        Khám phá
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.0254 4.94141L17.0837 9.99974L12.0254 15.0581"
            stroke="white"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2.91699 10H16.942"
            stroke="white"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
  </div>
</div>

<ng-template #countdown let-count="count" let-label="label">
  <div class="bg-[#f0f1f4] py-2 px-1 rounded-lg flex flex-col items-center w-[70px]">
    <div class="font-bold text-[28px] border-b border-[#c4c6d4] leading-10">
      {{ count }}
    </div>
    <div class="font-medium text-xs leading-[30px] uppercase">
      {{ label }}
    </div>
  </div>
</ng-template>
