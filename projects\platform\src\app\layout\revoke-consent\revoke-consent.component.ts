import { Component, OnInit } from '@angular/core';
import { RevokeConsentModalComponent } from '../revoke-consent-modal/revoke-consent-modal.component';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '@platform/app/core/services/user.service';
import { EMPTY, catchError, map, switchMap, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  template: '',
  selector: 'app-revoke-consent'
})
export class RevokeConsentComponent implements OnInit {
  constructor(
    public modal: NzModalService,
    private route: ActivatedRoute,
    private router: Router,
    private userService: UserService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    if (!this.route.snapshot.queryParams['token']) {
      this.router.navigate(['/']);
      return;
    }

    this.userService
      .getIdgAccount()
      .pipe(
        map((result) => result.object),
        catchError(() => {
          this.toastr.error(
            'Đã có lỗi xảy ra, vui lòng tải lại (F5) đường link để hoàn tất hành động'
          );
          return EMPTY;
        }),
        switchMap((account) =>
          this.userService.confirmDenyServicePermission({
            email: account.username,
            confirmationToken: this.route.snapshot.queryParams['token']
          })
        ),
        tap(() => {
          const modalRef = this.modal.create({
            nzContent: RevokeConsentModalComponent,
            nzFooter: null,
            nzMaskClosable: false,
            nzBodyStyle: { padding: '0px' },
            nzClassName: 'custom-ant-modal-common-styles',
            nzStyle: { width: '300px' },
            nzClosable: false
          });
          modalRef.componentInstance.step = 3;
          localStorage.clear(); // logout
          this.router.navigate(['login']);
        }),
        catchError(() => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
          this.router.navigate(['/']);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
