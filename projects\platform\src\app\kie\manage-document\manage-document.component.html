<div class="p-4">
  <nz-breadcrumb [nzSeparator]="separator">
    <nz-breadcrumb-item>
      <img src="assets/kie/document/folder.svg" class="inline-block mr-2" />
      <span class="text-base align-middle font-semibold inline-block text-text-1">
        {{ isSharedDocument ? 'Văn bản được chia sẻ' : folder?.name || 'Tên thư mục' }}
      </span>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <ng-container *ngIf="document?.isSelfConfig">
        <ng-template *ngTemplateOutlet="isSelfConfigBadge"></ng-template>
      </ng-container>
      <span class="text-base align-middle font-semibold inline-block text-text-1">{{
        document?.name || 'Tên văn bản'
      }}</span>
    </nz-breadcrumb-item>
  </nz-breadcrumb>
</div>
<nz-tabset class="flex-1 overflow-y-auto">
  <nz-tab
    class="w-[100px]"
    nzTitle="Tài liệu"
    [nzDisabled]="!checkTabPermission('file-tab')"
  >
    <app-file-tab
      [roleInDocument]="document?.myRole"
      [documentId]="documentId"
    ></app-file-tab>
  </nz-tab>
  <nz-tab
    class="w-[100px]"
    nzTitle="Cấu hình"
    [nzDisabled]="!checkTabPermission('config-tab')"
  >
    <!-- lazy load, reload all the time -->
    <ng-template nz-tab>
      <app-config-tab
        [document]="document"
        (onConfigSaved)="handleReloadDocDetail()"
      ></app-config-tab>
    </ng-template>
  </nz-tab>
  <nz-tab
    class="w-[100px]"
    nzTitle="Phân quyền"
    [nzDisabled]="!checkTabPermission('permission-tab')"
  >
    <app-permission-tab [documentId]="documentId"></app-permission-tab>
  </nz-tab>
</nz-tabset>

<ng-template #separator>
  <img class="inline-block mx-1" src="assets/kie/document/separator.svg" />
</ng-template>

<ng-template #isSelfConfigBadge>
  <img src="assets/kie/document/is-self-config-badge.svg" class="inline-block mr-1" />
</ng-template>
