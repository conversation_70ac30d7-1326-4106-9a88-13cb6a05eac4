import { Component, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-modal-transfer-ownership',
  templateUrl: './modal-transfer-ownership.component.html',
  styleUrls: ['./modal-transfer-ownership.component.scss']
})
export class ModalTransferOwnershipComponent implements OnInit {
  constructor(private modalRef: NzModalRef) {}

  ngOnInit(): void {}
  onOK(): void {
    this.modalRef.close();
  }
}
