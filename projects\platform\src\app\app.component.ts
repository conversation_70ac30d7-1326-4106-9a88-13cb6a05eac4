import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import UtilsService from './core/services/utils.service';

declare let gtag: Function;
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  APP_LOADING_SPINNER = 'app-loading-spinner';
  constructor(
    private router: Router,
    private titleService: Title,
    private utilsService: UtilsService
  ) {
    this.APP_LOADING_SPINNER = this.utilsService.APP_LOADING_SPINNER;
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (event.urlAfterRedirects.includes('/demo/ocr-experience'))
          this.titleService.setTitle('Trợ lý AI xử lý văn bản');
        else this.titleService.setTitle('VNPT Smart Reader');
        gtag('config', 'G-S89JXZ6TBY', { page_path: event.urlAfterRedirects });
      }
    });
  }
}
