import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { OcrService } from '@platform/app/core/services/ocr.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Subject, take, tap } from 'rxjs';
import { ExportModalComponent } from '../components/export-modal/export-modal.component';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-ocr-finished',
  templateUrl: './ocr-finished.component.html',
  styleUrls: ['./ocr-finished.component.scss']
})
export class OcrFinishedComponent implements OnInit {
  static readonly confirmLeaveMessage;

  /* feature flags */
  static documentViewerFF = {
    DisplayOcrResult: false,
    ViewerFitPageWidth: true,
    EditOcrResult: false,
    Zooming: false,
    PdfLazyLoading: true,
    OnlyDisplaySpecifiedPages: false
  };

  @Output() fileChange = new EventEmitter<{
    fileId: string;
    fileLink: string;
    fileName: string;
    exportedInJSONLink: string;
  }>();

  @ViewChild('exportModal')
  exportModal: TemplateRef<any>;

  fileList: {
    id: string;
    name: string;
    finishedAt: Date;
    exportedInJSONLink: string;
    file?: File;
    link: string;
  }[] = [];
  currentPageFileList: typeof this.fileList = []; // a subset of fileList
  tableLimit = 10;
  tablePageIndex = 1;
  checked = false;
  indeterminate = false;
  setOfCheckedId = new Set<string>();
  activeFile: (typeof this.fileList)[number];
  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('ocr-finished-loading');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('ocr-finished-loading');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }
  destroy$ = new Subject<void>();

  constructor(
    private ocrService: OcrService,
    private modalService: NzModalService,
    private spinner: NgxSpinnerService
  ) {}

  ngOnInit(): void {
    this.ocrService.finishedFileList$
      .pipe(
        tap((finishedFileList) => {
          this.fileList = finishedFileList;
        })
      )
      .subscribe();
  }

  selectFile(file?: (typeof this.fileList)[number]) {
    if (!file) this.fileChange.emit(null);
    this.fileChange.emit({
      fileId: file.id,
      fileLink: file.link,
      fileName: file.name,
      exportedInJSONLink: file.exportedInJSONLink
    });
    this.activeFile = file;
  }

  private refreshCheckedStatus(): void {
    if (!this.currentPageFileList.length) {
      this.checked = false;
      this.indeterminate = false;
      return;
    }
    this.checked = this.currentPageFileList.every(({ id }) =>
      this.setOfCheckedId.has(id)
    );
    this.indeterminate =
      this.currentPageFileList.some(({ id }) => this.setOfCheckedId.has(id)) &&
      !this.checked;
  }

  private updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onItemChecked(id: string, checked: boolean) {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.currentPageFileList.forEach(({ id }) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  uncheckAll() {
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }

  onCurrentPageDataChange(currentPageFileList: any[]): void {
    this.currentPageFileList = currentPageFileList;
    this.refreshCheckedStatus();
  }

  onLimitChange(limit) {
    this.tablePageIndex = 1;
    this.tableLimit = limit;
  }

  showExportModalForCheckedItems() {
    const setOfCheckedId = Array.from(this.setOfCheckedId);
    if (!setOfCheckedId.length) return;
    this.showExportModal(
      this.fileList.filter((file) => setOfCheckedId.includes(file.id))
    );
  }

  showExportModal(files: (typeof this.fileList)[number][]) {
    if (!files?.length) return;
    this.modalService.create({
      nzContent: ExportModalComponent,
      nzClosable: false,
      nzMaskClosable: false,
      nzFooter: null,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '350px' },
      nzData: { files }
    });
  }

  async deleteCheckedItems() {
    const setOfCheckedId = Array.from(this.setOfCheckedId);
    if (!setOfCheckedId.length) return;
    for (const fileId of setOfCheckedId) {
      const file = this.fileList.find((item) => item.id === fileId);
      if (!file) continue;
      await this.deleteFile(file);
    }
  }

  async deleteFile(file: (typeof this.fileList)[number]) {
    this.loading = true;
    await this.ocrService.deleteFinishedFilesFromCache([file]);
    this.fileList = this.fileList.filter((item) => item.id !== file.id);
    if (this.activeFile?.id === file.id) {
      this.activeFile = null;
      this.fileChange.emit(null);
    }
    this.onItemChecked(file.id, false);
    this.loading = false;
  }
}
