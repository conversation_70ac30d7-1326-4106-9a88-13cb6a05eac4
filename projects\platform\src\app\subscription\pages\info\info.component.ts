import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@platform/environment/environment';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ContactModalComponent } from '../../components/contact-modal/contact-modal.component';
import { SubscriptionService } from '@platform/app/core/services/subscription.service';

const subscriptionsInfo = [
  {
    label: 'Free',
    price: { 1: 0 },
    quota: { 1: '150' },
    img: 'assets/img/rpa/subscription/free.svg',
    isContact: true
  },
  {
    label: 'Trial',
    price: { 1: 0 },
    quota: { 1: '300' },
    img: 'assets/img/rpa/subscription/trial.svg',
    isContact: true
  },
  {
    labels: { 1: 'Reader_05', 6: 'Reader_30', 12: 'Reader_80' },
    price: { 1: '3.500.000', 6: '20.550.000', 12: '53.600.000' },
    quota: { 1: '5.000', 6: '30.000', 12: '80.000' },
    uuidPlan: {
      1: environment.Reader_05,
      6: environment.Reader_30,
      12: environment.Reader_80
    },
    img: 'assets/img/rpa/subscription/standard.svg',
    isContact: false
  },
  {
    labels: { 1: 'Reader_10', 6: 'Reader_60', 12: 'Reader_120' },
    price: { 1: '6.850.000', 6: '40.200.000', 12: '78.600.000' },
    quota: { 1: '10.000', 6: '60.000', 12: '120.000' },
    uuidPlan: {
      1: environment.Reader_10,
      6: environment.Reader_60,
      12: environment.Reader_120
    },
    img: 'assets/img/rpa/subscription/growth.svg',
    isContact: false
  },
  {
    labels: { 1: 'Reader_50', 6: 'Reader_300', 12: 'Reader_600' },
    price: { 1: '33.500.000', 6: '196.500.000', 12: '385.800.000' },
    quota: { 1: '50.000', 6: '300.000', 12: '600.000' },
    uuidPlan: {
      1: environment.Reader_50,
      6: environment.Reader_300,
      12: environment.Reader_600
    },
    img: 'assets/img/rpa/subscription/premium.svg',
    isContact: false
  },
  {
    label: 'Enterprise',
    quota: { 1: 'Tùy chọn', 6: 'Tùy chọn', 12: 'Tùy chọn' },
    price: { 1: 'Liên hệ', 6: 'Liên hệ', 12: 'Liên hệ' },
    img: 'assets/img/rpa/subscription/enterprise.svg',
    isContact: true
  }
];

@Component({
  selector: 'app-info',
  templateUrl: './info.component.html',
  styleUrls: ['./info.component.scss']
})
export class InfoComponent implements OnInit {
  constructor(
    private router: Router,
    private modal: NzModalService,
    private subscriptionService: SubscriptionService
  ) {}

  selectedSubscription: 1 | 6 | 12 = 1;
  subscriptionsInfo = subscriptionsInfo;

  userSubscriptionInfo;

  ngOnInit(): void {
    this.subscriptionService.fetchSubscriptionStatus().subscribe({
      next: (value) => (this.userSubscriptionInfo = value.object)
    });
  }

  setSelectedSubscription(value) {
    this.selectedSubscription = value;
    if (value !== 1)
      /* index === 1 tức là gói trial, gói này chỉ xuất hiện ở lựa chọn 1 tháng */
      this.subscriptionsInfo = subscriptionsInfo.filter(
        (_, index) => ![0, 1].includes(index)
      );
    else this.subscriptionsInfo = subscriptionsInfo;
  }

  isNumber(text: string) {
    return !isNaN(parseInt(text));
  }

  isCurrentPlan(plan) {
    if (!plan.uuidPlan) return false;
    return (
      plan.uuidPlan[this.selectedSubscription] === this.userSubscriptionInfo?.uuidPlan
    );
  }

  showPerNMonth(plan) {
    return (
      this.selectedSubscription > 1 &&
      !['Free', 'Trial', 'Enterprise'].includes(plan.label)
    );
  }

  paymentRequest(selection) {
    if (this.isCurrentPlan(selection)) {
      return; // BA yêu cầu
    }

    if (selection.isContact) {
      // open modal
      const modalRef = this.modal.create({
        nzContent: ContactModalComponent,
        nzFooter: null,
        nzMaskClosable: false,
        nzClassName: 'custom-ant-modal-common-styles'
      });
      modalRef.componentInstance.planName = selection.label;
    } else {
      this.router.navigate(['subscription/payment'], {
        state: {
          uuidPlan: selection.uuidPlan[this.selectedSubscription]
        }
      });
    }
  }
}
