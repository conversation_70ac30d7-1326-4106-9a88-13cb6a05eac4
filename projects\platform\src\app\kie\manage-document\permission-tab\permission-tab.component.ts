import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ModalInvitationComponent } from './modal-invatation/modal-invatation.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { KIEService } from '@platform/app/core/services/kie.service';
import {
  BehaviorSubject,
  EMPTY,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs';
import {
  ListDocumentPermission,
  ParamQueryListDocumentPermission,
  Role,
  RolePermisson,
  User
} from '@platform/app/kie/kie';
import { UserService } from '@platform/app/core/services/user.service';
import { ToastrService } from 'ngx-toastr';
import { HttpErrorResponse, HttpParams } from '@angular/common/http';
import { isArray } from 'lodash';
import { UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-permission-tab',
  templateUrl: './permission-tab.component.html',
  styleUrls: ['./permission-tab.component.scss']
})
export class PermissionTabComponent implements OnInit, OnDestroy {
  listPermission: ListDocumentPermission = {
    limit: 10,
    page: 1,
    total: 0,
    permissionList: []
  };
  readonly listRoles = [
    { id: 'all', name: 'Tất cả vai trò', value: [Role.Viewer, Role.Editor] },
    { id: Role.Viewer, name: 'Người xem', value: [Role.Viewer] },
    { id: Role.Editor, name: 'Người chỉnh sửa', value: [Role.Editor] }
  ];

  _documentId: string;
  @Input()
  set documentId(id: string) {
    this._documentId = id;
    this.filterForm.setValue({ page: 1, limit: 10, assigneeIds: [], roleId: 'all' }); // fresh start
  }
  get documentId(): string {
    return this._documentId;
  }

  assigneeSearchSubject = new Subject<string>();
  listUser: User[] = [];
  filterForm = this.formBuilder.group(
    {
      limit: this.formBuilder.control(10, [
        Validators.required,
        Validators.max(30),
        Validators.min(10)
      ]),
      page: this.formBuilder.control(1, [Validators.required, Validators.min(1)]),
      assigneeIds: this.formBuilder.control([]),
      roleId: this.formBuilder.control('all', (control) => {
        if (!this.listRoles.map((role) => role.id).includes(control.value))
          return {
            customError: `value ${control.value} not in ${this.listRoles.map((role) => role.id)}`
          };
        return null;
      })
    },
    {}
  );
  private destroy$ = new Subject<void>();

  constructor(
    private modal: NzModalService,
    private kieService: KIEService,
    private userService: UserService,
    private toastr: ToastrService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.fetchDocumentPermission$().subscribe();

    this.filterForm.valueChanges
      .pipe(takeUntil(this.destroy$), switchMap(this.fetchDocumentPermission$.bind(this)))
      .subscribe();

    this.assigneeSearchSubject
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Wait for 300ms pause in events
        distinctUntilChanged(),
        filter((text) => !!text.trim()),
        switchMap((text) =>
          this.userService.searchUser(text).pipe(
            tap((res) => {
              this.listUser = res.users;
            }),
            catchError((err) => {
              this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
              return EMPTY;
            })
          )
        )
      )
      .subscribe();
  }

  fetchDocumentPermission$() {
    if (this.filterForm.invalid) {
      console.log('invalid filterForm', this.filterForm);
      return EMPTY;
    }

    const filters = Object.assign({}, this.filterForm.value);
    filters['roles'] = this.listRoles.find((item) => item.id === filters['roleId'])[
      'value'
    ];
    delete filters['roleId'];

    let params = new HttpParams();
    for (const key in filters) {
      if (!filters[key]) continue;
      if (isArray(filters[key]))
        filters[key].forEach((value) => {
          params = params.append(`${key}[]`, value);
        });
      else params = params.append(key, filters[key]);
    }
    return this.kieService.getDocumentPermissionList(this.documentId, params).pipe(
      tap((result) => {
        this.listPermission = result.data;
      }),
      catchError((err) => {
        let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
        if (err instanceof HttpErrorResponse) {
          switch (err.status) {
            case 403:
              errorMessage =
                'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
          }
        }
        this.toastr.error(errorMessage);
        return EMPTY;
      })
    );
  }

  handleChangePage(page: number): void {
    this.filterForm.patchValue({ page });
  }

  handleChangeLimit(limit: number) {
    this.filterForm.patchValue({ limit, page: 1 });
  }

  handleSearchUser(text: string): void {
    this.assigneeSearchSubject.next(text);
  }

  handleFilterAssignee(assigneeIds) {
    this.filterForm.patchValue({ assigneeIds });
  }

  handleFilterRoles(roleId) {
    this.filterForm.patchValue({ roleId });
  }

  showModalInviteMember(): void {
    const modal = this.modal.create({
      nzTitle: 'Mời',
      nzContent: ModalInvitationComponent,
      nzData: { documentId: this.documentId },
      nzMaskClosable: true,
      nzClosable: true,
      nzFooter: null,
      nzClassName: 'modal-invitation'
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((value) => value),
        switchMap(this.fetchDocumentPermission$.bind(this))
      )
      .subscribe();
  }

  assignRoleMany(assigneeIds: string[], role: Exclude<RolePermisson, 'creator'>) {
    return this.kieService.inviteMember(this.documentId, { role, assigneeIds }).pipe(
      tap(() => {
        this.toastr.success('Chuyển vai trò thành công!');
      }),
      catchError((err) => {
        let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
        if (err instanceof HttpErrorResponse) {
          switch (err.status) {
            case 403:
              errorMessage =
                'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
          }
        }
        this.toastr.error(errorMessage);
        return EMPTY;
      })
    );
  }

  handleChangeAssigneeRole(e: { assigneeIds; role }) {
    this.assignRoleMany(e.assigneeIds, e.role)
      .pipe(switchMap(this.fetchDocumentPermission$.bind(this)))
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
