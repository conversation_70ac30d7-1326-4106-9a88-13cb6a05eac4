import { Component, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { EMPTY, catchError, switchMap, tap } from 'rxjs';
import {
  FieldOcrResultChange,
  FieldType,
  File,
  FileStatus,
  ValueTypeField,
  ValueTypeList,
  ValueTypeTable,
  excludedKeyFields
} from '../kie';
import { ActivatedRoute, Router } from '@angular/router';
import { cloneDeep, difference, get, set } from 'lodash';
import UtilsService from '@platform/app/core/services/utils.service';
import { ToastrService } from 'ngx-toastr';
import { NzModalService } from 'ng-zorro-antd/modal';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-extract-file',
  templateUrl: './extract-file.component.html',
  styleUrls: ['./extract-file.component.scss']
})
export class ExtractFileComponent implements OnInit {
  readonly FileStatus = FileStatus;
  file: Partial<File>;
  editingFieldSet = new Set<string>();
  goBackCommands: string[];

  constructor(
    private kieService: KIEService,
    private route: ActivatedRoute,
    private utils: UtilsService,
    private toastr: ToastrService,
    private modal: NzModalService
  ) {}

  ngOnInit(): void {
    this.load().subscribe();
  }

  load() {
    return this.kieService.getFileDetail(this.route.snapshot.paramMap.get('id')).pipe(
      tap((resp) => {
        const extraConfig = resp.data.document.extraConfig || [];
        const ocrResult = get(resp, 'data.ocrResult', {});
        const initialOcrResult = get(resp, 'data.initialOcrResult', {});
        const ocrResultEntries = [];

        /* some fields (like field with type table) exist only in ocrResult */
        /* some fields exist only document.extraConfig (document.templateVerNo !== file.templateVerNo) */
        const fieldNamesInOcrResultOnly = difference(
          Object.keys(ocrResult),
          extraConfig.map((item) => item.name)
        ).filter((fieldName) => !excludedKeyFields.includes(fieldName));

        /* pick all fields with key from document.extraConfig first */
        extraConfig.forEach((conf) => {
          const fieldName = get(conf, 'name');
          if (!fieldName || !ocrResult[fieldName]) return;
          const id = `${this.utils.removeAccents(fieldName.toLowerCase().split(' ').join('_'))}_${this.utils.makeId(6)}`;
          const field = ocrResult[fieldName];
          set(field, 'config', {
            name: fieldName,
            extraConfig: { ...conf, id }
          });
          set(field, 'initialOcrResult', initialOcrResult[fieldName]);
          ocrResultEntries.push([id, field]);
        });
        /* pick remaining fields, those only exist in ocrResult without associated config */
        fieldNamesInOcrResultOnly.forEach((fieldName) => {
          const id = `${this.utils.removeAccents(fieldName.toLowerCase().split(' ').join('_'))}_${this.utils.makeId(6)}`;
          const field = ocrResult[fieldName];
          set(field, 'config', {
            name: fieldName,
            extraConfig: { id, order: ocrResultEntries.length /* pushed to bottom */ }
          });
          set(field, 'initialOcrResult', initialOcrResult[fieldName]);
          ocrResultEntries.push([id, field]);
        });

        this.file = {
          documentId: resp.data.documentId,
          id: resp.data.id,
          document: resp.data.document,
          name: resp.data.name,
          fileLink: resp.data.fileLink,
          ocrResult: Object.fromEntries(ocrResultEntries) as any,
          status: resp.data.status
        };

        this.goBackCommands = [
          'key-information-extractor',
          'document',
          this.file.documentId
        ];
      }),
      catchError((e) => {
        // this.router.navigate(['/']);
        console.log(e);
        return EMPTY;
      })
    );
  }

  handleEditingOcrResult(e: FieldOcrResultChange) {
    if (this.file.status === this.FileStatus.Accepted) {
      this.toastr.info(
        'Không thể sửa được kết quả OCR khi giấy tờ ở trạng thái đã xác nhận'
      );
      return;
    }
    const newOcrResult = cloneDeep(this.file.ocrResult);
    this.editingFieldSet.add(e.fieldId);
    switch (newOcrResult[e.fieldId].type) {
      case FieldType.Field: {
        set(newOcrResult, `${e.fieldId}.text`, e.text);
        break;
      }

      case FieldType.List: {
        set(newOcrResult, `${e.fieldId}.cells.${e.valueTypeListPath}.text`, e.text);
        break;
      }

      case FieldType.Table: {
        const { columnId, cellId } = e.valueTypeTablePath;
        set(
          newOcrResult,
          `${e.fieldId}.columns.${columnId}.cells.${cellId}.text`,
          e.text
        );
        set(newOcrResult, `${e.fieldId}.rows.${cellId}.cells.${columnId}.text`, e.text);
        break;
      }

      default:
        break;
    }

    this.file.ocrResult = newOcrResult;
  }

  handleSaveFileOcrResult() {
    if (!this.checkActionsPermission) return;
    if (this.editingFieldSet.size === 0) return;
    const fields = Array.from(this.editingFieldSet)
      .map<{
        name: string;
        value: ValueTypeField | ValueTypeList | ValueTypeTable;
      }>((fieldId) => {
        const field = this.file.ocrResult[fieldId];
        switch (field.type) {
          case FieldType.Field:
            return {
              name: field.config.name,
              value: { type: FieldType.Field, text: field.text }
            };
          case FieldType.List:
            return {
              name: field.config.name,
              value: {
                type: FieldType.List,
                cells: field.cells.map((cell) => ({
                  text: cell.text,
                  type: FieldType.Field
                }))
              }
            };
          case FieldType.Table:
            return {
              name: field.config.name,
              value: {
                type: FieldType.Table,
                rows: field.rows.map((row) => ({
                  type: FieldType.List,
                  cells: row.cells.map((cell) => ({
                    type: FieldType.Field,
                    text: cell.text
                  }))
                }))
              }
            };
        }
      })
      .filter((field) => !!field);
    this.kieService
      .updateFileOcrResult(this.file.id, fields)
      .pipe(
        tap(() => {
          this.toastr.success('Lưu chỉnh sửa kết quả OCR của file thành công');
          this.editingFieldSet.clear();
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        }),
        switchMap(this.load.bind(this))
      )
      .subscribe();
  }

  openModalAcceptFileOcrResult(modalRef) {
    if (!this.checkActionsPermission) return;
    this.modal.create({
      nzContent: modalRef,
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '375px' },
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  handleCancelModalAcceptFileOcrResult() {
    this.modal.closeAll();
  }

  handleAcceptFileOcrResult() {
    if (!this.checkActionsPermission) return;
    if (this.file.status === this.FileStatus.NotAccepted) {
      this.kieService
        .updateFileStatus(this.file.id, this.FileStatus.Accepted)
        .pipe(
          tap(() => {
            this.toastr.success('Đã xác nhận kết quả OCR của file thành công');
            this.editingFieldSet.clear();
            this.handleCancelModalAcceptFileOcrResult();
          }),
          switchMap(this.load.bind(this)),
          catchError((err) => {
            let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
            if (err instanceof HttpErrorResponse) {
              switch (err.status) {
                case 403:
                  errorMessage =
                    'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
              }
            }
            this.toastr.error(errorMessage);
            return EMPTY;
          })
        )
        .subscribe();
    }
  }

  get checkActionsPermission() {
    return ['creator', 'editor'].includes(get(this.file, 'document.myRole'));
  }
}
