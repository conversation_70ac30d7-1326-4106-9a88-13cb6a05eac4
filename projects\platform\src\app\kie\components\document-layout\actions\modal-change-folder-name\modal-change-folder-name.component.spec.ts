import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalChangeFolderNameComponent } from './modal-change-folder-name.component';

describe('ModalChangeFolderNameComponent', () => {
  let component: ModalChangeFolderNameComponent;
  let fixture: ComponentFixture<ModalChangeFolderNameComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalChangeFolderNameComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalChangeFolderNameComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
