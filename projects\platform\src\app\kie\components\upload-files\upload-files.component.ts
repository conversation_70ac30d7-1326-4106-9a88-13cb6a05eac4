import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import UtilsService from '@platform/app/core/services/utils.service';
import { get } from 'lodash';
import { ToastrService } from 'ngx-toastr';

export type UploadFilesChange = {
  action: 'add' | 'remove';
  removeIndex?: number;
  addedFiles?: File[];
};

@Component({
  selector: 'app-upload-files',
  templateUrl: './upload-files.component.html',
  styleUrls: ['./upload-files.component.scss']
})
export class UploadFilesComponent implements OnInit {
  @Input()
  maxFileCount = 1;

  @Input()
  fileList: File[] = [];

  @Output()
  onUploadFilesChange = new EventEmitter<UploadFilesChange>();

  RULE_ACCEPT = {
    mimetypes: ['application/pdf', 'image/jpeg', 'image/png'],
    accept: '.jpeg, .jpg, .png, .pdf',
    typeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    extensions: ['pdf', 'jpeg', 'jpg', 'png'],
    size: 10 * 1024 * 1024, // 10MB
    getSizeStr() {
      return this.size / (1024 * 1024) + 'MB';
    }
  };

  convertBytesToMB = this.utilsService.convertBytesToMB;

  constructor(
    private utilsService: UtilsService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {}

  handleChange(e, inputFileElem) {
    let files: File[] = Array.from(get(e, 'target.files', []));
    if (!files.length) return;

    files = files
      .map((file) => {
        // Extracting the file extension from the file name
        const extension = file.name.split('.').pop().toLowerCase();

        // Check if the MIME type is in the accepted list and the file extension is in the accepted list
        if (
          !this.RULE_ACCEPT.mimetypes.includes(file.type) ||
          !this.RULE_ACCEPT.extensions.includes(extension)
        ) {
          this.toastrService.error(
            `File ${file.name} không đúng định dạng cho phép (Hỗ trợ file ${this.RULE_ACCEPT.extensions.join(', ')})`
          );
          return;
        }

        // Check file size
        if (file.size > this.RULE_ACCEPT.size) {
          this.toastrService.error(
            `File ${file.name} vượt quá dung lượng cho phép (Dung lượng tối đa cho phép ${this.RULE_ACCEPT.getSizeStr()})`
          );
          return;
        }

        return file;
      })
      .filter((file) => !!file);

    if (!files.length) return;

    let newAddedFiles = files;
    if (files.length + this.fileList.length > this.maxFileCount) {
      this.toastrService.error(`Tối đa upload ${this.maxFileCount} file`);
      if (this.maxFileCount - this.fileList.length <= 0) return;
      newAddedFiles = files.slice(0, this.maxFileCount - this.fileList.length);
    }

    this.onUploadFilesChange.emit({ action: 'add', addedFiles: newAddedFiles });

    // reset input file element
    if (inputFileElem) inputFileElem.value = null;
  }

  removeFile(id: number): void {
    this.onUploadFilesChange.emit({ action: 'remove', removeIndex: id });
  }
}
