<div class="ocr-result-viewer-header">
  <div
    nz-tooltip
    nzTooltipPlacement="topLeft"
    [nzTooltipTitle]="fileName"
    class="flex-1 text-base font-bold w-[1px] truncate break-words"
    *ngIf="fileName"
  >
    {{ fileName }}
  </div>
  <div
    class="flex-shrink-0 flex gap-6 items-center justify-between text-xs font-semibold"
    [ngStyle]="{
      flex: fileName ? 'initial' : '1'
    }"
  >
    <button (click)="prevPage()" class="flex items-center gap-2">
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.38016 3.95312L2.3335 7.99979L6.38016 12.0465"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M13.6668 8H2.44678"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Trước
    </button>
    <div class="flex items-center gap-[6px]">
      Trang
      <span
        class="p-[6px] rounded-[4px] border text-text-1 border-[#16161D] bg-bg-1 min-w-[30px] text-center"
      >
        {{ currentPage }}
      </span>
      /
      <span>{{ numPages }}</span>
    </div>
    <button (click)="nextPage()" class="flex items-center gap-2">
      Sau
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.62012 3.95312L13.6668 7.99979L9.62012 12.0465"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2.3335 8H13.5535"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
  <div
    class="cursor-pointer rounded-full w-6 h-6 flex items-center justify-end"
    nz-popover
    nzPopoverTrigger="click"
    [nzPopoverContent]="showBboxesMenu"
    nzPopoverPlacement="bottomRight"
  >
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.99984 2C7.2665 2 6.6665 2.6 6.6665 3.33333C6.6665 4.06667 7.2665 4.66667 7.99984 4.66667C8.73317 4.66667 9.33317 4.06667 9.33317 3.33333C9.33317 2.6 8.73317 2 7.99984 2Z"
        fill="var(--header-text-color, white)"
      />
      <path
        d="M7.99984 11.3335C7.2665 11.3335 6.6665 11.9335 6.6665 12.6668C6.6665 13.4002 7.2665 14.0002 7.99984 14.0002C8.73317 14.0002 9.33317 13.4002 9.33317 12.6668C9.33317 11.9335 8.73317 11.3335 7.99984 11.3335Z"
        fill="var(--header-text-color, white)"
      />
      <path
        d="M7.99984 6.6665C7.2665 6.6665 6.6665 7.2665 6.6665 7.99984C6.6665 8.73317 7.2665 9.33317 7.99984 9.33317C8.73317 9.33317 9.33317 8.73317 9.33317 7.99984C9.33317 7.2665 8.73317 6.6665 7.99984 6.6665Z"
        fill="var(--header-text-color, white)"
      />
    </svg>
  </div>
</div>
<app-document-reproduction
  class="flex-1"
  [documentReproductionInputs]="{ file: file, ocrResult: ocrResult }"
  [isShowingBbox]="{
    paragraph: isShowingParagraphBbox,
    line: isShowingLineBbox,
    phrase: isShowingPhraseBbox
  }"
  [currentPage]="currentPage"
  (afterFileLoaded)="numPages = $event.numPages; currentPage = 1"
></app-document-reproduction>
<ngx-spinner
  [name]="'ocr-result-viewer'"
  bdColor="rgba(0, 0, 0, 0.2)"
  size="medium"
  color="#fff"
  type="ball-scale-multiple"
  [fullScreen]="false"
  class="contents"
>
  <div class="text-text-2">Đang dựng lại văn bản</div>
</ngx-spinner>

<ng-template #showBboxesMenu>
  <div
    class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden"
  >
    <div class="hover:bg-bg-1 px-3 py-2 border-line">
      <label [(ngModel)]="isShowingParagraphBbox" nz-checkbox class="text-[red]"
        >Paragraph
      </label>
    </div>
    <div class="hover:bg-bg-1 px-3 py-2 border-line">
      <label [(ngModel)]="isShowingLineBbox" nz-checkbox class="text-[green]"
        >Line
      </label>
    </div>
    <div class="hover:bg-bg-1 px-3 py-2 border-line">
      <label [(ngModel)]="isShowingPhraseBbox" nz-checkbox class="text-[blue]"
        >Phrase
      </label>
    </div>
  </div>
</ng-template>
