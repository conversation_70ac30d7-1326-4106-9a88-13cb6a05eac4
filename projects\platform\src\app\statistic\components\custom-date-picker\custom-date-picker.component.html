<div class="text-lg font-bold">T<PERSON><PERSON> chọn</div>

<nz-tabset>
	<nz-tab [nzTitle]="'Ngày'" (nzClick)="currentMode = mode.day">
		<nz-date-picker [nzLocale]="NzLocaleDatePicker" nzInline [(ngModel)]="selectedDate" [nzDisabledDate]="disabledDate"></nz-date-picker>
	</nz-tab>
	<nz-tab [nzTitle]="'Tháng'" (nzClick)="currentMode = mode.month">
		<nz-date-picker [nzLocale]="NzLocaleDatePicker" nzInline nzMode="month" [(ngModel)]="selectedMonth"
			[nzDisabledDate]="disabledDate"></nz-date-picker>
	</nz-tab>
</nz-tabset>

<div class="flex gap-2 justify-end mt-4">
	<button type="button" (click)="modal.close()" class="rounded-md border border-line h-8 px-2">
		Hủy
	</button>
	<button type="button" (click)="apply()" class="rounded-md h-8 px-2 text-white bg-brand-1">
		Chọn
	</button>
</div>