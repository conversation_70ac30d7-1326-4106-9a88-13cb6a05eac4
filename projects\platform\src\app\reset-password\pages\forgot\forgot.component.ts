import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { environment } from '@platform/environment/environment';
import { get, isEmpty } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { ResetPasswordService } from '@platform/app/core/services/reset-password.service';

@Component({
  selector: 'app-forgot',
  templateUrl: './forgot.component.html',
  styleUrls: ['../../reset-password.component.scss'],
})
export class ForgotComponent implements OnInit {
  readonly landingPageUrl = environment.landingPageUrl;
  form: UntypedFormGroup;
  finishedForgotPassword = false;

  constructor(
    private resetPasswordService: ResetPasswordService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.form = new UntypedFormGroup({
      email: new UntypedFormControl(null, [Validators.required, Validators.email]),
    });
  }

  handleSubmit() {
    if (this.form.invalid) {
      this.form.controls['email'].markAsDirty({ onlySelf: true });
      this.form.controls['email'].updateValueAndValidity();
      return;
    }
    this.resetPasswordService
      .createForgotPassword(this.form.value['email'])
      .subscribe({
        error: (err: HttpErrorResponse) => {
          console.log('error');
          if (err.status === 400) {
            this.form.controls['email'].setErrors({ email: true });
          } else if (err.status === 404) {
            this.form.controls['email'].setErrors({ nonExisting: true });
          }
        },
        next: () => {
          this.finishedForgotPassword = true;
        },
      });
  }
}
