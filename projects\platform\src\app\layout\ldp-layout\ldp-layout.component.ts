import { Component, OnInit } from '@angular/core';
import { environment } from '@platform/environment/environment';
import { TranslocoService } from '@ngneat/transloco';

@Component({
  selector: 'app-ldp-layout',
  templateUrl: './ldp-layout.component.html',
  styleUrls: ['./ldp-layout.component.scss'],
})
export class LdpLayoutComponent implements OnInit {
  public landingPageUrl = environment.landingPageUrl;

  constructor(private translocoService: TranslocoService) {}

  setLang(lang: 'en' | 'vi') {
    this.translocoService.setActiveLang(lang);
  }

  getLang() {
    return this.translocoService.getActiveLang();
  }
  ngOnInit(): void {}
}
