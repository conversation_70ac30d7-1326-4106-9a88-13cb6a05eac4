import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LLMRoutingModule } from './llm-routing.module';
import { LLMComponent } from '../llm/llm.component';
import { DocumentViewerComponent } from '@platform/app/components/document-viewer/document-viewer.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [LLMComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LLMRoutingModule,
    NzToolTipModule,
    NzIconModule,
    NzButtonModule,
    NzInputModule,
    NzSkeletonModule,
    DocumentViewerComponent
  ]
})
export class LLMModule {}
