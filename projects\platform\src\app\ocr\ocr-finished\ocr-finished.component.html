<div
  *ngIf="setOfCheckedId.size > 0"
  class="bg-brand-3/10 p-[10px] rounded items-center gap-6 transition-all duration-500 flex"
>
  <div class="flex gap-2 border-r border-icon-1 pr-4 text-sm font-medium text-text-1">
    <img
      (click)="uncheckAll()"
      class="cursor-pointer"
      src="/assets/kie/header-table/cancel.svg"
      alt="cancel-icon"
    />
    <p>
      Đ<PERSON> chọn (<span class="text-brand-1">{{ setOfCheckedId.size }}</span
      >)
    </p>
  </div>
  <button
    class="flex gap-2 text-sm font-medium text-text-1"
    (click)="showExportModalForCheckedItems()"
  >
    <ng-template [ngTemplateOutlet]="exportManyIcon"></ng-template>
    <p>Tải về</p>
  </button>
  <button
    class="flex gap-2 text-sm font-medium text-text-1"
    (click)="deleteCheckedItems()"
  >
    <ng-template [ngTemplateOutlet]="deleteManyIcon"></ng-template>
    <p>Xóa khỏi danh sách</p>
  </button>
</div>
<nz-table
  class="xl:h-[1px] flex-auto overflow-auto"
  #rowSelectionTable
  [nzTemplateMode]="true"
  [nzShowPagination]="false"
  [nzFrontPagination]="true"
  [nzData]="fileList"
  [nzPageIndex]="tablePageIndex"
  [nzPageSize]="tableLimit"
  (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
  [nzTableLayout]="'fixed'"
>
  <thead>
    <tr>
      <th
        nzWidth="40px"
        [nzDisabled]="!rowSelectionTable.data?.length"
        [nzChecked]="checked"
        [nzIndeterminate]="indeterminate"
        (nzCheckedChange)="onAllChecked($event)"
      ></th>
      <th nzWidth="60px">STT</th>
      <th>Tài liệu</th>
      <th nzWidth="180px">Thời gian hoàn thành</th>
      <th nzWidth="100px" nzRight></th>
    </tr>
  </thead>
  <tbody>
    <ng-container *ngFor="let file of rowSelectionTable.data; let i = index">
      <ng-template
        *ngTemplateOutlet="
          tableRow;
          context: { file: file, fileIndex: (tablePageIndex - 1) * tableLimit + i }
        "
      ></ng-template>
    </ng-container>
    <ng-template #tableRow let-file="file" let-fileIndex="fileIndex">
      <tr [ngClass]="{ active: file.id === activeFile?.id }">
        <td
          [nzChecked]="setOfCheckedId.has(file.id)"
          (nzCheckedChange)="onItemChecked(file.id, $event)"
        ></td>
        <td>{{ fileIndex + 1 }}</td>
        <td>
          <div
            (click)="selectFile(file)"
            class="text-text-1 text-sm font-medium truncate break-words hover:text-brand-2 hover:cursor-pointer"
            nz-tooltip
            [nzTooltipTitle]="file.name.length > 30 ? file.name : ''"
          >
            {{ file.name }}
          </div>
        </td>
        <td>
          <div>{{ file.finishedAt | date: 'dd/MM/yyyy HH:mm' }}</div>
        </td>
        <td>
          <div class="flex gap-2">
            <button nz-tooltip nzTooltipTitle="Tải về" (click)="showExportModal([file])">
              <ng-template [ngTemplateOutlet]="exportIcon"></ng-template>
            </button>
            <button nz-tooltip nzTooltipTitle="Xóa" (click)="deleteFile(file)">
              <ng-template [ngTemplateOutlet]="deleteIcon"></ng-template>
            </button>
          </div>
        </td>
      </tr>
    </ng-template>
  </tbody>
</nz-table>
<div
  *ngIf="!fileList.length"
  class="w-full min-h-[500px] flex flex-col gap-5 items-center justify-center bg-[linear-gradient(0deg,rgba(255,255,255,1)50%,rgba(251,251,252,1)100%)]"
>
  <img src="assets/kie/document/empty-table.svg" />
</div>
<nz-pagination
  class=""
  [nzShowTotal]="rangeTemplate"
  [nzTotal]="fileList?.length || 0"
  [nzPageSize]="tableLimit"
  [(nzPageIndex)]="tablePageIndex"
  [nzItemRender]="renderItemTemplate"
>
</nz-pagination>
<ng-template #rangeTemplate let-range="range" let-total>
  <div class="flex justify-between items-center">
    <div>
      Tổng số: <span class="font-medium">{{ total }}</span> file
    </div>
    <div>
      Số file mỗi trang
      <nz-select
        class="w-[65px]"
        [ngModel]="tableLimit"
        (ngModelChange)="onLimitChange($event)"
      >
        <nz-option [nzValue]="5" nzLabel="5"></nz-option>
        <nz-option [nzValue]="10" nzLabel="10"></nz-option>
        <nz-option [nzValue]="20" nzLabel="20"></nz-option>
        <nz-option [nzValue]="30" nzLabel="30"></nz-option>
      </nz-select>
    </div>
  </div>
</ng-template>
<ng-template #renderItemTemplate let-type let-page="page">
  <ng-container [ngSwitch]="type">
    <a *ngSwitchCase="'page'">{{ page }}</a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'prev'">
      <img src="/assets/kie/header-table/previous_page.svg" alt="prev-icon" />
    </a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'next'">
      <img src="/assets/kie/header-table/next_page.svg" alt="next-icon" />
    </a>
    <a class="btn-dot" *ngSwitchCase="'prev_5'">...</a>
    <a class="btn-dot" *ngSwitchCase="'next_5'">...</a>
  </ng-container>
</ng-template>
<ngx-spinner
  [name]="'ocr-finished-loading'"
  bdColor="rgba(0, 0, 0, 0.2)"
  size="medium"
  color="#fff"
  type="ball-scale-multiple"
  [fullScreen]="false"
  class="contents"
>
  <div class="font-semibold text-text-2">Đang cập nhật trạng thái</div>
</ngx-spinner>

<ng-template #exportIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      class="stroke-brand-1"
      d="M18.3332 10C18.3332 10 18.3332 11.2857 18.3332 13.6964V13.7738C18.3332 16.4345 16.7655 17.5 12.8506 17.5H7.14915C3.2343 17.5 1.66661 16.4345 1.66661 13.7738V13.6964C1.66661 11.3036 1.6665 10 1.6665 10"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-brand-1"
      d="M10 12.5009V3.01758"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-brand-1"
      d="M12.7913 10.543L9.99967 13.3346L7.20801 10.543"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #deleteIcon>
  <svg
    class="group"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      class="stroke-status-error"
      d="M17.5 4.98307C14.725 4.70807 11.9333 4.56641 9.15 4.56641C7.5 4.56641 5.85 4.64974 4.2 4.81641L2.5 4.98307"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-status-error"
      d="M7.0835 4.14102L7.26683 3.04935C7.40016 2.25768 7.50016 1.66602 8.9085 1.66602H11.0918C12.5002 1.66602 12.6085 2.29102 12.7335 3.05768L12.9168 4.14102"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-status-error"
      d="M15.7082 7.61719L15.1665 16.0089C15.0748 17.3172 14.9998 18.3339 12.6748 18.3339H7.32484C4.99984 18.3339 4.92484 17.3172 4.83317 16.0089L4.2915 7.61719"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #exportManyIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.3332 10C18.3332 10 18.3332 11.2857 18.3332 13.6964V13.7738C18.3332 16.4345 16.7655 17.5 12.8506 17.5H7.14915C3.2343 17.5 1.66661 16.4345 1.66661 13.7738V13.6964C1.66661 11.3036 1.6665 10 1.6665 10"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10 12.5009V3.01758"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M12.7913 10.543L9.99967 13.3346L7.20801 10.543"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #deleteManyIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5 4.98307C14.725 4.70807 11.9333 4.56641 9.15 4.56641C7.5 4.56641 5.85 4.64974 4.2 4.81641L2.5 4.98307"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M7.0835 4.14102L7.26683 3.04935C7.40016 2.25768 7.50016 1.66602 8.9085 1.66602H11.0918C12.5002 1.66602 12.6085 2.29102 12.7335 3.05768L12.9168 4.14102"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M15.7082 7.61719L15.1665 16.0089C15.0748 17.3172 14.9998 18.3339 12.6748 18.3339H7.32484C4.99984 18.3339 4.92484 17.3172 4.83317 16.0089L4.2915 7.61719"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>
