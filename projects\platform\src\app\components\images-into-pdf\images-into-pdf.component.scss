:host {
	display: block;

	.cdk-drag-preview {
		box-sizing: border-box;
		border-radius: 4px;
		box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
			0 8px 10px 1px rgba(0, 0, 0, 0.14),
			0 3px 14px 2px rgba(0, 0, 0, 0.12);
	}

	.cdk-drag-placeholder {
		opacity: 0;
	}

	.cdk-drag-animating {
		transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
	}

	.file-list.cdk-drop-list-dragging .file-item:not(.cdk-drag-placeholder) {
		transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
	}

	::ng-deep ngx-spinner>.ngx-spinner-overlay>div:not(.loading-text) {
		top: 30% !important;
	}

}

::ng-deep .images-into-pdf-preview-drawer .ant-drawer-content {
	background: #000000D9;
	color: #fff;
}