import { Component, inject, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ExportService } from '@platform/app/core/services/export.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import {
  BehaviorSubject,
  Subject,
  debounceTime,
  distinctUntilChanged,
  map,
  skip,
  takeUntil,
  tap
} from 'rxjs';

enum OutputType {
  Pdf = 'pdf',
  Word = 'word',
  Excel = 'excel'
}

enum PageSelectionType {
  All = 'all',
  Custom = 'custom'
}

@Component({
  selector: 'app-scan-table-export-modal',
  templateUrl: './scan-table-export-modal.component.html',
  styleUrls: ['./scan-table-export-modal.component.scss']
})
export class ScanTableExportModalComponent implements OnInit, OnDestroy {
  readonly nzModalData: {
    fileName: string;
    fileLink: string;
    numPages: number;
    ocrResult: any;
  } = inject(NZ_MODAL_DATA);
  fileName = this.nzModalData.fileName;
  fileLink = this.nzModalData.fileLink;
  numPages = this.nzModalData.numPages;
  ocrResult = this.nzModalData.ocrResult;

  outputType: OutputType = OutputType.Word;
  pageSelectionType: PageSelectionType = PageSelectionType.All;

  pageSelectionSubject = new BehaviorSubject<string>(undefined);
  pageSelection: { valid: boolean; pages?: number[] };
  destroy$ = new Subject<void>();

  constructor(
    private modalRef: NzModalRef,
    private exportService: ExportService,
    private utils: UtilsService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.pageSelectionSubject
      .pipe(
        takeUntil(this.destroy$),
        skip(1),
        debounceTime(300),
        distinctUntilChanged(),
        map((text) => {
          /* validation */
          const regexPattern = /^\d+(-\d+)?(,\d+(-\d+)?)*$/;
          const regexValid = regexPattern.test(text);
          let pageSelectionValid = false;
          let pageSet = new Set<number>();
          if (regexValid) {
            const pageNumberAndPageRangeList = text.split(',').map((item) => item.trim());
            pageSelectionValid = pageNumberAndPageRangeList.every((item) => {
              if (item.includes('-')) {
                const [start, end] = item.split('-').map((item) => +item.trim());
                const rangeValid =
                  0 < start &&
                  start <= this.numPages &&
                  0 < end &&
                  end <= this.numPages &&
                  start < end;
                if (rangeValid) for (let i = start; i <= end; i++) pageSet.add(i);
                return rangeValid;
              } else {
                const page = +item;
                const pageValid = 0 < page && page <= this.numPages;
                if (pageValid) pageSet.add(page);
                return pageValid;
              }
            });
          }
          const valid = regexValid && pageSelectionValid;
          return {
            valid,
            pages: valid ? Array.from(pageSet).sort((a, b) => a - b) : undefined
          };
        }),
        tap((data) => (this.pageSelection = data))
      )
      .subscribe();
  }

  handlePageSelection(value) {
    this.pageSelectionSubject.next(value);
  }

  async handleExport() {
    if (
      this.pageSelectionType === PageSelectionType.Custom &&
      !this.pageSelection?.valid
    ) {
      this.pageSelectionSubject.next(''); // force pageSelection.valid is set to false
      return;
    }

    const pageSelectionList =
      this.pageSelectionType === PageSelectionType.Custom
        ? this.pageSelection && this.pageSelection.valid
          ? this.pageSelection.pages
          : undefined
        : undefined;

    try {
      await this.utils.toggleAppLoadingSpinner(true);
      switch (this.outputType) {
        case OutputType.Pdf:
          break;
        case OutputType.Word: {
          await this.exportService.exportDocxScanTable({
            fileLink: this.fileLink,
            fileType: this.fileName.split('.').pop() as any,
            fileName: this.fileName,
            ocrResult: this.ocrResult,
            outputType: 'file',
            isFigureIncluded: true,
            pageSelectionList
          });

          break;
        }
        case OutputType.Excel: {
          this.exportService.exportXlsxScanTableWithExcelJS({
            fileLink: this.fileLink,
            fileType: this.fileName.split('.').pop() as any,
            fileName: this.fileName,
            ocrResult: this.ocrResult,
            pageSelectionList,
            isFigureIncluded: true,
            outputType: 'file'
          });
          break;
        }
      }
      this.modalRef.close();
    } catch (error) {
      console.log(error);
      this.toastr.error('Đã có lỗi xảy ra khi xuất file');
    } finally {
      await this.utils.toggleAppLoadingSpinner(false);
    }
  }

  handleCancel() {
    this.modalRef.close();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
