<ng-container *transloco="let t; read: 'ocrExperience.wrapper'">
  <ng-container *ngIf="!shouldUseMobileHeader">
    <ng-template *ngTemplateOutlet="header"></ng-template>
  </ng-container>
  <ng-container *ngIf="shouldUseMobileHeader">
    <ng-template *ngTemplateOutlet="headerMobile"></ng-template>
  </ng-container>
  <router-outlet></router-outlet>
</ng-container>

<ng-template #header>
  <header
    class="flex items-center h-16 text-sm sticky top-0 z-10 bg-white shadow-[0px_-1px_0px_0px_#E2E2EA_inset]"
  >
    <div class="px-8 w-[300px] h-full flex items-center">
      <!-- style="background-image: url('assets/key-information-extractor/document/bg-sidebar.png')" TODO: drop background for now -->
      <a [routerLink]="['/']">
        <img src="assets/img/rpa/vnpt-smart-reader-logo.svg" alt="home" />
      </a>
    </div>
    <div class="flex items-center h-full flex-1 gap-7 font-medium">
      <ng-container *ngFor="let menu of menus">
        <a
          *ngIf="!menu.subMenus?.length"
          [routerLink]="menu.path"
          class="flex gap-2 items-center h-full border-t-4 border-transparent border-b-4"
          [ngClass]="{ 'border-b-brand-1': router.isActive(menu.path, false) }"
        >
          <img
            [src]="router.isActive(menu.path, false) ? menu.iconActive : menu.icon"
            alt="icon"
          />
          <div
            [ngClass]="{
              'text-brand-1 font-semibold': router.isActive(menu.path, false)
            }"
          >
            {{ menu.name }}
          </div>
        </a>
        <a
          *ngIf="menu.subMenus?.length"
          nz-dropdown
          [nzDropdownMenu]="subMenu"
          [routerLink]="menu.path"
          class="flex gap-2 items-center h-full border-t-4 border-transparent border-b-4"
          [ngClass]="{ 'border-b-brand-1': router.isActive(menu.path, false) }"
        >
          <img
            [src]="router.isActive(menu.path, false) ? menu.iconActive : menu.icon"
            alt="icon"
          />
          <div
            [ngClass]="{
              'text-brand-1 font-semibold': router.isActive(menu.path, false)
            }"
          >
            {{ menu.name }}
          </div>
          <nz-dropdown-menu #subMenu="nzDropdownMenu">
            <ul nz-menu>
              <li nz-menu-item *ngFor="let sub of menu.subMenus">
                <div class="flex-1" [routerLink]="sub.path">
                  {{ sub.name }}
                </div>
              </li>
            </ul>
          </nz-dropdown-menu>
        </a>
      </ng-container>
    </div>
    <div class="flex gap-6 mr-6">
      <button nz-popover [nzPopoverContent]="profileMenu" nzPopoverTrigger="click">
        <svg
          class="rounded-full object-cover h-10 w-10"
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            opacity="0.25"
            d="M20 40C31.0457 40 40 31.0457 40 20C40 8.9543 31.0457 0 20 0C8.9543 0 0 8.9543 0 20C0 31.0457 8.9543 40 20 40Z"
            fill="#989BB3"
          />
          <path
            d="M25.8594 12.9688C25.8594 9.73789 23.2309 7.10938 20 7.10938C16.7691 7.10938 14.1406 9.73789 14.1406 12.9688C14.1406 16.1996 16.7691 18.8281 20 18.8281C23.2309 18.8281 25.8594 16.1996 25.8594 12.9688ZM20 18.8281C14.1845 18.8281 9.45312 23.5595 9.45312 29.375V30.4733C9.45312 30.8064 9.59484 31.1238 9.84297 31.346C12.6975 33.9038 16.3047 35.3125 20 35.3125C23.6954 35.3125 27.3026 33.9038 30.157 31.346C30.4052 31.1238 30.5469 30.8063 30.5469 30.4733V29.375C30.5469 23.5595 25.8155 18.8281 20 18.8281Z"
            fill="#989BB3"
          />
        </svg>
      </button>
    </div>
  </header>
</ng-template>

<ng-template #headerMobile>
  <header
    class="flex items-center justify-between h-16 text-sm sticky top-0 z-10 bg-white shadow-[0px_-1px_0px_0px_#E2E2EA_inset]"
  >
    <button class="w-16 h-full" (click)="showMenuDrawer()">
      <span
        class="text-[20px] text-text-1"
        nz-icon
        nzType="menu"
        nzTheme="outline"
      ></span>
    </button>
    <a class="block h-full justify-items-center content-center" [routerLink]="['/']">
      <img src="assets/img/rpa/vnpt-smart-reader-logo.svg" alt="home" />
    </a>
    <div class="flex gap-6 mr-6">
      <button nz-popover [nzPopoverContent]="profileMenu" nzPopoverTrigger="click">
        <svg
          class="rounded-full object-cover h-10 w-10"
          width="40"
          height="40"
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            opacity="0.25"
            d="M20 40C31.0457 40 40 31.0457 40 20C40 8.9543 31.0457 0 20 0C8.9543 0 0 8.9543 0 20C0 31.0457 8.9543 40 20 40Z"
            fill="#989BB3"
          />
          <path
            d="M25.8594 12.9688C25.8594 9.73789 23.2309 7.10938 20 7.10938C16.7691 7.10938 14.1406 9.73789 14.1406 12.9688C14.1406 16.1996 16.7691 18.8281 20 18.8281C23.2309 18.8281 25.8594 16.1996 25.8594 12.9688ZM20 18.8281C14.1845 18.8281 9.45312 23.5595 9.45312 29.375V30.4733C9.45312 30.8064 9.59484 31.1238 9.84297 31.346C12.6975 33.9038 16.3047 35.3125 20 35.3125C23.6954 35.3125 27.3026 33.9038 30.157 31.346C30.4052 31.1238 30.5469 30.8063 30.5469 30.4733V29.375C30.5469 23.5595 25.8155 18.8281 20 18.8281Z"
            fill="#989BB3"
          />
        </svg>
      </button>
    </div>
  </header>
</ng-template>

<ng-template #menuDrawerTempl>
  <div class="mt-3 font-medium">
    <!-- only show menu, not show submenu -->
    <a
      *ngFor="let menu of menus"
      [routerLink]="menu.path"
      class="ml-6 h-12 flex gap-3 items-center border-transparent border-r-4"
      [ngClass]="{ 'border-r-brand-1': router.isActive(menu.path, false) }"
    >
      <img
        [src]="router.isActive(menu.path, false) ? menu.iconActive : menu.icon"
        alt="icon"
        class="w-6"
      />
      <div
        [ngClass]="{
          'text-brand-1 font-semibold': router.isActive(menu.path, false)
        }"
      >
        {{ menu.name }}
      </div>
    </a>
  </div>
</ng-template>

<ng-template #profileMenu>
  <div
    class="text-sm border border-line rounded-2xl bg-bg-3 shadow-[0_-4px_20px_0px_rgba(0,0,0,0.15)] min-w-[270px] mt-2"
  >
    <div class="flex gap-4 items-center p-4">
      <svg
        class="rounded-full object-cover h-10 w-10"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          opacity="0.25"
          d="M20 40C31.0457 40 40 31.0457 40 20C40 8.9543 31.0457 0 20 0C8.9543 0 0 8.9543 0 20C0 31.0457 8.9543 40 20 40Z"
          fill="#989BB3"
        />
        <path
          d="M25.8594 12.9688C25.8594 9.73789 23.2309 7.10938 20 7.10938C16.7691 7.10938 14.1406 9.73789 14.1406 12.9688C14.1406 16.1996 16.7691 18.8281 20 18.8281C23.2309 18.8281 25.8594 16.1996 25.8594 12.9688ZM20 18.8281C14.1845 18.8281 9.45312 23.5595 9.45312 29.375V30.4733C9.45312 30.8064 9.59484 31.1238 9.84297 31.346C12.6975 33.9038 16.3047 35.3125 20 35.3125C23.6954 35.3125 27.3026 33.9038 30.157 31.346C30.4052 31.1238 30.5469 30.8063 30.5469 30.4733V29.375C30.5469 23.5595 25.8155 18.8281 20 18.8281Z"
          fill="#989BB3"
        />
      </svg>
      <div>
        <div class="font-semibold">{{ user?.account?.fullName }}</div>
        <div class="text-xs text-text-3">{{ user?.username }}</div>
      </div>
    </div>
    <div class="h-[1px] bg-line mb-2"></div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="openUserInfoModal()"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.99998 12.4993C12.3012 12.4993 14.1666 10.6339 14.1666 8.33268C14.1666 6.0315 12.3012 4.16602 9.99998 4.16602C7.69879 4.16602 5.83331 6.0315 5.83331 8.33268C5.83331 10.6339 7.69879 12.4993 9.99998 12.4993Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M17.1583 18.3333C17.1583 15.1083 13.95 12.5 10 12.5C6.05001 12.5 2.84167 15.1083 2.84167 18.3333"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div>Thông tin tài khoản</div>
    </div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="openChangePasswordModal()"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 8.33268V6.66602C5 3.90768 5.83333 1.66602 10 1.66602C14.1667 1.66602 15 3.90768 15 6.66602V8.33268"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M10 15.4167C11.1506 15.4167 12.0834 14.4839 12.0834 13.3333C12.0834 12.1827 11.1506 11.25 10 11.25C8.84943 11.25 7.91669 12.1827 7.91669 13.3333C7.91669 14.4839 8.84943 15.4167 10 15.4167Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M14.1667 18.334H5.83335C2.50002 18.334 1.66669 17.5007 1.66669 14.1673V12.5007C1.66669 9.16732 2.50002 8.33398 5.83335 8.33398H14.1667C17.5 8.33398 18.3334 9.16732 18.3334 12.5007V14.1673C18.3334 17.5007 17.5 18.334 14.1667 18.334Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div>Đổi mật khẩu</div>
    </div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="openRevokeConsentModal()"
    >
      <svg
        width="18"
        height="20"
        viewBox="0 0 18 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.8641 2.42383H16.6522V18.3336H1.5V2.42383H5.28805"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
        <path
          d="M5.28809 1.66602V6.21167H7.56091C7.56091 5.37451 8.23897 4.69645 9.07613 4.69645C9.91329 4.69645 10.5914 5.37451 10.5914 6.21167H12.8642V1.66602H5.28809Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
        <path
          d="M6.30566 11.3887H12.3663"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
        <path
          d="M6.30566 14.166H9.33597"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
      </svg>
      <div class="truncate">Hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu</div>
    </div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="openRequestRemoveDataModal()"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.0607 7.72656V16.8175C16.0607 17.2193 15.901 17.6047 15.6169 17.8888C15.3327 18.173 14.9474 18.3326 14.5455 18.3326H5.4546C5.05276 18.3326 4.66738 18.173 4.38323 17.8888C4.09908 17.6047 3.93945 17.2193 3.93945 16.8175V7.72656"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
        <path
          d="M1.66699 4.69727H18.3337"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="square"
        />
        <path
          d="M6.96973 4.69632V1.66602H13.0303V4.69632"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
        />
      </svg>
      <div>Yêu cầu xóa dữ liệu cá nhân</div>
    </div>
    <div class="h-[1px] bg-line my-2"></div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="navigateRoute('/docs')"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.16667 18.3332H13.3333C16.25 18.3332 17.5 16.6665 17.5 14.1665V5.83317C17.5 3.33317 16.25 1.6665 13.3333 1.6665H6.66667C3.75 1.6665 2.5 3.33317 2.5 5.83317V11.6665"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.083 3.75V5.41667C12.083 6.33333 12.833 7.08333 13.7497 7.08333H15.4163"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path d="M3.33366 14.1665L1.66699 15.8332L3.33366 17.4998" fill="#6C7093" />
        <path
          d="M3.33366 14.1665L1.66699 15.8332L3.33366 17.4998"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M5.83301 14.1665L7.49967 15.8332L5.83301 17.4998"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div>Tích hợp</div>
    </div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="navigateRoute('/token-info')"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.4916 12.4426C14.775 14.1509 12.3166 14.6759 10.1583 14.0009L6.23331 17.9176C5.94998 18.2092 5.39165 18.3842 4.99165 18.3259L3.17498 18.0759C2.57498 17.9926 2.01665 17.4259 1.92498 16.8259L1.67498 15.0092C1.61665 14.6092 1.80831 14.0509 2.08331 13.7676L5.99998 9.85091C5.33331 7.68425 5.84998 5.22591 7.56665 3.51758C10.025 1.05924 14.0166 1.05924 16.4833 3.51758C18.95 5.97591 18.95 9.98425 16.4916 12.4426Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M5.74164 14.5742L7.6583 16.4909"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.0833 9.16602C12.7737 9.16602 13.3333 8.60637 13.3333 7.91602C13.3333 7.22566 12.7737 6.66602 12.0833 6.66602C11.393 6.66602 10.8333 7.22566 10.8333 7.91602C10.8333 8.60637 11.393 9.16602 12.0833 9.16602Z"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div>Quản lý Token</div>
    </div>
    <div class="h-[1px] bg-line my-2"></div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="navigateRoute('/subscription')"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.99998 18.9577C6.66665 18.9577 3.95831 16.566 3.95831 13.6243V10.541C3.95831 10.1993 4.24165 9.91602 4.58331 9.91602C4.92498 9.91602 5.20831 10.1993 5.20831 10.541C5.20831 12.7243 7.26665 14.3743 9.99998 14.3743C12.7333 14.3743 14.7916 12.7243 14.7916 10.541C14.7916 10.1993 15.075 9.91602 15.4166 9.91602C15.7583 9.91602 16.0416 10.1993 16.0416 10.541V13.6243C16.0416 16.566 13.3333 18.9577 9.99998 18.9577ZM5.20831 13.716C5.26665 15.9243 7.39165 17.7077 9.99998 17.7077C12.6083 17.7077 14.7333 15.9243 14.7916 13.716C13.7083 14.891 11.9916 15.6243 9.99998 15.6243C8.00831 15.6243 6.29998 14.891 5.20831 13.716Z"
          fill="#6C7093"
        />
        <path
          d="M9.99998 11.4577C7.69998 11.4577 5.6333 10.4243 4.62497 8.75768C4.19164 8.04935 3.95831 7.22435 3.95831 6.37435C3.95831 4.94102 4.59998 3.59102 5.75832 2.57435C6.89165 1.58268 8.39998 1.04102 9.99998 1.04102C11.6 1.04102 13.1 1.58268 14.2416 2.56602C15.4 3.59102 16.0416 4.94102 16.0416 6.37435C16.0416 7.22435 15.8083 8.04101 15.375 8.75768C14.3667 10.4243 12.3 11.4577 9.99998 11.4577ZM9.99998 2.29102C8.69998 2.29102 7.48333 2.72435 6.57499 3.52435C5.69166 4.29101 5.20831 5.30768 5.20831 6.37435C5.20831 6.99935 5.37497 7.58268 5.69164 8.10768C6.4833 9.40768 8.13331 10.2077 9.99998 10.2077C11.8666 10.2077 13.5167 9.39935 14.3083 8.10768C14.6333 7.58268 14.7916 6.99935 14.7916 6.37435C14.7916 5.30768 14.3083 4.29101 13.4167 3.50768C12.5083 2.72435 11.3 2.29102 9.99998 2.29102Z"
          fill="#6C7093"
        />
        <path
          d="M9.99998 15.6243C6.55831 15.6243 3.95831 13.441 3.95831 10.541V6.37435C3.95831 3.43268 6.66665 1.04102 9.99998 1.04102C11.6 1.04102 13.1 1.58268 14.2416 2.56602C15.4 3.59102 16.0416 4.94102 16.0416 6.37435V10.541C16.0416 13.441 13.4416 15.6243 9.99998 15.6243ZM9.99998 2.29102C7.35831 2.29102 5.20831 4.12435 5.20831 6.37435V10.541C5.20831 12.7243 7.26665 14.3743 9.99998 14.3743C12.7333 14.3743 14.7916 12.7243 14.7916 10.541V6.37435C14.7916 5.30768 14.3083 4.29101 13.4167 3.50768C12.5083 2.72435 11.3 2.29102 9.99998 2.29102Z"
          fill="#6C7093"
        />
      </svg>
      <div>Gói cước</div>
    </div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer"
      (click)="navigateRoute('/payment-history')"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.125 18.0577C15.7 17.116 18.3334 13.866 18.3334 9.99935C18.3334 5.39935 14.6334 1.66602 10 1.66602C4.44169 1.66602 1.66669 6.29935 1.66669 6.29935M1.66669 6.29935V2.49935M1.66669 6.29935H3.34169H5.36669"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M1.66669 10C1.66669 14.6 5.40002 18.3333 10 18.3333"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-dasharray="3 3"
        />
      </svg>
      <div>Lịch sử thanh toán</div>
    </div>
    <div class="h-[1px] bg-line my-2"></div>
    <div
      class="flex gap-3 items-center px-4 py-2 hover:bg-bg-1 hover:cursor-pointer rounded-b-2xl"
      (click)="handleLogout()"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.41669 6.29922C7.67502 3.29922 9.21669 2.07422 12.5917 2.07422H12.7C16.425 2.07422 17.9167 3.56589 17.9167 7.29089V12.7242C17.9167 16.4492 16.425 17.9409 12.7 17.9409H12.5917C9.24169 17.9409 7.70002 16.7326 7.42502 13.7826"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.5 10H3.01666"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M4.87498 7.20898L2.08331 10.0007L4.87498 12.7923"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div>Đăng xuất</div>
    </div>
  </div>
</ng-template>
