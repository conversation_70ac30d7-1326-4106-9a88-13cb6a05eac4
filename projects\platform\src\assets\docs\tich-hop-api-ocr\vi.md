# API số hoá văn bản (phiên bản cơ bản)

**Chức năng**: Nhận dạng ký tự từ văn bản đư<PERSON> trả về từ API `/file-service/v1/addFile`. <PERSON><PERSON><PERSON> bản cơ bản có khả năng OCR chữ đánh máy và xuất file kết quả OCR dưới dạng docx.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/ocr/scan`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Gi<PERSON> trị</strong></th>
<th><strong><PERSON><PERSON><PERSON> buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong><PERSON><PERSON> tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service auth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>String</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
</tbody></table>

Đối với nhận dạng văn bản thuần Text, ý nghĩa kết quả trả về bao gồm:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>Phrase</td>
<td>Hiển thị list các cụm từ, câu được OCR từ văn bản. OCR từ trên xuống dưới, từ trái qua phải, theo dòng. Các cụm từ được OCR hiển thị theo thứ tự từ trên xuống dưới.</td>
</tr>
<tr>
<td>2</td>
<td>Line</td>
<td>Hiển thị list các thông tin được OCR từ văn bản theo từng dòng. OCR từ trên xuống dưới, từ trái qua phải, theo dòng.</td>
</tr>
<tr>
<td>3</td>
<td>Paragraph</td>
<td>Hiển thị list các thông tin được OCR từ văn bản theo đoạn. OCR từ trên xuống dưới, từ trái qua phải, theo dòng.</td>
</tr>
</tbody></table>

![img-1.png](img-1.png)

**Example:**

Request body

```json
{ 
	"token": "8928skjhfa89298jahga1771vbvb",
	"client_session": "00-14-22-01-23-45-1548211589291",
	"file_type": "pdf",
	"file_hash" : "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
	"details": false
}
```

Response success

```json
{
	"object": {
		"lines": [
			"SAO Y; Ủy ban nhân dân Thành phố",
			"Hồ Chí Minh; 11/11/2022; 14:11:40"
		],
		"paragraphs": [
			"SAO Y; Ủy ban nhân dân Thành phố Hồ Chí Minh; 11/11/2022; 14:11:40"
		],
		"phrases": [
			"SAO Y; Ủy ban nhân dân Thành phố",
			"Hồ Chí Minh; 11/11/2022; 14:11:40"
		],
		"num_of_pages": 2,
		"warning_messages": [],
		"warnings": []
	},
	"server_version": "1.3.15",
	"status": "OK",
	"statusCode": 200
}
```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
	"token": "8928skjhfa89298jahga1771vbvb",
	"client_session": "00-14-22-01-23-45-1548211589291",
	"file_type": "pdf",
	"file_hash" : "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
	"details": true
}
```

Response success

```json
{
  "object": {
    "num_of_pages": 1,
    "warnings": [],
    "warning_messages": [],
    "phrases": [
      {
        "bbox_conf_score": 1,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 0.7583498954772949,
            "bboxes": {
              "1": [0.6164705882352941, 0.015, 0.84, 0.022727272727272728]
            },
            "confidence_score": 0.9968702793121338,
            "font_styles": ["normal"],
            "text": "SAO Y; Ủy ban nhân dân Thành phố",
            "type": "Phrase",
            "warnings": []
          }
        ],
        "confidence_score": 1,
        "type": "List",
        "warnings": []
      }
    ],
    "paragraphs": [
      {
        "bbox_conf_score": 1,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 1,
            "bboxes": {},
            "confidence_score": 1,
            "font_styles": ["normal"],
            "text": "SAO Y; Ủy ban nhân dân Thành phố Hồ Chí Minh; 11/11/2022; 14:11:40 ỦY BAN NHÂN DÂN CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM THÀNH PHỐ HỒ CHÍ MINH Độc lập - Tự do - Hạnh phúc Số: 210 /BC-UBND Thành phố Hồ Chí Minh, ngày 03 tháng 11 năm 2022 BÁO CÁO Tình hình kinh tế - xã hội tháng 10, 10 tháng đầu năm; nhiệm vụ, giải pháp trọng tâm tháng 11 năm 2022 Kính gửi: Chính phủ I.",
            "type": "Paragraph",
            "warnings": []
          }
        ],
        "confidence_score": 1,
        "type": "List",
        "warnings": []
      }
    ],
    "lines": [
      {
        "bbox_conf_score": 1,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 0.7583423120541744,
            "bboxes": {
              "1": [0.6164705882352941, 0.015, 0.84, 0.022727272727272728]
            },
            "confidence_score": 0.9968603107090266,
            "font_styles": ["normal"],
            "text": "SAO Y; Ủy ban nhân dân Thành phố",
            "type": "Phrase",
            "warnings": []
          }
        ],
        "confidence_score": 1,
        "type": "List",
        "warnings": []
      }
    ]
  }
}
```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>Bảng mô tả kết quả trả về của các trường thông tin</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
</tbody></table>

**Bảng mô tả kết quả trả về của các trường thông tin**

<!-- start using html to help with <table> colspan and rolspan rendering -->
<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Thông tin</strong></th>
<th><strong>Ý nghĩa</strong></th>
</tr>
</thead>
<tbody><tr>
<td colspan="3"><strong>Đối với thông tin ngoài bảng</strong></td>
</tr>
<tr>
<td>1</td>
<td><strong>bboxes</strong></td>
<td>Tọa độ vị trí của trường thông tin bóc tách Tọa độ của trường thông tin bóc tách (là một dict ghi tọa độ tương đối của 4 đỉnh bounding box, cụ thể là X min, Y min, X max, Y max, hai số đầu là góc trên bên trái, hai số cuối là góc dưới bên phải)</td>
</tr>
<tr>
<td>2</td>
<td><strong>confidence_score</strong></td>
<td>Độ chính xác của trường bóc tách</td>
</tr>
<tr>
<td>3</td>
<td><strong>bbox_conf_score</strong></td>
<td>Độ tự tin của toạ độ vị trí vùng đáp án nhận diện</td>
</tr>
<tr>
<td>4</td>
<td><strong>warnings</strong></td>
<td>Thông tin cảnh báo</td>
</tr>
<tr>
<td>5</td>
<td><strong>text</strong></td>
<td>Kết quả bóc tách thông tin</td>
</tr>
<tr>
<td>6</td>
<td><strong>type</strong></td>
<td>Kiểu OCR (nhận 1 trong các giá trị Line, Phrase, Paragraph, List)</td>
</tr>
<tr>
<td>7</td>
<td><strong>font_styles</strong></td>
<td>Kiểu dáng của thông tin bóc tách</td>
</tr>
</tbody></table>
<!-- end using html to help with <table> colspan and rolspan rendering -->

# API số hoá văn bản (phiên bản nâng cao)

**Chức năng**: Nhận dạng ký tự từ văn bản thuần chữ đánh máy, xử lý đối với văn bản có bảng được trả về từ API `/file-service/v1/addFile`. Phiên bản nâng cao có khả năng giữ nguyên cấu trúc văn bản, kiểu chữ (in đậm, in nghiêng), dựng lại bảng biểu và nhận dạng ảnh nằm trong văn bản. Phiên bản này hỗ trợ xuất file kết quả OCR dưới dạng docx, xlsx & json.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/ocr/scan-table`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service auth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>String</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
<tr>
<td></td>
<td>exporter</td>
<td>String</td>
<td></td>
<td></td>
<td>Hỗ trợ trả về link download File sau khi OCR dưới dạng docx; excel (Trình bày nội dung trong 1 sheet hoặc chia thành nhiều sheet tương ứng với các trang văn bản); json. Các giá trị được chấp nhận bao gồm:<br>'docx', 'xlsx', 'xlsx_nsheet', 'json'. Chỉ hỗ trợ thực hiện trong trường hợp details: <strong>true</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
</tbody></table>

Đối với nhận dạng văn bản thuần Text, ý nghĩa kết quả trả về bao gồm:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>Phrase</td>
<td>Hiển thị list các cụm từ, câu được OCR từ văn bản. OCR từ trên xuống dưới, từ trái qua phải, theo dòng. Các cụm từ được OCR hiển thị theo thứ tự từ trên xuống dưới.</td>
</tr>
<tr>
<td>2</td>
<td>Line</td>
<td>Hiển thị list các thông tin được OCR từ văn bản theo từng dòng. OCR từ trên xuống dưới, từ trái qua phải, theo dòng.</td>
</tr>
<tr>
<td>3</td>
<td>Paragraph</td>
<td>Hiển thị list các thông tin được OCR từ văn bản theo đoạn. OCR từ trên xuống dưới, từ trái qua phải, theo dòng.</td>
</tr>
<tr>
<td>4</td>
<td>Table</td>
<td>Hiển thị thông tin trong bảng, vị trí của các hàng và cột trong bảng</td>
</tr>
</tbody></table>

![img-2.png](img-2.png)

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
  "details": false
}
```

Response success

```json
{
  "message": "IDG-00000000",
  "object": {
    "lines": [
      ["d) Hình thức lựa chọn nhà thầu: Lựa chọn theo quy định tại khoản a, mục 4,"]
    ],
    "num_of_pages": 4,
    "paragraphs": [
      [
        "Trên cơ sở những nội dung phân tích nêu trên, Ủy ban nhân dân phường Tân\nHưng Thuận đề nghị phòng Tài chính - Kế hoạch xem xét, phê duyệt kế hoạch lựa\nchọn nhà thầu: Duy tu mặt đường hẻm 27, khu phố 2, phường Tân Hưng Thuận; tham\nmưu kính trình Ủy ban nhân dân Quận 12 xem xét, phê duyệt, để đơn vị làm cơ sở\nthực hiện cho việc triển khai các bước tiếp theo đúng tiến độ thời gian quy định\ncủa Luật đấu thầu."
      ]
    ],
    "phrases": [["2. Giải trình nội dung kế hoạch lựa chọn nhà thầu:"]],
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.15",
  "status": "OK",
  "statusCode": 200
}
```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
  "exporter": "docx",
  "details": true
}
```

Response success

```json
{
  "message": "IDG-00000000",
  "object": {
    "lines": [
      {
        "bbox_conf_score": 1.0,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 0.6728609867488666,
            "bboxes": {
              "1": [
                0.17641325536062377, 0.24855907780979827, 0.9327485380116959,
                0.2672910662824208
              ]
            },
            "confidence_score": 0.9900556740892551,
            "font_styles": ["normal"],
            "line_id": 9,
            "page_id": 1,
            "paragraph_id": 9,
            "text": "d) Hình thức lựa chọn nhà thầu: Lựa chọn theo quy định tại khoản a, mục 4,",
            "type": "Phrase",
            "warnings": []
          }
        ],
        "confidence_score": 1.0,
        "type": "List",
        "warnings": []
      }
    ],
    "link": "https://storage-cic.vnpt.vn/rpa-media/exporter/production/docx/71-e-36-c-92-1-e-24-4-b-67-bf-17-d-2-f-85-c-78-dfef-1739865991.docx?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=smartrpa%2F20250218%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250218T080631Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=eea937bfdc86583b9f7f58415a2c4abe0ad7283949b002c9df968ecc15f14574",
    "num_of_pages": 1,
    "paragraphs": [
      {
        "bbox_conf_score": 1.0,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 1.0,
            "bboxes": {
              "1": [
                0.1267056530214425, 0.4762247838616715, 0.9337231968810916,
                0.7334293948126801
              ]
            },
            "cells": [
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.1276803118908382, 0.4783861671469741, 0.19785575048732942,
                    0.5410662824207493
                  ]
                },
                "confidence_score": 0.9999826688020046,
                "font_styles": ["bold"],
                "text": "STT",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.19785575048732942, 0.4783861671469741, 0.7300194931773879,
                    0.5410662824207493
                  ]
                },
                "confidence_score": 0.9999878543542445,
                "font_styles": ["bold"],
                "text": "Nội dung",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.7300194931773879, 0.4783861671469741, 0.9327485380116959,
                    0.5410662824207493
                  ]
                },
                "confidence_score": 0.9664208210403552,
                "font_styles": ["bold"],
                "text": "Giá trị\n(Đồng)",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.1276803118908382, 0.5410662824207493, 0.19785575048732942,
                    0.5972622478386167
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["normal"],
                "text": "01",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.19785575048732942, 0.5410662824207493, 0.7300194931773879,
                    0.5972622478386167
                  ]
                },
                "confidence_score": 0.9925300467664115,
                "font_styles": ["normal"],
                "text": "Tổng giá trị phần công việc không áp dụng được một\ntrong các hình thức lựa chọn nhà thầu",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.7300194931773879, 0.5410662824207493, 0.9327485380116959,
                    0.5972622478386167
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["normal"],
                "text": "0",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.1276803118908382, 0.5972622478386167, 0.19785575048732942,
                    0.6570605187319885
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["normal"],
                "text": "02",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.19785575048732942, 0.5972622478386167, 0.7300194931773879,
                    0.6570605187319885
                  ]
                },
                "confidence_score": 0.978897051115472,
                "font_styles": ["normal"],
                "text": "Tổng giá trị phần công việc thuộc kế hoạch lựa chọn\nnhà thầu",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.7300194931773879, 0.5972622478386167, 0.9327485380116959,
                    0.6570605187319885
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["normal"],
                "text": "221.073.033",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.1276803118908382, 0.6570605187319885, 0.7300194931773879,
                    0.6974063400576369
                  ]
                },
                "confidence_score": 0.9700056093950182,
                "font_styles": ["bold"],
                "text": "Tổng giá trị các phần công việc",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.7300194931773879, 0.6570605187319885, 0.9327485380116959,
                    0.6974063400576369
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["bold"],
                "text": "221.073.033",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.1276803118908382, 0.6974063400576369, 0.7300194931773879,
                    0.7327089337175793
                  ]
                },
                "confidence_score": 0.9975423441804968,
                "font_styles": ["bold"],
                "text": "Tổng mức đầu tư của dự án",
                "type": "Cell",
                "warnings": []
              },
              {
                "bbox_conf_score": 0.9999,
                "bboxes": {
                  "1": [
                    0.7300194931773879, 0.6974063400576369, 0.9327485380116959,
                    0.7327089337175793
                  ]
                },
                "confidence_score": 0.949990500094999,
                "font_styles": ["bold"],
                "text": "221.073.033",
                "type": "Cell",
                "warnings": []
              }
            ],
            "confidence_score": 0.019999999552965164,
            "html": "<html><body><table border=\"1\"><tbody><tr><td>STT</td><td>Nội dung</td><td>Giá trị<br>(Đồng)</td></tr><tr><td>01</td><td>Tổng giá trị phần công việc không áp dụng được một<br>trong các hình thức lựa chọn nhà thầu</td><td>0</td></tr><tr><td>02</td><td>Tổng giá trị phần công việc thuộc kế hoạch lựa chọn<br>nhà thầu</td><td>221.073.033</td></tr><tr><td colspan=\"2\">Tổng giá trị các phần công việc</td><td>221.073.033</td></tr><tr><td colspan=\"2\">Tổng mức đầu tư của dự án<br></td><td>221.073.033</td></tr></tbody></table></body></html>",
            "page_id": 1,
            "paragraph_id": 11,
            "text": "Not available, please check attribution 'html'",
            "type": "Table",
            "warnings": []
          }
        ]
      }
    ],
    "phrases": [
      {
        "bbox_conf_score": 1.0,
        "bboxes": {},
        "cells": [
          {
            "bbox_conf_score": 0.6917905807495117,
            "bboxes": {
              "1": [
                0.5243664717348928, 0.028097982708933718, 0.5380116959064327,
                0.04178674351585014
              ]
            },
            "confidence_score": 0.95,
            "font_styles": ["normal"],
            "line_id": 0,
            "page_id": 1,
            "paragraph_id": 0,
            "text": "3",
            "type": "Phrase",
            "warnings": []
          },
          {
            "bbox_conf_score": 0.7546406388282776,
            "bboxes": {
              "1": [
                0.17738791423001948, 0.0468299711815562, 0.6978557504873294,
                0.06195965417867435
              ]
            },
            "confidence_score": 0.9887800812721252,
            "font_styles": ["bold"],
            "line_id": 1,
            "page_id": 1,
            "paragraph_id": 0,
            "text": "2. Giải trình nội dung kế hoạch lựa chọn nhà thầu:",
            "type": "Phrase",
            "warnings": []
          }
        ],
        "confidence_score": 1.0,
        "type": "List",
        "warnings": []
      }
    ],
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.15",
  "status": "OK",
  "statusCode": 200
}
```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>Bảng mô tả kết quả trả về của các trường thông tin</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
<tr>
<td></td>
<td>link</td>
<td>Đường link</td>
<td>Đường link tải File văn bản dưới dạng được lựa chọn</td>
</tr>
</tbody></table>

**Bảng mô tả kết quả trả về của các trường thông tin**

<!-- start using html to help with <table> colspan and rolspan rendering -->
<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Thông tin</strong></th>
<th><strong>Ý nghĩa</strong></th>
</tr>
</thead>
<tbody><tr>
<td colspan="3"><strong>Đối với thông tin ngoài bảng</strong></td>
</tr>
<tr>
<td>1</td>
<td><strong>bboxes</strong></td>
<td>Tọa độ vị trí của trường thông tin bóc tách Tọa độ của trường thông tin bóc tách (là một dict ghi tọa độ tương đối của 4 đỉnh bounding box, cụ thể là X min, Y min, X max, Y max, hai số đầu là góc trên bên trái, hai số cuối là góc dưới bên phải)</td>
</tr>
<tr>
<td>2</td>
<td><strong>confidence_score</strong></td>
<td>Độ chính xác của trường bóc tách</td>
</tr>
<tr>
<td>3</td>
<td><strong>bbox_conf_score</strong></td>
<td>Độ tự tin của toạ độ vị trí vùng đáp án nhận diện</td>
</tr>
<tr>
<td>4</td>
<td><strong>warnings</strong></td>
<td>Thông tin cảnh báo</td>
</tr>
<tr>
<td>5</td>
<td><strong>text</strong></td>
<td>Kết quả bóc tách thông tin</td>
</tr>
<tr>
<td>6</td>
<td><strong>type</strong></td>
<td>Kiểu OCR (nhận 1 trong các giá trị Line, Phrase, Paragraph, Figure)</td>
</tr>
<tr>
<td>7</td>
<td><strong>font_styles</strong></td>
<td>Kiểu dáng của thông tin bóc tách</td>
</tr>
<tr>
<td>8</td>
<td><strong>paragraph_id</strong></td>
<td>Mã đoạn mà thông tin nằm trong</td>
</tr>
<tr>
<td>9</td>
<td><strong>page_id</strong></td>
<td>Trang mà thông tin nằm trong</td>
</tr>
<tr>
<td>10</td>
<td><strong>line_id</strong></td>
<td>Thứ tự dòng thông tin được OCR</td>
</tr>
<tr>
<td colspan="3"><strong>Đối với thông tin trong bảng (Tương tự các thông tin ngoài bảng, thêm các thông tin sau)</strong></td>
</tr>
<tr>
<td>9</td>
<td><strong>rows</strong></td>
<td>Các trường thông tin OCR theo hàng (từ trái qua phải) trong bảng. Khởi đầu bóc tách ô đầu tiên trong hàng bằng ký tự [, kết thúc ô cuối cùng trong hàng bằng ký tự ]. Hiển thị kết quả khi OCR thông tin hết các ô theo hàng trong bảng</td>
</tr>
<tr>
<td>10</td>
<td><strong>columns</strong></td>
<td>Các trường thông tin OCR theo cột (từ trái qua phải) trong bảng. Khởi đầu bóc tách ô đầu tiên trong cột bằng ký tự [, kết thúc ô cuối cùng trong cột bằng ký tự ]. Hiển thị kết quả khi OCR thông tin hết các ô theo các cột trong bảng</td>
</tr>
<tr>
<td>11</td>
<td><strong>cell</strong></td>
<td>Thể hiện khi OCR cụm từ trong các ô theo thứ tự từ trên xuống dưới, theo từng cột (từ trái qua phải) trong bảng</td>
</tr>
<tr>
<td>12</td>
<td><strong>html</strong></td>
<td>Cấu trúc bảng được nhận dạng và trả về dưới dạng html</td>
</tr>
<tr>
<td>13</td>
<td><strong>bboxes_html</strong></td>
<td>Vị trí các box trong HTML</td>
</tr>
</tbody></table>
<!-- end using html to help with <table> colspan and rolspan rendering -->

# API xử lý bất đồng bộ cho Số hoá văn bản cơ bản

## API lấy session ID cho văn bản số hoá cơ bản

**Chức năng:** Khởi tạo một phiên làm việc để xử lý OCR cho văn bản (cơ bản), trả về một session ID giúp theo dõi trạng thái và lấy kết quả của file đã gửi.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan`

**Method:** POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th>Tên trường</th>
<th>Giá trị</th>
<th>Bắt buộc</th>
<th>Max length</th>
<th>Mô tả</th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service auth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max Length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>exporter</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng kết quả xuất ra ('docx', 'xlsx', 'xlsx_nsheet', 'json')</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Mặc định <strong>details=true</strong>.</td>
</tr>
</tbody></table>

**Request Response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
<tr>
<td></td>
<td>session_id</td>
<td>string</td>
<td>Thông tin session của file đang xử lý OCR. Sử dụng để lấy kết quả OCR</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "file_hash": "idg20250113-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_df077630-d17d-11ef-a89d-354ad30ac97a",
  "file_type": "pdf",
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "details": true,
  "exporter": "json"
}
```

Response success

```json
{
  "status": "OK",
  "statusCode": 200,
  "message": "IDG-00000000",
  "server_version": "1.3.13",
  "object": {
    "warning": [],
    "warning_messages": [],
    "num_of_pages": 0,
    "session_id": "9619746d-8775-43d5-9301-005569a375ff"
  }
}
```

## API kiểm tra trạng thái xử lý OCR cơ bản theo session_id

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan/result`

**Chức năng:**  Nhận session ID được trả về từ API tại mục **trên**, kiểm tra trạng thái xử lý của session OCR (cơ bản) và trả về kết quả nếu quá trình đã hoàn tất.

**Method:** POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th>Tên trường</th>
<th>Giá trị</th>
<th>Bắt buộc</th>
<th>Max length</th>
<th>Mô tả</th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service auth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max Length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>session_id</td>
<td>string</td>
<td>x</td>
<td></td>
<td>Thông tin session của file đang xử lý OCR, dùng để theo dõi trạng thái xử lý của file</td>
</tr>
</tbody></table>

**Request Response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>warnings</td>
<td>List</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", “request_dang_trong_qua_trinh_xu_ly”, ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>List</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", “Request đang trong quá trình xử lý”, ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>String</td>
<td>Số trang của file đã thực hiện OCR</td>
</tr>
<tr>
<td></td>
<td>link</td>
<td>string</td>
<td>Đường link kết quả OCR</td>
</tr>
<tr>
<td></td>
<td>num_of_processed_page</td>
<td>String</td>
<td>Số trang đã xử lý OCR của file. Trả về khi chưa xử lý xong OCR</td>
</tr>
<tr>
<td></td>
<td>num_of_remaining_pages</td>
<td>String</td>
<td>Số trang còn lại chưa xử lý OCR của file. Trả về khi chưa xử lý xong OCR</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "session_id": "4af8c267-798f-404f-ac16-464d3b3c4ab9"
}
```

Response success **Trường hợp đã xử lý xong OCR:**

```json
{
  "status": "OK",
  "statusCode": 200,
  "message": "IDG-00000000",
  "server_version": "1.3.13",
  "object": {
    "warnings": [],
    "warning_messages": [],
    "num_of_pages": 1,
    "link": "https://storage-cic.vnpt.vn/rpa-media/exporter/sandbox/json/-1736754157.json?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=smartrpa%2F20250113%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250113T074237Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=7d8cd2557b41f72b5e2104c0bef8458bf8faff5d679a6254c470bd33b95b4958"
  }
}
```

Response success **Trường hợp chưa xử lý xong OCR:**

```json
{
  "status": "OK",
  "statusCode": 200,
  "message": "IDG-00000000",
  "server_version": "1.3.14",
  "object": {
    "warning": ["request_dang_trong_qua_trinh_xu_ly"],
    "warning_messages": ["Request đang trong quá trình xử lý."],
    "num_of_pages": 0,
    "num_of_processed_page": 18,
    "num_of_remaining_pages": 27
  }
}
```

## API huỷ yêu cầu xử lý bất đồng bộ OCR cơ bản

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan/cancel`

**Chức năng:**  Huỷ bỏ yêu cầu xử lý bất đồng bộ OCR cơ bản đang chạy được gọi từ trước đó qua API `/rpa-service/aidigdoc/v1/integration/ocr/scan/result`.

**Method:** POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th>Tên trường</th>
<th>Giá trị</th>
<th>Bắt buộc</th>
<th>Max length</th>
<th>Mô tả</th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service auth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

Request body:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max Length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>session_id</td>
<td>string</td>
<td>x</td>
<td></td>
<td>Thông tin session của file đang xử lý OCR, dùng để theo dõi trạng thái xử lý của file</td>
</tr>
</tbody></table>

**Request Response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>message</td>
<td>string</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>string</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>string</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>json object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>status</td>
<td>string</td>
<td>Trạng thái của yêu cầu huỷ xử lý OCR bất đồng bộ (“success”)</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "session_id": "4af8c267-798f-404f-ac16-464d3b3c4ab9"
}
```

Response success

```json
{
  "status": "OK",
  "statusCode": 200,
  "message": "IDG-00000000",
  "server_version": "1.3.14",
  "object": {
    "status": "success"
  }
}
```

# API xử lý bất đồng bộ cho Số hoá văn bản nâng cao

## API lấy session ID cho văn bản số hoá nâng cao

**Chức năng:**  Khởi tạo một phiên làm việc để xử lý OCR cho văn bản (nâng cao - xử lý đối với văn bản có bảng), trả về một session ID giúp theo dõi trạng thái và lấy kết quả của file đã gửi.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan-table`

**Method:** POST

**Request, Response & Example:** tương tự với **API lấy session ID cho văn bản số hoá cơ bản**

## API kiểm tra trạng thái xử lý OCR nâng cao theo session_id

**Chức năng:** Nhận session ID được trả về từ API tại mục **trên**, kiểm tra trạng thái xử lý của session OCR (nâng cao - xử lý đối với văn bản có bảng) và trả về kết quả nếu quá trình đã hoàn tất.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan-table/result`

**Method:** POST

**Request, Response & Example:** tương tự với **API xử lý kết quả bất đồng bộ OCR cơ bản**

## API huỷ yêu cầu xử lý bất đồng bộ OCR nâng cao

**Chức năng:**  Huỷ bỏ yêu cầu xử lý bất đồng bộ OCR (nâng cao - xử lý đối với văn bản có bảng) đang chạy được gọi từ trước đó qua API `/integration/ocr/scan-table/result`.

**Endpoint**: `<domain-name>/rpa-service/aidigdoc/v1/integration/ocr/scan-table/cancel`

**Method:** POST

**Request, Response & Example:** tương tự với **API huỷ yêu cầu xử lý bất đồng bộ OCR cơ bản**