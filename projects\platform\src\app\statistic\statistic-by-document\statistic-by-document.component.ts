import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { StatisticService } from '@platform/app/core/services/statistic.service';
import { TableConfig } from '../statistic.interface';
import { tap } from 'rxjs';
import { format, parseISO } from 'date-fns';

@Component({
  selector: 'app-statistic-by-document',
  templateUrl: './statistic-by-document.component.html',
  styleUrls: ['./statistic-by-document.component.scss']
})
export class StatisticByDocumentComponent implements OnChanges {
  @Input() dateFilter;

  userDocumentCount = [
    {
      key: 'owned',
      label: 'Văn bản của tôi',
      color: '#FFA100',
      value: 0
    },
    {
      key: 'shared',
      label: 'Văn bản được chia sẻ',
      color: '#0A84FF',
      value: 0
    }
  ];
  userDocumentStatByTime: {
    time: number | string;
    document: number;
  }[] = [];
  ownedDocumentStat: any = {};
  sharedDocumentStat: any = {};
  documentTypeFilter: 'SelfConfig' | 'SystemBased';

  constructor(private statisticService: StatisticService) {}

  ngOnChanges(changes: SimpleChanges): void {
    const dateFilterValue = changes['dateFilter']?.currentValue?.value!;

    this.setUserDocumentCount();
    this.setUserDocumentStatByTime();
    this.setOwnedDocumentStat();
    this.setSharedDocumentStat();
  }

  handleDocumentTypeFilterChanged() {
    this.setOwnedDocumentStat();
    this.setSharedDocumentStat();
  }

  setUserDocumentCount(): void {
    this.statisticService
      .fetchUserDocumentCount(this.dateFilter?.value)
      .pipe(
        tap((result) => {
          this.userDocumentCount = this.userDocumentCount.map((item) => {
            return {
              ...item,
              value: result.data[item.key]
            };
          });
        })
      )
      .subscribe();
  }

  setOwnedDocumentStat(): void {
    const tableConfig: TableConfig = {
      hasTotalRow: false,
      title: '',
      columns: [
        {
          title: 'Tên văn bản',
          index: 'name',
          routerLink: (_, row, _1) => {
            if (row['id'])
              return ['/', 'key-information-extractor', 'document', row['id']];
            return null;
          }
        },
        {
          title: 'Loại văn bản',
          index: 'isSelfConfig',
          render: (isSelfConfig) => {
            return isSelfConfig ? 'Tự tạo' : 'Hệ thống';
          }
        },
        {
          title: 'Thời gian tạo',
          index: 'createdAt',
          render: (createdAt: string) => {
            return format(parseISO(createdAt), 'hh:mm, dd/MM/yyyy');
          }
        },
        {
          title: 'Số lượng tài liệu trong văn bản',
          index: 'fileCount',
          canSum: true
        },
        {
          title: 'Số người được cấp quyền',
          index: 'sharedUserCount'
        }
      ],
      data: []
    };
    this.ownedDocumentStat = tableConfig;

    this.statisticService
      .fetchOwnedDocumentStat(this.dateFilter?.value, this.documentTypeFilter)
      .subscribe((result) => {
        tableConfig.data = result.data;
      });
  }

  setSharedDocumentStat(): void {
    const tableConfig: TableConfig = {
      hasTotalRow: false,
      title: '',
      columns: [
        {
          title: 'Tên văn bản',
          index: 'name',
          routerLink: (_, obj, _1) => {
            if (obj['id'])
              return ['/', 'key-information-extractor', 'document', obj['id']];
            return null;
          }
        },
        {
          title: 'Loại văn bản',
          index: 'isSelfConfig',
          render: (isSelfConfig) => {
            return isSelfConfig ? 'Tự tạo' : 'Hệ thống';
          }
        },
        {
          title: 'Thời gian được chia sẻ',
          index: 'sharedAt',
          render: (sharedAt: string) => {
            return format(parseISO(sharedAt), 'hh:mm, dd/MM/yyyy');
          }
        },
        {
          title: 'Số lượng tài liệu trong văn bản',
          index: 'fileCount',
          canSum: true
        },
        {
          title: 'Tài khoản khởi tạo văn bản',
          index: 'owner'
        },
        {
          title: 'Vai trò được chia sẻ của tôi',
          index: 'myRole',
          render: (myRole: string) => {
            const roleMap = {
              viewer: 'Người xem',
              editor: 'Người chỉnh sửa'
            };
            return roleMap[myRole] || 'N/A';
          }
        }
      ],
      data: []
    };
    this.sharedDocumentStat = tableConfig;

    this.statisticService
      .fetchSharedDocumentStat(this.dateFilter?.value, this.documentTypeFilter)
      .subscribe((result) => {
        tableConfig.data = result.data;
      });
  }

  setUserDocumentStatByTime(): void {
    this.statisticService.fetchUserDocumentStatByTime(this.dateFilter?.value).subscribe(
      (result) =>
        (this.userDocumentStatByTime = result.data.map((item) => {
          return {
            document: Object.values(item.document).reduce((acc, curr) => acc + curr, 0),
            time: item.time
          };
        }))
    );
  }
}
