import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-move-document',
  templateUrl: './modal-move-document.component.html',
  styleUrls: ['./modal-move-document.component.scss']
})
export class ModalMoveDocumentComponent implements OnInit {
  readonly nzModalData: {
    document: Folder['documents'][number];
    folders: { name: string; id: string }[];
  } = inject(NZ_MODAL_DATA);
  document = this.nzModalData.document;
  folders = this.nzModalData.folders;
  selectedFolderId: string;

  constructor(
    private modalRef: NzModalRef,
    private toastr: ToastrService,
    private kieService: KIEService
  ) {}

  ngOnInit(): void {}

  cancel() {
    this.modalRef.close();
  }

  save() {
    if (!this.selectedFolderId) return this.toastr.error('Thư mục không được để trống');
    this.kieService
      .updateDocumentDetail(this.document.id, {
        folderId: this.selectedFolderId
      })
      .pipe(
        tap(() => {
          this.toastr.success('Chuyển thư mục thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
