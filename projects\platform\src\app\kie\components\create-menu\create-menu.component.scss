:host ::ng-deep {
  .ant-dropdown-menu {
    overflow: hidden;
    padding: 12px 0;
    border-radius: 16px;
    border: #e7ebef;
    background: #fff;
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.15);

    .ant-dropdown-menu-submenu-title:hover {
      background: #e6f1fe;
    }

    .ant-dropdown-menu-item {
      border-left: 3px solid transparent;
      /* Transparent border by default */
      transition: border-color 0.2s;

      &:hover {
        background-color: #e6f1fe;
        border-left-color: #0a84ff;
      }
    }
  }

  .ant-dropdown-menu-submenu-title {
    padding-left: 0px;
  }

  .ant-dropdown-menu-submenu-arrow-icon {
    display: inline-flex;
  }
}

::ng-deep {
  .modal-menu-create-common {
    .ant-modal-content {
      border-radius: 16px;
    }

    .ant-modal-header {
      border-radius: 16px 16px 0 0;
    }

    .ant-modal-title {
      transform: translateY(4px);
      font-size: 14px;
      font-weight: 600;
    }
  }
}