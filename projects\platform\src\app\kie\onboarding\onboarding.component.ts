import { Component, OnDestroy, OnInit } from '@angular/core';
import { ModalCreateDocumentComponent } from '@platform/app/kie/components/modal-create-document/modal-create-document.component';
import { ModalCreateFolderComponent } from '@platform/app/kie/components/modal-create-folder/modal-create-folder.component';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { Subject, takeUntil, tap } from 'rxjs';
import { ModalCreateSampleDocumentComponent } from '../components/modal-create-sample-document/modal-create-sample-document.component';

@Component({
  selector: 'app-onboarding',
  templateUrl: './onboarding.component.html',
  styleUrls: ['./onboarding.component.scss']
})
export class OnboardingComponent implements OnInit, OnDestroy {
  listFolders: Folder[] = [];

  constructor(
    private modal: NzModalService,
    private documentLayoutService: DocumentLayoutService
  ) {}

  destroyed$ = new Subject<void>();

  ngOnInit(): void {
    this.documentLayoutService.selectedDocumentId$.next(null);
    this.documentLayoutService.store$
      .pipe(
        takeUntil(this.destroyed$), // equal to manually unsubscribe() in ngOnDestroy(), prevent memory leak
        tap(({ folders }) => {
          this.listFolders = folders;
        })
      )
      .subscribe();
  }

  showModalCreateFolder(): void {
    this.modal.create({
      nzTitle: 'Thư mục mới',
      nzContent: ModalCreateFolderComponent,
      nzData: { listFolders: this.listFolders },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common'
    });
  }

  showModalCreateDocument(): void {
    this.modal.create({
      nzTitle: 'Tạo mới văn bản',
      nzData: { listFolders: this.listFolders },
      nzContent: ModalCreateDocumentComponent,
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common'
    });
  }

  showModalCreateSampleDocument(): void {
    this.modal.create({
      nzContent: ModalCreateSampleDocumentComponent,
      nzData: { listFolders: this.listFolders },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '500px' }
    });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
