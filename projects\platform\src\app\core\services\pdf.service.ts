import { Injectable, OnDestroy } from '@angular/core';
import * as pdfjsDist from 'pdfjs-dist';
import { PageSizes, PDFDocument } from 'pdf-lib';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';
import Rotator from 'exif-auto-rotate';

@Injectable({
  providedIn: 'root'
})
export class PdfService implements OnDestroy {
  pdfjsDist: typeof pdfjsDist;

  constructor() {
    pdfjsDist.GlobalWorkerOptions.workerSrc = `assets/lib/pdfjs-dist@${pdfjsDist.version}.worker.mjs`;
    this.pdfjsDist = pdfjsDist;
  }

  async createModifiedPdfFile({
    originalPdfLink,
    pageIndexSelectionList,
    editedPageLinkObj
  }: {
    originalPdfLink: string;
    pageIndexSelectionList?: number[];
    editedPageLinkObj: { [pageIndex: number]: string };
  }) {
    new URL(originalPdfLink);
    if (
      (!pageIndexSelectionList || pageIndexSelectionList.length === 0) &&
      Object.values(editedPageLinkObj || {}).length === 0
    ) {
      return originalPdfLink;
    }

    const originalPdfArrayBuffer = await (await fetch(originalPdfLink)).arrayBuffer();

    /* using pdflib method, faster, smaller footprint and truest to the original pdf */
    try {
      const sourceDoc = await PDFDocument.load(originalPdfArrayBuffer);
      const numPages = sourceDoc.getPageCount();

      const modifiedPdfDoc = await PDFDocument.create();
      for (let pageIndex = 0; pageIndex < numPages; pageIndex++) {
        /* check pageIndexSelectionList is presented and it does NOT include current page index */
        if (!!pageIndexSelectionList && !pageIndexSelectionList.includes(pageIndex))
          continue;

        if (!!editedPageLinkObj && editedPageLinkObj[pageIndex]) {
          const editedPageImageLink = editedPageLinkObj[pageIndex];
          const editedPageImageBytes = await fetch(editedPageImageLink).then((res) =>
            res.arrayBuffer()
          );
          const pngImage = await modifiedPdfDoc.embedPng(editedPageImageBytes);
          const pngDims = pngImage.scaleToFit(...PageSizes.A4); // jpgImage.scale(1);
          const page = modifiedPdfDoc.addPage([pngDims.width, pngDims.height]);
          const drawImageOptions = {
            x: 0,
            y: 0,
            width: pngDims.width,
            height: pngDims.height
          };
          page.drawImage(pngImage, drawImageOptions);
        } else
          modifiedPdfDoc.addPage(
            ...(await modifiedPdfDoc.copyPages(sourceDoc, [pageIndex]))
          );
      }

      const pdfBlob = new Blob([await modifiedPdfDoc.save()], {
        type: 'application/pdf'
      });
      return URL.createObjectURL(pdfBlob);
    } catch (error) {
      console.log('pdflib method failed', error);
    }

    console.warn('pdflib method had failed, trying pdfjs-dist method');

    /* pdfjs-dist method, fallback approach when above method had failed, slower and bigger output file, since each page is rendered to a png */
    try {
      const sourceDoc = await this.pdfjsDist.getDocument({
        // url: new URL(originalPdfLink)
        data: originalPdfArrayBuffer,
        isOffscreenCanvasSupported: true
        // cMapUrl: 'node_modules/pdfjs-dist/cmaps',
        // cMapPacked: true,
        // standardFontDataUrl: 'node_modules/pdfjs-dist/standard_fonts/'
        // canvasFactory,
      }).promise;
      const numPages = sourceDoc.numPages;
      const modifiedPdfDoc = await PDFDocument.create();
      const canvasFactory = new NodeCanvasFactory(); // create each canvas and its context for every page
      for (let pageIndex = 0; pageIndex < numPages; pageIndex++) {
        /* check pageIndexSelectionList is presented and it does NOT include current page index */
        if (!!pageIndexSelectionList && !pageIndexSelectionList.includes(pageIndex))
          continue;

        let pageArrayBuffer: ArrayBuffer;
        if (!!editedPageLinkObj && editedPageLinkObj[pageIndex]) {
          const editedPageImageLink = editedPageLinkObj[pageIndex];
          pageArrayBuffer = await fetch(editedPageImageLink).then((res) =>
            res.arrayBuffer()
          );
        } else {
          const page = await sourceDoc.getPage(pageIndex + 1);
          const viewport = page.getViewport({ scale: 1.0 });
          const renderScale = 1.5;

          const canvasAndContext = canvasFactory.create(
            viewport.width * renderScale,
            viewport.height * renderScale
          );
          const renderContext = {
            canvasContext: canvasAndContext.context,
            viewport: viewport.clone({ scale: renderScale })
          };

          await page.render(renderContext as any).promise;

          const pageBlob: Blob = await new Promise((resolve) => {
            canvasAndContext.canvas?.['toBlob'](resolve, 1);
          });
          pageArrayBuffer = await pageBlob.arrayBuffer();

          // Release page resources.
          canvasFactory.destroy(canvasAndContext);
          page.cleanup();
        }

        const pngImage = await modifiedPdfDoc.embedPng(pageArrayBuffer);
        const pngDims = pngImage.scaleToFit(...PageSizes.A4);
        const addedPage = modifiedPdfDoc.addPage([pngDims.width, pngDims.height]);
        const drawImageOptions = {
          x: 0,
          y: 0,
          width: pngDims.width,
          height: pngDims.height
        };
        addedPage.drawImage(pngImage, drawImageOptions);
      }

      const pdfBlob = new Blob([await modifiedPdfDoc.save()], {
        type: 'application/pdf'
      });
      return URL.createObjectURL(pdfBlob);
    } catch (error) {
      console.log('pdfjs-dist method failed', error);
    }

    throw new Error(
      'Both methods used to create modified pdf failed. Consider reparing the pdf first (https://www.ilovepdf.com/repair-pdf)'
    );
  }

  async createPdfFileFromImageList(imageList: File[], pdfName: string) {
    const pdfDoc = await PDFDocument.create();
    for (const imageFile of imageList) {
      let embeddedImage;
      try {
        switch (imageFile.type) {
          case 'image/jpeg': {
            let imageBytes;
            try {
              const rotatedJpegImage = new File(
                [await Rotator.createRotatedImageAsync(imageFile, 'blob')], // jpeg image with rotation metadata will not be taken into account in pdf-lib creation process => must rotate the jpeg first
                imageFile.name,
                { type: imageFile.type }
              );
              imageBytes = await rotatedJpegImage.arrayBuffer();
            } catch (error) {
              if (error === 'Image is NOT have a exif code') {
                console.warn(`${imageFile.name} - ${imageFile.type} - ${error}`);
                /* if there is no exif about rotation in jpeg, use the jpeg as it is */
                imageBytes = await imageFile.arrayBuffer();
              } else throw error;
            }

            embeddedImage = await pdfDoc.embedJpg(imageBytes);
            break;
          }
          case 'image/png': {
            const imageBytes = await imageFile.arrayBuffer();
            embeddedImage = await pdfDoc.embedPng(imageBytes);
            break;
          }
          default:
            throw new Error(
              `Invalid file type in imageList, allowed types are image/jpeg, image/png`
            );
        }
      } catch (err) {
        throw new Error(
          `createPdfFileFromImageList failed:\n${imageFile.name} - ${imageFile.type}\n${err}`
        );
      }
      const pngDims = embeddedImage.scaleToFit(...PageSizes.A4); // jpgImage.scale(1);
      const page = pdfDoc.addPage([pngDims.width, pngDims.height]);
      const drawImageOptions = {
        x: 0,
        y: 0,
        width: pngDims.width,
        height: pngDims.height
      };
      page.drawImage(embeddedImage, drawImageOptions);
    }
    return new File([await pdfDoc.save()], pdfName + '.pdf', {
      type: 'application/pdf'
    });
  }

  ngOnDestroy(): void {}
}
