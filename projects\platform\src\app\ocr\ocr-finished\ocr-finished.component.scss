:host {
	display: flex;
	flex-direction: column;
	gap: 16px;
	height: 100%;
	position: relative; // for spinner to work

	::ng-deep {
		.ant-select .ant-select-selector {
			border-radius: 8px;
		}

		nz-pagination {
			ul {
				display: flex;

				li.ant-pagination-total-text {
					flex: 1;
				}
			}

		}

		.ant-table-tbody>tr {
			&:nth-child(odd) {
				background-color: #ffffff;
				/* Light grey for odd rows */
			}

			&:nth-child(even) {
				background-color: #f0f1f4;
				/* White for even rows */
			}
		}

		.ant-table-tbody {
			tr>td {
				padding: 12px;
				border-top: 1px solid transparent;
				border-bottom: 1px solid transparent;

				&:first-child {
					border-left: 1px solid transparent;
				}

				&:last-child {
					border-right: 1px solid transparent;
				}
			}

			tr.active>td {
				background-color: #E6F1FE;
				border-top: 1px solid #0667e1;
				border-bottom: 1px solid #0667e1;

				&:first-child {
					border-left: 1px solid #0667e1;
					border-top-left-radius: 8px;
					border-bottom-left-radius: 8px;
				}

				&:last-child {
					border-right: 1px solid #0667e1;
					border-top-right-radius: 8px;
					border-bottom-right-radius: 8px;
				}

			}
		}

		.ant-table-thead {
			.ant-table-cell {
				padding: 12px;
				border-bottom: 1px solid #e2e2e9;
				font-size: 14px;
				font-weight: 600;
				color: #2b2d3b;
			}
		}

		.ant-pagination-item {
			border-radius: 8px;

			&:hover {
				a {
					color: #ffffff;
				}

				border-color: #0667e1;
				background-color: #0667e1;
			}
		}

		.btn-dot {
			display: block;
			min-width: 32px;
			height: 32px;
			text-align: center;
			list-style: none;
			background-color: #fff;
			border: 1px solid #d9d9d9;
			border-radius: 8px;
			outline: 0;
			cursor: pointer;

			&:hover {
				background-color: #0667e1;
				color: #ffffff;
			}
		}

		td,
		nz-table-selection {
			>label.ant-checkbox-wrapper>span.ant-checkbox>span.ant-checkbox-inner {
				border: 1px solid #0667e1;
			}
		}
	}
}