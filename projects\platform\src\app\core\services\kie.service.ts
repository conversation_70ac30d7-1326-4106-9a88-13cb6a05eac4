import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  Document,
  File as KIEFile,
  FileUploaded,
  Folder,
  ListFile,
  ListSharedDocuments,
  FileStatus,
  OcrModel,
  Response,
  TemplateConfigFieldModel,
  ParamQueryListFile,
  ParamQueryListSharedDocument,
  ValueTypeField,
  ValueTypeList,
  ValueTypeTable,
  ListDocumentPermission
} from '@platform/app/kie/kie';
import { environment as env } from '@platform/environment/environment';
import { forkJoin, from, map, of, switchMap, tap } from 'rxjs';
import UtilsService from './utils.service';

@Injectable({
  providedIn: 'root'
})
export class KIEService {
  private baseUrl = env.backendUrl + 'key-information-extractor';
  private fileHashCache = new Map();

  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  getListFolders(paramQuery?: HttpParams | any) {
    return this.http.get<
      Response<{
        folders: Folder[];
        sharedDocumentsCount: number;
        total: number;
        page: number;
        limit: number;
      }>
    >(`${this.baseUrl}/folders`, {
      headers: this.utilsService.headers,
      params: paramQuery
    });
  }

  createNewFolder(nameFolder: string) {
    return this.http.post<Response<Folder>>(
      `${this.baseUrl}/folders`,
      { name: nameFolder },
      { headers: this.utilsService.headers }
    );
  }

  createNewSystemDocument(payload: {
    name: string;
    folderId: string;
    systemTemplateName: string;
  }) {
    return this.http.post<Response<string>>(`${this.baseUrl}/documents/system`, payload, {
      headers: this.utilsService.headers
    });
  }

  createNewSelfDocument(payload: {
    name: string;
    folderId: string;
    templateOcrModel: 'location_only' | 'default';
    templateFile: File;
  }) {
    const formData = new FormData();
    formData.append('name', payload.name);
    formData.append('folderId', payload.folderId);
    formData.append('templateOcrModel', payload.templateOcrModel);
    formData.append('templateFile', payload.templateFile);

    return this.http.post<Response<string>>(
      `${this.baseUrl}/documents/config`,
      formData,
      { headers: this.utilsService.headers }
    );
  }

  inviteMember(documentId: string, payload: { role: string; assigneeIds: string[] }) {
    return this.http.post<{ status: number; message: string }>(
      `${this.baseUrl}/documents/${documentId}/permission`,
      payload,
      { headers: this.utilsService.headers }
    );
  }

  uploadFile(documentId: string, files: File[]) {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append('ocrFiles', file);
    });

    return this.http.post<Response<FileUploaded[]>>(
      `${this.baseUrl}/documents/${documentId}/files`,
      formData,
      { headers: this.utilsService.headers }
    );
  }

  getListFileByDocumentId(id: string, paramQuery) {
    return this.http.get<Response<ListFile>>(`${this.baseUrl}/documents/${id}/files`, {
      headers: this.utilsService.headers,
      params: paramQuery
    });
  }

  getListSharedDocuments(paramQuery: HttpParams) {
    return this.http.get<Response<ListSharedDocuments>>(`${this.baseUrl}/documents/`, {
      headers: this.utilsService.headers,
      params: paramQuery
    });
  }

  getFileDetail(fileId: string) {
    return this.http.get<{ status: number; data: KIEFile }>(
      `${this.baseUrl}/files/${fileId}`,
      { headers: this.utilsService.headers }
    );
  }

  getDocumentDetail(documentId) {
    return this.http.get<{ status: number; data: Document }>(
      `${this.baseUrl}/documents/${documentId}`,
      {
        headers: new HttpHeaders({
          Authorization: `bearer ${this.utilsService.getAccessToken()}`
        })
      }
    );
  }

  getHashFromFileLink(link) {
    return this.utilsService.fetchFile(link).pipe(
      switchMap((blob) => {
        let extension = '.';
        switch (blob.type) {
          case 'application/pdf':
            extension += 'pdf';
            break;
          case 'image/png':
            extension += 'png';
            break;
          case 'image/jpeg':
            extension += 'jpeg';
            break;
          default:
            extension = '';
            break;
        }
        return forkJoin({
          arrayBuffer: from(blob.arrayBuffer()),
          extension: of(extension)
        });
      }),
      switchMap(({ arrayBuffer, extension }) => {
        const formData = new FormData();
        formData.append('file', new File([arrayBuffer], `fileName${extension}`));
        formData.append('title', 'fileName');
        formData.append('description', 'fileName');
        return this.http.post<any>(
          `${env.backendUrl}idg-api/file-service/addFile`,
          formData,
          {
            headers: new HttpHeaders({
              Authorization: `bearer ${this.utilsService.getAccessToken()}`
            })
          }
        );
      }),
      map((addFileRes) => addFileRes['object'])
    );
  }

  ocrWithHash(body: {
    file_hash: string;
    detail: boolean;
    file_type: string;
    template_id: string;
  }) {
    return this.http.post(
      `${env.backendUrl}idg-api/template/ocr`,
      {
        // token: '',
        file_type: body.file_type,
        file_hash: body.file_hash,
        template_id: body.template_id,
        details: body.detail
      },
      {
        headers: new HttpHeaders({
          Authorization: `bearer ${this.utilsService.getAccessToken()}`
        })
      }
    );
  }

  ocrWithFileLink(ocrBody: { fileLink: string; detail: boolean; template_id: string }) {
    /* fileLink is presigned => same file but different fileLink => use URL().pathname as cache key */
    const fileHashCacheKey = new URL(ocrBody.fileLink).pathname;
    const isCached = this.fileHashCache.has(fileHashCacheKey);

    return (
      isCached
        ? of(this.fileHashCache.get(fileHashCacheKey))
        : this.getHashFromFileLink(ocrBody.fileLink)
    ).pipe(
      tap(
        (addFileResult) =>
          !isCached && this.fileHashCache.set(fileHashCacheKey, addFileResult)
      ),
      switchMap((addFileResult) => {
        return this.ocrWithHash({
          file_hash: addFileResult.hash,
          file_type: addFileResult.fileType,
          detail: ocrBody.detail,
          template_id: ocrBody.template_id
        });
      }),
      map((result) => result['object'])
    );
  }

  updateDocumentTemplateConfig(
    documentId,
    templateBody: {
      templateConfigOcrModel: OcrModel;
      templateConfigFields: {
        name: string;
        extraConfig: {
          color: string;
          is_visible: boolean;
          order: number;
        };
        location: {
          pageNumber: number;
          xMin: number;
          yMin: number;
          xMax: number;
          yMax: number;
        };
        model: TemplateConfigFieldModel;
        is_date?: boolean;
        is_number?: boolean;
        is_title_or_uppercase?: boolean;
        is_visible?: boolean;
        multilines?: boolean;
        prewords?: string[];
        sufwords?: string[];
      }[];
    }
  ) {
    return this.http.patch(
      `${this.baseUrl}/documents/${documentId}/config`,
      templateBody,
      { headers: this.utilsService.headers }
    );
  }

  updateDocumentDetail(
    documentId,
    body: { name?: string; folderId?: string; extraConfig?: any }
  ) {
    return this.http.patch(`${this.baseUrl}/documents/${documentId}`, body, {
      headers: this.utilsService.headers
    });
  }

  updateFileOcrResult(
    fileId: string,
    fields: {
      name: string;
      value: ValueTypeField | ValueTypeList | ValueTypeTable;
    }[]
  ) {
    return this.http.patch(
      `${this.baseUrl}/files/${fileId}/ocr-result`,
      { fields },
      { headers: this.utilsService.headers }
    );
  }

  updateFileStatus(fileId: string, status: FileStatus) {
    return this.http.patch(
      `${this.baseUrl}/files/${fileId}/status`,
      { status },
      { headers: this.utilsService.headers }
    );
  }

  getDocumentPermissionList(documentId: string, query) {
    return this.http.get<Response<ListDocumentPermission>>(
      `${this.baseUrl}/documents/${documentId}/permission`,
      {
        headers: this.utilsService.headers,
        params: query
      }
    );
  }

  updateFileDetail(fileId: string, updates: { name?: string }) {
    return this.http.patch(`${this.baseUrl}/files/${fileId}`, updates, {
      headers: this.utilsService.headers
    });
  }

  deleteFile(fileId: string) {
    return this.http.delete(`${this.baseUrl}/files/${fileId}`, {
      headers: this.utilsService.headers
    });
  }

  deleteDocument(documentId: string) {
    return this.http.delete(`${this.baseUrl}/documents/${documentId}`, {
      headers: this.utilsService.headers
    });
  }

  updateFolderDetail(folderId: string, body: { name?: string }) {
    return this.http.patch(`${this.baseUrl}/folders/${folderId}`, body, {
      headers: this.utilsService.headers
    });
  }

  deleteFolder(folderId: string) {
    return this.http.delete(`${this.baseUrl}/folders/${folderId}`, {
      headers: this.utilsService.headers
    });
  }

  deleteUsersFromDocument(documentId: string, assigneeIds: string[]) {
    return this.http.delete(`${this.baseUrl}/documents/${documentId}/permission`, {
      headers: this.utilsService.headers,
      body: { assigneeIds }
    });
  }

  updateDocumentTemplateFile(documentId: string, file: File) {
    const body = new FormData();
    body.append('templateFile', file);
    return this.http.patch(
      `${this.baseUrl}/documents/${documentId}/template-file`,
      body,
      { headers: this.utilsService.headers }
    );
  }
}
