import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FilterOption } from '../../statistic.interface';
import { CustomDatePickerComponent } from '../custom-date-picker/custom-date-picker.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { take, tap } from 'rxjs';
import { format, subDays, subYears } from 'date-fns';

@Component({
  selector: 'app-date-filter',
  templateUrl: './date-filter.component.html',
  styleUrls: ['./date-filter.component.scss']
})
export class DateFilterComponent implements OnInit {
  filterOptions: FilterOption[] = [];
  @Input() selectedOption?: FilterOption;
  @Output() selectedOptionChange = new EventEmitter<FilterOption>();

  constructor(private modal: NzModalService) {
    const formatDate = { title: 'dd/MM/yyyy', value: 'yyyy-MM-dd' };
    const formatMonth = { title: 'MM/yyyy', value: 'yyyy-MM' };
    const formatYear = { title: 'yyyy', value: 'yyyy' };

    const options: FilterOption[] = [];
    const today = new Date();

    options.push({
      title: `Hôm nay`,
      value: format(today, formatDate.value),
      xAxisTitleForChart: 'Giờ'
    });
    options.push({
      title: `Hôm qua`,
      value: format(subDays(today, 1), formatDate.value),
      xAxisTitleForChart: 'Giờ'
    });
    options.push({
      title: `Tháng này`,
      value: format(today, formatMonth.value),
      xAxisTitleForChart: 'Ngày'
    });
    options.push({
      title: `${format(today, formatYear.title)}`,
      value: format(today, formatYear.value),
      xAxisTitleForChart: 'Tháng'
    });
    options.push({
      title: `${format(subYears(today, 1), formatYear.title)}`,
      value: format(subYears(today, 1), formatYear.value),
      xAxisTitleForChart: 'Tháng'
    });
    options.push({
      title: 'Tùy chọn',
      value: 'custom',
      xAxisTitleForChart: 'Đơn vị thời gian'
    });

    this.filterOptions = options;
  }

  ngOnInit(): void {
    if (!this.selectedOption) {
      this.setSelectedOption(this.filterOptions[0]);
    }
  }

  setSelectedOption(option: FilterOption): void {
    if (option.value !== 'custom') {
      this.selectedOption = option;
      this.selectedOptionChange.emit(this.selectedOption);
    } else {
      const modalRef = this.modal.create({
        nzContent: CustomDatePickerComponent,
        nzMaskClosable: false,
        nzStyle: { width: 'fit-content' },
        nzFooter: null
      });

      modalRef.afterClose
        .pipe(
          take(1),
          tap((result) => {
            if (!result) return;
            this.selectedOption = result;
            this.selectedOptionChange.emit(this.selectedOption);
          })
        )
        .subscribe();
    }
  }
}
