import { Component, OnInit, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { SubscriptionService } from '@platform/app/core/services/subscription.service';

@Component({
  selector: 'app-statistic',
  templateUrl: './statistic.component.html',
  styleUrls: ['./statistic.component.scss']
})
export class StatisticComponent implements OnInit, AfterViewInit {
  dateFilter: { title: string; value: string };

  constructor(
    private subscriptionService: SubscriptionService,
    private cd: ChangeDetectorRef
  ) {}
  subscription = '';

  ngAfterViewInit(): void {
    this.cd.detectChanges();
  }

  ngOnInit(): void {
    this.subscriptionService
      .fetchSubscriptionStatus()
      .subscribe((result) => (this.subscription = result.object?.planName));
  }
}
