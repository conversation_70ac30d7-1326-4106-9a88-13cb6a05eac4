import { Component, Input, OnInit } from '@angular/core';
import { UserService } from '@platform/app/core/services/user.service';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError, take, tap, timer } from 'rxjs';

@Component({
  selector: 'app-revoke-consent-modal',
  templateUrl: './revoke-consent-modal.component.html',
  styleUrls: ['./revoke-consent-modal.component.scss'],
})
export class RevokeConsentModalComponent implements OnInit {
  confirmed = false;
  @Input()
  step: 1 | 2 | 3 = 1;
  account;
  constructor(
    public modal: NzModalRef,
    private toastr: ToastrService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    if (this.step === 3)
      timer(3000, 1000)
        .pipe(
          take(1),
          tap(() => this.modal.close())
        )
        .subscribe();
    else
      this.userService
        .getIdgAccount()
        .pipe(
          tap((result) => {
            this.account = result.object;
          }),
          catchError(() => {
            this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
            this.modal.close();
            return EMPTY;
          })
        )
        .subscribe();
  }

  confirmRevokeConsent() {
    if (this.step === 3) return;
    this.userService
      .createDenyServicePermission()
      .pipe(
        tap(() => {
          this.step = 2;
        }),
        catchError(() => {
          this.toastr.error(
            'Hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu thất bại'
          );
          return EMPTY;
        })
      )
      .subscribe();
  }

  close() {
    this.modal.close();
  }
}
