import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { UserService } from '@platform/app/core/services/user.service';
import { NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-user-info-modal',
  templateUrl: './user-info-modal.component.html',
  styleUrls: ['./user-info-modal.component.scss'],
})
export class UserInfoModalComponent implements OnInit {
  isEditing = false;
  account: { fullName: string; phoneNumber: string };
  accountForm: UntypedFormGroup;

  constructor(
    public modal: NzModalRef,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.accountForm = new UntypedFormGroup({
      fullName: new UntypedFormControl('', Validators.required),
      phoneNumber: new UntypedFormControl('', [
        Validators.required,
        Validators.pattern('^((\\+91-?)|0)?[0-9]{10}$'),
      ]),
    });
    this.userService.getIdgAccount().subscribe((result) => {
      this.account = result.object;
      this.accountForm.setValue({
        fullName: this.account.fullName,
        phoneNumber: this.account.phoneNumber,
      });
    });
  }

  handleSubmit() {
    if (!this.accountForm.valid) return;
    this.userService
      .updateIdgAccount(this.accountForm.value)
      .subscribe((result) => {
        // this.modal.close();
        this.modal.destroy();
      });
  }

  cancelEditing() {
    this.isEditing = false;
    this.accountForm.setValue({
      fullName: this.account.fullName,
      phoneNumber: this.account.phoneNumber,
    });
  }
}
