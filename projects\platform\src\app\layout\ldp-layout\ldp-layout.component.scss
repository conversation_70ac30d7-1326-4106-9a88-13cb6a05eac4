div.menu {
	margin: 0;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: end;

	div {
		margin: 0 4px;

		&.active>a {
			color: #2140D2;
			font-weight: 700;
		}

		>a,
		button {
			color: #666C8A;
			padding: 16px;
			font-size: 16px;
			transition: all 0.3s ease-out 0s;
			text-decoration: none;
			background-color: transparent;

			&:hover {
				color: #2140D2;
			}
		}
	}
}

.btn-register {
	padding: 12px 20px !important;
	border-radius: 10px;
	font-weight: 600;
	text-transform: uppercase !important;
	letter-spacing: 1px;
	color: #0F67CE !important;
	border: 1px solid #0F67CE;
	font-size: 14px;

	&:hover {
		background-color: #0F67CE !important;
		transition: 0.5s !important;
		color: #FFFFFF !important;
	}
}

.btn-login {
	color: #2140D2 !important;
	background: #E9ECFB !important;
	padding: 10px 15px !important;
	margin: 0 36px 0 60px;
	border-radius: 6px;
	font-weight: 700;
	letter-spacing: 1px;
	font-size: 14px !important;

	&:hover {
		color: #fff !important;
		background: #2140D2 !important;
	}
}

footer {
	background-color: #273266;
	color: #FFFFFF;
	font-size: 14px;
}

// no option to set responsive width for ngx-recaptcha2
// hacky solution: scaling up to fit
// ::ng-deep ngx-recaptcha2>div {
// 	transform: scale(1.195);
// 	transform-origin: 0 0;
// 	padding-bottom: 1rem;
// }

.container {
	margin: 0 auto;
	padding: 0 16px;

	// @media (min-width: 1536px) {
	// 	padding: 0
	// }
}