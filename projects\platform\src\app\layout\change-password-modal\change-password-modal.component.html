<h5 class="text-xl font-bold"><PERSON><PERSON><PERSON> mật khẩu</h5>

<form class="mt-5" nz-form [formGroup]="changePasswordForm" (ngSubmit)="handleSubmit()">
	<nz-form-item>
		<nz-form-label [nzSpan]="10" nzNoColon nzRequired nzFor="oldPassword">M<PERSON><PERSON> kh<PERSON>u hiện tại</nz-form-label>
		<nz-form-control [nzSpan]="14" nzErrorTip="Mật khẩu hiện tại không hợp lệ ">
			<nz-input-group [nzSuffix]="showOldPasswordBtn">
				<input [type]="showOldPassword ? 'text': 'password'" nz-input formControlName="oldPassword" id="oldPassword" />
				<ng-template #showOldPasswordBtn>
					<img (click)="showOldPassword = !showOldPassword" src="assets/img/rpa/eye.svg" alt="eye"
						class="cursor-pointer">
				</ng-template>
			</nz-input-group>
		</nz-form-control>
	</nz-form-item>

	<nz-form-item>
		<nz-form-label [nzSpan]="10" nzNoColon nzRequired nzFor="password">Mật khẩu mới</nz-form-label>
		<nz-form-control [nzSpan]="14" [nzErrorTip]="errorTpl">
			<nz-input-group [nzSuffix]="showPasswordBtn">
				<input [type]="showPassword ? 'text': 'password'" nz-input formControlName="password" id="password" />
				<ng-template #showPasswordBtn>
					<img (click)="showPassword = !showPassword" src="assets/img/rpa/eye.svg" alt="eye" class="cursor-pointer">
				</ng-template>
			</nz-input-group>
			<ng-template #errorTpl let-control>
				<ng-container
					*ngIf="control.hasError('required')  || control.hasError('pattern') || control.hasError('minlength')">
					Mật khẩu mới tối thiểu 8 ký tự, có ít nhất một số, một chữ hoa và một ký tự đặc biệt.
				</ng-container>
				<ng-container *ngIf="control.hasError('newPasswordSameAsOldPassword')">
					Không được trùng với mật khẩu cũ.
				</ng-container>
			</ng-template>
		</nz-form-control>
	</nz-form-item>

	<nz-form-item>
		<nz-form-label [nzSpan]="10" nzNoColon nzRequired nzFor="passwordConfirm">
			Nhập lại mật khẩu mới
		</nz-form-label>
		<nz-form-control [nzSpan]="14" nzErrorTip="Mật khẩu mới nhập lại không hợp lệ">
			<nz-input-group [nzSuffix]="showConfirmPasswordBtn">
				<input [type]="showConfirmPassword ? 'text': 'password'" nz-input formControlName="passwordConfirm"
					id="passwordConfirm" />
				<ng-template #showConfirmPasswordBtn>
					<img (click)="showConfirmPassword = !showConfirmPassword" src="assets/img/rpa/eye.svg" alt="eye" class="cursor-pointer">
				</ng-template>
			</nz-input-group>
		</nz-form-control>
	</nz-form-item>


	<div class="flex justify-end gap-3 col-span-full">
		<button type="button" (click)="modal.close()" class="bg-icon-2 text-white p-2 rounded-[4px] font-semibold uppercase"
			style="font-weight: 600;">HỦY</button>
		<button type="submit" [ngClass]="{ 'opacity-50 cursor-not-allowed': !this.changePasswordForm.valid }"
			class="bg-brand-1 text-white p-2 rounded-[4px] font-semibold uppercase">Chỉnh sửa</button>
	</div>
</form>