import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Dom<PERSON>anitizer, SafeResourceUrl } from '@angular/platform-browser';
import UtilsService from '@platform/app/core/services/utils.service';
import { NzDrawerRef, NzDrawerService } from 'ng-zorro-antd/drawer';
import { EMPTY, Observable, catchError, switchMap, take, tap } from 'rxjs';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';

@Component({
  selector: 'app-file-preview-drawer',
  templateUrl: './file-preview-drawer.component.html',
  styleUrls: ['./file-preview-drawer.component.scss']
})
export class FilePreviewDrawerComponent implements OnInit {
  @Input('fileLink') originalFileLink: string;
  @Input() fileName: string;
  @Input() triggerTemplate: TemplateRef<{
    text: string;
    click: Function;
    mouseover: Function;
  }>;

  @ViewChild('previewDrawerTmpl', { static: false })
  previewDrawerTmpl?: TemplateRef<{
    $implicit: typeof this.cachedPreviewParams;
    drawerRef: NzDrawerRef<string>;
  }>;

  /* cached content drawer params */
  previewLink: SafeResourceUrl;
  numPages: number;
  warning: string;

  getPreviewLink$: Observable<{
    previewLink: SafeResourceUrl;
    numPages?: number;
    warning?: string;
  }>;

  constructor(
    private drawerService: NzDrawerService,
    private utils: UtilsService,
    public sanitizer: DomSanitizer,
    public pdfService: PdfService
  ) {}

  ngOnInit(): void {
    if (!this.originalFileLink) {
      this.previewLink = 'assets/img/rpa/ocr-experience/empty.svg';
      this.warning = 'Không có file link để preview!';
      return;
    }
    this.getPreviewLink$ = this.utils.fetchFile(this.originalFileLink).pipe(
      take(1),
      switchMap(async (blob) => {
        switch (blob.type) {
          case 'application/pdf': {
            /* render only first page */
            const pdfDocument = await this.pdfService.pdfjsDist.getDocument({
              url: URL.createObjectURL(blob)
            }).promise;

            const canvasFactory = new NodeCanvasFactory();

            const page1 = await pdfDocument.getPage(1);
            const viewport = page1.getViewport({ scale: 1.0 });

            const canvasAndContext = canvasFactory.create(
              viewport.width,
              viewport.height
            );
            const renderContext = {
              canvasContext: canvasAndContext.context,
              viewport
            };
            const renderTask = page1.render(renderContext as any);
            await renderTask.promise;

            const page1Blob = await new Promise<Blob>((resolve) => {
              (canvasAndContext.canvas as any).toBlob(resolve);
            });

            canvasFactory.destroy(canvasAndContext);
            page1.cleanup();

            return {
              previewLink: this.sanitizer.bypassSecurityTrustResourceUrl(
                URL.createObjectURL(page1Blob)
              ),
              numPages: pdfDocument.numPages
            };
          }

          case 'image/png':
          case 'image/jpeg':
            return {
              previewLink: this.sanitizer.bypassSecurityTrustResourceUrl(
                URL.createObjectURL(blob)
              )
            };

          default: {
            console.log(blob);
            return {
              previewLink: 'assets/img/rpa/ocr-experience/empty.svg',
              warning: 'Định dạng file không được hỗ trợ!'
            };
          }
        }
      }),
      catchError((error) => {
        console.log(error);
        return EMPTY;
      }),
      tap(({ previewLink, numPages, warning }) => {
        /* cache data */
        this.previewLink = previewLink;
        this.numPages = numPages;
        this.warning = warning;
      })
    );
  }

  openDrawer(): void {
    if (this.previewLink)
      this.createDrawer({
        previewLink: this.previewLink,
        numPages: this.numPages,
        warning: this.warning
      });
    else
      this.getPreviewLink$
        .pipe(
          tap(({ previewLink, numPages, warning }) => {
            this.createDrawer({ previewLink, numPages, warning });
          })
        )
        .subscribe();
  }

  private createDrawer(contentParams: {
    previewLink: SafeResourceUrl;
    numPages: number;
    warning: string;
  }) {
    const drawerRef = this.drawerService.create({
      nzTitle: null,
      nzFooter: null,
      // nzMask: false,
      nzContent: this.previewDrawerTmpl,
      nzClosable: false,
      nzWidth: 650,
      nzWrapClassName: 'kie-file-preview-drawer',
      nzMaskStyle: { background: 'transparent' },
      nzContentParams: {
        name: this.fileName,
        previewLink: contentParams.previewLink,
        numPages: contentParams.numPages,
        warning: contentParams.warning
      }
    });

    // drawerRef.afterOpen.subscribe(() => {
    //   console.log('Drawer(Template) open');
    // });

    // drawerRef.afterClose.subscribe(() => {
    //   console.log('Drawer(Template) close');
    // });

    return drawerRef;
  }
}
