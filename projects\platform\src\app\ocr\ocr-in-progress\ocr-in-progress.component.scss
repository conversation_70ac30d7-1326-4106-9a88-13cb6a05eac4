:host {
	display: flex;
	flex-direction: column;
	gap: 16px;
	height: 100%;
	position: relative; // for spinner to work

	div.file {
		border: 1px solid transparent;

		&.active {
			border: 1px solid #0667e1;
		}
	}

	::ng-deep {
		nz-progress {
			.ant-progress-inner {
				background-color: #C4C6D4;
			}
		}

		label.ant-checkbox-wrapper>span.ant-checkbox>span.ant-checkbox-inner {
			border: 1px solid #0667e1;
		}
	}
}