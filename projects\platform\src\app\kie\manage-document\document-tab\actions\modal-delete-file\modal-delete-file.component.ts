import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { KIEService } from '@platform/app/core/services/kie.service';
import { File } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, concatMap, delay, EMPTY, finalize, from, tap } from 'rxjs';

@Component({
  selector: 'app-modal-delete-file',
  templateUrl: './modal-delete-file.component.html',
  styleUrls: ['./modal-delete-file.component.scss']
})
export class ModalDeleteFileComponent implements OnInit {
  readonly nzModalData: { files: Partial<File>[] } = inject(NZ_MODAL_DATA);
  files = this.nzModalData.files;
  warningText = '';
  confirmDeleted = false;

  constructor(
    private modalRef: NzModalRef,
    private toastr: ToastrService,
    private kieService: KIEService,
    private documentLayoutService: DocumentLayoutService,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    let tmpText = '';
    if (this.files?.length === 1 && this.files[0].name)
      tmpText = `Bạn có muốn xóa tài liệu <b>${this.files[0].name}</b> này không?`;
    else tmpText = `Bạn có muốn xóa <b>${this.files.length}</b> tài liệu này không?`;
    this.warningText = this.sanitizer.sanitize(SecurityContext.HTML, tmpText);
  }

  cancel() {
    this.modalRef.close();
  }

  delete() {
    if (this.confirmDeleted) return;
    this.confirmDeleted = true;
    if (!this.files?.length) return;
    if (this.files.length === 1) return this.deleteOne();
    else return this.deleteMany();
  }

  private deleteOne() {
    this.kieService
      .deleteFile(this.files[0].id)
      .pipe(
        tap(() => {
          this.toastr.success('Xóa tài liệu thành công');
          this.documentLayoutService.updateFileCountOfDocument(
            this.files[0].documentId ||
              this.documentLayoutService.selectedDocumentId$.value,
            -1
          );
          this.modalRef.close([this.files[0].id]);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }

  private deleteMany() {
    if (!this.files?.length) return;
    const listSuccessDeletedFileId = [];
    const listFailedDeletedFileId = [];
    from(this.files.map((file) => file.id))
      .pipe(
        concatMap((fileId, index) => {
          return this.kieService.deleteFile(fileId).pipe(
            tap(() => listSuccessDeletedFileId.push(fileId)),
            delay(300),
            catchError((err) => {
              console.log(err);
              listFailedDeletedFileId.push(fileId);
              // return throwError(() => err); // rethrow error to stop execution at that point
              return EMPTY; // continue next execution
            })
          );
        }),
        finalize(() => {
          if (this.files.length === listSuccessDeletedFileId.length)
            this.toastr.success(
              `Xóa thành công ${listSuccessDeletedFileId.length} tài liệu`
            );
          else
            this.toastr.info(
              `Xóa tài liệu: thành công ${listSuccessDeletedFileId.length} - thất bại ${listFailedDeletedFileId.length}`
            );
          this.documentLayoutService.updateFileCountOfDocument(
            this.documentLayoutService.selectedDocumentId$.value,
            -listSuccessDeletedFileId.length
          );
          this.modalRef.close(listSuccessDeletedFileId);
        })
      )
      .subscribe();
  }
}
