<div
  *ngIf="setOfCheckedId.size > 0"
  class="bg-brand-3/10 p-[10px] rounded items-center gap-6 transition-all duration-500 flex"
>
  <div class="flex gap-2 border-r border-icon-1 pr-4 text-sm font-medium text-text-1">
    <img
      (click)="uncheckAll()"
      class="cursor-pointer"
      src="/assets/kie/header-table/cancel.svg"
      alt="cancel-icon"
    />
    <p>
      Đ<PERSON> chọn (<span class="text-brand-1">{{ setOfCheckedId.size }}</span
      >)
    </p>
  </div>
  <button
    class="flex gap-2 text-sm font-medium text-text-1"
    (click)="retrySelectedFailedFiles()"
  >
    <ng-template [ngTemplateOutlet]="retryIcon"></ng-template>
    <p>Thử lại</p>
  </button>
  <button
    class="flex gap-2 text-sm font-medium text-text-1"
    (click)="deleteSelectedFiles()"
  >
    <img src="/assets/kie/header-table/delete.svg" alt="delete-icon" />
    <p>Xóa khỏi danh sách</p>
  </button>
</div>
<div *ngIf="fileList.length" class="flex-auto h-[1px] flex flex-col gap-2 overflow-auto">
  <div
    *ngFor="let file of fileList"
    class="flex items-center gap-3 bg-bg-1 rounded-lg p-3 file"
    [ngClass]="{
      active: file.id === activeFile?.id,
      '!bg-[#ffebee]': getFileStatus(file) === 'failed'
    }"
  >
    <label
      nz-checkbox
      [ngModel]="setOfCheckedId.has(file.id)"
      (ngModelChange)="onItemChecked(file.id, $event)"
    ></label>
    <ng-container [ngSwitch]="getFileStatus(file)">
      <ng-container *ngSwitchCase="null">
        <ng-template [ngTemplateOutlet]="inProgressIcon"></ng-template>
      </ng-container>
      <ng-container *ngSwitchCase="'in-progress'">
        <ng-template [ngTemplateOutlet]="inProgressIcon"></ng-template>
      </ng-container>
      <ng-container *ngSwitchCase="'failed'">
        <ng-template [ngTemplateOutlet]="failedIcon"></ng-template>
      </ng-container>
    </ng-container>
    <div
      class="flex-1 truncate font-medium hover:text-brand-2 hover:cursor-pointer"
      (click)="selectFile(file)"
    >
      {{ file.name }}
    </div>
    <div [ngSwitch]="getFileStatus(file)" class="flex items-center gap-3">
      <ng-container *ngSwitchCase="null">
        <div class="font-medium text-xs">File vừa được tải lên</div>
        <button nz-tooltip nzTooltipTitle="Hủy" (click)="cancelInProgressFile(file)">
          <img src="assets/ocr-experience/cancel.svg" />
        </button>
      </ng-container>
      <ng-container *ngSwitchCase="'in-progress'">
        <nz-progress
          class="w-[150px]"
          nz-tooltip
          [nzStatus]="'active'"
          [nzStrokeColor]="'#009B4E'"
          [nzTooltipTitle]="getFileProgressPercent(file) + '%'"
          [nzPercent]="getFileProgressPercent(file)"
          [nzShowInfo]="false"
        ></nz-progress>
        <div
          nz-tooltip
          [nzTooltipTitle]="
            'Cập nhật lúc: ' + (file.lastUpdated | date: 'dd/MM/yyyy HH:mm')
          "
          class="font-medium text-xs w-14 text-right"
        >
          {{ getFileProgressText(file) }}
        </div>
        <button nz-tooltip nzTooltipTitle="Hủy" (click)="cancelInProgressFile(file)">
          <img src="assets/ocr-experience/cancel.svg" />
        </button>
      </ng-container>
      <ng-container *ngSwitchCase="'failed'">
        <div class="font-semibold text-xs text-status-error">
          {{ getFileProgressText(file) }}
        </div>
        <button
          *ngIf="file?.errorAttemptList?.length > MaxErrorAttemptCount"
          nz-tooltip
          nzTooltipTitle="Thử lại"
          (click)="retryFailedFile(file)"
        >
          <ng-template [ngTemplateOutlet]="retryIcon"></ng-template>
        </button>
        <button nz-tooltip nzTooltipTitle="Xóa" (click)="deleteInProgressFile(file)">
          <ng-template [ngTemplateOutlet]="deleteIcon"></ng-template>
        </button>
      </ng-container>
    </div>
  </div>
</div>
<div
  *ngIf="!fileList.length"
  class="w-full h-full flex flex-col gap-5 items-center justify-center"
>
  <img src="assets/kie/document/empty-table.svg" />
</div>
<ngx-spinner
  [name]="'ocr-in-progress-loading'"
  bdColor="rgba(0, 0, 0, 0.2)"
  size="medium"
  color="#fff"
  type="ball-scale-multiple"
  [fullScreen]="false"
  class="contents"
>
  <div class="font-semibold text-text-2">Đang cập nhật trạng thái</div>
</ngx-spinner>

<ng-template #inProgressIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.3332 10.7493V14.2494C13.3332 17.166 12.1665 18.3327 9.24984 18.3327H5.74984C2.83317 18.3327 1.6665 17.166 1.6665 14.2494V10.7493C1.6665 7.83268 2.83317 6.66602 5.74984 6.66602H9.24984C12.1665 6.66602 13.3332 7.83268 13.3332 10.7493Z"
      fill="#FFA100"
    />
    <path
      d="M14.2498 1.66602H10.7498C8.24635 1.66602 7.03822 2.53167 6.74575 4.61509C6.66834 5.1666 7.1298 5.62435 7.68671 5.62435H9.2498C12.7498 5.62435 14.3748 7.24935 14.3748 10.7493V12.3124C14.3748 12.8694 14.8326 13.3308 15.3841 13.2534C17.4675 12.9609 18.3331 11.7528 18.3331 9.24935V5.74935C18.3331 2.83268 17.1665 1.66602 14.2498 1.66602Z"
      fill="#FFA100"
    />
  </svg>
</ng-template>

<ng-template #failedIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="9.99984" cy="9.99935" r="8.33333" fill="#FF3355" />
    <rect x="9.375" y="5.83398" width="1.25" height="5.83333" rx="0.625" fill="white" />
    <circle cx="9.99984" cy="13.7493" r="0.833333" fill="white" />
  </svg>
</ng-template>

<ng-template #retryIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.3327 9.99984C18.3327 14.5998 14.5993 18.3332 9.99935 18.3332C5.39935 18.3332 2.59102 13.6998 2.59102 13.6998M2.59102 13.6998H6.35768M2.59102 13.6998V17.8665M1.66602 9.99984C1.66602 5.39984 5.36602 1.6665 9.99935 1.6665C15.5577 1.6665 18.3327 6.29984 18.3327 6.29984M18.3327 6.29984V2.13317M18.3327 6.29984H14.6327"
      stroke="#0667E1"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #deleteIcon>
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.5 4.98307C14.725 4.70807 11.9333 4.56641 9.15 4.56641C7.5 4.56641 5.85 4.64974 4.2 4.81641L2.5 4.98307"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M7.0835 4.14102L7.26683 3.04935C7.40016 2.25768 7.50016 1.66602 8.9085 1.66602H11.0918C12.5002 1.66602 12.6085 2.29102 12.7335 3.05768L12.9168 4.14102"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M15.7082 7.61719L15.1665 16.0089C15.0748 17.3172 14.9998 18.3339 12.6748 18.3339H7.32484C4.99984 18.3339 4.92484 17.3172 4.83317 16.0089L4.2915 7.61719"
      stroke="#FF3355"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>
