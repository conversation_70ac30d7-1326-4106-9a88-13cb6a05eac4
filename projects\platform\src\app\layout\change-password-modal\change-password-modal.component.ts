import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { catchError, EMPTY, of } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { get, isEmpty } from 'lodash';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { UserService } from '@platform/app/core/services/user.service';

@Component({
  selector: 'app-change-password-modal',
  templateUrl: './change-password-modal.component.html',
  styleUrls: ['./change-password-modal.component.scss'],
})
export class ChangePasswordModalComponent implements OnInit {
  changePasswordForm: UntypedFormGroup;
  showOldPassword = false;
  showPassword = false;
  showConfirmPassword = false;

  constructor(
    public modal: NzModalRef,
    private userService: UserService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.changePasswordForm = new UntypedFormGroup(
      {
        oldPassword: new UntypedFormControl(null, Validators.required),
        password: new UntypedFormControl(null, [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(
            /^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[\d\x])(?=.*[!@$#%]).*$/
          ),
        ]),
        passwordConfirm: new UntypedFormControl('', [Validators.required]),
      },
      [
        (formGroup: UntypedFormGroup) => {
          const newPass = formGroup.get('password');
          const verifiedPass = formGroup.get('passwordConfirm');

          const error = {
            ...verifiedPass.errors,
          };
          if (newPass.value !== verifiedPass.value) {
            error['passwordConfirmFailed'] = true;
            verifiedPass.setErrors(error);
          }

          return error;
        },
        (formGroup: UntypedFormGroup) => {
          const oldPass = formGroup.get('oldPassword');
          const newPass = formGroup.get('password');

          const error = { ...newPass.errors };
          if (
            !!newPass.value &&
            !!oldPass.value &&
            newPass.value === oldPass.value
          ) {
            error['newPasswordSameAsOldPassword'] = true;
            newPass.setErrors(error);
          }

          return error;
        },
      ]
    );
  }

  getFieldError(fieldName, errorName) {
    return get(
      this.changePasswordForm,
      `controls.${fieldName}.errors.${errorName}`,
      false
    );
  }

  checkInvalidField(fieldName) {
    return (
      !this.changePasswordForm.controls[fieldName].pristine &&
      !isEmpty(this.changePasswordForm.controls[fieldName].errors)
    );
  }

  handleSubmit() {
    if (!this.changePasswordForm.valid) return;
    const body = this.changePasswordForm.value;
    this.changePassword(body).subscribe((_) => {
      // this.modal.close();
      this.modal.destroy();
      this.toastr.success('Đổi mật khẩu thành công');
    });
  }

  private changePassword(body) {
    return this.userService.changePassword(body).pipe(
      catchError((e: HttpErrorResponse) => {
        const wrongOldPassword = 'Mật khẩu hiện tại chưa chính xác';
        const changePasswordFailed = 'Đổi mật khẩu thất bại!';
        if (e.status === 400) {
          if (
            get(e, 'error.messageFields', []).some(
              (item) => item.fieldName === 'oldPassword'
            ) ||
            get(e, 'error.message') === 'IDG-00000032'
          ) {
            this.toastr.error(wrongOldPassword);
          } else {
            this.toastr.error(changePasswordFailed);
          }
        }
        return EMPTY;
      })
    );
  }
}
