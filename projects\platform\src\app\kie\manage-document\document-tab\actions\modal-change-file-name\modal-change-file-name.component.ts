import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { File } from '@platform/app/kie/kie';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-change-file-name',
  templateUrl: './modal-change-file-name.component.html',
  styleUrls: ['./modal-change-file-name.component.scss']
})
export class ModalChangeFileNameComponent implements OnInit {
  readonly nzModalData: { file: File } = inject(NZ_MODAL_DATA);
  file = this.nzModalData.file;
  newFileName: string;

  constructor(
    private modalRef: NzModalRef,
    private toastr: ToastrService,
    private kieService: KIEService
  ) {}

  ngOnInit(): void {
    this.newFileName = this.file.name;
  }

  cancel() {
    this.modalRef.close();
  }

  save() {
    if (this.newFileName.trim().length < 1)
      return this.toastr.error('Tên tài liệu không được để trống');

    if (this.newFileName.trim().length > 255)
      return this.toastr.error('Tên tài liệu không được quá 255 ký tự');

    this.kieService
      .updateFileDetail(this.file.id, { name: this.newFileName })
      .pipe(
        tap((result) => {
          this.toastr.success('Đổi tên tài liệu thành công');
          this.modalRef.close(result);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
