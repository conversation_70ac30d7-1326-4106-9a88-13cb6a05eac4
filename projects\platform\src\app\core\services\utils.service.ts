import { Injectable } from '@angular/core';
import { UntypedFormArray } from '@angular/forms';
import { environment } from '@platform/environment/environment';
import { NgxSpinnerService } from 'ngx-spinner';
import * as forge from 'node-forge';
import { load } from 'cheerio';
import { jwtDecode } from 'jwt-decode';
import { defer, EMPTY, finalize, Subject, switchMap } from 'rxjs';
import { cloneDeep } from 'lodash';
import { HttpHeaders } from '@angular/common/http';

@Injectable({ providedIn: 'root' })
export default class UtilsService {
  constructor(private spinnerService: NgxSpinnerService) {}

  /* Subject that emits logout event, any component/service interests in this event could subscribe and do it own required logic */
  onLogout$ = new Subject();
  /* Subject that emits login event, any component/service interests in this event could subscribe and do it own required logic */
  onLogin$ = new Subject();

  APP_LOADING_SPINNER = 'app-loading-spinner';
  SKIP_APP_LOADING_SPINNER = 'skip-app-loading-spinner';

  convertDateTime(val: number) {
    const date = new Date(val);
    return (
      (date.getDate() > 9 ? date.getDate() : '0' + date.getDate()) +
      '/' +
      (date.getMonth() > 8 ? date.getMonth() + 1 : '0' + (date.getMonth() + 1)) +
      '/' +
      date.getFullYear()
    );
  }

  removeAccents(str: string) {
    return (
      str
        ?.normalize('NFKD')
        ?.replace(/[\u0300-\u036f]/g, '')
        /* FIXME: verify all cases */
        ?.replace(/đ/g, 'd')
        ?.replace(/Đ/g, 'D')
    );
  }

  getFileTypeByName(name: string) {
    return /[.]/.exec(name) ? /[^.]+$/.exec(name) : undefined;
  }
  getExtension(filename: string) {
    var parts = filename.split('/');
    var extension = parts[parts.length - 1].split('.');

    return extension[extension.length - 1];
  }

  makeId(length = 4) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  encryptMessage(message) {
    const publicKey = forge.pki.publicKeyFromPem(environment.publicKey);
    const encrypted = publicKey.encrypt(message, 'RSA-OAEP');
    return forge.util.encode64(encrypted);
  }

  getCurrentUser() {
    try {
      return JSON.parse(localStorage.getItem('currentUser'));
    } catch (error) {
      return null;
    }
  }

  getAccessToken() {
    return localStorage.getItem('accessToken');
  }

  get headers() {
    const accessToken = this.getAccessToken();
    return new HttpHeaders().set('Authorization', 'bearer ' + accessToken);
  }

  get headersWithSkipAppLoadingSpinner() {
    return this.headers.append(this.SKIP_APP_LOADING_SPINNER, 'true');
  }

  formatAmount(i: any) {
    if (!i) return 0;
    const re = '\\d(?=(\\d{3})+$)',
      num = i.toFixed(Math.max(0, 0));

    return num.replace(new RegExp(re, 'g'), '$&.');
  }

  convertBytesToMB(bytes: number) {
    const megabytes = bytes / (1024 * 1024);
    return megabytes.toFixed(2) + ' MB';
  }

  private loadingInProgressCount = 0;
  toggleAppLoadingSpinner(show: boolean, forceHide?: boolean) {
    if (show) {
      this.loadingInProgressCount++;
      this.spinnerService.show(this.APP_LOADING_SPINNER);
    } else {
      if (forceHide) this.loadingInProgressCount = 0;
      else this.loadingInProgressCount--;

      if (this.loadingInProgressCount <= 0) {
        this.spinnerService.hide(this.APP_LOADING_SPINNER);
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }

  moveItemInFormArray(
    formArray: UntypedFormArray,
    fromIndex: number,
    toIndex: number
  ): void {
    const dir = toIndex > fromIndex ? 1 : -1;

    const from = fromIndex;
    const to = toIndex;

    const temp = formArray.at(from);
    for (let i = from; i * dir < to * dir; i = i + dir) {
      const current = formArray.at(i + dir);
      formArray.setControl(i, current);
    }
    formArray.setControl(to, temp);
  }

  /* old version of parseTableHtml(), return columns */
  oldParseTableHtml(tableHtmlText: string) {
    const $ = load(tableHtmlText);

    const columns = [];
    let curr_x = 0,
      curr_y = 0;

    $('tr').each(function (row_idx, row) {
      curr_y = 0;
      $('td, th', row).each(function (col_idx, col) {
        const rowspan = +$(col).attr('rowspan') || 1;
        const colspan = +$(col).attr('colspan') || 1;
        let content = ''; // const content = $(col).text().trim() || '';
        $(col)
          .contents()
          .each((i, el) => {
            if (el.type === 'text') content += el['data'];
            else if (el.type === 'tag' && el.name === 'br') content += '\n';
          });

        let x = 0,
          y = 0;
        for (x = 0; x < rowspan; x++) {
          for (y = 0; y < colspan; y++) {
            if (columns[curr_y + y] === undefined) {
              columns[curr_y + y] = [];
            }

            while (columns[curr_y + y][curr_x + x] !== undefined) {
              curr_y += 1;
              if (columns[curr_y + y] === undefined) {
                columns[curr_y + y] = [];
              }
            }

            if (x === 0 && y === 0) {
              columns[curr_y + y][curr_x + x] = {
                text: content,
                y: curr_y + y,
                x: curr_x + x
              };
              if (colspan > 1) columns[curr_y + y][curr_x + x].colspan = colspan;
              if (rowspan > 1) columns[curr_y + y][curr_x + x].rowspan = rowspan;
            } else {
              columns[curr_y + y][curr_x + x] = {
                y: curr_y + y,
                x: curr_x + x,
                original_y: curr_y,
                original_x: curr_x
              };
              if (colspan > 1) columns[curr_y + y][curr_x + x].colspan = colspan;
              if (rowspan > 1) columns[curr_y + y][curr_x + x].rowspan = rowspan;
            }
          }
        }
        curr_y += 1;
      });
      curr_x += 1;
    });
    return columns;
  }

  /* parse table, return rows with each row consists of columns */
  parseTableHtml(tableHtmlText: string) {
    const $ = load(tableHtmlText);
    let columnCount = 0;
    let rowCount = 0;
    $('tr').each(function (_, row) {
      rowCount++;
      /* must take the each column's colspan into finding maximum number of column in table */
      let currentRowColumnCount = 0;
      $('td, th', row).each(function (_, col) {
        currentRowColumnCount += +$(col).attr('colspan') || 1;
      });
      columnCount = Math.max(columnCount, currentRowColumnCount);
    });
    const rows = new Array(rowCount)
      // aware of each row in rows is the same ref to an array
      .fill(new Array(columnCount).fill(null))
      // remap each row
      .map((columnCells, rowId) =>
        columnCells.map((_, columnId) => ({
          x: columnId,
          y: rowId,
          filled: false
        }))
      );

    $('tr').each((rowId, row) => {
      let currentColumnPosition = 0;
      $('td, th', row).each((_, col) => {
        const rowspan = +$(col).attr('rowspan') || 1;
        const colspan = +$(col).attr('colspan') || 1;

        /* move column-cursor to pos that have not been filled */
        while (rows[rowId][currentColumnPosition].filled) {
          currentColumnPosition += rows[rowId][currentColumnPosition].colspan;
          if (rows[rowId][currentColumnPosition] === undefined) {
            this.addMoreColumns(rows, 1);
          }
        }

        /* get content of each cell */
        let content = ''; // const content = $(col).text().trim() || '';
        $(col)
          .contents()
          .each((i, el) => {
            if (el.type === 'text') content += el['data'];
            else if (el.type === 'tag' && el.name === 'br') content += '\n';
          });

        rows[rowId][currentColumnPosition] = {
          y: rowId,
          x: currentColumnPosition,
          filled: true,
          text: content,
          rowspan,
          colspan
        };

        /* find and fill other merged cells */
        for (let i = 0; i < colspan; i++) {
          for (let j = 0; j < rowspan; j++) {
            if (i === 0 && j === 0) continue;
            if (rows[rowId + j] === undefined) this.addMoreRows(rows, 1);
            if (rows[rowId + j][currentColumnPosition + i] === undefined)
              this.addMoreColumns(rows, 1);
            rows[rowId + j][currentColumnPosition + i] = {
              ...rows[rowId][currentColumnPosition],
              original_x: currentColumnPosition,
              original_y: rowId,
              x: currentColumnPosition + i,
              y: rowId + j
            };
          }
        }
      });
    });

    return rows;
  }

  private addMoreColumns(tableAsAOA: any[][], count: number, debugInfo?) {
    debugInfo && console.log('addMoreColumns', debugInfo);
    tableAsAOA.forEach((cells, rowId) =>
      cells.push(
        ...new Array(count).fill(null).map((_, index) => ({
          x: cells.length + index,
          y: rowId,
          filled: false,
          padded: true
        }))
      )
    );
  }

  private addMoreRows(tableAsAOA: any[][], count: number, debugInfo?) {
    debugInfo && console.log('addMoreRows', debugInfo);
    const lastRow = tableAsAOA[tableAsAOA.length - 1].map(() => null);
    tableAsAOA.push(
      ...new Array(count).fill(null).map((_, rowIndex) =>
        cloneDeep(lastRow).map((_, colIndex) => ({
          x: colIndex,
          y: tableAsAOA.length + rowIndex,
          filled: false,
          padded: true
        }))
      )
    );
  }

  getDecodedAccessTokenPayload() {
    return jwtDecode<{
      idg_token_key: string;
      idg_token_id: string;
      idg_access_token: string;
      exp: number;
    }>(this.getAccessToken());
  }

  isAccessTokenExpired() {
    const payload = this.getDecodedAccessTokenPayload();
    const exp = new Date(payload.exp * 1000),
      now = new Date();
    return exp < now;
  }

  createBlobFromDataUrl(dataUrl, blobType) {
    // Convert the data URL to a Blob
    const byteString = atob(dataUrl.split(',')[1]); // Decode base64 part of the data URL
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const uint8Array = new Uint8Array(arrayBuffer);

    // Copy the byteString into the Uint8Array
    for (let i = 0; i < byteString.length; i++) {
      uint8Array[i] = byteString.charCodeAt(i);
    }

    // Create a Blob from the ArrayBuffer
    return new Blob([uint8Array], { type: blobType }); // Specify MIME type (e.g., 'image/png')
  }

  convertAbsoluteToRelativeBbox(
    absoluteBbox: [number, number, number, number],
    canvasWidth: number,
    canvasHeight: number
  ): [number, number, number, number] {
    const x1 = absoluteBbox[0] / canvasWidth;
    const y1 = absoluteBbox[1] / canvasHeight;
    const x2 = absoluteBbox[2] / canvasWidth;
    const y2 = absoluteBbox[3] / canvasHeight;
    return [x1, y1, x2, y2];
  }

  getDrawingBoundingBox(
    relativeBboxes = [0, 0, 0, 0],
    canvasWidth = 0,
    canvasHeight = 0
  ) {
    const [x1, y1, x2, y2] = relativeBboxes;
    return [
      x1 * canvasWidth,
      y1 * canvasHeight,
      (x2 - x1) * canvasWidth,
      (y2 - y1) * canvasHeight
    ];
  }

  fetchFile(linkToFile) {
    try {
      if (!linkToFile) return EMPTY;
      new URL(linkToFile); // try to create new URL out of linkToFile, invalid link will result in catch() returning EMPTY
    } catch (error) {
      return EMPTY;
    }

    // return this.http.get(linkToFile, { responseType: 'blob' }); // unable to http.get() with link presign from minio, return 404

    /* manually toggle app loading spinner because of using native fetch() instead of httpClient.get() bypassed the loading.interceptor */
    return defer(
      () =>
        new Promise((resolve) =>
          setTimeout(() => resolve(this.toggleAppLoadingSpinner(true)))
        )
    ).pipe(
      // must use defer() to convert HOT promise into COLD observable, otherwise, fetchFile() will always be triggered on declaration
      switchMap(() =>
        fetch(linkToFile)
          .then((v) => v.blob())
          .catch((e) => {
            console.log('fetch() in fetchFile() failed', e);
            return new Blob([]);
          })
      ), // fetch(): only way to fetch to presigned link from minio
      finalize(() => setTimeout(() => this.toggleAppLoadingSpinner(false)))
    );
  }
}
