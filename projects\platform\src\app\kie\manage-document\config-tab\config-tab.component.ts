import { Clipboard } from '@angular/cdk/clipboard';
import { CdkDragDrop } from '@angular/cdk/drag-drop';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { KIEService } from '@platform/app/core/services/kie.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { Document, listSystemTemplate } from '@platform/app/kie/kie';
import { environment } from '@platform/environment/environment';
import { kebabCase } from 'lodash';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError, filter, take, tap } from 'rxjs';
import { ModalChangeDocumentTemplateFileComponent } from './actions/modal-change-document-template-file/modal-change-document-template-file.component';

@Component({
  selector: 'app-config-tab',
  templateUrl: './config-tab.component.html',
  styleUrls: ['./config-tab.component.scss']
})
export class ConfigTabComponent implements OnInit {
  documentName = 'N/A';
  apiUrl = 'N/A';
  templateId = null;
  fieldExtraConfigList = this.fb.array([]);
  private _document: Document;

  @Input()
  set document(document: Document) {
    this._document = document;
    if (!document) return;

    let documentName = 'Tự tạo';
    let apiUrl = environment.idgUrl;
    let templateId = null;
    if (document.isSelfConfig) {
      apiUrl += '/rpa-service/aidigdoc/template/ocr';
      templateId = document?.template?.id;
    } else {
      const template = listSystemTemplate.find(
        (template) => template.id === document.templateRef
      );
      documentName = template?.title || 'N/A';
      apiUrl += `${template.idgEndpoint}`;
    }
    this.documentName = documentName;
    this.apiUrl = apiUrl;
    this.templateId = templateId;

    this.fieldExtraConfigList.clear();
    if (document.extraConfig) {
      document.extraConfig
        .sort((a, b) => a.order - b.order)
        .forEach((field) => {
          this.fieldExtraConfigList.push(
            this.fb.group({
              color: this.fb.control(field.color, [Validators.required]),
              is_visible: this.fb.control(field.is_visible, [Validators.required]),
              name: this.fb.control(field.name, [Validators.required])
            })
          );
        });
    }
  }
  get document(): Document {
    return this._document;
  }

  @Output()
  onConfigSaved = new EventEmitter();

  tokens = {
    key: '',
    id: '',
    token: ''
  };

  constructor(
    private clipboard: Clipboard,
    private toastr: ToastrService,
    private fb: UntypedFormBuilder,
    private utils: UtilsService,
    private kieService: KIEService,
    private modalService: NzModalService
  ) {}

  ngOnInit(): void {
    const decodedAccessToken = this.utils.getDecodedAccessTokenPayload();
    this.tokens = {
      key: decodedAccessToken?.idg_token_key,
      id: decodedAccessToken?.idg_token_id,
      token: 'Bearer ' + decodedAccessToken?.idg_access_token
    };
  }

  copyText(text: string) {
    this.clipboard.copy(text);
    this.toastr.clear();
    this.toastr.success(`Đã được copy`);
  }

  openTokenInfoModal(tokenInfoModalRef) {
    this.modalService.create({
      nzContent: tokenInfoModalRef,
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-config-tab-token-info',
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '730px' }
    });
  }

  closeModal() {
    this.modalService.closeAll();
  }

  fieldExtraConfigDrop(event: CdkDragDrop<string>) {
    this.utils.moveItemInFormArray(
      this.fieldExtraConfigList,
      event.previousIndex,
      event.currentIndex
    );
  }

  saveExtraConfigList() {
    this.kieService
      .updateDocumentDetail(this.document.id, {
        extraConfig: this.fieldExtraConfigList.value
      })
      .pipe(
        tap(() => {
          this.toastr.success('Lưu các trường thông tin bóc tách thành công');
          this.onConfigSaved.emit();
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }

  openChangeDocumentTemplateFile() {
    const modal = this.modalService.create({
      nzTitle: 'Sửa mẫu giấy tờ',
      nzContent: ModalChangeDocumentTemplateFileComponent,
      nzData: { documentId: this.document.id },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '16px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => this.onConfigSaved.emit())
      )
      .subscribe();
  }
}
