<div class="bg-white shadow-[0px_4px_12px_rgba(64,75,68,0.05)] rounded-lg p-6 pt-0">
  <div class="flex justify-between items-center mb-3">
    <span *ngIf="title" class="font-bold text-base mt-6">{{ title }}</span>
    <div class="flex items-center gap-7">
      <div class="relative min-w-[320px]" [class.hidden]="hideSearch">
        <input
          class="rounded-md"
          nz-input
          [(ngModel)]="searchTerm"
          (ngModelChange)="handleSearch()"
          type="text"
          placeholder="Tìm kiếm"
        />
        <a class="absolute cursor-pointer top-[4px] right-[4px]">
          <img alt="search" src="assets/img/search.svg" class="bg-white" />
        </a>
      </div>
    </div>
  </div>
  <div class="rpa-table-wrapper">
    <table>
      <thead class="rpa-top-row">
        <th class="p-3" *ngFor="let col of columns">
          <div class="flex items-center gap-2">
            {{ col.title }}
            <img
              *ngIf="col.tooltip"
              src="assets/statistic/tooltip.svg"
              alt="tooltip"
              nz-tooltip
              [nzTooltipTitle]="col.tooltip"
              nzTooltipPlacement="top"
              nzTooltipOverlayClassName="statistic-tooltip"
            />
          </div>
        </th>
      </thead>
      <tbody>
        <tr *ngFor="let row of dataSource">
          <td class="p-3" *ngFor="let col of columns">
            <ng-template
              *ngTemplateOutlet="
                tableCell;
                context: {
                  routerLink: getRouterLink(col, row),
                  content: renderCell(col, row)
                }
              "
            ></ng-template>
          </td>
        </tr>
      </tbody>
      <thead *ngIf="hasTotalRow" class="rpa-bottom-row">
        <th class="p-3" *ngFor="let value of totalRow">{{ value }}</th>
      </thead>
    </table>
  </div>
</div>

<ng-template #tableCell let-routerLink="routerLink" let-content="content">
  <div *ngIf="!routerLink">
    {{ content }}
  </div>
  <a *ngIf="routerLink" [routerLink]="routerLink">
    {{ content }}
  </a>
</ng-template>
