<div class="py-5 px-5 bg-[#272836] font-bold text-base flex justify-between items-center">
  Thông tin bóc tách
  <ng-template *ngTemplateOutlet="addFieldBtn"></ng-template>
</div>
<form [formGroup]="form" class="overflow-auto flex-1" nz-form [nzLayout]="'vertical'">
  <ng-container *ngFor="let field of fieldConfigListFormArray['controls']; let i = index">
    <ng-template
      *ngTemplateOutlet="fieldConfig; context: { field: field, fieldIndex: i }"
    ></ng-template>
  </ng-container>
  <ng-container *ngIf="!fieldConfigListFormArray['controls']?.length">
    <ng-template *ngTemplateOutlet="empty"></ng-template>
  </ng-container>
</form>
<div
  class="py-3 px-4 bg-[#222233] border-t border-[#E7EBEF1A] text-text-2 font-medium flex gap-4 items-center justify-center"
>
  <button
    (click)="handleSave()"
    class="py-2 px-4 rounded-lg bg-brand-1 font-medium text-white flex items-center gap-2"
  >
    <img class="inline-block" src="assets/kie/document/save.svg" alt="" />
    Lưu lại
  </button>
</div>

<ng-template #fieldConfig let-field="field" let-index="fieldIndex">
  <nz-collapse
    class="mx-5 mb-3 text-text-2 rounded-lg !border !border-[#E7EBEF1A] bg-bg-5"
    [nzBordered]="false"
    [nzGhost]="true"
  >
    <nz-collapse-panel [nzActive]="false" [nzHeader]="fieldHeader" [nzShowArrow]="false">
      <div class="text-text-2" [formGroup]="field">
        <nz-form-item>
          <nz-form-label
            class="text-xs font-medium !text-white"
            [nzTooltipTitle]="tooltipName"
            nzNoColon
            nzRequired
          >
            Tên trường thông tin
          </nz-form-label>
          <nz-form-control nzErrorTip="Tên trường thông tin không được bỏ trống">
            <input
              class="bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg w-full text-white py-2 px-3"
              type="text"
              formControlName="name"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="templateConfig.ocr_model === OcrModel.Default" class="mb-0">
          <nz-form-label
            class="text-xs font-medium !text-white"
            [nzTooltipTitle]="tooltipPrewords"
            nzNoColon
            nzRequired
          >
            Prewords
          </nz-form-label>
          <div class="flex flex-col gap-2" formArrayName="prewords">
            <nz-form-control
              nzErrorTip="Prewords không được bỏ trống, phải có ít nhất 1 preword"
              *ngFor="
                let preword of field.controls['prewords'].controls;
                let wordIndex = index
              "
              class="relative"
            >
              <input
                class="bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg w-full text-white py-2 pl-3 pr-6"
                type="text"
                [formControl]="preword"
              />
              <img
                class="cursor-pointer absolute right-2 top-1/2 -translate-y-1/2"
                (click)="removeWord(field.controls['prewords'], wordIndex)"
                src="assets/kie/config-fields/clear.svg"
              />
            </nz-form-control>
          </div>
          <button
            class="text-white hover:opacity-80 bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg mt-2 mb-6 text-xs py-[2px]"
            (click)="addWord(field.controls['prewords'])"
          >
            Thêm keyword
          </button>
        </nz-form-item>
        <nz-form-item *ngIf="templateConfig.ocr_model === OcrModel.Default" class="mb-0">
          <nz-form-label
            class="text-xs font-medium !text-white"
            [nzTooltipTitle]="tooltipSufwords"
            nzNoColon
            nzRequired
          >
            Sufwords
          </nz-form-label>
          <div class="flex flex-col gap-2" formArrayName="sufwords">
            <nz-form-control
              nzErrorTip="Sufwords không được bỏ trống, phải có ít nhất 1 sufword"
              *ngFor="
                let sufword of field.controls['sufwords'].controls;
                let wordIndex = index
              "
              class="relative"
            >
              <input
                class="bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg w-full text-white py-2 pl-3 pr-6"
                type="text"
                [formControl]="sufword"
              />
              <img
                class="cursor-pointer absolute right-2 top-1/2 -translate-y-1/2"
                (click)="removeWord(field.controls['sufwords'], wordIndex)"
                src="assets/kie/config-fields/clear.svg"
              />
            </nz-form-control>
          </div>
          <button
            class="text-white hover:opacity-80 bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg mt-2 mb-6 text-xs py-[2px]"
            (click)="addWord(field.controls['sufwords'])"
          >
            Thêm keyword
          </button>
        </nz-form-item>
        <nz-form-item *ngIf="templateConfig.ocr_model === OcrModel.Default">
          <nz-form-label
            class="text-xs font-medium !text-white"
            [nzTooltipTitle]="tooltipDatatype"
            nzNoColon
          >
            Datatype
          </nz-form-label>
          <nz-form-control nzErrorTip="Datatype...">
            <nz-select
              nzSize="large"
              nzPlaceHolder="Datatype..."
              nzAllowClear
              nzBorderless
              formControlName="datatype"
              [nzSuffixIcon]="chevron"
              [nzClearIcon]="clear"
              class="bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg w-full text-white text-sm"
            >
              <nz-option nzValue="is_number" nzLabel="Number"></nz-option>
              <nz-option nzValue="is_date" nzLabel="Date"></nz-option>
              <nz-option
                nzValue="is_title_or_uppercase"
                nzLabel="Title or Uppercase"
              ></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
        <div class="grid grid-cols-2 gap-x-4">
          <nz-form-item
            [ngClass]="{
              'col-span-full': templateConfig.ocr_model === OcrModel.LocationOnly
            }"
          >
            <nz-form-label
              class="text-xs font-medium !text-white"
              [nzTooltipTitle]="tooltipModel"
              nzNoColon
              nzRequired
            >
              Model
            </nz-form-label>
            <nz-form-control nzErrorTip="Model không được bỏ trống">
              <nz-select
                nzSize="large"
                nzPlaceHolder="Model..."
                nzAllowClear
                nzBorderless
                formControlName="model"
                [nzSuffixIcon]="chevron"
                [nzClearIcon]="clear"
                class="bg-[#FFFFFF0D] border border-[#E7EBEF1A] rounded-lg w-full text-white text-sm"
              >
                <nz-option
                  [nzValue]="TemplateConfigFieldModel.Default"
                  nzLabel="Default"
                ></nz-option>
                <nz-option
                  [nzValue]="TemplateConfigFieldModel.Special"
                  nzLabel="Special"
                ></nz-option>
                <nz-option
                  [nzValue]="TemplateConfigFieldModel.Handwriting"
                  nzLabel="Handwriting"
                ></nz-option>
                <nz-option
                  [nzValue]="TemplateConfigFieldModel.NumberHandwriting"
                  nzLabel="Number handwriting"
                ></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item
            *ngIf="templateConfig.ocr_model === OcrModel.Default"
            class="col-span-1"
          >
            <nz-form-label
              class="text-xs font-medium !text-white"
              [nzTooltipTitle]="tooltipMultilines"
              nzNoColon
              nzRequired
            >
              Multilines
            </nz-form-label>
            <nz-form-control nzErrorTip="Multilines...">
              <nz-radio-group formControlName="multilines">
                <label class="text-white" nz-radio [nzValue]="true">True</label>
                <label class="text-white" nz-radio [nzValue]="false">False</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
        </div>
        <ng-template
          *ngTemplateOutlet="bboxField; context: { formGroup: field }"
        ></ng-template>
      </div>
    </nz-collapse-panel>
    <ng-template #fieldHeader>
      <svg
        class="shrink-0"
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.1665 10H15.8332"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M10 15.8332V4.1665"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      <div class="flex-1 break-words w-[1px] text-text-2 px-3">
        {{ field.controls['name'].value || 'Thêm trường' }}
      </div>
      <div class="flex items-center gap-4" [formGroup]="field">
        <nz-switch
          formControlName="is_visible"
          (click)="$event.stopPropagation()"
        ></nz-switch>
        <button (click)="handleRemoveField($event, index)" class="hover:opacity-75">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <mask
              id="mask0_10433_49232"
              style="mask-type: alpha"
              maskUnits="userSpaceOnUse"
              x="1"
              y="0"
              width="18"
              height="20"
            >
              <path
                d="M17.5 4.98308C14.725 4.70808 11.9333 4.56641 9.15 4.56641C7.5 4.56641 5.85 4.64975 4.2 4.81641L2.5 4.98308"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M7.0835 4.14102L7.26683 3.04936C7.40016 2.25769 7.50016 1.66602 8.9085 1.66602H11.0918C12.5002 1.66602 12.6085 2.29102 12.7335 3.05769L12.9168 4.14102"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M15.7082 7.61719L15.1665 16.0089C15.0748 17.3172 14.9998 18.3339 12.6748 18.3339H7.32484C4.99984 18.3339 4.92484 17.3172 4.83317 16.0089L4.2915 7.61719"
                stroke="white"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </mask>
            <g mask="url(#mask0_10433_49232)">
              <rect class="hover:fill-status-error" width="20" height="20" fill="#fff" />
            </g>
          </svg>
        </button>
      </div>
    </ng-template>
  </nz-collapse>
</ng-template>

<ng-template #bboxField let-formGroup="formGroup">
  <div class="flex gap-[14px] items-center justify-between mb-2" [formGroup]="formGroup">
    <button
      (click)="updateFieldBbox(formGroup)"
      class="flex items-center gap-2 bg-[#FFFFFF1A] px-2 py-3 rounded-[4px] text-xs"
      [ngClass]="{
        'bg-status-success': formGroup.controls['id'].value === isUpdatingBbox
      }"
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask id="path-1-inside-1_10101_12254" fill="white">
          <path
            d="M6 3H4V1H3V3H1V4H3V6H4V4H6V3ZM15 5V2H12V3H8V4H12V5H13V12H12V13H5V12H4V8H3V12H2V15H5V14H12V15H15V12H14V5H15ZM4 14H3V13H4V14ZM14 14H13V13H14V14ZM13 3H14V4H13V3Z"
          />
        </mask>
        <path
          d="M6 3H7.5V1.5H6V3ZM4 3H2.5V4.5H4V3ZM4 1H5.5V-0.5H4V1ZM3 1V-0.5H1.5V1H3ZM3 3V4.5H4.5V3H3ZM1 3V1.5H-0.5V3H1ZM1 4H-0.5V5.5H1V4ZM3 4H4.5V2.5H3V4ZM3 6H1.5V7.5H3V6ZM4 6V7.5H5.5V6H4ZM4 4V2.5H2.5V4H4ZM6 4V5.5H7.5V4H6ZM15 5V6.5H16.5V5H15ZM15 2H16.5V0.5H15V2ZM12 2V0.5H10.5V2H12ZM12 3V4.5H13.5V3H12ZM8 3V1.5H6.5V3H8ZM8 4H6.5V5.5H8V4ZM12 4H13.5V2.5H12V4ZM12 5H10.5V6.5H12V5ZM13 5H14.5V3.5H13V5ZM13 12V13.5H14.5V12H13ZM12 12V10.5H10.5V12H12ZM12 13V14.5H13.5V13H12ZM5 13H3.5V14.5H5V13ZM5 12H6.5V10.5H5V12ZM4 12H2.5V13.5H4V12ZM4 8H5.5V6.5H4V8ZM3 8V6.5H1.5V8H3ZM3 12V13.5H4.5V12H3ZM2 12V10.5H0.5V12H2ZM2 15H0.5V16.5H2V15ZM5 15V16.5H6.5V15H5ZM5 14V12.5H3.5V14H5ZM12 14H13.5V12.5H12V14ZM12 15H10.5V16.5H12V15ZM15 15V16.5H16.5V15H15ZM15 12H16.5V10.5H15V12ZM14 12H12.5V13.5H14V12ZM14 5V3.5H12.5V5H14ZM4 14V15.5H5.5V14H4ZM3 14H1.5V15.5H3V14ZM3 13V11.5H1.5V13H3ZM4 13H5.5V11.5H4V13ZM14 14V15.5H15.5V14H14ZM13 14H11.5V15.5H13V14ZM13 13V11.5H11.5V13H13ZM14 13H15.5V11.5H14V13ZM13 3V1.5H11.5V3H13ZM14 3H15.5V1.5H14V3ZM14 4V5.5H15.5V4H14ZM13 4H11.5V5.5H13V4ZM6 1.5H4V4.5H6V1.5ZM5.5 3V1H2.5V3H5.5ZM4 -0.5H3V2.5H4V-0.5ZM1.5 1V3H4.5V1H1.5ZM3 1.5H1V4.5H3V1.5ZM-0.5 3V4H2.5V3H-0.5ZM1 5.5H3V2.5H1V5.5ZM1.5 4V6H4.5V4H1.5ZM3 7.5H4V4.5H3V7.5ZM5.5 6V4H2.5V6H5.5ZM4 5.5H6V2.5H4V5.5ZM7.5 4V3H4.5V4H7.5ZM16.5 5V2H13.5V5H16.5ZM15 0.5H12V3.5H15V0.5ZM10.5 2V3H13.5V2H10.5ZM12 1.5H8V4.5H12V1.5ZM6.5 3V4H9.5V3H6.5ZM8 5.5H12V2.5H8V5.5ZM10.5 4V5H13.5V4H10.5ZM12 6.5H13V3.5H12V6.5ZM11.5 5V12H14.5V5H11.5ZM13 10.5H12V13.5H13V10.5ZM10.5 12V13H13.5V12H10.5ZM12 11.5H5V14.5H12V11.5ZM6.5 13V12H3.5V13H6.5ZM5 10.5H4V13.5H5V10.5ZM5.5 12V8H2.5V12H5.5ZM4 6.5H3V9.5H4V6.5ZM1.5 8V12H4.5V8H1.5ZM3 10.5H2V13.5H3V10.5ZM0.5 12V15H3.5V12H0.5ZM2 16.5H5V13.5H2V16.5ZM6.5 15V14H3.5V15H6.5ZM5 15.5H12V12.5H5V15.5ZM10.5 14V15H13.5V14H10.5ZM12 16.5H15V13.5H12V16.5ZM16.5 15V12H13.5V15H16.5ZM15 10.5H14V13.5H15V10.5ZM15.5 12V5H12.5V12H15.5ZM14 6.5H15V3.5H14V6.5ZM4 12.5H3V15.5H4V12.5ZM4.5 14V13H1.5V14H4.5ZM3 14.5H4V11.5H3V14.5ZM2.5 13V14H5.5V13H2.5ZM14 12.5H13V15.5H14V12.5ZM14.5 14V13H11.5V14H14.5ZM13 14.5H14V11.5H13V14.5ZM12.5 13V14H15.5V13H12.5ZM13 4.5H14V1.5H13V4.5ZM12.5 3V4H15.5V3H12.5ZM14 2.5H13V5.5H14V2.5ZM14.5 4V3H11.5V4H14.5Z"
          fill="white"
          mask="url(#path-1-inside-1_10101_12254)"
        />
      </svg>
      Chọn vùng thông tin bóc tách
    </button>
    <label
      class="relative cursor-pointer py-2 px-3 rounded-lg border border-[#E7EBEF1A] bg-[#FFFFFF0D] flex items-center gap-[10px]"
    >
      <input
        #colorPickerInput
        formControlName="color"
        type="color"
        class="absolute top-0 left-0 w-full h-full z-10 text-[0px] hidden"
      />
      <div
        class="w-4 h-4 rounded-[4px] shrink-0"
        [ngStyle]="{ 'background-color': formGroup.controls['color'].value }"
      ></div>
      <div class="min-w-[68px] text-center">
        {{ formGroup.controls['color'].value }}
      </div>
    </label>
  </div>
  <div
    class="py-2 pl-3 pr-6 rounded-lg border border-[#E7EBEF1A] bg-[#FFFFFF0D] truncate relative"
    [ngClass]="{
      'border-status-error':
        formGroup.controls['bbox'].touched && formGroup.controls['bbox'].invalid
    }"
    nz-tooltip
    [nzTooltipTitle]="bboxTooltip"
  >
    {{
      formGroup.controls['bbox'].value
        ? 'Page: ' +
          formGroup.controls['bbox'].value['page'] +
          ' | ' +
          formGroup.controls['bbox'].value['value'][0] +
          ', ' +
          formGroup.controls['bbox'].value['value'][1] +
          ', ' +
          formGroup.controls['bbox'].value['value'][2] +
          ', ' +
          formGroup.controls['bbox'].value['value'][3]
        : '&nbsp;'
    }}
    <img
      class="cursor-pointer absolute right-2 top-1/2 -translate-y-1/2"
      (click)="handleClearBbox(formGroup.controls['id'].value)"
      src="assets/kie/config-fields/clear.svg"
    />
  </div>
  <div
    *ngIf="formGroup.controls['bbox'].touched && formGroup.controls['bbox'].invalid"
    class="text-xs text-status-error mt-1"
  >
    Vùng thông tin bóc tách không được để trống
  </div>
  <ng-template #bboxTooltip>
    <ng-container *ngIf="formGroup.controls['bbox'].value">
      <div>xMin: {{ formGroup.controls['bbox'].value['value'][0] }}</div>
      <div>yMin: {{ formGroup.controls['bbox'].value['value'][1] }}</div>
      <div>xMax: {{ formGroup.controls['bbox'].value['value'][2] }}</div>
      <div>yMax: {{ formGroup.controls['bbox'].value['value'][3] }}</div>
    </ng-container>
    <ng-container *ngIf="!formGroup.controls['bbox'].value">[NULL] </ng-container>
  </ng-template>
</ng-template>

<ng-template #empty>
  <div class="flex flex-col items-center h-full justify-center gap-4">
    <svg
      width="168"
      height="180"
      viewBox="0 0 168 180"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.1159 167.669H8.25657C8.20354 167.364 8.10577 167.094 7.96325 166.858C7.82073 166.62 7.64341 166.417 7.43129 166.252C7.21916 166.086 6.97721 165.962 6.70543 165.879C6.43697 165.793 6.14696 165.75 5.83541 165.75C5.2819 165.75 4.79137 165.889 4.36381 166.167C3.93626 166.442 3.6015 166.847 3.35955 167.38C3.1176 167.911 2.99663 168.559 2.99663 169.324C2.99663 170.103 3.1176 170.759 3.35955 171.293C3.60482 171.823 3.93957 172.224 4.36381 172.496C4.79137 172.764 5.28024 172.899 5.83043 172.899C6.13536 172.899 6.4204 172.859 6.68555 172.779C6.95401 172.697 7.19431 172.576 7.40643 172.416C7.62186 172.257 7.8025 172.062 7.94833 171.83C8.09748 171.598 8.20023 171.333 8.25657 171.034L10.1159 171.044C10.0463 171.528 9.89554 171.982 9.66353 172.407C9.43484 172.831 9.13488 173.205 8.76367 173.53C8.39246 173.852 7.95827 174.103 7.46112 174.286C6.96396 174.465 6.41211 174.554 5.80558 174.554C4.91069 174.554 4.11192 174.347 3.40927 173.933C2.70662 173.518 2.15311 172.92 1.74876 172.138C1.3444 171.356 1.14222 170.418 1.14222 169.324C1.14222 168.227 1.34606 167.289 1.75373 166.51C2.1614 165.728 2.71656 165.13 3.41921 164.715C4.12186 164.301 4.91732 164.094 5.80558 164.094C6.37234 164.094 6.89933 164.174 7.38654 164.333C7.87376 164.492 8.30794 164.725 8.6891 165.034C9.07025 165.339 9.38346 165.713 9.62873 166.157C9.87731 166.598 10.0397 167.102 10.1159 167.669ZM13.5252 169.941V174.415H11.7255V164.233H13.4854V168.076H13.5749C13.7539 167.645 14.0307 167.306 14.4052 167.057C14.783 166.805 15.2636 166.679 15.8469 166.679C16.3772 166.679 16.8396 166.79 17.234 167.012C17.6284 167.234 17.9334 167.559 18.1488 167.987C18.3675 168.414 18.4769 168.936 18.4769 169.553V174.415H16.6772V169.831C16.6772 169.318 16.5446 168.918 16.2795 168.633C16.0176 168.345 15.6497 168.201 15.1758 168.201C14.8576 168.201 14.5726 168.27 14.3207 168.409C14.0721 168.545 13.8765 168.742 13.734 169.001C13.5948 169.259 13.5252 169.573 13.5252 169.941ZM27.9876 166.386H29.146C29.146 166.936 29.0664 167.398 28.9073 167.773C28.7515 168.144 28.4847 168.424 28.1069 168.613C27.729 168.802 27.207 168.897 26.5408 168.897V167.992C26.985 167.992 27.3081 167.935 27.5103 167.823C27.7125 167.707 27.8417 167.529 27.8981 167.291C27.9544 167.052 27.9843 166.751 27.9876 166.386ZM25.2085 171.203V166.779H27.0131V174.415H25.2631V173.058H25.1836C25.0112 173.485 24.7279 173.835 24.3335 174.107C23.9424 174.379 23.4601 174.514 22.8867 174.514C22.3862 174.514 21.9438 174.403 21.5593 174.181C21.1781 173.956 20.8799 173.63 20.6644 173.202C20.449 172.771 20.3413 172.251 20.3413 171.641V166.779H22.141V171.362C22.1443 171.846 22.2769 172.231 22.5387 172.516C22.8039 172.801 23.1519 172.943 23.5827 172.943C23.8479 172.943 24.1048 172.879 24.3533 172.75C24.6052 172.62 24.8107 172.428 24.9698 172.173C25.1322 171.914 25.2118 171.591 25.2085 171.203ZM32.0245 174.569C31.5406 174.569 31.1048 174.483 30.717 174.311C30.3325 174.135 30.0276 173.876 29.8022 173.535C29.5801 173.194 29.4691 172.773 29.4691 172.272C29.4691 171.841 29.5487 171.485 29.7077 171.203C29.8668 170.922 30.0839 170.696 30.359 170.527C30.6341 170.358 30.944 170.231 31.2887 170.144C31.6367 170.055 31.9963 169.99 32.3675 169.951C32.815 169.904 33.1779 169.863 33.4563 169.826C33.7347 169.786 33.9369 169.727 34.0629 169.647C34.1921 169.564 34.2567 169.437 34.2567 169.264V169.235C34.2567 168.86 34.1457 168.57 33.9237 168.365C33.7016 168.159 33.3817 168.056 32.9641 168.056C32.5233 168.056 32.1737 168.152 31.9151 168.345C31.6599 168.537 31.4876 168.764 31.3981 169.026L29.7177 168.787C29.8503 168.323 30.069 167.935 30.3739 167.624C30.6789 167.309 31.0517 167.074 31.4925 166.918C31.9334 166.759 32.4206 166.679 32.9542 166.679C33.3221 166.679 33.6883 166.722 34.0529 166.809C34.4175 166.895 34.7506 167.037 35.0522 167.236C35.3538 167.432 35.5958 167.698 35.7781 168.036C35.9637 168.375 36.0565 168.797 36.0565 169.304V174.415H34.3263V173.366H34.2667C34.1573 173.578 34.0032 173.777 33.8043 173.963C33.6088 174.145 33.3619 174.292 33.0636 174.405C32.7686 174.514 32.4222 174.569 32.0245 174.569ZM32.4918 173.247C32.8531 173.247 33.1663 173.175 33.4315 173.033C33.6966 172.887 33.9004 172.695 34.043 172.456C34.1888 172.218 34.2617 171.957 34.2617 171.676V170.776C34.2054 170.822 34.1093 170.865 33.9734 170.905C33.8408 170.945 33.6916 170.98 33.5259 171.009C33.3602 171.039 33.1961 171.066 33.0337 171.089C32.8713 171.112 32.7305 171.132 32.6112 171.149C32.3427 171.185 32.1024 171.245 31.8903 171.328C31.6781 171.411 31.5108 171.527 31.3881 171.676C31.2655 171.822 31.2042 172.01 31.2042 172.242C31.2042 172.574 31.3252 172.824 31.5671 172.993C31.8091 173.162 32.1173 173.247 32.4918 173.247ZM44.6697 174.564C43.9074 174.564 43.2528 174.397 42.706 174.062C42.1624 173.727 41.7431 173.265 41.4482 172.675C41.1565 172.082 41.0107 171.399 41.0107 170.627C41.0107 169.851 41.1598 169.167 41.4581 168.573C41.7564 167.977 42.1773 167.513 42.7209 167.181C43.2678 166.847 43.9141 166.679 44.6598 166.679C45.2796 166.679 45.8281 166.794 46.3054 167.022C46.786 167.248 47.1688 167.568 47.4538 167.982C47.7389 168.393 47.9013 168.873 47.9411 169.424H46.2209C46.1513 169.056 45.9856 168.749 45.7237 168.504C45.4652 168.255 45.1188 168.131 44.6847 168.131C44.3168 168.131 43.9936 168.23 43.7152 168.429C43.4368 168.625 43.2197 168.907 43.0639 169.274C42.9115 169.642 42.8352 170.083 42.8352 170.597C42.8352 171.117 42.9115 171.565 43.0639 171.939C43.2164 172.31 43.4302 172.597 43.7053 172.799C43.9837 172.998 44.3101 173.098 44.6847 173.098C44.9498 173.098 45.1868 173.048 45.3956 172.948C45.6077 172.846 45.785 172.698 45.9276 172.506C46.0701 172.314 46.1679 172.08 46.2209 171.805H47.9411C47.898 172.345 47.7389 172.824 47.4638 173.242C47.1887 173.656 46.8142 173.981 46.3402 174.216C45.8662 174.448 45.3094 174.564 44.6697 174.564ZM52.7498 174.564C52.0041 174.564 51.3578 174.4 50.8109 174.072C50.264 173.744 49.8398 173.285 49.5382 172.695C49.2399 172.105 49.0907 171.416 49.0907 170.627C49.0907 169.838 49.2399 169.147 49.5382 168.554C49.8398 167.96 50.264 167.5 50.8109 167.171C51.3578 166.843 52.0041 166.679 52.7498 166.679C53.4956 166.679 54.1419 166.843 54.6887 167.171C55.2356 167.5 55.6582 167.96 55.9565 168.554C56.2581 169.147 56.4089 169.838 56.4089 170.627C56.4089 171.416 56.2581 172.105 55.9565 172.695C55.6582 173.285 55.2356 173.744 54.6887 174.072C54.1419 174.4 53.4956 174.564 52.7498 174.564ZM52.7598 173.122C53.1641 173.122 53.5022 173.011 53.774 172.789C54.0458 172.564 54.2479 172.262 54.3805 171.884C54.5164 171.507 54.5843 171.086 54.5843 170.622C54.5843 170.154 54.5164 169.732 54.3805 169.354C54.2479 168.973 54.0458 168.67 53.774 168.444C53.5022 168.219 53.1641 168.106 52.7598 168.106C52.3455 168.106 52.0008 168.219 51.7257 168.444C51.4539 168.67 51.2501 168.973 51.1142 169.354C50.9816 169.732 50.9153 170.154 50.9153 170.622C50.9153 171.086 50.9816 171.507 51.1142 171.884C51.2501 172.262 51.4539 172.564 51.7257 172.789C52.0008 173.011 52.3455 173.122 52.7598 173.122ZM52.0588 165.774L53.1724 163.502H54.9324L53.4061 165.774H52.0588ZM65.2223 166.779V168.171H60.8324V166.779H65.2223ZM61.9162 164.949H63.7159V172.118C63.7159 172.36 63.7524 172.546 63.8253 172.675C63.9015 172.801 64.0009 172.887 64.1236 172.934C64.2462 172.98 64.3821 173.003 64.5312 173.003C64.6439 173.003 64.7467 172.995 64.8395 172.978C64.9356 172.962 65.0085 172.947 65.0582 172.934L65.3615 174.34C65.2654 174.374 65.1278 174.41 64.9489 174.45C64.7732 174.49 64.5578 174.513 64.3026 174.519C63.8518 174.533 63.4458 174.465 63.0845 174.316C62.7232 174.163 62.4366 173.928 62.2244 173.61C62.0156 173.291 61.9129 172.894 61.9162 172.416V164.949ZM68.6912 169.941V174.415H66.8915V164.233H68.6515V168.076H68.7409C68.9199 167.645 69.1967 167.306 69.5712 167.057C69.949 166.805 70.4296 166.679 71.013 166.679C71.5433 166.679 72.0056 166.79 72.4 167.012C72.7944 167.234 73.0994 167.559 73.3148 167.987C73.5336 168.414 73.6429 168.936 73.6429 169.553V174.415H71.8432V169.831C71.8432 169.318 71.7106 168.918 71.4455 168.633C71.1837 168.345 70.8158 168.201 70.3418 168.201C70.0236 168.201 69.7386 168.27 69.4867 168.409C69.2381 168.545 69.0426 168.742 68.9 169.001C68.7608 169.259 68.6912 169.573 68.6912 169.941ZM78.8084 174.564C78.0627 174.564 77.4164 174.4 76.8695 174.072C76.3226 173.744 75.8984 173.285 75.5968 172.695C75.2985 172.105 75.1493 171.416 75.1493 170.627C75.1493 169.838 75.2985 169.147 75.5968 168.554C75.8984 167.96 76.3226 167.5 76.8695 167.171C77.4164 166.843 78.0627 166.679 78.8084 166.679C79.5542 166.679 80.2005 166.843 80.7473 167.171C81.2942 167.5 81.7168 167.96 82.0151 168.554C82.3167 169.147 82.4675 169.838 82.4675 170.627C82.4675 171.416 82.3167 172.105 82.0151 172.695C81.7168 173.285 81.2942 173.744 80.7473 174.072C80.2005 174.4 79.5542 174.564 78.8084 174.564ZM78.8184 173.122C79.2227 173.122 79.5608 173.011 79.8326 172.789C80.1043 172.564 80.3065 172.262 80.4391 171.884C80.575 171.507 80.6429 171.086 80.6429 170.622C80.6429 170.154 80.575 169.732 80.4391 169.354C80.3065 168.973 80.1043 168.67 79.8326 168.444C79.5608 168.219 79.2227 168.106 78.8184 168.106C78.4041 168.106 78.0594 168.219 77.7843 168.444C77.5125 168.67 77.3087 168.973 77.1728 169.354C77.0402 169.732 76.9739 170.154 76.9739 170.622C76.9739 171.086 77.0402 171.507 77.1728 171.884C77.3087 172.262 77.5125 172.564 77.7843 172.789C78.0594 173.011 78.4041 173.122 78.8184 173.122ZM79.8176 165.864L78.8084 164.591L77.7992 165.864H76.2232V165.794L78.1074 163.647H79.5144L81.3936 165.794V165.864H79.8176ZM85.7947 169.941V174.415H83.995V166.779H85.7152V168.076H85.8047C85.9804 167.649 86.2604 167.309 86.6449 167.057C87.0327 166.805 87.5116 166.679 88.0817 166.679C88.6087 166.679 89.0677 166.792 89.4588 167.017C89.8532 167.243 90.1581 167.569 90.3736 167.997C90.5923 168.424 90.7 168.943 90.6967 169.553V174.415H88.897V169.831C88.897 169.321 88.7644 168.921 88.4993 168.633C88.2375 168.345 87.8745 168.201 87.4105 168.201C87.0956 168.201 86.8156 168.27 86.5703 168.409C86.3284 168.545 86.1378 168.742 85.9986 169.001C85.8627 169.259 85.7947 169.573 85.7947 169.941ZM95.8473 177.438C95.201 177.438 94.6458 177.35 94.1818 177.174C93.7178 177.002 93.3449 176.77 93.0632 176.478C92.7815 176.187 92.5859 175.863 92.4766 175.509L94.0973 175.116C94.1702 175.265 94.2763 175.413 94.4155 175.559C94.5547 175.708 94.742 175.83 94.9773 175.926C95.2159 176.026 95.5159 176.076 95.8771 176.076C96.3875 176.076 96.8101 175.951 97.1449 175.703C97.4796 175.457 97.647 175.053 97.647 174.49V173.043H97.5575C97.4647 173.228 97.3288 173.419 97.1499 173.615C96.9742 173.81 96.7405 173.974 96.4489 174.107C96.1605 174.239 95.7976 174.306 95.3601 174.306C94.7734 174.306 94.2415 174.168 93.7642 173.893C93.2902 173.615 92.9124 173.2 92.6307 172.65C92.3523 172.097 92.2131 171.404 92.2131 170.572C92.2131 169.733 92.3523 169.026 92.6307 168.449C92.9124 167.869 93.2919 167.43 93.7692 167.132C94.2464 166.83 94.7784 166.679 95.3651 166.679C95.8125 166.679 96.1804 166.755 96.4688 166.908C96.7604 167.057 96.9924 167.238 97.1648 167.45C97.3371 167.659 97.468 167.856 97.5575 168.041H97.657V166.779H99.4318V174.539C99.4318 175.192 99.276 175.733 98.9645 176.16C98.6529 176.588 98.227 176.907 97.6868 177.12C97.1465 177.332 96.5334 177.438 95.8473 177.438ZM95.8622 172.894C96.2434 172.894 96.5682 172.801 96.8366 172.615C97.1051 172.43 97.3089 172.163 97.4482 171.815C97.5874 171.467 97.657 171.049 97.657 170.562C97.657 170.081 97.5874 169.661 97.4482 169.299C97.3123 168.938 97.1101 168.658 96.8416 168.459C96.5765 168.257 96.25 168.156 95.8622 168.156C95.4612 168.156 95.1264 168.26 94.858 168.469C94.5895 168.678 94.3873 168.965 94.2514 169.329C94.1155 169.69 94.0476 170.101 94.0476 170.562C94.0476 171.029 94.1155 171.439 94.2514 171.79C94.3906 172.138 94.5945 172.41 94.8629 172.605C95.1347 172.798 95.4678 172.894 95.8622 172.894ZM108.562 166.779V168.171H104.172V166.779H108.562ZM105.256 164.949H107.056V172.118C107.056 172.36 107.092 172.546 107.165 172.675C107.241 172.801 107.341 172.887 107.463 172.934C107.586 172.98 107.722 173.003 107.871 173.003C107.984 173.003 108.087 172.995 108.179 172.978C108.275 172.962 108.348 172.947 108.398 172.934L108.701 174.34C108.605 174.374 108.468 174.41 108.289 174.45C108.113 174.49 107.898 174.513 107.642 174.519C107.192 174.533 106.786 174.465 106.424 174.316C106.063 174.163 105.776 173.928 105.564 173.61C105.355 173.291 105.253 172.894 105.256 172.416V164.949ZM110.067 174.415V166.779H111.867V174.415H110.067ZM110.972 165.695C110.687 165.695 110.442 165.6 110.236 165.411C110.031 165.219 109.928 164.989 109.928 164.72C109.928 164.449 110.031 164.218 110.236 164.029C110.442 163.837 110.687 163.741 110.972 163.741C111.26 163.741 111.506 163.837 111.708 164.029C111.913 164.218 112.016 164.449 112.016 164.72C112.016 164.989 111.913 165.219 111.708 165.411C111.506 165.6 111.26 165.695 110.972 165.695ZM115.517 169.941V174.415H113.718V166.779H115.438V168.076H115.527C115.703 167.649 115.983 167.309 116.368 167.057C116.755 166.805 117.234 166.679 117.804 166.679C118.331 166.679 118.79 166.792 119.181 167.017C119.576 167.243 119.881 167.569 120.096 167.997C120.315 168.424 120.423 168.943 120.419 169.553V174.415H118.62V169.831C118.62 169.321 118.487 168.921 118.222 168.633C117.96 168.345 117.597 168.201 117.133 168.201C116.818 168.201 116.538 168.27 116.293 168.409C116.051 168.545 115.86 168.742 115.721 169.001C115.585 169.259 115.517 169.573 115.517 169.941ZM129.053 174.564C128.29 174.564 127.636 174.397 127.089 174.062C126.545 173.727 126.126 173.265 125.831 172.675C125.539 172.082 125.393 171.399 125.393 170.627C125.393 169.851 125.543 169.167 125.841 168.573C126.139 167.977 126.56 167.513 127.104 167.181C127.651 166.847 128.297 166.679 129.043 166.679C129.662 166.679 130.211 166.794 130.688 167.022C131.169 167.248 131.552 167.568 131.837 167.982C132.122 168.393 132.284 168.873 132.324 169.424H130.604C130.534 169.056 130.368 168.749 130.107 168.504C129.848 168.255 129.502 168.131 129.067 168.131C128.7 168.131 128.376 168.23 128.098 168.429C127.82 168.625 127.603 168.907 127.447 169.274C127.294 169.642 127.218 170.083 127.218 170.597C127.218 171.117 127.294 171.565 127.447 171.939C127.599 172.31 127.813 172.597 128.088 172.799C128.366 172.998 128.693 173.098 129.067 173.098C129.333 173.098 129.57 173.048 129.778 172.948C129.991 172.846 130.168 172.698 130.31 172.506C130.453 172.314 130.551 172.08 130.604 171.805H132.324C132.281 172.345 132.122 172.824 131.847 173.242C131.571 173.656 131.197 173.981 130.723 174.216C130.249 174.448 129.692 174.564 129.053 174.564ZM137.133 174.564C136.387 174.564 135.741 174.4 135.194 174.072C134.647 173.744 134.223 173.285 133.921 172.695C133.623 172.105 133.474 171.416 133.474 170.627C133.474 169.838 133.623 169.147 133.921 168.554C134.223 167.96 134.647 167.5 135.194 167.171C135.741 166.843 136.387 166.679 137.133 166.679C137.878 166.679 138.525 166.843 139.072 167.171C139.618 167.5 140.041 167.96 140.339 168.554C140.641 169.147 140.792 169.838 140.792 170.627C140.792 171.416 140.641 172.105 140.339 172.695C140.041 173.285 139.618 173.744 139.072 174.072C138.525 174.4 137.878 174.564 137.133 174.564ZM137.143 173.122C137.547 173.122 137.885 173.011 138.157 172.789C138.429 172.564 138.631 172.262 138.763 171.884C138.899 171.507 138.967 171.086 138.967 170.622C138.967 170.154 138.899 169.732 138.763 169.354C138.631 168.973 138.429 168.67 138.157 168.444C137.885 168.219 137.547 168.106 137.143 168.106C136.728 168.106 136.384 168.219 136.108 168.444C135.837 168.67 135.633 168.973 135.497 169.354C135.364 169.732 135.298 170.154 135.298 170.622C135.298 171.086 135.364 171.507 135.497 171.884C135.633 172.262 135.837 172.564 136.108 172.789C136.384 173.011 136.728 173.122 137.143 173.122ZM144.119 169.941V174.415H142.319V166.779H144.039V168.076H144.129C144.305 167.649 144.585 167.309 144.969 167.057C145.357 166.805 145.836 166.679 146.406 166.679C146.933 166.679 147.392 166.792 147.783 167.017C148.177 167.243 148.482 167.569 148.698 167.997C148.917 168.424 149.024 168.943 149.021 169.553V174.415H147.221V169.831C147.221 169.321 147.089 168.921 146.824 168.633C146.562 168.345 146.199 168.201 145.735 168.201C145.42 168.201 145.14 168.27 144.895 168.409C144.653 168.545 144.462 168.742 144.323 169.001C144.187 169.259 144.119 169.573 144.119 169.941ZM154.718 166.779V168.171H150.204V166.779H154.718ZM151.333 174.415V166.058C151.333 165.544 151.439 165.117 151.651 164.775C151.866 164.434 152.155 164.179 152.516 164.009C152.877 163.84 153.278 163.756 153.719 163.756C154.031 163.756 154.307 163.781 154.549 163.831C154.791 163.88 154.97 163.925 155.086 163.965L154.728 165.357C154.652 165.334 154.556 165.31 154.44 165.287C154.324 165.261 154.195 165.247 154.052 165.247C153.717 165.247 153.48 165.329 153.341 165.491C153.205 165.65 153.137 165.879 153.137 166.177V174.415H151.333ZM156.128 174.415V166.779H157.928V174.415H156.128ZM157.033 165.695C156.748 165.695 156.502 165.6 156.297 165.411C156.091 165.219 155.989 164.989 155.989 164.72C155.989 164.449 156.091 164.218 156.297 164.029C156.502 163.837 156.748 163.741 157.033 163.741C157.321 163.741 157.566 163.837 157.768 164.029C157.974 164.218 158.077 164.449 158.077 164.72C158.077 164.989 157.974 165.219 157.768 165.411C157.566 165.6 157.321 165.695 157.033 165.695ZM163.099 177.438C162.453 177.438 161.898 177.35 161.434 177.174C160.97 177.002 160.597 176.77 160.315 176.478C160.033 176.187 159.838 175.863 159.729 175.509L161.349 175.116C161.422 175.265 161.528 175.413 161.667 175.559C161.807 175.708 161.994 175.83 162.229 175.926C162.468 176.026 162.768 176.076 163.129 176.076C163.64 176.076 164.062 175.951 164.397 175.703C164.732 175.457 164.899 175.053 164.899 174.49V173.043H164.809C164.717 173.228 164.581 173.419 164.402 173.615C164.226 173.81 163.992 173.974 163.701 174.107C163.412 174.239 163.05 174.306 162.612 174.306C162.025 174.306 161.493 174.168 161.016 173.893C160.542 173.615 160.164 173.2 159.883 172.65C159.604 172.097 159.465 171.404 159.465 170.572C159.465 169.733 159.604 169.026 159.883 168.449C160.164 167.869 160.544 167.43 161.021 167.132C161.498 166.83 162.03 166.679 162.617 166.679C163.064 166.679 163.432 166.755 163.721 166.908C164.012 167.057 164.244 167.238 164.417 167.45C164.589 167.659 164.72 167.856 164.809 168.041H164.909V166.779H166.684V174.539C166.684 175.192 166.528 175.733 166.216 176.16C165.905 176.588 165.479 176.907 164.939 177.12C164.398 177.332 163.785 177.438 163.099 177.438ZM163.114 172.894C163.495 172.894 163.82 172.801 164.089 172.615C164.357 172.43 164.561 172.163 164.7 171.815C164.839 171.467 164.909 171.049 164.909 170.562C164.909 170.081 164.839 169.661 164.7 169.299C164.564 168.938 164.362 168.658 164.094 168.459C163.828 168.257 163.502 168.156 163.114 168.156C162.713 168.156 162.378 168.26 162.11 168.469C161.841 168.678 161.639 168.965 161.503 169.329C161.367 169.69 161.3 170.101 161.3 170.562C161.3 171.029 161.367 171.439 161.503 171.79C161.643 172.138 161.846 172.41 162.115 172.605C162.387 172.798 162.72 172.894 163.114 172.894Z"
        fill="white"
      />
      <path
        d="M18.0484 134.632C19.8905 134.273 21.6737 135.514 22.0274 137.381C22.3811 139.249 21.1726 141.057 19.3305 141.415C17.4884 141.774 15.6757 140.564 15.3221 138.696C14.9831 136.828 16.2063 134.991 18.0484 134.632Z"
        fill="#D6DCE8"
        fill-opacity="0.25"
      />
      <path
        d="M12.7128 128.103C13.8918 127.879 15.0266 128.671 15.2476 129.851C15.4687 131.046 14.7023 132.197 13.5381 132.421C12.3592 132.645 11.2097 131.868 10.9886 130.688C10.7676 129.507 11.5486 128.327 12.7128 128.103Z"
        fill="#D6DCE8"
        fill-opacity="0.25"
      />
      <path
        d="M13.053 0.672119C14.8951 0.313537 16.6783 1.55363 17.0319 3.42124C17.3856 5.28885 16.1772 7.0967 14.3351 7.45528C12.4929 7.8288 10.695 6.60365 10.3413 4.73604C9.97292 2.86843 11.2108 1.0307 13.053 0.672119Z"
        fill="#D6DCE8"
        fill-opacity="0.25"
      />
      <path
        d="M138.081 62.2542V52.0986C138.081 47.3175 134.264 43.4478 129.548 43.4478H68.8169C65.9137 43.4478 63.2169 41.9537 61.64 39.4885L58.5452 34.6327C56.9684 32.1674 54.2715 30.6733 51.3683 30.6733H31.7681C27.0522 30.6733 23.2354 34.543 23.2354 39.3241V62.2542H138.081Z"
        fill="url(#paint0_linear_10201_26520)"
        fill-opacity="0.25"
      />
      <path
        opacity="0.25"
        d="M21.4995 46C19.0826 45.5667 21.6591 46.1818 20.2738 45.629C19.6991 45.4049 19.4191 44.7475 19.6401 44.1648C19.8612 43.5821 20.5096 43.2982 21.0843 43.5223C22.7202 44.1648 20.129 43.4601 21.8974 43.7738C22.5016 43.8784 22.9143 44.476 22.7964 45.0886C22.6785 45.7161 22.1037 46.1195 21.4995 46ZM15.0274 42.6259C13.3179 41.2663 11.9327 39.7274 10.9453 38.039C10.6211 37.5012 10.7979 36.8139 11.3284 36.4852C11.859 36.1714 12.5369 36.3358 12.8611 36.8736C13.7158 38.3229 14.9095 39.6526 16.398 40.833C16.8843 41.2214 16.9727 41.9237 16.5896 42.4167C16.2211 42.9247 15.5138 43.0143 15.0274 42.6259ZM10.2674 33.0488C9.66317 33.0488 9.17685 32.6155 9.16211 32.0029C9.16211 31.988 9.16211 31.8685 9.16211 31.8535V31.734C9.16211 29.7468 9.67791 27.7747 10.68 25.8622C10.9748 25.3094 11.6527 25.1002 12.1832 25.3991C12.7285 25.6979 12.9348 26.3851 12.64 26.923C11.8148 28.4918 11.3874 30.1054 11.3874 31.719C11.3874 31.734 11.3874 31.8236 11.3874 31.8386C11.3874 32.4511 10.9011 33.0189 10.2969 33.0189C10.2821 33.0488 10.2821 33.0488 10.2674 33.0488ZM92.4704 29.6721C91.8662 29.6721 91.3651 29.1791 91.3504 28.5665C91.3357 27.939 91.8367 27.431 92.4409 27.4161C94.2241 27.3862 95.9483 26.9977 97.5842 26.2955C98.1442 26.0564 98.8073 26.3254 99.0431 26.8931C99.2789 27.4758 99.0136 28.1332 98.4536 28.3723C96.5231 29.209 94.5336 29.6273 92.4852 29.6721C92.4704 29.6721 92.4704 29.6721 92.4704 29.6721ZM86.5314 28.8355C84.4977 28.2677 82.5082 27.4758 81.0198 26.8334C80.445 26.5943 80.1797 25.9369 80.4155 25.3542C80.6513 24.7715 81.2998 24.5026 81.8745 24.7417C83.304 25.3542 85.2051 26.1162 87.1209 26.6391C87.7104 26.8035 88.0641 27.431 87.9019 28.0286C87.7398 28.6412 87.1356 29.0147 86.5314 28.8355ZM109.669 25.7277C109.61 25.1002 110.052 24.5474 110.656 24.4877C111.658 24.3382 113.235 24.2486 116.05 23.397C116.639 23.2177 117.258 23.5613 117.435 24.159C117.612 24.7566 117.273 25.3841 116.683 25.5634C113.647 26.4748 111.466 26.6391 110.892 26.7288C110.847 26.7437 109.727 26.3403 109.669 25.7277ZM104.879 26.5794C102.86 26.2059 100.782 25.414 99.161 24.144C98.6747 23.7556 98.5863 23.0533 98.9694 22.5603C99.3526 22.0672 100.045 21.9776 100.532 22.3661C100.974 22.7246 101.519 23.0533 102.02 23.3073C103.391 21.9626 104.422 20.4237 105.026 19.0193C105.277 18.4515 105.925 18.1826 106.5 18.4366C107.06 18.6906 107.325 19.348 107.075 19.9307C106.485 21.3052 105.542 22.8143 104.319 24.159C104.628 24.2337 104.938 24.3084 105.262 24.3681C105.866 24.4727 106.279 25.0703 106.161 25.6829C106.073 26.2955 105.483 26.6989 104.879 26.5794ZM75.7586 24.5922C74.0197 23.8751 72.2365 23.2177 70.4533 22.6051C69.8638 22.4109 69.5544 21.7684 69.7459 21.1708C69.9375 20.5731 70.5712 20.2594 71.1607 20.4536C72.9881 21.0811 74.8155 21.7684 76.5839 22.4856C77.1587 22.7246 77.4239 23.382 77.2029 23.9647C76.9818 24.5325 76.3334 24.8313 75.7586 24.5922ZM121.679 23.4717C120.5 23.4717 120.132 21.873 121.178 21.3351C122.814 20.4984 124.391 19.4974 125.85 18.3768C126.336 18.0033 127.043 18.093 127.412 18.6009C127.78 19.094 127.692 19.8112 127.191 20.1847C125.629 21.38 123.934 22.4557 122.18 23.3521C122.018 23.4418 121.856 23.4717 121.679 23.4717ZM14.4085 22.6947C14.0106 22.2166 14.0843 21.4995 14.5559 21.0961C16.118 19.7813 17.8717 18.7802 19.7728 18.0033C20.3475 17.7643 20.9959 18.0481 21.217 18.6308C21.4528 19.2135 21.1728 19.8709 20.598 20.095C18.9327 20.7674 17.3706 21.6489 15.9706 22.8292C15.5138 23.2476 14.8211 23.1878 14.4085 22.6947ZM65.0448 20.9317C63.2322 20.4237 61.3901 19.9755 59.5479 19.587C58.9437 19.4526 58.5606 18.8549 58.6785 18.2424C58.8111 17.6298 59.4006 17.2413 60.0048 17.3609C61.8764 17.7643 63.7775 18.2274 65.6343 18.7354C66.2238 18.8998 66.5775 19.5273 66.4154 20.1249C66.2533 20.7375 65.6491 21.0961 65.0448 20.9317ZM95.6094 18.8549C95.1378 17.1816 95.1378 15.4185 95.7568 13.79C95.9483 13.2671 96.2136 12.774 96.5526 12.3258C96.921 11.8327 97.6136 11.7132 98.1147 12.0867C98.6158 12.4602 98.7189 13.1625 98.3505 13.6705C97.4221 14.9255 97.3189 16.6736 97.761 18.2274C97.9231 18.8251 97.5842 19.4526 96.9947 19.6319C96.3905 19.8112 95.7715 19.4526 95.6094 18.8549ZM24.5918 17.7045C24.4739 17.0919 24.8718 16.4943 25.476 16.3897C28.2465 15.8668 30.855 15.6725 31.2529 15.6277C31.8866 15.5829 32.4024 16.0311 32.4613 16.6586C32.5203 17.2862 32.0634 17.824 31.4445 17.8838L30.6634 17.9585C28.8213 18.1378 27.3034 18.347 25.8886 18.6159C25.2844 18.7205 24.7097 18.3171 24.5918 17.7045ZM53.9921 18.5711C52.1352 18.2872 50.2489 18.0631 48.3773 17.8838C47.7583 17.824 47.3162 17.2712 47.3604 16.6586C47.4194 16.0311 47.9794 15.5829 48.5689 15.6277C50.4847 15.807 52.4152 16.0311 54.3163 16.3299C54.9205 16.4196 55.3479 17.0023 55.2595 17.6148C55.171 18.2424 54.5963 18.6607 53.9921 18.5711ZM42.7477 17.5551C40.8614 17.4953 38.9751 17.4953 37.1035 17.5401C36.4993 17.5551 35.9687 17.062 35.954 16.4345C35.9393 15.807 36.4256 15.2841 37.0445 15.2691C38.9751 15.2094 40.9056 15.2094 42.8214 15.2841C43.4404 15.299 43.9267 15.8219 43.8972 16.4495C43.8678 17.0919 43.3225 17.585 42.7477 17.5551ZM105.572 14.1486C105.13 12.6844 104.024 11.7132 102.388 11.6833C101.769 11.6684 101.283 11.1604 101.298 10.5329C101.313 9.9203 101.814 9.42725 102.418 9.42725H102.433C105.041 9.47207 107.001 11.1006 107.708 13.4912C107.885 14.0888 107.561 14.7163 106.972 14.8956C106.382 15.0749 105.763 14.7462 105.572 14.1486Z"
        fill="url(#paint1_linear_10201_26520)"
      />
      <path
        d="M151.404 77.3337L140.483 123.113C139.083 128.984 133.896 133.123 127.928 133.123H33.4044C27.4506 133.123 22.2632 128.984 20.8485 123.113L9.92834 77.3337C8.10095 69.6691 13.8189 62.3032 21.5853 62.3032H139.747C147.513 62.3032 153.246 69.6691 151.404 77.3337Z"
        fill="url(#paint2_linear_10201_26520)"
        fill-opacity="0.25"
      />
      <path
        opacity="0.25"
        d="M146.774 2.77789L147.747 2.1205C147.57 2.01591 147.379 1.91132 147.143 1.85156C145.993 1.38839 146.612 -0.329809 147.777 0.058654C148.381 0.297708 148.956 0.611467 149.353 1.02981L149.84 0.701112C150.282 0.402294 150.842 0.536762 151.137 0.955107C151.417 1.37345 151.328 1.95615 150.886 2.26991L150.4 2.5986C150.606 3.13648 150.694 3.71917 150.694 4.37657C150.694 4.8995 150.282 5.34773 149.766 5.33279C149.25 5.33279 148.838 4.8995 148.838 4.37657C148.823 4.12258 148.808 3.9134 148.779 3.70423L147.836 4.34669C147.718 4.07775 147.556 3.79387 147.379 3.52494C147.187 3.256 146.996 3.00201 146.774 2.77789Z"
        fill="url(#paint3_linear_10201_26520)"
      />
      <path
        opacity="0.25"
        d="M138.831 16.4933C138.699 16.06 138.654 15.6118 138.64 15.1635C140.909 14.2372 143.753 12.7132 145.537 11.1743C146.553 13.2959 145.654 16.807 143.473 18.3161C141.572 19.6159 139.48 18.8091 138.831 16.4933Z"
        fill="url(#paint4_linear_10201_26520)"
      />
      <path
        opacity="0.25"
        d="M132.376 1.67254C134.557 0.178455 138.139 0.641622 139.671 2.35982C137.579 3.49533 135.117 5.602 133.452 7.36502C133.025 7.20067 132.642 6.97656 132.317 6.72256C130.431 5.24341 130.461 2.98734 132.376 1.67254Z"
        fill="url(#paint5_linear_10201_26520)"
      />
      <path
        opacity="0.25"
        d="M144.225 12.1761C139.215 15.6125 130.888 18.4064 129.238 15.9412C127.587 13.4759 133.261 6.67781 138.272 3.24141C143.282 -0.194992 145.729 1.0451 147.379 3.51035C149.015 5.99053 149.236 8.7546 144.225 12.1761Z"
        fill="url(#paint6_linear_10201_26520)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_10201_26520"
          x1="80.4422"
          y1="48.7919"
          x2="80.8528"
          y2="76.9439"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#989FB0" />
          <stop offset="1" stop-color="#AAB2C5" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_10201_26520"
          x1="9.15767"
          y1="30.5202"
          x2="133.656"
          y2="30.5202"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#D6DCE8" />
          <stop offset="1" stop-color="white" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_10201_26520"
          x1="80.5546"
          y1="53.6315"
          x2="80.7954"
          y2="96.3922"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#D6DCE8" />
          <stop offset="1" stop-color="white" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_10201_26520"
          x1="147.545"
          y1="-0.253742"
          x2="150.944"
          y2="4.70333"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#989FB0" />
          <stop offset="1" stop-color="#AAB2C5" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_10201_26520"
          x1="146.611"
          y1="12.8544"
          x2="139.334"
          y2="17.7088"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#D6DCE8" />
          <stop offset="1" stop-color="white" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_10201_26520"
          x1="138.465"
          y1="0.644585"
          x2="131.188"
          y2="5.49897"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#D6DCE8" />
          <stop offset="1" stop-color="white" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_10201_26520"
          x1="135.323"
          y1="5.26013"
          x2="141.4"
          y2="14.1222"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#989FB0" />
          <stop offset="1" stop-color="#AAB2C5" />
        </linearGradient>
      </defs>
    </svg>
  </div>
</ng-template>

<ng-template #addFieldBtn>
  <button
    (click)="handleAddField()"
    class="text-white text-sm font-medium flex gap-2 items-center py-2 px-3 rounded-lg bg-brand-1"
  >
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.16699 10.415H15.8337"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10 16.2482V4.58154"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
    Thêm trường
  </button></ng-template
>

<ng-template #tooltipName>Tên trường thông tin cần bóc tách</ng-template>

<ng-template #tooltipPrewords
  >Keywords đứng TRƯỚC trường thông tin cần bóc tách</ng-template
>

<ng-template #tooltipSufwords
  >Keywords đứng SAU trường thông tin cần bóc tách</ng-template
>

<ng-template #tooltipDatatype
  >Loại dữ liệu của trường thông tin bóc tách: Date, Number, Text, Tittle or
  uppercase</ng-template
>

<ng-template #tooltipMultilines>
  <div>True: trường thông tin bóc tách nằm trên nhiều dòng</div>
  <div>False: trường thông tin bóc tách nằm trong 1 dòng</div>
</ng-template>

<ng-template #tooltipModel>
  <div>Model sử dụng để bóc tách thông tin. Trong đó:</div>
  <div>- Default: Bóc tách cho chữ in</div>
  <div>- Special: Bóc tách con dấu</div>
  <div>- Number Handwriting: Bóc tách số viết tay</div>
  <div>- Handwriting: Bóc tách chữ viết tay</div>
</ng-template>

<ng-template #chevron>
  <img src="assets/kie/config-fields/chevron.svg" />
</ng-template>

<ng-template #clear>
  <img class="bg-[#2d2d3d]" src="assets/kie/config-fields/clear.svg" />
</ng-template>
