import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { KIEService } from '@platform/app/core/services/kie.service';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, Subject, catchError, switchMap, takeUntil, tap } from 'rxjs';
import { Document, Folder, Role } from '../kie';
import { DocumentLayoutService } from '../services/document-layout.service';
@Component({
  selector: 'app-manage-document',
  templateUrl: './manage-document.component.html',
  styleUrls: ['./manage-document.component.scss']
})
export class ManageDocumentComponent implements OnInit, OnDestroy {
  constructor(
    private route: ActivatedRoute,
    private kieService: KIEService,
    private toastr: ToastrService,
    private documentLayoutService: DocumentLayoutService
  ) {}

  document: Document;
  documentId: string;
  folder: Folder;
  destroyed$ = new Subject<void>();
  isSharedDocument = false;

  ngOnInit(): void {
    this.route.params
      .pipe(
        takeUntil(this.destroyed$),
        tap((params) => {
          this.documentLayoutService.selectedDocumentId$.next(params['id']);
          this.documentId = params['id'];
        }),
        switchMap(this.getDocumentDetail.bind(this))
      )
      .subscribe();
  }

  getDocumentDetail() {
    return this.kieService.getDocumentDetail(this.documentId).pipe(
      /* nested catchError() here to prevent inner error from kieService.getDocumentDetail cancel the outer route.params observable */
      catchError((err) => {
        /* temp error handling */
        this.toastr.error(
          'Đã có lỗi xảy ra khi load chi tiết văn bản này! Hãy thử lại sau'
        );
        console.log('error fetch doc detail', err);
        return EMPTY;
      }),
      tap((resp) => {
        this.document = resp.data;
        this.folder = this.documentLayoutService.folders.find(
          (folder) => folder.id === this.document.folderId
        );
        this.isSharedDocument = this.document.myRole !== Role.Creator;
        if (this.isSharedDocument)
          this.documentLayoutService.selectedDocumentId$.next('shared');
      })
    );
  }

  handleReloadDocDetail() {
    this.getDocumentDetail().subscribe();
  }

  checkTabPermission(tab: 'file-tab' | 'config-tab' | 'permission-tab'): boolean {
    if (!this.document) return true;

    switch (this.document.myRole) {
      case Role.Creator:
        return true; // creator => full access to any tab

      case Role.Editor:
        return ['file-tab', 'config-tab'].includes(tab);

      case Role.Viewer:
        return ['file-tab'].includes(tab);

      default:
        return false;
    }
  }

  ngOnDestroy(): void {
    console.log('STOP listen to doc id change');
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
