<div class="py-3 px-4 bg-[#222233] text-text-2 font-bold text-base"><PERSON><PERSON><PERSON> quả OCR</div>
<div class="overflow-auto flex-1">
  <ng-container *ngIf="ocrResultFieldList?.length; then fieldList; else empty">
  </ng-container>
</div>
<div
  class="py-3 px-4 bg-[#222233] border-t border-[#E7EBEF1A] text-text-2 font-medium flex gap-4 items-center justify-center"
  *ngIf="actionsTemplate"
>
  <ng-container [ngTemplateOutlet]="actionsTemplate"></ng-container>
</div>

<nz-drawer
  [nzHeight]="tableDrawerHeight"
  [nzBodyStyle]="{
    background: '#222233',
    padding: 0,
    color: '#fff'
  }"
  [nzMaskStyle]="{ background: 'transparent' }"
  [nzClosable]="false"
  [nzPlacement]="'bottom'"
  [nzVisible]="displayingTable<PERSON>ield"
  [nzTitle]="null"
  (nzOnClose)="hideTableDrawer()"
>
  <ng-container *nzDrawerContent>
    <div
      class="h-full py-4 px-8"
      nz-resizable
      nzBounds="window"
      [nzMinHeight]="25"
      (nzResize)="handleTableDrawerResize($event)"
    >
      <nz-resize-handle
        nzDirection="top"
        class="bg-brand-1 bg-opacity-25 hover:bg-opacity-75 hover:cursor-grab active:bg-opacity-75"
      ></nz-resize-handle>
      <div class="font-bold py-2">{{ displayingTableField?.name }}</div>
      <nz-table
        #table
        [nzFrontPagination]="false"
        [nzShowPagination]="false"
        [nzData]="displayingTableField?.table?.rows || []"
        [nzNoResult]="null"
      >
        <thead>
          <tr>
            <th
              class="!bg-[#272836] !p-2 !border-y !border-[#E7EBEF1A] !font-semibold !text-white !rounded-none"
              nzEllipsis
              nz-tooltip
              nzTooltipColor="#000"
              [nzTooltipTitle]="header.colId"
              *ngFor="let header of displayingTableField?.table?.headers"
            >
              {{ header.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-container
            *ngIf="
              !!displayingTableField?.table?.rows[0];
              then tableFieldContent;
              else tableFieldNoContent
            "
          ></ng-container>
          <ng-template #tableFieldContent>
            <tr *ngFor="let row of table.data">
              <td
                *ngFor="let col of displayingTableField?.table?.headers"
                class="!p-2 !bg-[#1C1C26] text-white !border-none"
                [ngClass]="{
                  '!text-status-success': row[col.colId]?.isEdited,
                  '!opacity-50': !row[col.colId]
                }"
              >
                {{ row[col.colId] ? row[col.colId].text : '[NULL]' }}
              </td>
            </tr>
          </ng-template>
          <ng-template #tableFieldNoContent>
            <tr>
              <td
                class="!p-2 !bg-[#1C1C26] text-white text-center !border-none"
                [colSpan]="displayingTableField?.table?.headers?.length"
              >
                Không có dữ liệu
              </td>
            </tr>
          </ng-template>
        </tbody>
      </nz-table>
    </div>
  </ng-container>
</nz-drawer>

<ng-template #fieldList>
  <ng-container *ngFor="let field of ocrResultFieldList">
    <div *ngIf="field.isVisible" class="p-4 pb-0 hover:bg-[#FFFFFF0D]">
      <div class="flex gap-2 items-baseline mb-2">
        <div
          class="shrink-0 w-3 h-3 rounded-sm bg-status-success"
          [ngStyle]="{ background: field.color }"
        ></div>
        <div
          class="font-semibold break-words truncate"
          nz-tooltip
          nzTooltipColor="#000"
          [nzTooltipTitle]="field.name"
        >
          {{ field.name }}
        </div>
      </div>
      <ng-container *ngIf="field.isNew">
        <ng-template
          *ngTemplateOutlet="
            textWithScore;
            context: { field: { score: 'Mới', scoreBgColor: ScoreBgColor.Medium } }
          "
        ></ng-template>
      </ng-container>
      <ng-container *ngIf="!field.isNew" [ngSwitch]="field.type">
        <ng-container *ngSwitchCase="'Field'">
          <ng-template
            *ngTemplateOutlet="textWithScore; context: { field: field }"
          ></ng-template>
        </ng-container>

        <div *ngSwitchCase="'List'" class="flex flex-col gap-1">
          <ng-container *ngFor="let i of field.list">
            <ng-template
              *ngTemplateOutlet="textWithScore; context: { field: i }"
            ></ng-template>
          </ng-container>
        </div>

        <ng-container *ngSwitchCase="'Table'">
          <div class="flex gap-3 items-baseline">
            <div class="self-center shrink-0 text-center" *ngIf="field.isEdited">
              <ng-container [ngTemplateOutlet]="checkedSvg"></ng-container>
            </div>
            <div
              *ngIf="!field.isEdited"
              class="w-11 h-6 my-2 shrink-0 p-1 rounded-[4px] font-semibold text-xs text-center bg-status-error"
              [ngStyle]="{ background: field.scoreBgColor }"
            >
              {{ field.score }}
            </div>
            <div
              class="flex items-center gap-2 justify-between w-full h-full px-4 py-2 rounded-lg bg-[#1F1F28] border border-[#E7EBEF1A]"
            >
              Xem chi tiết bảng...
              <button (click)="showTableDrawer(field)">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12.9833 9.99993C12.9833 11.6499 11.6499 12.9833 9.99993 12.9833C8.34993 12.9833 7.0166 11.6499 7.0166 9.99993C7.0166 8.34993 8.34993 7.0166 9.99993 7.0166C11.6499 7.0166 12.9833 8.34993 12.9833 9.99993Z"
                    stroke="#989BB3"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M9.99987 16.8913C12.9415 16.8913 15.6832 15.1579 17.5915 12.1579C18.3415 10.9829 18.3415 9.00794 17.5915 7.83294C15.6832 4.83294 12.9415 3.09961 9.99987 3.09961C7.0582 3.09961 4.31654 4.83294 2.4082 7.83294C1.6582 9.00794 1.6582 10.9829 2.4082 12.1579C4.31654 15.1579 7.0582 16.8913 9.99987 16.8913Z"
                    stroke="#6C7093"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
          </div>
        </ng-container>

        <ng-container *ngSwitchDefault>
          new type of field: {{ field.type }}, yet to be handle
        </ng-container>
      </ng-container>
      <div class="mt-4 border border-[#E7EBEF1A]"></div>
    </div>
  </ng-container>
</ng-template>

<ng-template #empty>
  <div class="flex items-center justify-center flex-col gap-4 h-full">
    <svg
      width="200"
      height="126"
      viewBox="0 0 200 126"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_9479_20284)">
        <path
          d="M170.899 0H28.5261C25.8103 0 23.6191 2.18248 23.6191 4.86704V100.066H175.806V4.86704C175.806 2.18248 173.605 0 170.899 0Z"
          fill="white"
          fill-opacity="0.05"
        />
        <path
          d="M31.3345 87.0433V93.3038H168.101V6.7627H31.3345V87.0433Z"
          fill="#F0F1F4"
          fill-opacity="0.1"
        />
        <mask
          id="mask0_9479_20284"
          style="mask-type: luminance"
          maskUnits="userSpaceOnUse"
          x="31"
          y="6"
          width="138"
          height="88"
        >
          <path
            d="M31.3345 87.0433V93.3038H168.101V6.7627H31.3345V87.0433Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_9479_20284)">
          <path
            d="M175.272 1.45508H24.2983V101.255H175.272V1.45508Z"
            fill="#F0F1F4"
            fill-opacity="0.1"
          />
        </g>
        <g opacity="0.3">
          <path
            d="M140.337 51.0884H87.4814V53.0352H140.337V51.0884Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M83.963 51.0884H37.6094V53.0352H83.963V51.0884Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 51.0884H143.854V53.0352H161.795V51.0884Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M56.692 54.9717H37.5889V56.9185H56.692V54.9717Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M97.3772 54.9717H60.21V56.9185H97.3772V54.9717Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M146.704 54.9717H100.896V56.9185H146.704V54.9717Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 54.9717H150.212V56.9185H161.795V54.9717Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M64.1913 58.855H37.5889V60.8018H64.1913V58.855Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M124.875 58.855H94.5278V60.8018H124.875V58.855Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 58.855H128.393V60.8018H161.795V58.855Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M91.0097 58.855H67.7095V60.8018H91.0097V58.855Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M49.6453 62.7388H37.5889V64.6856H49.6453V62.7388Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M100.33 62.7388H53.1636V64.6856H100.33V62.7388Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 62.7388H103.848V64.6856H161.795V62.7388Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M68.7382 66.6221H37.5889V68.5689H68.7382V66.6221Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 66.6221H152.938V68.5484C152.938 68.5586 152.938 68.5586 152.938 68.5689H161.795V66.6221Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M128.619 66.6221V68.5689H149.43C149.43 68.5586 149.43 68.5586 149.43 68.5484V66.6221H128.619Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M125.111 66.6221H72.2563V68.5689H125.111V66.6221Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M55.2312 70.6181C55.2312 70.5771 55.2415 70.5464 55.2518 70.5054H37.5889V72.4522H55.2312V70.6181Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M107.665 72.4522V70.5054H58.729C58.7393 70.5361 58.7496 70.5771 58.7496 70.6181V72.4522H107.665Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 70.5054H111.183V72.4522H161.795V70.5054Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M73.8303 74.5424C73.8303 74.4911 73.8406 74.4297 73.8612 74.3887H37.5889V76.3355H73.8303V74.5424Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M77.338 76.3355H98.1076V74.3887H77.3071C77.3277 74.4399 77.338 74.4911 77.338 74.5424V76.3355Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M136.859 74.3887H101.626V76.3355H136.859V74.3887Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 74.3887H140.367V76.3355H161.795V74.3887Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M52.9063 78.4769C52.9063 78.4052 52.9269 78.3335 52.9578 78.272H37.5889V80.2188H52.9063V78.4769Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M102.758 80.2188V78.4769C102.758 78.4052 102.778 78.3335 102.809 78.272H56.3628C56.3937 78.3335 56.4142 78.4052 56.4142 78.4769V80.2188H102.758Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M134.267 78.272H106.214C106.245 78.3335 106.265 78.4052 106.265 78.4769V80.2188H134.267V78.272Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 78.272H137.785V80.2188H161.795V78.272Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M82.3583 82.1553H37.5889V84.1021H82.3583V82.1553Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M151.323 82.1553H122.55V84.1021H151.323V82.1553Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M162.135 82.1553H154.841V84.1021H162.135V82.1553Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M119.032 82.1553H85.8662V84.1021H119.032V82.1553Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M64.7879 86.0391H37.2905V87.9859H64.7879V86.0391Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M101.986 86.0391H68.3062V87.9859H101.986V86.0391Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M139.431 86.0391H105.494V87.9859H139.431V86.0391Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M162.124 86.0391H142.949V87.9859H162.124V86.0391Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
        </g>
        <g opacity="0.3">
          <path
            d="M140.337 12.0806H87.4814V14.0274H140.337V12.0806Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M83.963 12.0806H37.6094V14.0274H83.963V12.0806Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 12.0806H143.854V14.0274H161.795V12.0806Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M56.692 15.9639H37.5889V17.9107H56.692V15.9639Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M97.3772 15.9639H60.21V17.9107H97.3772V15.9639Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M146.704 15.9639H100.896V17.9107H146.704V15.9639Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 15.9639H150.212V17.9107H161.795V15.9639Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M64.1913 19.8472H37.5889V21.794H64.1913V19.8472Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M124.875 19.8472H94.5278V21.794H124.875V19.8472Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 19.8472H128.393V21.794H161.795V19.8472Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M91.0097 19.8472H67.7095V21.794H91.0097V19.8472Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M49.6453 23.731H37.5889V25.6778H49.6453V23.731Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M100.33 23.731H53.1636V25.6778H100.33V23.731Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 23.731H103.848V25.6778H161.795V23.731Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M68.7382 27.6143H37.5889V29.5611H68.7382V27.6143Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M152.938 27.6143V29.5406C152.938 29.5508 152.938 29.5508 152.938 29.5611H161.795V27.6143H152.938Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M149.43 29.5406V27.6143H128.629V29.5611H149.44C149.43 29.5611 149.43 29.5508 149.43 29.5406Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M125.111 27.6143H72.2563V29.5611H125.111V27.6143Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M55.2312 31.6103C55.2312 31.5693 55.2415 31.5385 55.2518 31.4976H37.5889V33.4444H55.2312V31.6103Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M107.665 33.4444V31.4976H58.729C58.7393 31.5283 58.7496 31.5693 58.7496 31.6103V33.4444H107.665Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 31.5078H111.183V33.4546H161.795V31.5078Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M73.8303 35.5448C73.8303 35.4936 73.8406 35.4321 73.8612 35.3911H37.5889V37.3379H73.8303V35.5448Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M77.3071 35.3911C77.3277 35.4423 77.338 35.4936 77.338 35.5448V37.3277H98.1076V35.3809H77.3071V35.3911Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M136.859 35.3911H101.626V37.3379H136.859V35.3911Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 35.3911H140.367V37.3379H161.795V35.3911Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M52.9063 39.4793C52.9063 39.4076 52.9269 39.3359 52.9578 39.2744H37.5889V41.2212H52.9063V39.4793Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M102.758 41.211V39.4691C102.758 39.3974 102.778 39.3256 102.809 39.2642H56.3628C56.3937 39.3256 56.4142 39.3974 56.4142 39.4691V41.211H102.758Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M134.267 41.211V39.2642H106.214C106.245 39.3256 106.265 39.3974 106.265 39.4691V41.211H134.267Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M161.795 39.2744H137.785V41.2212H161.795V39.2744Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M82.3583 43.1577H37.5889V45.1045H82.3583V43.1577Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M151.323 43.1577H122.55V45.1045H151.323V43.1577Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M162.135 43.1577H154.841V45.1045H162.135V43.1577Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M119.032 43.1577H85.8662V45.1045H119.032V43.1577Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M64.7879 47.0415H37.2905V48.9883H64.7879V47.0415Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M101.986 47.0415H68.3062V48.9883H101.986V47.0415Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M139.431 47.0415H105.494V48.9883H139.431V47.0415Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
          <path
            d="M162.124 47.0415H142.949V48.9883H162.124V47.0415Z"
            fill="#CBCBCB"
            fill-opacity="0.3"
          />
        </g>
        <path
          d="M0 128.727C0 129.278 0.226409 129.706 0.493984 129.706H198.849C199.127 129.706 199.343 129.257 199.343 128.727V121.368H0V128.727Z"
          fill="#E2E2E2"
          fill-opacity="0.1"
        />
        <path
          d="M88.2842 122.383C88.2842 123.018 88.7985 123.52 89.426 123.52H109.866C110.494 123.52 111.008 123.008 111.008 122.383V121.932H88.2842V122.383Z"
          fill="#E2E2E2"
          fill-opacity="0.1"
        />
        <path
          d="M23.6089 98.3447L0 121.809H199.302L175.693 98.3447H23.6089Z"
          fill="#EEEEEE"
          fill-opacity="0.1"
        />
        <path
          d="M15.6055 112.392H29.3285L38.988 100.066H27.1579L15.6055 112.392Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M160.314 100.066L169.974 112.392H183.697L172.144 100.066H160.314Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M119.69 115.456C119.443 114.339 118.23 113.448 116.975 113.448H82.3276C81.0623 113.448 79.8484 114.339 79.6118 115.456L78.9534 118.499C78.686 119.719 79.5707 120.733 80.9388 120.733H118.384C119.752 120.733 120.637 119.719 120.369 118.499L119.69 115.456Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M33.3711 112.392H41.0658L42.5369 110.138H35.0376L33.3711 112.392Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M40.3457 102.853H47.4129L48.6268 100.999H41.7139L40.3457 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M42.0327 100.732H51.9495L52.3404 100.066H42.4956L42.0327 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M53.1118 100.732H60.1482L60.4774 100.066H53.5027L53.1118 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M61.2798 100.732H68.3264L68.5939 100.066H61.609L61.2798 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M69.3555 100.732H76.3918L76.5873 100.066H69.6126L69.3555 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M77.5337 100.732H84.5701L84.6935 100.066H77.7189L77.5337 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M85.5576 100.732H92.6043L92.666 100.066H85.6811L85.5576 100.732Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M93.6022 100.066L93.5508 100.732H100.597L100.587 100.066H93.6022Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M101.791 100.066L101.811 100.732H108.847L108.765 100.066H101.791Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M109.567 100.066L109.65 100.732H116.696L116.552 100.066H109.567Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M117.499 100.066L117.653 100.732H124.69L124.484 100.066H117.499Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M125.791 100.066L126.017 100.732H133.053L132.775 100.066H125.791Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M133.896 100.066L134.195 100.732H141.231L140.881 100.066H133.896Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M141.9 100.066L142.26 100.732H149.296L148.885 100.066H141.9Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M150.212 100.066L150.623 100.732H157.269L156.806 100.066H150.212Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M37.1772 107.228H50.0361L51.1986 105.24H38.6483L37.1772 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M38.9775 104.974H49.7173L50.8386 103.119H40.3457L38.9775 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M35.2949 109.739H54.1512L55.2622 107.618H36.8688L35.2949 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M146.93 105.24L148.062 107.228H162.093L160.633 105.24H146.93Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M144.019 107.618L145.12 109.739H163.976L162.412 107.618H144.019Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M42.5161 112.392H50.2006L51.4453 110.138H43.9563L42.5161 112.392Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M55.5811 109.739H63.0289L63.9239 107.618H56.6612L55.5811 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M64.7056 109.739H72.1534L72.8221 107.618H65.5594L64.7056 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M73.4805 109.739H80.9283L81.381 107.618H74.1183L73.4805 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M82.605 109.739H90.0528L90.2894 107.618H83.0165L82.605 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M91.5239 109.739H98.9821V107.618H91.7194L91.5239 109.739Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M100.597 107.618L100.618 109.739H108.066L107.86 107.618H100.597Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M109.177 107.618L109.424 109.739H116.861L116.45 107.618H109.177Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M118.085 107.618L118.548 109.739H125.996L125.348 107.618H118.085Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M126.603 107.618L127.282 109.739H134.73L133.876 107.618H126.603Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M135.512 107.618L136.407 109.739H143.855L142.774 107.618H135.512Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M51.7334 107.228H58.9138L59.8705 105.24H52.8547L51.7334 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M60.6011 107.228H67.7815L68.5221 105.24H61.5166L60.6011 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M69.1187 107.228H76.299L76.8443 105.24H69.8387L69.1187 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M77.9863 107.228H85.1667L85.5062 105.24H78.4904L77.9863 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M86.6479 107.228H93.8283L93.9621 105.24H86.9566L86.6479 107.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M95.6185 105.24L95.5156 107.228H102.696L102.624 105.24H95.6185Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M103.94 105.24L104.043 107.228H111.213L110.946 105.24H103.94Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M112.592 105.24L112.9 107.228H120.081L119.608 105.24H112.592Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M120.883 105.24L121.387 107.228H128.568L127.889 105.24H120.883Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M129.535 105.24L130.255 107.228H137.435L136.55 105.24H129.535Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M138.228 105.24L139.153 107.228H146.334L145.233 105.24H138.228Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M51.5073 104.974H58.3894L59.3152 103.119H52.5875L51.5073 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M60.1279 104.974H67.01L67.7507 103.119H61.0229L60.1279 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M68.4087 104.974H75.301L75.8463 103.119H69.1185L68.4087 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M77.0293 104.974H83.9216L84.2714 103.119H77.5437L77.0293 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M85.4546 104.974H92.3469L92.5115 103.119H85.7838L85.4546 104.974Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M94.2397 103.119L94.1162 104.974H100.998L100.967 103.119H94.2397Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M102.305 103.119L102.366 104.974H109.248L109.032 103.119H102.305Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M110.74 103.119L110.987 104.974H117.879L117.458 103.119H110.74Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M119.145 103.119L119.587 104.974H126.48L125.873 103.119H119.145Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M127.23 103.119L127.858 104.974H134.75L133.958 103.119H127.23Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M135.676 103.119L136.51 104.974H143.392L142.404 103.119H135.676Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M143.947 103.119L144.966 104.974H151.848L150.675 103.119H143.947Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M152.238 103.119L153.452 104.974H160.345L158.966 103.119H152.238Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M48.5137 102.853H55.5912L56.6096 100.999H49.6967L48.5137 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M56.9082 102.853H63.9857L64.8087 100.999H57.8958L56.9082 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M64.9834 102.853H72.0506L72.6884 100.999H65.7858L64.9834 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M73.3774 102.853H80.4447L80.8973 100.999H73.9844L73.3774 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M81.5762 102.853H88.6537L88.9109 100.999H81.9979L81.5762 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M89.981 102.853H97.0482L97.1099 100.999H90.197L89.981 102.853Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M98.0768 100.999L98.0459 102.853H105.113L104.99 100.999H98.0768Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M106.276 100.999L106.44 102.853H113.508L113.189 100.999H106.276Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M114.125 100.999L114.475 102.853H121.542L121.038 100.999H114.125Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M122.334 100.999L122.869 102.853H129.946L129.247 100.999H122.334Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M130.553 100.999L131.294 102.853H138.361L137.466 100.999H130.553Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M138.598 100.999L139.523 102.853H146.601L145.511 100.999H138.598Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M146.817 100.999L147.898 102.853H158.792L157.475 100.999H146.817Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M51.7954 112.392H59.4902L60.4983 110.138H52.999L51.7954 112.392Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M129.658 110.138L130.43 112.392H138.114L137.157 110.138H129.658Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M138.701 110.138L139.709 112.392H147.393L146.2 110.138H138.701Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M156.662 110.005L158.133 112.259H165.828L164.161 110.005H156.662Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M147.681 110.005L148.226 110.937H155.757L155.129 110.005H147.681Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M148.247 111.337L148.833 112.392H156.806L156.127 111.337H148.247Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M60.9404 112.392H70.9703L71.6904 110.138H61.9074L60.9404 112.392Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M118.559 110.138L119.042 112.392H129.072L128.342 110.138H118.559Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M73.5627 110.138L72.894 112.392H117.447L117.005 110.138H73.5627Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M155.551 98.3447H43.7304V98.355C43.4424 98.7136 43.6481 99.0107 44.2036 99.0107H155.078C155.633 99.0107 155.839 98.7136 155.551 98.3447Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <g opacity="0.5">
          <path
            d="M98.5712 80.352C114.098 80.352 126.686 67.8145 126.686 52.3486C126.686 36.8828 114.098 24.3452 98.5712 24.3452C83.0439 24.3452 70.4565 36.8828 70.4565 52.3486C70.4565 67.8145 83.0439 80.352 98.5712 80.352Z"
            fill="#F0F1F4"
            fill-opacity="0.15"
          />
          <mask
            id="mask1_9479_20284"
            style="mask-type: luminance"
            maskUnits="userSpaceOnUse"
            x="71"
            y="24"
            width="57"
            height="57"
          >
            <path
              d="M99.5712 80.352C115.098 80.352 127.686 67.8145 127.686 52.3486C127.686 36.8828 115.098 24.3452 99.5712 24.3452C84.0439 24.3452 71.4565 36.8828 71.4565 52.3486C71.4565 67.8145 84.0439 80.352 99.5712 80.352Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask1_9479_20284)">
            <g opacity="0.3">
              <path
                d="M129.021 51.0879H87.4814V53.0347H129.021V51.0879Z"
                fill="#757575"
              />
              <path
                d="M83.9635 51.0879H62.2988V53.0347H83.9635V51.0879Z"
                fill="#757575"
              />
              <path d="M97.3779 54.9712H67.4116V56.918H97.3779V54.9712Z" fill="#757575" />
              <path d="M135.389 54.9712H100.896V56.918H135.389V54.9712Z" fill="#757575" />
              <path
                d="M124.875 58.8545H94.5283V60.8013H124.875V58.8545Z"
                fill="#757575"
              />
              <path d="M91.0102 58.8545H67.71V60.8013H91.0102V58.8545Z" fill="#757575" />
              <path d="M100.33 62.7383H60.3647V64.6851H100.33V62.7383Z" fill="#757575" />
              <path
                d="M137.106 62.7383H103.848V64.6851H137.106V62.7383Z"
                fill="#757575"
              />
              <path
                d="M125.112 66.6216H72.2568V68.5684H125.112V66.6216Z"
                fill="#757575"
              />
              <path
                d="M107.665 72.4517V70.5049H65.9302C65.9405 70.5356 65.9507 70.5766 65.9507 70.6176V72.4517H107.665Z"
                fill="#757575"
              />
              <path
                d="M137.107 70.5049H111.183V72.4517H137.107V70.5049Z"
                fill="#757575"
              />
              <path
                d="M73.8307 74.5419C73.8307 74.4906 73.841 74.4292 73.8616 74.3882H62.2783V76.335H73.8307V74.5419Z"
                fill="#757575"
              />
              <path
                d="M77.3385 76.335H98.1081V74.3882H77.3076C77.3282 74.4394 77.3385 74.4906 77.3385 74.5419V76.335Z"
                fill="#757575"
              />
              <path d="M136.86 74.3882H101.626V76.335H136.86V74.3882Z" fill="#757575" />
              <path
                d="M102.758 80.2183V78.4764C102.758 78.4047 102.778 78.333 102.809 78.2715H63.564C63.5948 78.333 63.6154 78.4047 63.6154 78.4764V80.2183H102.758Z"
                fill="#757575"
              />
              <path
                d="M134.267 78.2715H106.214C106.245 78.333 106.266 78.4047 106.266 78.4764V80.2183H134.267V78.2715Z"
                fill="#757575"
              />
            </g>
            <g opacity="0.3">
              <path d="M100.33 23.7305H60.3647V25.6773H100.33V23.7305Z" fill="#757575" />
              <path
                d="M137.106 23.7305H103.848V25.6773H137.106V23.7305Z"
                fill="#757575"
              />
              <path
                d="M125.112 27.6138H72.2568V29.5606H125.112V27.6138Z"
                fill="#757575"
              />
              <path
                d="M107.665 33.4439V31.4971H65.9302C65.9405 31.5278 65.9507 31.5688 65.9507 31.6098V33.4439H107.665Z"
                fill="#757575"
              />
              <path
                d="M137.107 31.5073H111.183V33.4541H137.107V31.5073Z"
                fill="#757575"
              />
              <path
                d="M73.8307 35.5443C73.8307 35.4931 73.841 35.4316 73.8616 35.3906H62.2783V37.3374H73.8307V35.5443Z"
                fill="#757575"
              />
              <path
                d="M77.3076 35.3906C77.3282 35.4419 77.3385 35.4931 77.3385 35.5443V37.3272H98.1081V35.3804H77.3076V35.3906Z"
                fill="#757575"
              />
              <path d="M136.86 35.3906H101.626V37.3374H136.86V35.3906Z" fill="#757575" />
              <path
                d="M102.758 41.2105V39.4686C102.758 39.3969 102.778 39.3252 102.809 39.2637H63.564C63.5948 39.3252 63.6154 39.3969 63.6154 39.4686V41.2105H102.758Z"
                fill="#757575"
              />
              <path
                d="M134.267 41.2105V39.2637H106.214C106.245 39.3252 106.266 39.3969 106.266 39.4686V41.2105H134.267Z"
                fill="#757575"
              />
              <path d="M82.3587 43.1572H62.2783V45.104H82.3587V43.1572Z" fill="#757575" />
              <path d="M140.007 43.1572H122.55V45.104H140.007V43.1572Z" fill="#757575" />
              <path d="M119.032 43.1572H85.8662V45.104H119.032V43.1572Z" fill="#757575" />
              <path d="M101.986 47.041H68.3062V48.9878H101.986V47.041Z" fill="#757575" />
              <path d="M128.115 47.041H105.494V48.9878H128.115V47.041Z" fill="#757575" />
            </g>
          </g>
        </g>
        <path
          d="M146.79 45.8615C145.294 38.9136 141.176 32.9723 135.184 29.1217C129.201 25.2693 122.065 23.9712 115.093 25.4669C108.121 26.9627 102.157 31.0713 98.2899 37.0458C94.4206 43.012 93.1132 50.126 94.609 57.0738C96.1048 64.0217 100.223 69.9631 106.214 73.8137C111.274 77.0719 117.155 78.5002 123.056 77.9566C124.135 77.8557 125.227 77.6912 126.304 77.4601C133.276 75.9644 139.239 71.8557 143.107 65.8812C146.976 59.9151 148.285 52.8094 146.79 45.8615ZM108.857 69.7385C103.961 66.5931 100.59 61.7276 99.3664 56.0445C98.1447 50.3697 99.2073 44.553 102.375 39.6689C105.534 34.7865 110.418 31.4231 116.121 30.1997C121.823 28.9762 127.65 30.033 132.548 33.1867C137.444 36.3322 140.815 41.1976 142.039 46.8808C143.262 52.5639 142.198 58.3722 139.03 63.2564C135.871 68.1387 130.987 71.5021 125.284 72.7256C119.582 73.949 113.755 72.8922 108.857 69.7385Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M142.195 61.538L138.764 66.8286L145.292 71.027L148.723 65.7364L142.195 61.538Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M178.637 88.7261C176.676 91.7496 172.635 92.6166 169.602 90.6643L148.055 76.7974C145.023 74.8451 144.156 70.8178 146.117 67.7943C148.078 64.7708 152.119 63.9038 155.151 65.8561L176.698 79.723C179.722 81.6771 180.589 85.7044 178.637 88.7261Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M114.341 37.6175C114.29 37.609 114.239 37.609 114.188 37.6089C112.812 37.6088 111.446 37.6086 110.071 37.6084C109.729 37.6083 109.396 37.6593 109.063 37.7699C107.662 38.2291 106.783 39.471 106.774 40.9939C106.775 41.6234 106.775 42.2444 106.775 42.874C106.775 43.5971 106.775 44.3202 106.775 45.0434C106.775 45.137 106.784 45.239 106.809 45.3241C106.869 45.5283 107.065 45.6474 107.279 45.6219C107.475 45.5965 107.629 45.4518 107.655 45.2477C107.663 45.1626 107.663 45.0775 107.663 44.9839C107.663 43.6483 107.663 42.3211 107.663 40.9855C107.663 40.7728 107.68 40.5516 107.731 40.3389C108.004 39.2245 108.952 38.4845 110.114 38.4846C111.412 38.4848 112.719 38.485 114.017 38.4852C114.128 38.4852 114.248 38.4852 114.359 38.4682C114.572 38.4257 114.709 38.2471 114.7 38.0344C114.7 37.8217 114.555 37.6515 114.341 37.6175Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M133.844 38.7997C133.194 38.0425 132.374 37.634 131.375 37.6169C130.521 37.6082 129.658 37.6081 128.804 37.608C128.48 37.6079 128.155 37.5994 127.831 37.6078C127.574 37.6163 127.395 37.795 127.387 38.0332C127.378 38.2714 127.549 38.4501 127.805 38.4841C127.874 38.4926 127.942 38.4926 128.019 38.4927C129.112 38.4928 130.205 38.493 131.298 38.4931C132.101 38.4932 132.75 38.8251 133.237 39.4547C133.553 39.8631 133.724 40.3396 133.724 40.8585C133.733 42.2112 133.725 43.5554 133.725 44.9081C133.725 45.0102 133.716 45.1208 133.734 45.2229C133.759 45.4356 133.913 45.5972 134.118 45.6228C134.34 45.6483 134.528 45.5208 134.588 45.2996C134.613 45.2145 134.613 45.1294 134.613 45.0443C134.613 44.3467 134.613 43.6491 134.613 42.9515C134.613 42.2709 134.621 41.5903 134.613 40.9182C134.604 40.1185 134.365 39.4038 133.844 38.7997Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M114.308 64.4441C114.24 64.4356 114.171 64.4441 114.094 64.444C112.745 64.4439 111.404 64.4437 110.054 64.435C109.841 64.4349 109.619 64.4009 109.414 64.3413C108.363 64.0434 107.671 63.1075 107.662 62.0016C107.654 60.9552 107.662 59.9088 107.662 58.8709C107.662 58.8029 107.662 58.7348 107.662 58.6582C107.645 58.4285 107.483 58.2754 107.252 58.2583C107.021 58.2413 106.85 58.3774 106.799 58.6071C106.782 58.6921 106.782 58.7772 106.782 58.8623C106.782 59.8831 106.774 60.8955 106.783 61.9164C106.783 62.1546 106.8 62.3843 106.843 62.614C107.15 64.2134 108.491 65.311 110.14 65.3197C110.78 65.3198 111.413 65.3199 112.053 65.32C112.557 65.3201 113.07 65.3201 113.573 65.3202C113.813 65.3203 114.043 65.3288 114.282 65.3203C114.539 65.3118 114.709 65.1247 114.718 64.8865C114.718 64.6653 114.547 64.4781 114.308 64.4441Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M134.587 58.5656C134.519 58.3529 134.348 58.2423 134.126 58.2677C133.921 58.2847 133.767 58.4293 133.741 58.6335C133.733 58.7016 133.733 58.7696 133.733 58.8377C133.733 59.8841 133.733 60.922 133.725 61.9684C133.725 62.1896 133.699 62.4193 133.64 62.6405C133.358 63.7123 132.427 64.4353 131.317 64.4437C130.215 64.452 129.113 64.4434 128.011 64.4432C127.943 64.4432 127.875 64.4432 127.798 64.4432C127.55 64.4687 127.371 64.6643 127.379 64.894C127.388 65.1322 127.567 65.3024 127.823 65.3195C127.875 65.3195 127.926 65.3195 127.977 65.3195C128.558 65.3196 129.13 65.3196 129.711 65.3197C130.232 65.3198 130.762 65.3199 131.283 65.3199C133.127 65.3117 134.588 63.8656 134.605 62.0281C134.613 60.9561 134.604 59.8842 134.604 58.8208C134.613 58.7357 134.613 58.6421 134.587 58.5656Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M108.27 51.4678C108.27 50.8552 108.381 50.2768 108.611 49.7493C108.842 49.2219 109.158 48.7541 109.559 48.3628C109.96 47.963 110.447 47.6568 111.011 47.4357C111.575 47.2146 112.189 47.1041 112.873 47.1042C113.539 47.1043 114.162 47.2149 114.726 47.4362C115.29 47.6575 115.777 47.9723 116.187 48.3637C116.597 48.7636 116.913 49.2231 117.144 49.7505C117.374 50.278 117.486 50.8565 117.486 51.4691C117.486 52.0816 117.375 52.6601 117.144 53.1875C116.914 53.7149 116.598 54.1828 116.188 54.574C115.778 54.9738 115.291 55.28 114.727 55.5011C114.164 55.7223 113.54 55.8328 112.874 55.8327C112.199 55.8326 111.576 55.7219 111.012 55.5006C110.448 55.2793 109.961 54.9645 109.56 54.5731C109.158 54.1732 108.842 53.7138 108.612 53.1863C108.381 52.6588 108.27 52.0803 108.27 51.4678ZM110.533 51.4681C110.533 51.7999 110.593 52.1061 110.721 52.3784C110.849 52.6592 111.012 52.8974 111.225 53.1016C111.439 53.3058 111.686 53.4675 111.968 53.5781C112.25 53.6887 112.549 53.7483 112.865 53.7484C113.181 53.7484 113.48 53.6889 113.762 53.5783C114.044 53.4678 114.291 53.3062 114.514 53.102C114.727 52.8979 114.898 52.6512 115.026 52.379C115.154 52.0983 115.214 51.8005 115.214 51.4687C115.214 51.137 115.154 50.8392 115.026 50.5584C114.897 50.2777 114.727 50.0394 114.513 49.8352C114.299 49.631 114.043 49.4694 113.761 49.3587C113.479 49.2481 113.181 49.1885 112.865 49.1885C112.548 49.1884 112.25 49.2479 111.968 49.3585C111.686 49.469 111.438 49.6306 111.225 49.8348C111.011 50.0389 110.84 50.2856 110.721 50.5578C110.593 50.83 110.533 51.1363 110.533 51.4681Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M124.841 50.0906C124.337 49.4865 123.722 49.1801 122.987 49.18C122.663 49.18 122.364 49.2395 122.09 49.3586C121.817 49.4776 121.578 49.6392 121.382 49.8349C121.185 50.0391 121.023 50.2772 120.912 50.558C120.801 50.8387 120.741 51.1365 120.741 51.4598C120.741 51.7916 120.801 52.0979 120.912 52.3701C121.023 52.6509 121.186 52.8891 121.391 53.0933C121.596 53.2976 121.826 53.4592 122.099 53.5784C122.373 53.6975 122.663 53.7571 122.979 53.7572C123.671 53.7573 124.294 53.4681 124.841 52.8811L124.841 55.4335L124.619 55.51C124.286 55.629 123.979 55.7141 123.688 55.7651C123.398 55.8161 123.116 55.8501 122.843 55.85C122.271 55.85 121.715 55.7393 121.194 55.5265C120.665 55.3138 120.204 55.0074 119.802 54.616C119.401 54.2246 119.076 53.7566 118.837 53.2206C118.598 52.6846 118.469 52.0975 118.469 51.4595C118.469 50.8214 118.589 50.2344 118.828 49.7069C119.067 49.1795 119.383 48.7201 119.784 48.3288C120.186 47.946 120.647 47.6398 121.176 47.4272C121.706 47.2146 122.261 47.104 122.842 47.1041C123.175 47.1042 123.499 47.1383 123.815 47.2064C124.131 47.2745 124.473 47.3851 124.823 47.5298L124.823 50.0906L124.841 50.0906Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
        <path
          d="M133.136 55.6121L130.445 55.6117L128.378 52.421L128.378 55.6114L126.218 55.6111L126.216 47.3247L129.581 47.3252C130.043 47.3252 130.453 47.3934 130.794 47.5295C131.136 47.6657 131.426 47.8529 131.648 48.0826C131.871 48.3209 132.041 48.5846 132.152 48.8994C132.264 49.2057 132.323 49.5375 132.323 49.8863C132.323 50.5159 132.17 51.0263 131.862 51.4176C131.555 51.809 131.102 52.0726 130.513 52.2172L133.136 55.6121ZM128.378 51.0258L128.788 51.0259C129.215 51.0259 129.539 50.9409 129.77 50.7623C130 50.5836 130.111 50.3369 130.111 50.0051C130.111 49.6733 130 49.4266 129.77 49.2479C129.539 49.0692 129.214 48.9841 128.787 48.984L128.377 48.984L128.378 51.0258Z"
          fill="#CBCBCB"
          fill-opacity="0.1"
        />
      </g>
      <defs>
        <clipPath id="clip0_9479_20284">
          <rect width="199.343" height="126" fill="white" />
        </clipPath>
      </defs>
    </svg>
    <div class="font-semibold text-sm text-center">
      Cấu hình các trường để <br />
      sẵn sàng bóc tách
    </div>
  </div>
</ng-template>

<ng-template #textWithScore let-field="field">
  <div class="flex gap-3 items-baseline">
    <div class="self-center shrink-0 text-center" *ngIf="field.isEdited">
      <ng-container [ngTemplateOutlet]="checkedSvg"></ng-container>
    </div>
    <div
      *ngIf="!field.isEdited"
      class="w-11 h-6 my-2 shrink-0 p-1 rounded-[4px] font-semibold text-xs text-center bg-status-error"
      [ngStyle]="{ background: field.scoreBgColor }"
    >
      {{ field.score }}
    </div>
    <div
      class="flex-1 break-words w-[1px] h-full px-4 py-2 rounded-lg bg-[#1F1F28] border border-[#E7EBEF1A]"
    >
      {{ field.text || '&nbsp;' }}
    </div>
  </div>
</ng-template>

<ng-template #checkedSvg>
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="11.5" fill="#009B4E" stroke="#009B4E" />
    <path
      d="M16.1082 9.03155C15.8194 8.74274 15.3802 8.74274 15.0914 9.03155L10.6798 13.4432L8.90819 11.6716C8.61938 11.3827 8.18023 11.3827 7.89142 11.6716C7.6026 11.9604 7.6026 12.3995 7.89142 12.6883L10.1714 14.9683C10.2388 15.0357 10.3111 15.0912 10.3965 15.1292C10.4825 15.1674 10.5752 15.1849 10.6798 15.1849C10.7844 15.1849 10.8771 15.1674 10.9631 15.1292C11.0485 15.0912 11.1208 15.0357 11.1882 14.9683L16.1082 10.0483C16.397 9.75951 16.397 9.32037 16.1082 9.03155Z"
      fill="white"
      stroke="white"
      stroke-width="0.25"
    />
  </svg>
</ng-template>
