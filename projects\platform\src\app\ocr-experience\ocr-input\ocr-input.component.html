<div class="py-5 px-6" *transloco="let t; read: 'ocrExperience'">
  <ng-container [ngSwitch]="mode">
    <div class="font-semibold text-xl mb-5" *ngSwitchCase="Mode.Platform">
      TRẢI NGHIỆM DỊCH VỤ
    </div>
    <div
      class="text-center font-bold text-[40px] text-[#273266] mb-3"
      *ngSwitchCase="Mode.LDP"
    >
      {{ t('experience') }}
    </div>
    <div class="font-bold text-[32px] text-[#273266] mb-3" *ngSwitchCase="Mode.Demo">
      Tr<PERSON> lý AI xử lý văn bản
    </div>
  </ng-container>
  <div class="grid grid-cols-3 gap-4">
    <div class="col-span-full xl:col-span-1 bg-white rounded-lg px-[2rem] py-[1.5rem]">
      <div class="mb-4">
        <div class="mb-3 flex items-center font-medium gap-1 text-base">
          Bước 1. <PERSON><PERSON><PERSON> chứ<PERSON> năng
          <img
            nz-tooltip
            [nzTooltipTitle]="functionsTooltip"
            nzTooltipPlacement="bottom"
            nzTooltipOverlayClassName="ocr-input-tooltip"
            src="assets/img/rpa/ocr-experience/tooltip.svg"
          />
        </div>
        <nz-select
          nzSize="large"
          class="w-full"
          [ngModel]="selectedFunction?.value"
          (ngModelChange)="handleSelectFunction($event)"
        >
          <nz-option
            *ngFor="let functionType of functionOptions"
            [nzValue]="functionType.value"
            [nzLabel]="functionType.label"
          ></nz-option>
        </nz-select>
      </div>
      <div class="mb-4">
        <div class="mb-3 flex items-center font-medium gap-1 text-base">
          Bước 2. Chọn {{ selectedFunction?.step2Label }}
          <img
            nz-tooltip
            [nzTooltipTitle]="templatesTooltip"
            nzTooltipPlacement="bottom"
            nzTooltipOverlayClassName="ocr-input-tooltip"
            src="assets/img/rpa/ocr-experience/tooltip.svg"
          />
        </div>
        <nz-select
          nzShowSearch
          nzSize="large"
          class="w-full"
          [ngModel]="selectedTemplate"
          (ngModelChange)="handleSelectTemplate($event)"
        >
          <ng-container *ngIf="!selectedFunction?.allowTemplateGroup">
            <nz-option
              *ngFor="let option of templateOptions"
              [nzValue]="option"
              [nzLabel]="option.label"
              nzCustomContent
            >
              <div class="flex items-center justify-between">
                {{ option.label }}
                <img
                  *ngIf="mode === Mode.LDP && option?.restrictedInLDP"
                  src="assets/img/rpa/ocr-experience/lock.svg"
                />
              </div>
            </nz-option>
          </ng-container>
          <ng-container *ngIf="selectedFunction?.allowTemplateGroup">
            <nz-option-group
              *ngFor="let group of groupedTemplateOptions"
              [nzLabel]="group.label"
            >
              <nz-option
                *ngFor="let option of group.templates"
                [nzValue]="option"
                [nzLabel]="option.label"
                nzCustomContent
              >
                <div class="flex items-center justify-between">
                  {{ option.label }}
                  <img
                    *ngIf="mode === Mode.LDP && option?.restrictedInLDP"
                    src="assets/img/rpa/ocr-experience/lock.svg"
                  />
                </div>
              </nz-option>
            </nz-option-group>
          </ng-container>
        </nz-select>
      </div>
      <div class="mb-3 font-medium text-base">Bước 3. Tải file bóc tách</div>
      <div *ngIf="!selectedTemplate?.value" class="mb-4">
        Hãy chọn loại văn bản trước!
      </div>
      <app-file-input
        [selectedTemplateValue]="selectedTemplate?.value"
        [selectedFunctionValue]="selectedTemplate?.functionType"
        class="mb-4"
        (inputFileEvent)="setOcrInputFile($event)"
        [restriction]="restriction"
      >
      </app-file-input>
      <div class="text-center" *ngIf="mode === Mode.LDP">
        Để trải nghiệm sản phẩm một cách đầy đủ hơn <br />
        vui lòng <a class="font-bold text-[#2140D2]" routerLink="/login">Đăng nhập</a>
      </div>
    </div>
    <div class="col-span-full xl:col-span-2">
      <app-ocr-preview
        [template]="selectedTemplate"
        [restriction]="restriction"
        [ocrFile]="ocrInputFile"
      >
      </app-ocr-preview>
    </div>
  </div>
</div>

<ng-template #functionsTooltip>
  <div class="text-base font-bold mb-2">Số hóa văn bản</div>
  <div class="mb-6">
    Chuyển đổi toàn bộ thông tin dạng hình ảnh chữ đánh máy trong văn bản thành dạng text
  </div>
  <div class="text-base font-bold mb-2">Trích xuất thông tin</div>
  <div>
    Tìm kiếm và bóc tách các trường thông tin theo yêu cầu của của từng loại văn bản.
  </div>
</ng-template>

<ng-template #templatesTooltip>
  <ng-container [ngSwitch]="selectedTemplate.functionType">
    <ng-container *ngSwitchCase="FunctionType.SoHoaVanBan">
      <div class="text-base font-bold mb-2">Cơ bản</div>
      <div class="mb-6">Xử lý tối ưu đối với văn bản thuần text</div>
      <div class="text-base font-bold mb-2">Nâng cao</div>
      <div>
        Xử lý tối ưu đối với văn bản có bảng. Hỗ trợ phân tích cách trình bày của văn bản
      </div>
    </ng-container>
    <ng-container *ngSwitchCase="FunctionType.TrichXuatThongTin">
      <div class="text-base font-bold mb-2">Chọn văn bản bóc tách</div>
      <div>Danh sách văn bản sẵn sàng sử dụng</div>
    </ng-container>
    <ng-container *ngSwitchCase="FunctionType.GoiYXuLyVanBan">
      <div class="text-base font-bold mb-2"></div>
    </ng-container>
  </ng-container>
</ng-template>
