.info-text {
  font-weight: 600 !important;
  display: block;

  &.title::after {
    content: "*";
    color: red;
    margin-left: 3px;
  }
}

:host ::ng-deep {
  nz-form-label {
    @apply text-left;

    label {
      @apply h-10 text-base font-semibold;
    }
  }

  nz-form-control {
    input {
      @apply h-10 rounded-[4px];

      .ant-form-item-explain {
        @apply text-sm
      }
    }
  }

  nz-input-group {
    @apply h-10 rounded-[4px];

    input {
      height: auto; // override style of input without group
    }
  }
}