<div
  #inputFile
  *ngIf="fileList.length < maxFileCount"
  class="flex relative items-center justify-center gap-3 bg-[#F0F1F4] rounded-lg p-5 border border-dashed border-line hover:border-brand-2"
  (dragover)="inputFile?.classList?.add('!border-brand-2')"
  (dragleave)="inputFile?.classList?.remove('!border-brand-2')"
  (drop)="inputFile?.classList?.remove('!border-brand-2')"
>
  <input
    multiple
    class="absolute top-0 left-0 w-full h-full z-10 opacity-0 text-[0px] cursor-pointer"
    type="file"
    [accept]="RULE_ACCEPT.accept"
    #inputFileElem
    (change)="handleChange($event, inputFileElem)"
  />
  <div class="flex items-center justify-center gap-2">
    <img src="assets/kie/header-table/upload.svg" alt="icon-upload" />
    <div class="font-medium">
      <span class="text-brand-1">Chọn</span> hoặc kéo thả file tại đây
    </div>
  </div>
</div>

<div
  *ngFor="let file of fileList; index as i"
  class="bg-[#F0F1F4] border border-line rounded-lg px-4 py-2 mt-2"
>
  <div class="w-full flex items-center gap-2">
    <img
      class="shrink-0"
      *ngIf="file.type === 'application/pdf'"
      src="assets/kie/header-table/pdf-icon.svg"
      alt="PDF icon"
    />
    <img
      class="shrink-0"
      *ngIf="file.type === 'image/png'"
      src="assets/kie/header-table/png-icon.svg"
      alt="PNG icon"
    />
    <img
      class="shrink-0"
      *ngIf="file.type === 'image/jpeg' || fileType === 'image/jpg'"
      src="assets/kie/header-table/jpeg-icon.svg"
      alt="JPEG icon"
    />
    <div class="flex-1 truncate">
      <p
        nz-tooltip
        [nzTooltipTitle]="file.name.length > 50 ? file.name : ''"
        nzTooltipColor="#000"
        class="truncate font-semibold"
      >
        {{ file.name }}
      </p>
      <p>{{ convertBytesToMB(file.size) }}</p>
    </div>
    <button class="shrink-0" nz-button nzType="link" (click)="removeFile(i)">
      <i nz-icon class="text-2xl text-red-500" nz-size="large" nzType="delete"></i>
    </button>
  </div>
</div>
