import { Injectable } from '@angular/core';
import { AuthConfig } from 'angular-oauth2-oidc';
import { OAuthService, OAuthErrorEvent } from 'angular-oauth2-oidc';
import { Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '@platform/environment/environment';

export const authCodeFlowConfig: AuthConfig = {
  issuer: 'https://accounts.google.com',
  redirectUri: window.location.origin + '/login',
  clientId:
    '************-9j1kft5l716sf2do9d84guovtuvaj3oc.apps.googleusercontent.com',
  responseType: 'id_token token',
  scope: 'openid profile email',
  strictDiscoveryDocumentValidation: false,
  showDebugInformation: true,
  requireHttps: false,
};

@Injectable({ providedIn: 'root' })
export class SingleSignOnService {
  oAuthAccessToken$ = new Subject<{
    profile: { email: string };
    accessToken: string;
  }>();
  constructor(
    private readonly oAuthService: OAuthService,
    private http: HttpClient
  ) {
    // Useful for debugging:
    /* this.oAuthService.events.subscribe((event) => {
      if (event instanceof OAuthErrorEvent) {
        console.error(event);
      } else {
        console.warn(event);
      }
    }); */
    this.oAuthService.configure(authCodeFlowConfig);
    this.oAuthService.loadDiscoveryDocument().then(() => {
      // parse and retrieve token from hash(#) fragment once being redirected from gg auth screen or retrieve from session token
      this.oAuthService.tryLoginImplicitFlow().then(() => {
        if (this.oAuthService.hasValidAccessToken()) {
          this.oAuthService.loadUserProfile().then((profile) => {
            this.oAuthAccessToken$.next({
              accessToken: this.oAuthService.getAccessToken(),
              profile: profile['info'],
            });
            this.oAuthAccessToken$.complete();
          });
        }
      });
    });
  }

  initLogin() {
    this.oAuthService.initLoginFlow();
  }

  loginWithSSO() {
    return this.http.post<{ accessToken: string }>(
      environment.backendUrl + 'auth/sso',
      {
        accessToken: this.oAuthService.getAccessToken(),
      }
    );
  }

  logout() {
    this.oAuthService.logOut();
  }
}
