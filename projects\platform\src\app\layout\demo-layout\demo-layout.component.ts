import {
  Component,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { environment } from '@platform/environment/environment';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslocoService } from '@ngneat/transloco';
import { get } from 'lodash';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-demo-layout',
  templateUrl: './demo-layout.component.html',
  styleUrls: ['./demo-layout.component.scss'],
})
export class DemoLayoutComponent implements OnInit, OnDestroy {
  public landingPageUrl = environment.landingPageUrl;
  @ViewChild('configModal')
  configModal: TemplateRef<any>;
  readonly cacheName = 'demoOcrExperience';
  logoObjectUrl: string;
  logoFile: File;
  cacheInfoForm: UntypedFormGroup = new UntypedFormGroup({
    title: new UntypedFormControl('', []),
    logo: new UntypedFormControl('', []),
    alignment: new UntypedFormControl('left', function (control) {
      const valid = ['left', 'center', 'right'].includes(control.value);
      if (valid) return null;
      else return { alignment: 'Invalid value' };
    }),
    titleFontSize: new UntypedFormControl('medium', function (control) {
      const valid = ['small', 'medium', 'large'].includes(control.value);
      if (valid) return null;
      else return { titleFontSize: 'Invalid value' };
    }),
    logoSize: new UntypedFormControl('medium', function (control) {
      const valid = ['small', 'medium', 'large'].includes(control.value);
      if (valid) return null;
      else return { logoSize: 'Invalid value' };
    }),
  });
  cacheStorage: Cache;

  constructor(
    private translocoService: TranslocoService,
    private modal: NzModalService,
    private toastrService: ToastrService,
    public sanitizer: DomSanitizer
  ) {}

  async ngOnInit(): Promise<void> {
    /* LOAD title and logo from cache */
    this.cacheStorage = await caches.open(this.cacheName);
    const cacheInfo: { title: string; logo: string } = JSON.parse(
      localStorage.getItem(this.cacheName)
    );

    for (const key in cacheInfo) {
      this.cacheInfoForm.controls[key].setValue(cacheInfo[key]);
    }

    const logoResp = await this.cacheStorage.match(cacheInfo?.logo);
    if (logoResp) {
      const logoBlob = await logoResp.blob();
      this.logoObjectUrl = URL.createObjectURL(logoBlob);
      this.logoFile = new File([logoBlob], cacheInfo.logo);
    }
  }

  setLang(lang: 'en' | 'vi') {
    this.translocoService.setActiveLang(lang);
  }

  getLang() {
    return this.translocoService.getActiveLang();
  }

  openConfigModal() {
    this.modal.create({
      nzContent: this.configModal,
      nzFooter: null,
      nzBodyStyle: { padding: '0' },
    });
  }

  handleLogoInputChange(event) {
    const fileMimetype = get(event, 'target.files.0.type', '');
    const fileExtension = get(event, 'target.files.0.name', '')
      .split('.')
      .pop()
      .toLowerCase();
    if (
      !'image/jpeg, image/png, image/svg+xml'
        .split(',')
        .map((item) => item.trim())
        .includes(fileMimetype) ||
      !['jpeg', 'jpg', 'png', 'svg'].includes(fileExtension)
    ) {
      return this.toastrService.error(
        'File không đúng định dạng. Vui lòng kiểm tra lại!'
      );
    }
    this.logoFile = event.target.files[0];
    this.logoObjectUrl = URL.createObjectURL(this.logoFile);
  }

  async saveConfigToCache() {
    if (this.logoObjectUrl) {
      const resp = await fetch(this.logoObjectUrl);
      if (this.cacheInfoForm.controls['logo'].value)
        await this.cacheStorage.delete(
          this.cacheInfoForm.controls['logo'].value
        );
      await this.cacheStorage.put(this.logoFile.name, resp);
      this.cacheInfoForm.controls['logo'].setValue(this.logoFile.name);
    }

    localStorage.setItem(
      this.cacheName,
      JSON.stringify(this.cacheInfoForm.value)
    );
    this.toastrService.success('Lưu cài đặt thành công');
    this.modal.closeAll();
  }

  clearConfigAndResetPage() {
    caches.delete(this.cacheName);
    localStorage.removeItem(this.cacheName);
    location.reload();
  }

  ngOnDestroy(): void {
    URL.revokeObjectURL(this.logoObjectUrl);
  }
}
