import { Injectable } from '@angular/core';
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import * as docx from 'docx';
import { cloneDeep, flatten, get, isNumber, isString, reverse, set, uniq } from 'lodash';
import { excludedKeyFields } from '@platform/app/ocr-experience/ocr-experience';
import { DocumentField } from '@platform/app/kie/kie';
import UtilsService from './utils.service';
import { ToastrService } from 'ngx-toastr';
import * as ExcelJS from 'exceljs';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';
import { loadImage } from 'canvas';
import { PDFDocumentLoadingTask } from 'pdfjs-dist';
import { PdfService } from './pdf.service';
import {
  Bbox,
  HalfPoint,
  Inch,
  Line,
  OcrResult,
  PageMargin,
  PagesDimension,
  Paragraph,
  Pixel,
  Point,
  RelativeUnit,
  RenderedPageImage,
  SupportedFileType,
  SupportedMimetype,
  supportedMimetypeList,
  Twip
} from './export.service.type';
import { fabric } from 'fabric';

@Injectable({
  providedIn: 'root'
})
export class ExportService {
  constructor(
    private utils: UtilsService,
    private pdfService: PdfService,
    private toastr: ToastrService
  ) {}

  public exportXlsxTemplateOcr(
    ocrResult: {
      [field: string]: DocumentField;
    },
    fileName: string
  ): void {
    const mainWorkSheetObj = {};
    const mainColInfoList: { wch: number }[] = [];
    const otherWorkSheetObjList: { ws: XLSX.WorkSheet; wsName: string }[] = [];
    const sheetNameSet = new Set(); // prevent duplicate sheet name
    for (const field in ocrResult) {
      if (excludedKeyFields.includes(field)) continue;

      const fieldType = get(ocrResult[field], 'type');
      switch (fieldType) {
        case undefined: {
          break;
        }
        case 'Field': {
          Object.assign(mainWorkSheetObj, {
            [field]: get(ocrResult[field], 'text', 'N/A')
          });
          mainColInfoList.push({
            wch: Math.max(get(ocrResult[field], 'text', 'N/A').length, field.length)
          });
          break;
        }
        case 'List': {
          const otherSheetColInfoList: { wch: number }[] = [];
          const workSheetObject = get(ocrResult[field], 'cells', []).map((item) => {
            const obj = { name: item.name, text: item.text };
            /* calculate latest max column width -> use order resulting from Object.entries */
            Object.entries(obj).forEach(
              ([key, value], i) =>
                (otherSheetColInfoList[i] = {
                  wch: Math.max(
                    otherSheetColInfoList[i]?.wch || 0,
                    key.length,
                    value.length
                  )
                })
            );
            return obj;
          });
          /* worksheet name is unique and length cannot exceed 31 chars */
          let wsName;
          do {
            wsName = field.slice(0, 27) + '_' + this.utils.makeId(3);
          } while (sheetNameSet.has(wsName)); // while generated wsName is collided with sheet name already in set
          sheetNameSet.add(wsName);
          const listWorkSheet = XLSX.utils.json_to_sheet(workSheetObject);
          /* setup cols width */
          listWorkSheet['!cols'] = otherSheetColInfoList;
          otherWorkSheetObjList.push({
            wsName,
            ws: listWorkSheet
          });
          /* setup reference from main work sheet to other worksheet */
          const referenceText = `Xem ${fieldType}...`;
          Object.assign(mainWorkSheetObj, {
            [field]: { v: referenceText, t: 's', l: { Target: `#${wsName}!A1` } }
          });
          mainColInfoList.push({ wch: Math.max(referenceText.length, field.length) });
          break;
        }
        case 'Table': {
          const otherSheetColInfoList: { wch: number }[] = [];
          const workSheetObject = get(ocrResult[field], 'rows', []).map((row) => {
            const obj = Object.fromEntries(
              get(row, 'cells', []).map((cell) => [cell.name, cell.text])
            );
            /* calculate latest max column width -> use order resulting from Object.entries */
            Object.entries(obj).forEach(
              ([key, value], i) =>
                (otherSheetColInfoList[i] = {
                  wch: Math.max(
                    otherSheetColInfoList[i]?.wch || 0,
                    key.length,
                    value.length
                  )
                })
            );
            return obj;
          });
          /* worksheet name is unique and length cannot exceed 31 chars */
          let wsName;
          do {
            wsName = field.slice(0, 27) + '_' + this.utils.makeId(3);
          } while (sheetNameSet.has(wsName)); // while generated wsName is collided with sheet name already in set
          sheetNameSet.add(wsName);
          const tableWorkSheet = XLSX.utils.json_to_sheet(workSheetObject);
          /* setup cols width */
          tableWorkSheet['!cols'] = otherSheetColInfoList;
          otherWorkSheetObjList.push({
            wsName,
            ws: tableWorkSheet
          });
          /* setup reference from main work sheet to other worksheet */
          const referenceText = `Xem ${fieldType}`;
          Object.assign(mainWorkSheetObj, {
            [field]: { v: referenceText, t: 's', l: { Target: `#${wsName}!A1` } }
          });
          mainColInfoList.push({ wch: Math.max(referenceText.length, field.length) });
          break;
        }
        default: {
          const todoMessage = `TODO: field ${field} with type ${fieldType} is WIP`;
          Object.assign(mainWorkSheetObj, { [field]: todoMessage });
          mainColInfoList.push({ wch: todoMessage.length });
          break;
        }
      }
    }
    const mainWorkSheet = XLSX.utils.json_to_sheet([mainWorkSheetObj]);
    mainWorkSheet['!cols'] = mainColInfoList;

    const workSheetList = [
      { wsName: 'main', ws: mainWorkSheet },
      ...otherWorkSheetObjList
    ];
    const wb: XLSX.WorkBook = {
      Sheets: Object.fromEntries(workSheetList.map((ws) => [ws.wsName, ws.ws])),
      SheetNames: workSheetList.map((ws) => ws.wsName)
    };

    const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    const data: Blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });
    FileSaver.saveAs(data, fileName + '.xlsx');
  }

  public exportXlsxTemplateOcrMergeMany(
    listOcrResults: { ocrResult: { [field: string]: DocumentField }; name: string }[],
    fileName: string
  ) {
    const fieldsSet = new Set<string>();
    listOcrResults.forEach(({ ocrResult }) => {
      for (const field in ocrResult) {
        if (excludedKeyFields.includes(field)) continue;
        fieldsSet.add(field);
      }
    });

    const mainWorkSheetHeaders = ['tài liệu'].concat(Array.from(fieldsSet));
    const mainWsColInfoList: { wch: number }[] = new Array(mainWorkSheetHeaders.length)
      .fill(true)
      .map((_, index) => ({ wch: mainWorkSheetHeaders[index].length }));
    const mainWorkSheet = XLSX.utils.json_to_sheet([], { header: mainWorkSheetHeaders });

    const typeListMap = new Map<
      string,
      { valuesInSheet: { origin: string; data: DocumentField }[]; sheetName: string }
    >();
    const typeTableMap = new Map<
      string,
      { valuesInSheet: { origin: string; data: DocumentField }[]; sheetName: string }
    >();
    const sheetNameSet = new Set(); // prevent duplicate sheet name

    listOcrResults.forEach(({ ocrResult, name }) => {
      const rowObj = { ['tài liệu']: name };
      mainWsColInfoList[0].wch = Math.max(mainWsColInfoList[0].wch, name.length);
      for (const field in ocrResult) {
        if (excludedKeyFields.includes(field)) continue;
        const fieldType = get(ocrResult[field], 'type');
        const fieldIndexInMainWsColInfoList = mainWorkSheetHeaders.findIndex(
          (header) => header === field
        );

        switch (fieldType) {
          case undefined: {
            break;
          }
          case 'Field': {
            const cellVal = get(ocrResult[field], 'text', 'N/A');
            Object.assign(rowObj, {
              [field]: cellVal
            });
            if (fieldIndexInMainWsColInfoList !== -1)
              mainWsColInfoList[fieldIndexInMainWsColInfoList].wch = Math.max(
                mainWsColInfoList[fieldIndexInMainWsColInfoList].wch,
                cellVal.length
              );
            break;
          }
          case 'List':
          case 'Table': {
            /* worksheet name is unique and length cannot exceed 31 chars */
            let wsName;
            const map = fieldType === 'List' ? typeListMap : typeTableMap;
            if (map.has(field)) {
              wsName = map.get(field).sheetName;
              map.get(field).valuesInSheet.push({ origin: name, data: ocrResult[field] });
            } else {
              do {
                wsName = field.slice(0, 27) + '_' + this.utils.makeId(3);
              } while (sheetNameSet.has(wsName)); // while generated wsName is collided with sheet name already in set
              sheetNameSet.add(wsName);
              map.set(field, {
                sheetName: wsName,
                valuesInSheet: [{ origin: name, data: ocrResult[field] }]
              });
            }

            /* setup reference from main work sheet to other worksheet */
            const referenceText = `Xem ${fieldType}...`;
            Object.assign(rowObj, {
              [field]: { v: referenceText, t: 's', l: { Target: `#${wsName}!A1` } }
            });
            if (fieldIndexInMainWsColInfoList !== -1)
              mainWsColInfoList[fieldIndexInMainWsColInfoList].wch = Math.max(
                mainWsColInfoList[fieldIndexInMainWsColInfoList].wch,
                referenceText.length
              );
            break;
          }
          default: {
            const todoMessage = `TODO: field ${field} with type ${fieldType} is WIP`;
            Object.assign(rowObj, { [field]: todoMessage });
            break;
          }
        }
      }
      XLSX.utils.sheet_add_json(mainWorkSheet, [rowObj], {
        header: mainWorkSheetHeaders,
        origin: -1,
        skipHeader: true,
        WTF: true
      });
    });
    mainWorkSheet['!cols'] = mainWsColInfoList;

    const otherWorkSheetObjList: { ws: XLSX.WorkSheet; wsName: string }[] = [];

    /* create sheets with merged lists */
    typeListMap.forEach(({ sheetName, valuesInSheet }) => {
      const headers = ['tài liệu', 'name', 'text'];
      const colInfoList = headers.map((header) => ({ wch: header.length }));
      const ws = XLSX.utils.json_to_sheet([], { header: headers });
      valuesInSheet.forEach(({ data, origin }) => {
        colInfoList[0].wch = Math.max(origin.length, colInfoList[0].wch);
        XLSX.utils.sheet_add_json(
          ws,
          data.cells.map((cell) => {
            const name = cell.name,
              text = cell.text;
            colInfoList[1].wch = Math.max(name.length, colInfoList[1].wch);
            colInfoList[2].wch = Math.max(text.length, colInfoList[2].wch);
            return { 'tài liệu': origin, name: cell.name, text: cell.text };
          }),
          { header: headers, origin: -1, skipHeader: true, WTF: true }
        );
      });
      ws['!cols'] = colInfoList;
      otherWorkSheetObjList.push({ ws, wsName: sheetName });
    });

    /* create sheets with merged tables */
    typeTableMap.forEach(({ sheetName, valuesInSheet }) => {
      const headers = ['tài liệu'].concat(
        uniq(
          flatten(
            valuesInSheet.map(({ data: { columns } }) =>
              columns.map((column) => column.name)
            )
          )
        )
      );
      const colInfoList = headers.map((header) => ({ wch: header.length }));
      const ws = XLSX.utils.json_to_sheet([], { header: headers });
      valuesInSheet.forEach(({ data: { rows }, origin }) => {
        colInfoList[0].wch = Math.max(origin.length, colInfoList[0].wch);
        XLSX.utils.sheet_add_json(
          ws,
          rows.map((row) => {
            const data = Object.fromEntries(
              row.cells.map((cell) => [cell.name, cell.text])
            );
            for (const header in data) {
              const fieldIndexInColInfoList = headers.findIndex(
                (item) => item === header
              );
              if (fieldIndexInColInfoList !== -1)
                colInfoList[fieldIndexInColInfoList].wch = Math.max(
                  colInfoList[fieldIndexInColInfoList].wch,
                  data[header].length
                );
            }
            data['tài liệu'] = origin;
            return data;
          }),
          {
            header: headers,
            origin: -1,
            skipHeader: true,
            WTF: true
          }
        );
      });
      ws['!cols'] = colInfoList;
      otherWorkSheetObjList.push({ ws, wsName: sheetName });
    });

    const workSheetList = [
      { wsName: 'main', ws: mainWorkSheet },
      ...otherWorkSheetObjList
    ];
    const wb: XLSX.WorkBook = {
      Sheets: Object.fromEntries(workSheetList.map((ws) => [ws.wsName, ws.ws])),
      SheetNames: workSheetList.map((ws) => ws.wsName)
    };

    const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    const data: Blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });
    FileSaver.saveAs(data, fileName + '.xlsx');
  }

  public exportXlsxScanTableWithXLSX(
    ocrResult,
    fileName: string,
    pageSelectionList?: number[]
  ) {
    /* filter tables from ocrResult */
    const listTableParagraph = get<any[]>(ocrResult, 'paragraphs', []).map((page) => {
      return page.cells.filter((paragraph) => ['Table'].includes(paragraph.type));
    });
    // construct tables into each sheet
    const workSheetList = flatten(
      listTableParagraph
        .map((tablesInPage, pageId) => {
          if (pageSelectionList?.length && !pageSelectionList.includes(pageId + 1))
            return null;
          return tablesInPage.map((table, tableId) => {
            const tmpWb = XLSX.read(table.html, { type: 'string', raw: true });
            return {
              wsName: `Page_${pageId + 1}_Table_${tableId + 1}`,
              ws: tmpWb.Sheets[tmpWb.SheetNames[0]]
            };
          });
        })
        .filter((item) => !!item)
    );

    if (!workSheetList.length)
      return this.toastr.warning(
        'Không thể xuất file Excel do không tồn tại bảng nào trong các trang được chọn'
      );

    /* construct workbook from sheets */
    const wb: XLSX.WorkBook = {
      Sheets: Object.fromEntries(workSheetList.map((ws) => [ws.wsName, ws.ws])),
      SheetNames: workSheetList.map((ws) => ws.wsName)
    };

    const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    const data: Blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });
    FileSaver.saveAs(data, fileName + '.xlsx');
  }

  public async exportXlsxScanTableWithExcelJS({
    fileLink,
    fileType,
    fileName,
    ocrResult,
    pageSelectionList,
    mapPageToSheet,
    isFigureIncluded = true,
    outputType
  }: {
    fileLink: string;
    fileType: SupportedFileType;
    fileName: string;
    ocrResult: OcrResult['object'];
    pageSelectionList?: number[];
    mapPageToSheet?: boolean;
    isFigureIncluded?: boolean;
    outputType: 'file';
  }) {
    const doesPageHaveFigureArr = ocrResult.paragraphs.map(
      (page) =>
        isFigureIncluded && page.cells.some((paragraph) => paragraph.type === 'Figure')
    );
    let pagesImage, pagesDimension;
    /* performance optimization: only fetchOriginalFile and parseOriginalFile to retrieve pagesImage and pagesDimension when there are type Figure in the ocrResult */
    if (doesPageHaveFigureArr.some((item) => item)) {
      const { arrayBuffer, mimetype } = await this.fetchOriginalFile(fileLink);
      ({ pagesImage, pagesDimension } = await this.parseOriginalFile({
        fileLink: fileLink,
        fileArrayBuffer: arrayBuffer,
        fileMimetype: mimetype as SupportedMimetype,
        fileType: fileType,
        shouldRenderPageArr: doesPageHaveFigureArr
      }));
    }

    const paragraphsInput = this.constructExportByParagraphsInput(ocrResult).filter(
      (_, index) =>
        pageSelectionList?.length ? pageSelectionList.includes(index + 1) : true
    );
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'VNPT Smart Reader Exporter';
    workbook.created = new Date();
    workbook.views = [
      {
        x: 0,
        y: 0,
        width: 10000,
        height: 20000,
        firstSheet: 0,
        activeTab: 0,
        visibility: 'visible'
      }
    ];

    const worksheetOptions: Partial<ExcelJS.AddWorksheetOptions> = {
      properties: { tabColor: { argb: '217346' } },
      views: [{ showGridLines: false, state: 'normal' }],
      pageSetup: {},
      state: 'visible'
    };
    const worksheetsList: ExcelJS.Worksheet[] = [];
    if (mapPageToSheet) {
      pageSelectionList?.sort((a, b) => a - b);
      for (let i = 0; i < paragraphsInput.length; i++)
        worksheetsList.push(
          workbook.addWorksheet(
            `Page ${pageSelectionList?.length ? pageSelectionList[i] : i + 1}`,
            worksheetOptions
          )
        );
    } else worksheetsList.push(workbook.addWorksheet('Sheet', worksheetOptions));

    for (let index = 0; index < paragraphsInput.length; index++) {
      const page = paragraphsInput[index];
      const worksheet = worksheetsList[mapPageToSheet ? index : 0];
      /* in the case of pageSelectionList is provided, 'index' does NOT equal pageIndex */
      const pageIndex = pageSelectionList?.length ? pageSelectionList[index] - 1 : index;
      for (const paragraph of page.cells) {
        switch (paragraph.type) {
          case 'Paragraph':
          case 'Header':
          case 'Title':
          case 'Footer':
          case 'Footnote': {
            paragraph.lines.forEach((line) => {
              const row = worksheet.addRow(['ADD EMPTY ROW']);
              row.eachCell(
                { includeEmpty: true },
                (cell) =>
                  (cell.value = {
                    richText: line.phrases.map((phrase) => ({
                      text: `${phrase.text} `,
                      font: {
                        bold: phrase.font_styles.includes('bold'),
                        italic: phrase.font_styles.includes('italic')
                      }
                    }))
                  })
              );
            });
            break;
          }

          case 'Table': {
            let parsedTableRows = [];
            try {
              parsedTableRows = this.utils.parseTableHtml(paragraph['html']);
            } catch (error) {
              const row = worksheet.addRow([
                `parsed table failed at page_id ${paragraph.page_id}, paragraph_id: ${paragraph.paragraph_id}`
              ]);
              row.getCell('A').style = {
                font: { bold: true, color: { argb: 'ff0000' } }
              };
              return; // skip invalid table
            }
            const tableTopPos = worksheet.dimensions.bottom + 1;
            const tableLeftPos = 1;
            const tableCellMergesMap = new Map<
              string,
              { x: number; y: number; rowspan?: number; colspan?: number }
            >();
            const font_styles = paragraph['cells'].map((cell) => cell.font_styles);

            parsedTableRows.forEach((columnCells, rowId) => {
              columnCells.forEach((cell, columnId) => {
                const isOriginalCell =
                  cell['filled'] &&
                  !cell['padded'] &&
                  !isNumber(cell['original_x']) &&
                  !isNumber(cell['original_y']);
                if (!isOriginalCell) return;
                cell['font_styles'] = font_styles.splice(0, 1);

                const cellKey = `x:${cell.x}-y:${cell.y}`;
                if (cell.rowspan > 1 || cell.colspan > 1)
                  tableCellMergesMap.set(cellKey, {
                    x: cell.x,
                    y: cell.y,
                    colspan: cell.colspan,
                    rowspan: cell.rowspan
                  });
              });
            });

            parsedTableRows.forEach((rowCells) => {
              const row = worksheet.addRow(rowCells.map((cell) => cell.text));
              row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                // cell.alignment = { wrapText: true };
                cell.style = {
                  font: {
                    bold: get(rowCells, `${colNumber - 1}.font_styles`, []).includes(
                      'bold'
                    ),
                    italic: get(rowCells, `${colNumber - 1}.font_styles`, []).includes(
                      'italic'
                    )
                  }
                };
                cell.border = {
                  top: { color: { argb: '0000' }, style: 'thin' },
                  bottom: { color: { argb: '0000' }, style: 'thin' },
                  left: { color: { argb: '0000' }, style: 'thin' },
                  right: { color: { argb: '0000' }, style: 'thin' }
                };
              });
            });

            tableCellMergesMap.forEach((mergeInfo) => {
              const top = tableTopPos + mergeInfo.y;
              const bottom = top + (mergeInfo.rowspan - 1 || 0);
              const left = tableLeftPos + mergeInfo.x;
              const right = left + (mergeInfo.colspan - 1 || 0);
              worksheet.unMergeCells({ top, left, bottom, right }); // prevent overlapping cells span causing error / Cannot merge already merged cells
              worksheet.mergeCells({ top, left, bottom, right });
            });

            break;
          }

          case 'Figure': {
            if (!pagesImage || !pagesDimension) break;
            const pageImage = pagesImage[pageIndex];
            const pageDimension = pagesDimension[pageIndex];
            let figureImage: fabric.Image;
            try {
              figureImage = await this.fromURLPromise(pageImage.base64Url, {
                scaleX: 1 / pageImage.renderScale,
                scaleY: 1 / pageImage.renderScale
              });
            } catch (error) {
              console.log(
                `getImageURLByPageNumber() or fromURLPromise() failed => NOT extracting stamps`
              );
            }
            const [x, y, bboxWidth, bboxHeight] = this.getDrawingBoundingBox(
              paragraph.bboxes[pageIndex + 1],
              pageDimension.width,
              pageDimension.height
            );

            const figureId = workbook.addImage({
              base64: figureImage.toDataURL({
                left: x,
                top: y,
                width: bboxWidth,
                height: bboxHeight
              }),
              extension: 'png'
            });

            const startRowIndex = worksheet.lastRow?.number + 1 || 1;
            const figureRange = `A${startRowIndex}:F${startRowIndex + 5}`; // fit figure into 6x6 range
            worksheet.addImage(figureId, figureRange); // always one more row added below the image, the image take up exactly 6 rows tho, but one more empty row is added below it

            break;
          }

          default:
            break;
        }
      }
      if (!worksheet.lastRow) worksheet.addRow([]); // add at least 1 row if there isn't one
      worksheet.lastRow.addPageBreak();
    }

    const data: Blob = new Blob([await workbook.xlsx.writeBuffer()], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    });
    FileSaver.saveAs(data, fileName + '.xlsx');
  }

  public async exportJSON(content: string | Blob, fileName?: string) {
    /* content: url to a file or Blob-like */
    return FileSaver.saveAs(content, fileName + '.json' || 'test.json');
  }

  /* start export ocr/scan-table */
  readonly A4DefaultMarginInTwip = {
    top: docx.convertMillimetersToTwip(20),
    right: docx.convertMillimetersToTwip(15),
    bottom: docx.convertMillimetersToTwip(20),
    left: docx.convertMillimetersToTwip(30)
  };

  public async exportDocxScanTable({
    fileLink,
    fileType,
    fileName,
    ocrResult,
    outputType,
    isFigureIncluded,
    pageSelectionList
  }: {
    fileLink: string;
    fileType: SupportedFileType;
    fileName: string;
    ocrResult: OcrResult['object'];
    outputType: 'file';
    isFigureIncluded: boolean;
    pageSelectionList?: number[];
  }) {
    const doesPageHaveStampArr = ocrResult.paragraphs.map(
      (page) =>
        isFigureIncluded && page.cells.some((paragraph) => paragraph.type === 'Figure')
    );

    const { arrayBuffer, mimetype } = await this.fetchOriginalFile(fileLink);

    const { pagesDimension, pagesImage } = await this.parseOriginalFile({
      fileLink: fileLink,
      fileArrayBuffer: arrayBuffer,
      fileMimetype: mimetype as SupportedMimetype,
      fileType: fileType,
      shouldRenderPageArr: doesPageHaveStampArr
    });

    FileSaver.saveAs(
      await this.exportByParagraphs({
        isFigureIncluded,
        ocrResult: ocrResult,
        pagesDimension: pagesDimension,
        pagesImage: pagesImage,
        pageSelectionList
      }),
      fileName + '.docx'
    );
  }

  private async exportByParagraphs({
    pagesDimension,
    ocrResult,
    pagesImage,
    isFigureIncluded,
    pageSelectionList
  }: {
    pagesDimension: PagesDimension;
    ocrResult: OcrResult['object'];
    pagesImage: RenderedPageImage[];
    isFigureIncluded: boolean;
    pageSelectionList?: number[];
  }): Promise<Blob> {
    const tablesInPages = this.findAllTableBboxesForPages(ocrResult);
    const listPagesMargin = this.findPagesMargin(ocrResult, tablesInPages);
    const paragraphsInput = this.constructExportByParagraphsInput(ocrResult);

    const paragraphs: {
      type: 'paragraph' | 'paragraphs' | 'table' /* | 'figure' */;
      bboxes: { [key: number]: Bbox };
      isFrame?: boolean;
      table?: Paragraph;
      paragraphs?: Paragraph[];
      paragraph?: Paragraph;
    }[][] = ocrResult.paragraphs.map((_) => []);

    paragraphsInput.forEach((page, pageIndex) => {
      page.cells.forEach((paragraph) => {
        switch (paragraph.type) {
          case 'Paragraph':
          case 'Header':
          case 'Title':
          case 'Footer':
          case 'Footnote': {
            const [x1, y1, x2, y2] = paragraph.bboxes[pageIndex + 1];
            let newParaAdded = true;
            for (const previousPara of paragraphs[pageIndex].slice().reverse()) {
              const [prevX1, prevY1, prevX2, prevY2] = previousPara.bboxes[pageIndex + 1];
              const paraYCenter = (y1 + y2) / 2;
              const prevParaYCenter = (prevY1 + prevY2) / 2;
              // if vertical center of current para's bboxes is between previous paragraph [y1, y2] => group them together
              // and vice versa
              if (previousPara.type === 'table') continue;
              if (
                (prevY1 <= paraYCenter && paraYCenter <= prevY2) ||
                (y1 <= prevParaYCenter && prevParaYCenter <= y2)
              ) {
                if (previousPara.type === 'paragraph') {
                  previousPara.type = 'paragraphs';
                  previousPara.paragraphs = [previousPara.paragraph];
                  delete previousPara.paragraph;
                }
                previousPara.paragraphs.push(paragraph);
                if (y1 <= prevParaYCenter && prevParaYCenter <= y2)
                  previousPara.bboxes = paragraph.bboxes;
                newParaAdded = false;
                break;
              }
            }

            if (newParaAdded) {
              paragraphs[pageIndex].push({
                paragraph: paragraph,
                bboxes: paragraph.bboxes,
                isFrame: this.isParagraphAFrame(paragraph, pageIndex, listPagesMargin),
                type: 'paragraph'
              });
            }
            break;
          }
          case 'Table': {
            const font_styles = paragraph['cells'].map((cell) => cell.font_styles);
            let parsedTableRows = [];
            try {
              parsedTableRows = this.utils.parseTableHtml(paragraph['html']);
            } catch (error) {
              return; // skip invalid table
            }
            const constructedTable: (typeof paragraphs)[number][number] = {
              type: 'table',
              bboxes: paragraph.bboxes,
              table: Object.assign(paragraph, {
                rows: parsedTableRows.map((cells) => ({
                  type: 'List',
                  cells: cells
                    .filter(
                      (cell) =>
                        cell['filled'] &&
                        !cell['padded'] &&
                        !isNumber(cell['original_x']) &&
                        !isNumber('original_y')
                    )
                    .map((cell) => ({
                      ...cell,
                      font_styles: font_styles.splice(0, 1)
                    }))
                }))
              })
            };
            paragraphs[pageIndex].push(constructedTable);
            // console.log(constructedTable);
            break;
          }
          case 'Figure': {
            /* TODO: do nothing */
            break;
          }
        }
      });
    });

    const sections: docx.ISectionOptions[] = [];

    for (let pageIndex = 0; pageIndex < paragraphs.length; pageIndex++) {
      const paragraphsInPage = paragraphs[pageIndex];
      const pageNumber = pageIndex + 1;
      if (pageSelectionList?.length && !pageSelectionList.includes(pageNumber)) continue;
      const children = [];
      const { A4Width, A4Height } = pagesDimension[pageIndex];
      const pageMargin = this.findPageMarginInPixel(
        pageIndex,
        pagesDimension,
        listPagesMargin
      );
      let lastParagraphIndentation: {
        firstLineX1?: RelativeUnit;
        paragraphX1?: RelativeUnit;
        paragraphX2?: RelativeUnit;
      } = {};
      paragraphsInPage.forEach((paragraph, index) => {
        switch (paragraph.type) {
          case 'paragraph': {
            const paragraphSpacing = this.findLineSpacing(
              paragraph.paragraph.bboxes[pageNumber],
              get(paragraphsInPage, `${index - 1}.bboxes.${pageNumber}`),
              get(paragraphsInPage, `${index + 1}.bboxes.${pageNumber}`),
              pageIndex,
              pagesDimension
            );

            const commonFontSizeInPara = this.findCommonFontSizeInPara(
              paragraph.paragraph,
              pageIndex,
              pagesDimension
            );

            const lineRanges = this.divideParagraphIntoLineRanges(
              paragraph.paragraph,
              pageNumber,
              A4Width
            );

            const magicNumber = 10; // in percentage

            lineRanges.forEach((item, itemIndex) => {
              const isSeamlessRangeOfLines = typeof item !== 'number' && !!item.length;

              if (isSeamlessRangeOfLines) {
                const lines = paragraph.paragraph.lines.filter((_, index) =>
                  item.includes(index)
                );

                const firstLine = lines[0];
                const firstLineBbox = firstLine.bboxes[pageIndex + 1];
                const linesBbox: Bbox = [
                  Math.min(...lines.map((line) => line.bboxes[pageIndex + 1][0])),
                  Math.min(...lines.map((line) => line.bboxes[pageIndex + 1][1])),
                  Math.max(...lines.map((line) => line.bboxes[pageIndex + 1][2])),
                  Math.max(...lines.map((line) => line.bboxes[pageIndex + 1][3]))
                ];

                let leftIndentInInch = this.calculateLeftIndent(
                  linesBbox[0],
                  pageMargin.left,
                  A4Width
                );
                let firstLineIndentInInch = this.calculateFirstLineIndent(
                  firstLineBbox[0],
                  pageMargin.left,
                  A4Width,
                  leftIndentInInch
                );
                let rightIndentInInch = this.calculateRightIndent(
                  linesBbox[2],
                  pageMargin.right,
                  A4Width
                );

                /* reuse last paragraph indentation */
                // frame paragraph is replaced with indent approach but not letting its indentations will NOT be reused
                if (!paragraph.isFrame) {
                  const lastLeftIndentInInch = this.calculateLeftIndent(
                      lastParagraphIndentation.paragraphX1,
                      pageMargin.left,
                      A4Width
                    ),
                    lastRightIndentInInch = this.calculateRightIndent(
                      lastParagraphIndentation.paragraphX2,
                      pageMargin.right,
                      A4Width
                    ),
                    lastFirstLineIndentInInch = this.calculateFirstLineIndent(
                      lastParagraphIndentation.firstLineX1,
                      pageMargin.left,
                      A4Width,
                      lastLeftIndentInInch
                    );

                  let isLastLeftIndentChanged = false;
                  if (
                    this.calculateDifferenceInPercentage(
                      lastLeftIndentInInch,
                      leftIndentInInch
                    ) > magicNumber
                  ) {
                    lastParagraphIndentation.paragraphX1 = linesBbox[0];
                    isLastLeftIndentChanged = true;
                  } else leftIndentInInch = lastLeftIndentInInch;

                  if (
                    !lastParagraphIndentation.firstLineX1 || // if there no prev first line indent, calculate anyway
                    isLastLeftIndentChanged || // if last left indent is NOT changed, then this calculated firstLineIndent and last calculated firstLintIndent is based on the same left indent => the result is favorable
                    this.calculateDifferenceInPercentage(
                      lastFirstLineIndentInInch,
                      firstLineIndentInInch
                    ) > magicNumber
                  ) {
                    lastParagraphIndentation.firstLineX1 = firstLineBbox[0];
                  } else firstLineIndentInInch = lastFirstLineIndentInInch;

                  if (
                    this.calculateDifferenceInPercentage(
                      lastRightIndentInInch,
                      rightIndentInInch
                    ) > magicNumber
                  ) {
                    lastParagraphIndentation.paragraphX2 = linesBbox[2];
                  } else rightIndentInInch = lastRightIndentInInch;
                }

                children.push(
                  new docx.Paragraph({
                    children: [].concat(
                      flatten(
                        lines.map((line, index) =>
                          line.phrases.map(
                            (phrase) =>
                              new docx.TextRun({
                                size: 12 * 2, // commonFontSizeInPara,
                                text: phrase.text.trim() + ' ',
                                italics: phrase.font_styles?.includes('italic'),
                                bold: phrase.font_styles?.includes('bold')
                              })
                          )
                        )
                      )
                    ),
                    indent: {
                      left: `${leftIndentInInch}in`,
                      firstLine: `${firstLineIndentInInch}in`,
                      right: `${rightIndentInInch}in`
                    },
                    alignment: docx.AlignmentType.JUSTIFIED,
                    spacing: {
                      before: itemIndex === 0 ? paragraphSpacing.before : 0,
                      after:
                        itemIndex === lineRanges.length - 1 ? paragraphSpacing.after : 0
                    }
                  })
                );
              } else {
                // is standalone line
                const line = paragraph.paragraph.lines[item as number];
                const listTextRun = [];
                const tabStops = [];
                let leftIndentInInch = 0;
                let rightIndentInInch = 0;

                const firstLineLeftIndent = this.roundToAtMost3Decimal(
                  this.convertPixelsToInch(
                    line.bboxes[pageIndex + 1][0] * A4Width - pageMargin.left
                  )
                );
                const lastLeftIndentInInch = this.calculateLeftIndent(
                  lastParagraphIndentation.paragraphX1,
                  pageMargin.left,
                  A4Width
                );
                if (
                  lastParagraphIndentation.paragraphX1 &&
                  lastLeftIndentInInch &&
                  lastLeftIndentInInch <= firstLineLeftIndent
                )
                  leftIndentInInch = lastLeftIndentInInch;

                const lastRightIndentInInch = this.calculateRightIndent(
                  lastParagraphIndentation.paragraphX2,
                  pageMargin.right,
                  A4Width
                );
                if (lastParagraphIndentation.paragraphX2 && lastRightIndentInInch)
                  rightIndentInInch = lastRightIndentInInch;

                const firstLineIndentInInch = this.calculateFirstLineIndent(
                  line.bboxes[pageIndex + 1][0],
                  pageMargin.left,
                  A4Width,
                  leftIndentInInch
                );

                line.phrases.forEach((phrase, index) => {
                  const { shouldAddTabStop, tabStopPosition, tabStopType } =
                    this.findTabstopPostion(
                      phrase,
                      pageNumber,
                      A4Width,
                      this.convertPixelsToTwip(pageMargin.left)
                    );
                  listTextRun.push(
                    new docx.TextRun({
                      text: `${
                        index === 0 ? '' : shouldAddTabStop ? '\t' : ''
                      }${phrase.text}`,
                      size: 12 * 2, // commonFontSizeInPara
                      italics: phrase.font_styles?.includes('italic'),
                      bold: phrase.font_styles?.includes('bold')
                    })
                  );
                  index !== 0 &&
                    shouldAddTabStop &&
                    tabStops.push({
                      type: tabStopType,
                      position: tabStopPosition
                    });
                });

                children.push(
                  new docx.Paragraph({
                    children: listTextRun,
                    tabStops,
                    indent: {
                      left: `${leftIndentInInch}in`,
                      firstLine: `${firstLineIndentInInch}in`,
                      right: `${rightIndentInInch}in`
                    },
                    alignment: docx.AlignmentType.JUSTIFIED,
                    spacing: {
                      before: itemIndex === 0 ? paragraphSpacing.before : 0,
                      after:
                        itemIndex === lineRanges.length - 1 ? paragraphSpacing.after : 0
                    }
                  })
                );
              }
            });

            break;
          }
          case 'paragraphs': {
            // case paragraphs often be header or footer and will be represented as borderless table
            const cols = this.constructTable(paragraph.paragraphs, pageIndex);
            const borderless = {
              bottom: { style: docx.BorderStyle.NONE },
              top: { style: docx.BorderStyle.NONE },
              left: { style: docx.BorderStyle.NONE },
              right: { style: docx.BorderStyle.NONE },
              insideHorizontal: { style: docx.BorderStyle.NONE },
              insideVertical: { style: docx.BorderStyle.NONE }
            };

            /* 
              newRow - 1 row in top-level Table
              newRow.children[n]: each TableCell is a Table
              newRow.children[n].rows: each Row is corresponse to a paragraph
            */
            const newRow = new docx.TableRow({
              children: cols.map((col) => {
                const table = new docx.Table({
                  rows: col.rows.map((para, paraIndex) => {
                    const paraSpacing = this.findLineSpacing(
                      para.bboxes[pageNumber],
                      get(col.rows, `${paraIndex - 1}.bboxes.${pageIndex + 1}`),
                      get(col.rows, `${paraIndex + 1}.bboxes.${pageIndex + 1}`),
                      pageIndex,
                      pagesDimension
                    );

                    const commonFontSizeInPara = this.findCommonFontSizeInPara(
                      para,
                      pageIndex,
                      pagesDimension
                    );
                    const lines = para.lines.map((line, lineIndex) => {
                      const alignment = this.classifyLineAlignmentInParagraph(
                        line.bboxes[pageNumber],
                        para.bboxes[pageNumber]
                      );
                      // console.log({ alignment, line });
                      return new docx.Paragraph({
                        spacing: {
                          before: lineIndex === 0 ? paraSpacing.before : 0,
                          after:
                            lineIndex === para.lines.length - 1 ? paraSpacing.after : 0
                        },
                        children: line.phrases.map(
                          (phrase) =>
                            new docx.TextRun({
                              text: phrase.text,
                              size: 12 * 2, // commonFontSizeInPara,
                              italics: phrase.font_styles?.includes('italic'),
                              bold: phrase.font_styles?.includes('bold')
                            })
                        ),
                        alignment
                      });
                    });

                    const cell = new docx.TableCell({ children: lines });
                    const row = new docx.TableRow({
                      children: [cell]
                    });
                    return row;
                  }),
                  width: {
                    size: 100,
                    type: docx.WidthType.PERCENTAGE
                  },
                  borders: borderless
                });
                // col.rows.map((para) => {
                // });
                return new docx.TableCell({ children: [table] });
              })
            });
            children.push(
              new docx.Table({
                rows: [newRow],
                borders: borderless,
                width: {
                  size: 100,
                  type: docx.WidthType.PERCENTAGE
                }
              })
            );
            break;
          }
          case 'table': {
            const rows = [];
            paragraph['table'].rows.forEach((row) => {
              rows.push(
                new docx.TableRow({
                  children: row.cells.map((cell) => {
                    return new docx.TableCell({
                      children: [
                        new docx.Paragraph({
                          children: [
                            new docx.TextRun({
                              text: cell.text,
                              italics: cell.font_styles?.includes('italic'),
                              bold: cell.font_styles?.includes('bold')
                            })
                          ]
                        })
                      ],
                      columnSpan: cell['colspan'],
                      rowSpan: cell['rowspan']
                    });
                  })
                })
              );
            });

            children.push(
              new docx.Table({
                rows,
                width: {
                  size: 100,
                  type: docx.WidthType.PERCENTAGE
                }
              }),
              new docx.Paragraph({})
            );
            break;
          }
          // case 'figure': {
          //   /* TODO: do nothing */
          //   break;
          // }
        }
      });

      /* add stamp crop image */
      const figures = ocrResult.paragraphs[pageIndex].cells.filter(
        (cell) => cell.type === 'Figure'
      );

      if (isFigureIncluded && figures.length) {
        const pageDimension = pagesDimension[pageIndex];
        const pageImage = pagesImage[pageIndex];
        let pageImageScaledInA4: fabric.Image;
        try {
          pageImageScaledInA4 = await this.fromURLPromise(pageImage.base64Url, {
            scaleX: A4Width / (pageDimension.width * pageImage.renderScale),
            scaleY: A4Height / (pageDimension.height * pageImage.renderScale)
          });
        } catch (error) {
          console.log(
            `getImageURLByPageNumber() or fromURLPromise() failed => NOT extracting stamps`
          );
        }
        if (pageImageScaledInA4) {
          figures.forEach((figure) => {
            const [x, y, bboxWidth, bboxHeight] = this.getDrawingBoundingBox(
              figure.bboxes[pageIndex + 1],
              A4Width,
              A4Height
            );

            const stampURL = pageImageScaledInA4.toDataURL({
              left: x,
              top: y,
              width: bboxWidth,
              height: bboxHeight
            });

            const stampImage = new docx.ImageRun({
              data: stampURL,
              transformation: {
                width: bboxWidth,
                height: bboxHeight
              },
              floating: {
                // allowOverlap: true,
                horizontalPosition: {
                  // relative: docx.HorizontalPositionRelativeFrom.PAGE,
                  // align: docx.HorizontalPositionAlign.CENTER,
                  offset: this.convertPixelsToEmu(x)
                },
                verticalPosition: {
                  // relative: docx.VerticalPositionRelativeFrom.PAGE,
                  // align: docx.VerticalPositionAlign.CENTER,
                  offset: this.convertPixelsToEmu(y)
                }
              }
            });
            children.push(
              new docx.Paragraph({
                children: [stampImage]
              })
            );
          });
        }
      }

      sections.push({
        properties: {
          page: {
            margin: {
              bottom: this.convertPixelsToTwip(pageMargin.bottom),
              top: this.convertPixelsToTwip(pageMargin.top),
              left: this.convertPixelsToTwip(pageMargin.left),
              right: this.convertPixelsToTwip(pageMargin.right)
            },
            size: { orientation: pagesDimension[pageIndex].orientation }
          }
        },
        children
      });
    }

    const doc = new docx.Document({ sections });
    return await docx.Packer.toBlob(doc); // cannot use toBuffer() like in exporter, browser does NOT have native Buffer support
  }

  /* start util functions */
  private convertPixelsToTwip(px: Pixel): Twip {
    return Math.round(px * 15);
  }

  private convertTwipsToHalfPoint(twip: Twip): HalfPoint {
    return Math.round(twip / 10);
  }

  private convertPixelsToInch(px: Pixel): Inch {
    return (px * 15) / 1440;
  }

  private convertPointsToTwip(points: Point): Twip {
    return Math.round(points * 20);
  }

  private convertPixelsToEmu(px) {
    return Math.round((px * 914400 * 15) / 1440);
  }

  private calculateDifferenceInPercentage(original: number, value: number) {
    if (typeof original !== 'number' || typeof value !== 'number') return 100;
    const diff = Math.abs(value - original);
    return (diff / original) * 100;
  }

  private calculateLeftIndent(
    x1: RelativeUnit,
    pageLeftMargin: Pixel,
    A4Width: Pixel
  ): Inch {
    if (!isNumber(x1) || !isNumber(pageLeftMargin) || !isNumber(A4Width)) return 0;
    return this.roundToAtMost3Decimal(
      this.convertPixelsToInch(x1 * A4Width - pageLeftMargin)
    );
  }

  private calculateRightIndent(
    x2: RelativeUnit,
    pageRightMargin: Pixel,
    A4Width: Pixel
  ): Inch {
    if (!isNumber(x2) || !isNumber(pageRightMargin) || !isNumber(A4Width)) return 0;
    return this.roundToAtMost3Decimal(
      this.convertPixelsToInch((1 - x2) * A4Width - pageRightMargin)
    );
  }

  private calculateFirstLineIndent(
    x1: RelativeUnit,
    pageLeftMargin: Pixel,
    A4Width: Pixel,
    paragraphLeftIndentInInch: Inch
  ): Inch {
    if (
      !isNumber(x1) ||
      !isNumber(pageLeftMargin) ||
      !isNumber(A4Width) ||
      !isNumber(paragraphLeftIndentInInch)
    )
      return 0;

    let firstLineLeftIndent = this.roundToAtMost3Decimal(
      this.convertPixelsToInch(x1 * A4Width - pageLeftMargin)
    );
    console.assert(
      firstLineLeftIndent >= paragraphLeftIndentInInch,
      `first line indent (${firstLineLeftIndent}) < left indent (${paragraphLeftIndentInInch}) ???`
    );

    return Math.abs(
      this.roundToAtMost3Decimal(firstLineLeftIndent - paragraphLeftIndentInInch)
    );
  }

  /* end util functions */

  private findPageMarginInPixel(
    pageIndex: number,
    pagesDimension: PagesDimension,
    listPagesMargin: PageMargin[]
  ): {
    top: Pixel;
    left: Pixel;
    right: Pixel;
    bottom: Pixel;
  } {
    const { A4Width, A4Height } = pagesDimension[pageIndex];
    return {
      bottom: this.convertTwipsToPixel(this.A4DefaultMarginInTwip.bottom),
      top: listPagesMargin[pageIndex].topMargin * A4Height,
      left: listPagesMargin[pageIndex].leftMargin * A4Width,
      right: A4Width - listPagesMargin[pageIndex].rightMargin * A4Width
    };
  }

  private findAllTableBboxesForPages(ocrResult: OcrResult['object']): Bbox[][] {
    return ocrResult.paragraphs.map((page, index) => {
      let tableBboxes = [];
      get(page, `cells`, []).forEach((paragraph) => {
        if (paragraph.type === 'Table') {
          /* paragraph.type: paragraph not having correct bboxes data to display yet */
          /* draw bboxes for paragraph.type: paragraph by using top-left cell and bottom-right cell instead */
          const topLeftX = Math.min(
            ...paragraph.cells.map((tableCell) => tableCell.bboxes[paragraph.page_id][0])
          );
          const topLeftY = Math.min(
            ...paragraph.cells.map((tableCell) => tableCell.bboxes[paragraph.page_id][1])
          );
          const bottomRightX = Math.max(
            ...paragraph.cells.map((tableCell) => tableCell.bboxes[paragraph.page_id][2])
          );
          const bottomRightY = Math.max(
            ...paragraph.cells.map((tableCell) => tableCell.bboxes[paragraph.page_id][3])
          );
          const bbox = [topLeftX, topLeftY, bottomRightX, bottomRightY];
          tableBboxes.push(bbox);
        }
      });
      return tableBboxes;
    });
  }

  private findPagesMargin(
    ocrResult: OcrResult['object'],
    tablesInPages: Bbox[][]
  ): PageMargin[] {
    // TODO: find pages indent: paragraph left-most x1, paragraph right-most x2
    return ocrResult.paragraphs.map((page, pageIndex) => {
      const tablesInPage = cloneDeep(tablesInPages[pageIndex]);
      let leftMargin, rightMargin, topMargin, bottomMargin;
      page.cells.map((paragraph, paragraphIndex) => {
        let paragraphX1 = paragraph.bboxes[pageIndex + 1][0];
        let paragraphY1 = paragraph.bboxes[pageIndex + 1][1];
        let paragraphX2 = paragraph.bboxes[pageIndex + 1][2];
        let paragraphY2 = paragraph.bboxes[pageIndex + 1][3];
        if (paragraph.type === 'Table') {
          paragraphX1 = tablesInPage[0][0];
          paragraphY1 = tablesInPage[0][1];
          paragraphX2 = tablesInPage[0][2];
          paragraphY2 = tablesInPage[0][3];
          tablesInPage.splice(0, 1);
        }
        leftMargin = Math.min(
          paragraphIndex === 0 ? paragraphX1 : leftMargin,
          paragraphX1
        );
        rightMargin = Math.max(
          paragraphIndex === 0 ? paragraphX2 : rightMargin,
          paragraphX2
        );
        topMargin = Math.min(paragraphIndex === 0 ? paragraphY1 : topMargin, paragraphY1);
        bottomMargin = Math.max(
          paragraphIndex === 0 ? paragraphY2 : bottomMargin,
          paragraphY2
        );
      });

      return {
        leftMargin: !leftMargin || Math.abs(leftMargin) === Infinity ? 0 : leftMargin,
        rightMargin: !rightMargin || Math.abs(rightMargin) === Infinity ? 1 : rightMargin,
        topMargin: !topMargin || Math.abs(topMargin) === Infinity ? 0 : topMargin,
        bottomMargin:
          !bottomMargin || Math.abs(bottomMargin) === Infinity ? 1 : bottomMargin
      };
    });
  }

  private findLineSpacing(
    lineBbox: Bbox,
    prevBbox: Bbox,
    nextBbox: Bbox,
    pageIndex: number,
    pagesDimension: PagesDimension
  ): { before: number; after: number } {
    let before = 0,
      after = 0;
    const [_, y1, _1, y2] = lineBbox;
    if (prevBbox?.length) {
      const [_, _1, _2, prevY2] = prevBbox;
      before =
        this.convertPointsToTwip(
          Math.abs(y1 - prevY2) * pagesDimension[pageIndex].height
        ) || 0;
    }

    if (nextBbox?.length) {
      const [_, nextY1, _1, _2] = nextBbox;
      after =
        this.convertPointsToTwip(
          Math.abs(nextY1 - y2) * pagesDimension[pageIndex].height
        ) || 0;
    }

    return { before, after };
  }

  private findLineFontSize(
    line: Line,
    pageIndex: number,
    pagesDimension: PagesDimension
  ): number {
    const lineBboxes = line.bboxes[pageIndex + 1];
    const lineHeight = (lineBboxes[3] - lineBboxes[1]) * pagesDimension[pageIndex].height;
    // console.log(
    //   line.text,
    //   lineBboxes,
    //   pagesDimension[pageIndex].height,
    //   lineHeight,
    //   convertTwipsToHalfPoint(convertPixelsToTwip(lineHeight))
    // );
    return this.convertTwipsToHalfPoint(this.convertPixelsToTwip(lineHeight));
  }

  private findCommonFontSizeInPara(
    paragraph: Paragraph,
    pageIndex: number,
    pagesDimension: PagesDimension
  ): number {
    let commonFontSizeInPara = Math.round(
      paragraph.lines.reduce(
        (acc, curr) => acc + this.findLineFontSize(curr, pageIndex, pagesDimension),
        0
      ) / paragraph.lines.length
    );
    commonFontSizeInPara % 2 !== 0 && commonFontSizeInPara++;
    return commonFontSizeInPara;
  }

  private divideParagraphIntoLineRanges(
    paragraph: Paragraph,
    pageNumber: number,
    A4Width: number
  ): (number[] | number)[] {
    if (!paragraph.lines.length) return [];
    if (paragraph.lines.length === 1) return [0];
    /* find all line nearness */
    const paragraphBbox = paragraph.bboxes[pageNumber];
    /* eg. [{ nearStart: bool, nearEnd: bool }] */
    const linesNearness = paragraph.lines.map((line) => {
      /* if line with more than 1 phrase, then itself is will not be consider standalone line */
      if (line.phrases?.length > 1) return { nearStart: false, nearEnd: false };
      const lineBbox = line.bboxes[pageNumber];

      /* magic number (in pixel) represent nearness, larger than this magic number => far, smaller => near */
      const magicNumber = 10;
      // in pixel
      const distanceBetweenLineStartAndPageStart = Math.abs(
        (lineBbox[0] - paragraphBbox[0]) * A4Width
      );
      const distanceBetweenLineEndAndPageEnd = Math.abs(
        (paragraphBbox[2] - lineBbox[2]) * A4Width
      );
      return {
        nearStart: distanceBetweenLineStartAndPageStart <= magicNumber,
        nearEnd: distanceBetweenLineEndAndPageEnd <= magicNumber
      };
    });

    let adjacentLinePairs = [];
    for (let i = 0; i < paragraph.lines.length - 1; i++) {
      const currLineId = i;
      const nextLineId = i + 1;
      const seamless =
        linesNearness[currLineId].nearEnd && linesNearness[nextLineId].nearStart;
      adjacentLinePairs.push({ seamless, pair: [currLineId, nextLineId] });
    }
    let seamlessLineRanges;
    seamlessLineRanges = adjacentLinePairs.reduce((acc, curr, index) => {
      const prev = acc[acc.length - 1];
      // console.log(index, acc, acc.length, prev?.seamless, curr.seamless);
      if (!prev) {
        acc.push({
          seamless: curr.seamless,
          pairs: [curr.pair]
        });
        return acc;
      }
      if (prev.seamless !== curr.seamless) {
        acc.push({
          seamless: curr.seamless,
          pairs: [curr.pair]
        });
        return acc;
      } else {
        acc[acc.length - 1].pairs.push(curr.pair);
        return acc;
      }
    }, []);
    /* ... => true => false => true => ... */
    seamlessLineRanges = seamlessLineRanges
      .map(({ seamless, pairs }, index) => {
        const range = uniq(flatten(pairs));
        if (!seamless) {
          if (index !== 0) {
            range.shift();
          }
          if (index !== seamlessLineRanges.length - 1) {
            range.pop();
          }
        }
        return { seamless, range };
      })
      .filter(({ range }) => range.length)
      .map(({ seamless, range }) => (seamless ? [range] : range));
    seamlessLineRanges = flatten(seamlessLineRanges);
    // console.log({ linesNearness, seamlessLineRanges, lines: paragraph.lines });
    return seamlessLineRanges;
  }

  private findTabstopPostion(
    subject,
    pageNumber,
    A4Width,
    pageLeftMargin,
    lastParagraphFirstLineIndentPosition?
  ) {
    const bbox = get(subject, `bboxes.${pageNumber}`, [0, 0, 0, 0]);
    const lineLeft = this.convertPixelsToTwip(bbox[0] * A4Width);
    // const lineCenter = convertPixelsToTwip(((bbox[2] + bbox[0]) * A4Width) / 2);
    const tabStopType = docx.TabStopType.LEFT;
    let tabStopPosition = lineLeft - pageLeftMargin;

    const shouldAddTabStop = tabStopPosition > 50; // 50 twip/dxa

    /* TODO: */
    // if (shouldAddTabStop && !!lastParagraphFirstLineIndentPosition) {
    //   // compare to last paragraph first line indent, if current first line indent > last first line indent 20% => use current first line indent, else use last first line indent
    //   let compareFirstLineIndent = tabStopPosition / lastParagraphFirstLineIndentPosition;
    //   compareFirstLineIndent =
    //     compareFirstLineIndent < 1 ? 1 / compareFirstLineIndent : compareFirstLineIndent;
    //   if ((compareFirstLineIndent - 1) * 100 <= 20)
    //     tabStopPosition = lastParagraphFirstLineIndentPosition;
    //   // console.log(shouldAddTabStop, tabStopPosition);
    // }

    return {
      tabStopType,
      tabStopPosition,
      shouldAddTabStop
    };
  }

  private classifyLineAlignmentInParagraph(
    lineBboxes: Bbox,
    paraBboxes: Bbox
  ): docx.AlignmentType {
    let alignmentType = docx.AlignmentType.JUSTIFIED;
    const paraWidth = paraBboxes[2] - paraBboxes[0]; // as 100%
    const lineWidthPercent = ((lineBboxes[2] - lineBboxes[0]) * 100) / paraWidth;
    const d1Percent = ((lineBboxes[0] - paraBboxes[0]) * 100) / paraWidth,
      d2Percent = ((paraBboxes[2] - lineBboxes[2]) * 100) / paraWidth;
    // mdlt1e: maximum drift length (from center of line) toward 1 end (of paragraph)
    const mdlt1e = (100 - lineWidthPercent) / 2;

    /* 
      compare d1/d2 to the mdlt1e
      d1 < d2 => line drifted to left,
      if line drift passed half of mdlt1e then it stick to left
      vice versa to drifting to right
    */
    if (d1Percent === d2Percent) return docx.AlignmentType.CENTER;
    if (d1Percent < d2Percent) {
      if (d1Percent * 2 < mdlt1e) alignmentType = docx.AlignmentType.LEFT;
      else alignmentType = docx.AlignmentType.CENTER;
    } else {
      if (d2Percent * 2 < mdlt1e) alignmentType = docx.AlignmentType.RIGHT;
      else alignmentType = docx.AlignmentType.CENTER;
    }
    return alignmentType;
  }

  private isParagraphAFrame(
    paragraph: Paragraph,
    pageIndex: number,
    listPagesMargin: PageMargin[]
  ): boolean {
    /* 
    1-line paragraph could easily put anywhere horizontally using tabstop, not using frame in this case to avoid some layout misplacement issue
    still, some actual case require 1-line paragraph as a frame WILL be missed
    */
    if (paragraph.lines?.length === 1) return false;
    const [x1, y1, x2, y2] = get(paragraph, `bboxes.${pageIndex + 1}`, [0, 0, 0, 0]);
    const { leftMargin, rightMargin } = listPagesMargin[pageIndex];
    const pageCenter = (rightMargin + leftMargin) / 2;
    if (x2 < pageCenter || x1 > pageCenter) return true;
    return false;
  }

  private constructTable(
    paragraphList: Paragraph[],
    pageIndex: number
  ): { rows: Paragraph[]; bbox: Bbox }[] {
    // console.log(paragraphList);
    const cols = [];
    /* 
      const cols = [
        rows: [para1, para2],
        bbox: [x1, y1, x2, y2] // smallest x1 y1 and largest x2 y2 from rows list
      ]
    */
    paragraphList
      /* 
        sort by x coord helps with col order
        not help with order of rows in a col
        re-sort rows order later
      */
      .sort(
        (a, b) =>
          a.bboxes[pageIndex + 1][0] - b.bboxes[pageIndex + 1][0] ||
          a.bboxes[pageIndex + 1][2] - b.bboxes[pageIndex + 1][2]
      )
      .forEach((para) => {
        // console.log(cols);
        if (!cols.length) {
          cols.push({
            rows: [para],
            bbox: para.bboxes[pageIndex + 1]
          });
          return;
        }

        let foundCol = cols.find(({ rows, bbox }, colIndex) => {
          const [paraX1, paraY1, paraX2, paraY2] = para.bboxes[pageIndex + 1];
          const [colX1, colY1, colX2, colY2] = bbox;
          // check if horizontally, if a para graph is OVERLAPPING on eachother, then group them as row of 1 big imaginary col => wrong representation to purposely placing paragraph all over the place from the original file, or to a complex table with col/row span
          return (
            (paraX1 >= colX1 && paraX1 <= colX2) || (paraX2 >= colX1 && paraX2 <= colX2)
          );
        });
        if (foundCol) {
          foundCol.rows.push(para);
          foundCol.bbox[0] = Math.min(
            ...foundCol.rows.map((para) => para.bboxes[pageIndex + 1][0])
          );
          foundCol.bbox[1] = Math.min(
            ...foundCol.rows.map((para) => para.bboxes[pageIndex + 1][1])
          );
          foundCol.bbox[2] = Math.max(
            ...foundCol.rows.map((para) => para.bboxes[pageIndex + 1][2])
          );
          foundCol.bbox[3] = Math.max(
            ...foundCol.rows.map((para) => para.bboxes[pageIndex + 1][3])
          );
        } else
          cols.push({
            rows: [para],
            bbox: para.bboxes[pageIndex + 1]
          });
        // console.log(para, paraIndex);
      });

    // re-sort rows order
    cols.forEach((col) => {
      col.rows.sort(
        (a, b) =>
          a.bboxes[pageIndex + 1][1] - b.bboxes[pageIndex + 1][1] ||
          a.bboxes[pageIndex + 1][3] - b.bboxes[pageIndex + 1][3]
      );
    });
    return cols;
  }
  /* end export ocr/scan-table */

  /* start export ocr/scan */
  public async exportDocxScan(args: {
    fileName;
    ocrResult;
    pagesDimension;
    exportOption;
    categorizedPageRanges;
    currentPage;
  }) {
    FileSaver.saveAs(await this.exportByLines(args), args.fileName + '.docx');
  }

  /* loop through every lines again, in a line loop through every phrase, determine whether it's left, center, right */
  private classifyPhraseAlignment(phrase, pageNumber, pagesDimension) {
    // TODO: do văn bản tiêu chuẩn có cách trái 3cm, cách phải 1.5cm => tâm của văn bản = (độ rộng văn bản / 2) + (lề trái - lề phải)
    const pageHorizontalCenter = pagesDimension[pageNumber - 1].width / 2;
    const deviationDelta = 20; // offset?, magic value
    const x1 = phrase.bboxes[pageNumber][0];
    const x2 = phrase.bboxes[pageNumber][2];
    const phraseBboxHorizontalCenter =
      ((x2 + x1) * pagesDimension[pageNumber - 1].width) / 2;
    // console.log(
    //   pageHorizontalCenter,
    //   deviationDelta,
    //   phraseBboxHorizontalCenter
    // );

    if (
      pageHorizontalCenter - deviationDelta <= phraseBboxHorizontalCenter &&
      phraseBboxHorizontalCenter <= pageHorizontalCenter + deviationDelta
    ) {
      phrase.alignment = 'center';
    } else {
      if (pageHorizontalCenter - deviationDelta > phraseBboxHorizontalCenter) {
        phrase.alignment = 'left';
      }
      if (pageHorizontalCenter + deviationDelta < phraseBboxHorizontalCenter) {
        phrase.alignment = 'right';
      }
    }
  }

  /* loop through every lines, find all the phrase belongs to that line */
  private prepareLinesForExportingDocx(ocrResult) {
    const lines = [];
    ocrResult.lines.forEach(({ cells: linesInPage }, index) => {
      lines[index] = [];
      const pageNumber = index + 1;
      // if (pageNumber !== 2) return;
      let phrasesInPage = ocrResult.phrases[index].cells;
      /* filter all phrases with (x1, y1) & (x2, y2) lie inside table's bbox */
      const tablesInPage = this.findAllTableBboxesForPage(pageNumber, ocrResult);
      phrasesInPage = phrasesInPage.filter((phrase) => {
        const [x1, y1, x2, y2] = phrase.bboxes[pageNumber];
        const phraseTopLeft = { x: x1, y: y1 };
        const phraseBottomRight = { x: x2, y: y2 };
        return !tablesInPage.some(([x1, y1, x2, y2]) => {
          const tableTopLeft = { x: x1, y: y1 };
          const tableBottomRight = { x: x2, y: y2 };
          return (
            tableTopLeft.x <= phraseTopLeft.x &&
            phraseTopLeft.x <= tableBottomRight.x &&
            tableTopLeft.y <= phraseTopLeft.y &&
            phraseTopLeft.y <= tableBottomRight.y &&
            tableTopLeft.x <= phraseBottomRight.x &&
            phraseBottomRight.x <= tableBottomRight.x &&
            tableTopLeft.y <= phraseBottomRight.y &&
            phraseBottomRight.y <= tableBottomRight.y
          );
        });
      });
      let phrasesGroupByLine = [];
      linesInPage.forEach((line, index) => {
        phrasesGroupByLine[index] = {
          phrases: [],
          type: 'Line',
          text: line.text
        };
        const [x1, y1, x2, y2] = line.bboxes[pageNumber];
        const lineTopLeft = { x: x1, y: y1 };
        const lineBottomRight = { x: x2, y: y2 };
        phrasesInPage.forEach((phrase) => {
          const [x1, y1, x2, y2] = phrase.bboxes[pageNumber];
          const phraseTopLeft = { x: x1, y: y1 };
          const phraseBottomRight = { x: x2, y: y2 };
          if (
            /* both (x1, y1) & (x2, y2) of phrase lie inside line's bbox */
            lineTopLeft.x <= phraseTopLeft.x &&
            phraseTopLeft.x <= lineBottomRight.x &&
            lineTopLeft.y <= phraseTopLeft.y &&
            phraseTopLeft.y <= lineBottomRight.y &&
            lineTopLeft.x <= phraseBottomRight.x &&
            phraseBottomRight.x <= lineBottomRight.x &&
            lineTopLeft.y <= phraseBottomRight.y &&
            phraseBottomRight.y <= lineBottomRight.y
          ) {
            phrasesGroupByLine[index].phrases.push(phrase);
            return;
          }
        });
      });
      const tableIndexRanges = []; // find all the ranges of line that are tables
      phrasesGroupByLine.forEach((line, index) => {
        // console.log(line);
        if (!line.phrases.length) {
          if (
            get(tableIndexRanges, `${tableIndexRanges.length - 1}.${1}`) ===
            index - 1
          ) {
            tableIndexRanges[tableIndexRanges.length - 1][1] = index;
          } else tableIndexRanges.push([index, index]);
        }
      });
      const { tables } = this.getTablesAndParagraphsForPage(pageNumber, ocrResult);
      /**
       * each tables should have had its range in lines
       * fill that range with corresponding table
       * if tables.length !== tableIndexRanges.length
       *  => ocr response is not correct, some phrases bboxes don't lie within table in ocr response => replace it with blank line
       */

      reverse(tableIndexRanges).forEach(([start, end]) => {
        phrasesGroupByLine.splice(
          start,
          end - start + 1,
          tables.pop() ?? { phrases: [], type: 'Line' } /*  */
        );
      });

      lines[index] = phrasesGroupByLine;
    });
    return lines;
  }

  private sortPhrasesInLineAfterClassified = (phrases, line, foundPhrases) => {
    phrases.forEach((phrase) => {
      const index = line.text.indexOf(phrase.text, foundPhrases[phrase.text] + 1);
      foundPhrases[phrase.text] = index;
      phrase.index = index;
    });
    phrases.sort((a, b) => a.index - b.index);
  };

  private joinPhrases = (phrases, type) => {
    return phrases.reduce(
      (joined, curr, i) => ({
        ...joined,
        text: joined.text + (i === 0 ? '' : ' ') + curr.text
      }), // a single space " " is not the solution for all cases
      { alignment: type, text: '' }
    );
  };

  private getTablesAndParagraphsForPage(pageNumber, ocrResult) {
    const paragraphs = get(ocrResult, `paragraphs.${pageNumber - 1}.cells`, []);
    const paras = [],
      tables = [];
    paragraphs.forEach((item) => {
      if (item.type === 'Table') {
        tables.push(item);
      } else paras.push(item);
    });
    return { tables, paragraphs: paras };
  }

  private findAllTableBboxesForPage(pageNumber, ocrResult) {
    const tableBboxes = [];
    get(ocrResult, `paragraphs.${pageNumber - 1}.cells`, []).forEach((paragraph) => {
      if (paragraph.type === 'Table') {
        /* paragraph.type: paragraph not having correct bboxes data to display yet */
        /* draw bboxes for paragraph.type: paragraph by using top-left cell and bottom-right cell instead */
        const topLeftCellBboxes = get(paragraph, `rows.0.cells.0.bboxes.${pageNumber}`);
        const numberOfRowsInTable = get(paragraph, 'rows.length');
        const numberOfCellsInLastRow = get(
          paragraph,
          `rows.${numberOfRowsInTable - 1}.cells.length`
        );
        const bottomRightCellsBboxes = get(
          paragraph,
          `rows.${numberOfRowsInTable - 1}.cells.${
            numberOfCellsInLastRow - 1
          }.bboxes.${pageNumber}`
        );

        tableBboxes.push([
          topLeftCellBboxes[0],
          topLeftCellBboxes[1],
          bottomRightCellsBboxes[2],
          bottomRightCellsBboxes[3]
        ]);
      }
    });
    return tableBboxes;
  }

  private exportByLines({
    fileName,
    exportOption,
    categorizedPageRanges,
    currentPage,
    pagesDimension,
    ocrResult
  }) {
    const A4Width = docx.convertInchesToTwip(8.27);
    const A4Height = docx.convertInchesToTwip(11.69);
    // https://luatvietnam.vn/hanh-chinh/cach-trinh-bay-van-ban-570-20126-article.html
    const A4DefaultMargin = {
      top: docx.convertMillimetersToTwip(20),
      right: docx.convertMillimetersToTwip(15),
      bottom: docx.convertMillimetersToTwip(20),
      left: docx.convertMillimetersToTwip(30)
    };
    const A4TabStopMaxPositionVertical =
      A4Width - A4DefaultMargin.left - A4DefaultMargin.right;

    const A4TabStopMaxPositionHorizontal =
      A4Height - A4DefaultMargin.left - A4DefaultMargin.right;
    const lines = this.prepareLinesForExportingDocx(ocrResult);
    // console.log(lines);
    // console.log(ocrResult);
    const sections: docx.ISectionOptions[] = [];

    let currentRange: { template: string; start: number; end: number };
    if (exportOption === 'currentRange') {
      currentRange = categorizedPageRanges.find(({ start, end }) => {
        return start <= currentPage - 1 && currentPage - 1 <= end;
      });
      fileName = `${fileName}_${currentRange.template}`;
    }

    lines.forEach((page, index) => {
      const pageNumber = index + 1;
      if (exportOption === 'currentPage' && pageNumber !== currentPage) return;
      if (currentRange) {
        if (pageNumber - 1 < currentRange.start || currentRange.end < pageNumber - 1)
          return;
      }
      const children = [];
      page.forEach((line) => {
        if (line.type === 'Line') {
          let text = '';
          const tabStops = [];
          const leftPhrases = [],
            centerPhrases = [],
            rightPhrases = [],
            foundPhrases = {};
          line.phrases.forEach((phrase) => {
            foundPhrases[phrase.text] = -1;
            this.classifyPhraseAlignment(phrase, pageNumber, pagesDimension);
            if (phrase.alignment === 'left') leftPhrases.push(phrase);
            if (phrase.alignment === 'center') centerPhrases.push(phrase);
            if (phrase.alignment === 'right') rightPhrases.push(phrase);
          });
          this.sortPhrasesInLineAfterClassified(leftPhrases, line, foundPhrases);
          this.sortPhrasesInLineAfterClassified(centerPhrases, line, foundPhrases);
          this.sortPhrasesInLineAfterClassified(rightPhrases, line, foundPhrases);
          const phrases = [];
          leftPhrases.length && phrases.push(this.joinPhrases(leftPhrases, 'left'));
          centerPhrases.length && phrases.push(this.joinPhrases(centerPhrases, 'center'));
          rightPhrases.length && phrases.push(this.joinPhrases(rightPhrases, 'right'));
          phrases.forEach((phrase, index) => {
            switch (phrase.alignment) {
              case 'left': {
                text += phrase.text;
                break;
              }
              case 'center': {
                text += `\t${phrase.text}`;
                tabStops.push({
                  type: docx.TabStopType.CENTER,
                  position:
                    (pagesDimension[pageNumber - 1].orientation ===
                    docx.PageOrientation.PORTRAIT
                      ? A4TabStopMaxPositionVertical
                      : A4TabStopMaxPositionHorizontal) / 2
                });
                break;
              }
              case 'right': {
                text += `\t${phrase.text}`;
                tabStops.push({
                  type: docx.TabStopType.RIGHT,
                  position:
                    pagesDimension[pageNumber - 1].orientation ===
                    docx.PageOrientation.PORTRAIT
                      ? A4TabStopMaxPositionVertical
                      : A4TabStopMaxPositionHorizontal
                });
                break;
              }
              default: {
                break;
              }
            }
          });
          children.push(
            new docx.Paragraph({
              text,
              tabStops
            })
          );
        } else if (line.type === 'Table') {
          const rows = [];
          line.rows.forEach((row) => {
            rows.push(
              new docx.TableRow({
                children: row.cells.map((cell) => {
                  return new docx.TableCell({
                    children: [new docx.Paragraph(cell.text)]
                  });
                })
              })
            );
          });

          children.push(
            new docx.Table({
              rows,
              width: {
                size: 100,
                type: docx.WidthType.PERCENTAGE
              }
            }),
            new docx.Paragraph({})
          );
        }
      });
      sections.push({
        properties: {
          page: {
            margin: A4DefaultMargin,
            size: {
              orientation: pagesDimension[index].orientation
            }
          }
        },
        children
      });
    });
    const doc = new docx.Document({ sections });
    // console.log(sections, doc);
    return docx.Packer.toBlob(doc);
  }
  /* end export ocr/scan */

  /* start reusable functions are shared between more than 2 export types */
  private constructExportByParagraphsInput = (ocrResult) => {
    const paragraphs = ocrResult.paragraphs.map((page, pageIndex) => {
      const paragraphsInPage = page.cells;
      const phrasesInPage = ocrResult.phrases[pageIndex].cells;
      paragraphsInPage.forEach((paragraph, paragraphIndex) => {
        const phrasesInParagraph = phrasesInPage.filter(
          (phrase) => phrase.paragraph_id === paragraph.paragraph_id // paragraphIndex <- should not be trusted when list of para order is not sorted from top down
        );

        const linesInParagraph = [];
        phrasesInParagraph.forEach((phrase) => {
          if (linesInParagraph[phrase.line_id]) {
            linesInParagraph[phrase.line_id].phrases.push(phrase);
          } else {
            linesInParagraph[phrase.line_id] = {
              phrases: [phrase],
              bboxes: phrase.bboxes,
              text: phrase.text
            };
          }
        });
        linesInParagraph.forEach((line) => {
          const phrasesInLine = line.phrases;
          const lineX1List = [],
            lineY1List = [],
            lineX2List = [],
            lineY2List = [];
          line.text = phrasesInLine
            .map((phrase) => {
              const phraseBbox = phrase.bboxes[pageIndex + 1];
              lineX1List.push(phraseBbox[0]);
              lineY1List.push(phraseBbox[1]);
              lineX2List.push(phraseBbox[2]);
              lineY2List.push(phraseBbox[3]);
              return phrase.text;
            })
            .join(' ');

          line.bboxes = {
            [pageIndex + 1]: [
              Math.min(...lineX1List),
              Math.min(...lineY1List),
              Math.max(...lineX2List),
              Math.max(...lineY2List)
            ]
          };
        });
        paragraph.lines = linesInParagraph;
        // console.log(linesInParagraph);
      });
      return page;
    });
    // console.log(paragraphs);
    return paragraphs;
  };

  private getDrawingBoundingBox(bboxes: Bbox, canvasWidth: number, canvasHeight: number) {
    const [x1, y1, x2, y2] = bboxes;
    return [
      x1 * canvasWidth,
      y1 * canvasHeight,
      (x2 - x1) * canvasWidth,
      (y2 - y1) * canvasHeight
    ];
  }

  private fromURLPromise(
    imageURL: string,
    imgOptions?: fabric.IImageOptions
  ): Promise<fabric.Image> {
    /* wrapper promise for fabric.Image.fromURL */
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(
        imageURL,
        (img) => {
          // console.log(img);
          /* there is no way to handle error for this func yet */
          if (!img) reject('image NOT OK');
          resolve(img);
        },
        imgOptions
      );
    });
  }

  private async fetchOriginalFile(
    link: string
  ): Promise<{ arrayBuffer: ArrayBuffer; mimetype: string }> {
    let resp: Response;
    try {
      resp = await fetch(link);
    } catch (error) {
      throw new Error(
        'Invalid fileLink. fileLink should be downloadable pdf file or image file'
      );
    }

    if (!resp) throw new Error('No response from fetching fileLink');

    if (
      supportedMimetypeList.some(
        (mimetype) => resp.headers.get('Content-Type').trim() === mimetype
      )
    )
      return {
        arrayBuffer: await resp.arrayBuffer(),
        mimetype: resp.headers.get('Content-Type').trim()
      };
    else
      throw new Error(
        'Invalid fileLink. fileLink should be downloadable pdf file or image file'
      );
  }

  private async parseOriginalFile({
    fileLink,
    fileArrayBuffer,
    fileMimetype,
    fileType,
    shouldRenderPageArr
  }: {
    fileLink: string;
    fileArrayBuffer: ArrayBuffer;
    fileMimetype: SupportedMimetype;
    fileType?: SupportedFileType; // user input, should not be trusted, only need in exportDocxV2
    shouldRenderPageArr: boolean[];
  }): Promise<{
    pagesImage: RenderedPageImage[];
    pagesDimension: PagesDimension;
  }> {
    if (fileMimetype === 'application/octet-stream') {
      switch (fileType) {
        case 'pdf': {
          fileMimetype = 'application/pdf';
          break;
        }
        case 'png': {
          fileMimetype = 'image/png';
          break;
        }
        case 'jpeg':
        case 'jpg': {
          fileMimetype = 'image/jpeg';
          break;
        }
        default: {
          throw new Error('Invalid fileType');
        }
      }
    }

    const _A4Height = this.convertInchesToPixel(11.69); // longer size
    const _A4Width = this.convertInchesToPixel(8.27); // shorter size
    const pagesImage: RenderedPageImage[] = [];
    const pagesDimension: PagesDimension = [];

    const canvasFactory = new NodeCanvasFactory(); // create each canvas and its context for every page
    if (fileMimetype === 'application/pdf') {
      /* convert pdf page into image url */
      const loadingTask: PDFDocumentLoadingTask = this.pdfService.pdfjsDist.getDocument({
        data: fileArrayBuffer,
        cMapUrl: 'node_modules/pdfjs-dist/cmaps',
        cMapPacked: true,
        standardFontDataUrl: 'node_modules/pdfjs-dist/standard_fonts/',
        canvasFactory
      });
      const pdfDocument = await loadingTask.promise;
      for (let pageNo = 1; pageNo <= pdfDocument.numPages; pageNo++) {
        const page = await pdfDocument.getPage(pageNo);
        const viewport = page.getViewport({ scale: 1.0 });
        const orientation =
          viewport.width / viewport.height >= 1
            ? docx.PageOrientation.LANDSCAPE
            : docx.PageOrientation.PORTRAIT;
        const A4Width =
          orientation === docx.PageOrientation.PORTRAIT ? _A4Width : _A4Height;
        const A4Height =
          orientation === docx.PageOrientation.PORTRAIT ? _A4Height : _A4Width;
        pagesDimension.push({
          height: viewport.height,
          width: viewport.width,
          orientation,
          A4Width,
          A4Height
        });
        /* 
         render page into canvas, for cropping from original file into output docx purpose. unnecessary when inputs has no Stamp.
         => check the need for actually rendering page/image into canvas for cropping === performant
        */
        if (!shouldRenderPageArr[pageNo - 1]) {
          pagesImage.push(null);
          continue;
        }
        const scaleX = A4Width / viewport.width;
        const scaleY = A4Height / viewport.height;

        // compensate image scaling in docx A4 size
        const renderScale = Math.min(
          this.roundToAtMost3Decimal(Math.max(scaleX, scaleY)),
          2
        ); // down scale if possible (< 1), if up scale (> 1) then max scale at 2 (performant)
        const canvasAndContext = canvasFactory.create(
          viewport.width * renderScale,
          viewport.height * renderScale
        );
        const renderContext = {
          canvasContext: canvasAndContext.context,
          viewport: viewport.clone({ scale: renderScale })
        };
        const renderTask = page.render(renderContext as any);
        await renderTask.promise;
        // Convert the canvas to an image buffer.
        const image = canvasAndContext.canvas.toDataURL('image/png');
        pagesImage.push({ base64Url: image, renderScale });
        // Release page resources.
        canvasFactory.destroy(canvasAndContext);
        page.cleanup();
      }
    } else {
      // MUST use canvas to render in order to retrieve width and height
      const image = await loadImage(fileLink); // cannot use loadImage(Buffer.from(fileArrayBuffer)) like in exporter, browser does NOT have native Buffer support
      const orientation =
        image.width / image.height >= 1
          ? docx.PageOrientation.LANDSCAPE
          : docx.PageOrientation.PORTRAIT;
      const A4Width =
        orientation === docx.PageOrientation.PORTRAIT ? _A4Width : _A4Height;
      const A4Height =
        orientation === docx.PageOrientation.PORTRAIT ? _A4Height : _A4Width;
      pagesDimension.push({
        width: image.width,
        height: image.height,
        orientation,
        A4Height,
        A4Width
      });
      if (shouldRenderPageArr.at(0)) {
        const { canvas, context } = canvasFactory.create(image.width, image.height);
        context.drawImage(image, 0, 0);
        pagesImage.push({ base64Url: canvas.toDataURL(), renderScale: 1 });
        canvasFactory.destroy({ canvas, context });
      }
    }

    return {
      pagesImage,
      pagesDimension
    };
  }

  private convertInchesToPixel(inches: Inch): Pixel {
    return Math.round(this.convertTwipsToPixel(docx.convertInchesToTwip(inches)));
  }

  private convertTwipsToPixel(twips: Twip): Pixel {
    return Math.round(twips / 15);
  }

  private roundToAtMost3Decimal(num: number): number {
    return Math.round((num + Number.EPSILON) * 1000) / 1000;
  }
  /* end reusable functions are shared between more than 2 export types */
}
