<section section class="relative" style="
  background:#F6F6F6;
  /* overflow: hidden !important; */
  min-height: 100vh;
  background-size: cover;
  color: #273266;
  background-image: url('assets/img/rpa/login/bg-blur.png');
  " class="text-sm">
  <div id="wrapper" class="h-full relative flex justify-between items-center">
    <a class="mt-[52px] mb-[48px]" [href]="landingPageUrl" id="logo">
      <img src="assets/img/rpa/login/smart-reader-logo.svg" alt="">
    </a>

    <form [formGroup]="lgForm" (ngSubmit)="handleLoginBtnClicked()"
      class="text-center bg-bg-3 rounded-[18px] shadow-[0_15px_20px_-2px_rgba(39,142,255,0.08)] max-w-[540px] /* height: 614px; */ px-8 pt-8 pb-5">
      <h2 class="text-[34px] font-bold leading-[46px] mb-2"><PERSON><PERSON><PERSON> nhập</h2>
      <div id="tagline" class="font-medium px-[70px] mb-[30px] text-[#666C8A]">Chào mừng bạn
        đến với Trợ
        lý AI xử lý văn bản
        VNPT Smart Reader</div>

      <!-- <div *ngIf="error.length" class="invalid-feedback d-block alert alert-danger mb-3 py-1">
          Tên đăng nhập hoặc mật khẩu không chính xác.</div> -->
      <div class="mb-6">
        <div class="flex gap-2 items-center text-left mb-2">
          <div class=" font-bold leading-[22px]">
            Email
          </div>
          <div *ngIf="f['username']?.dirty && f['username']?.errors" class="w-full text-xs text-[#dc3545]">
            Email không được để trống.
          </div>
        </div>
        <input class="h-12 rounded-lg border border-[#E4E6F3] outline-none w-full px-3 py-1 " type="email"
          placeholder="Nhập email" formControlName="username" />
      </div>
      <div class="mb-2 relative">
        <div class="flex gap-2 items-center text-left mb-2">
          <div class=" font-bold leading-[22px] flex-none">
            Mật khẩu
          </div>
          <div *ngIf="f['password']?.dirty && f['password'].errors" class="w-full text-xs text-[#dc3545]">
            Vui lòng nhập mật khẩu.
          </div>
        </div>
        <input class="h-12 rounded-lg border border-[#E4E6F3] outline-none w-full px-3 pl-3 pr-12 "
          [type]="passwordType" formControlName="password" placeholder="Nhập mật khẩu" />
        <img (click)="showHidePassword()" src="assets/img/rpa/eye.svg" alt="eye"
          class="absolute right-4 top-[54%] cursor-pointer">
      </div>
      <div class="text-right">
        <a class="font-bold text-[#2140D2] " routerLink="/reset-password/forgot">Quên mật
          khẩu?</a>
      </div>
      <ngx-recaptcha2 #captchaElem [siteKey]="siteKey" (reset)="handleReset()" (expire)="handleExpire()"
        (error)="handleError()" (load)="handleLoad()" (success)="handleSuccess($event)" [useGlobalDomain]="false"
        [size]="size" [hl]="lang" [theme]="theme" [type]="type" formControlName="recaptcha">
      </ngx-recaptcha2>
      <div *ngIf="f['recaptcha']?.dirty && f['recaptcha'].errors" class="w-full text-xs text-[#dc3545] mt-2">
        Vui lòng xác thực recaptcha.
      </div>
      <div *ngIf="agreementCode === AgreementCode.UserNotAgreed"
        style="color: #DA1E28; text-align: justify; font-size: 12px; margin-top: 12px;">
        Tài khoản đã bị khóa do Quý khách đã thực hiện Xóa dữ liệu cá nhân hoặc Hạn chế, phản đối, rút lại sự đồng ý xử
        lý dữ liệu. Trường hợp muốn kích hoạt lại tài khoản, Quý khách vui lòng <a
          [href]="landingPageUrl + 'vi/contact'" target="_blank">Liên hệ</a> với Chúng tôi
      </div>
      <button class="font-bold h-12 mt-4 bg-[#2140D2] rounded-[10px] w-full text-white " [ngClass]="{'opacity-70 cursor-not-allowed': loginFailedCount === maxLoginFailedCount || loginFailedTimeout}" type="submit" type="submit">Đăng nhập <span *ngIf="loginFailedTimeout">({{loginFailedTimeout}}s)</span></button>
      <div class="mt-3 ">
        Bạn chưa có tài khoản? <a [href]="registerUrl" class="font-bold text-[#2140D2]">Đăng ký</a>
      </div>
      <div class="border-t border-[#E4E6F3] relative my-5">
        <span class="absolute top-[-9px] px-3 bg-white text-[#A1A5BA] text-xs -translate-x-1/2">Hoặc</span>
      </div>
      <div class="flex items-center justify-center gap-3 p-3 rounded-[10px] border border-[#E4E6F3] cursor-pointer"
        (click)="ssoWithGoogle()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M5.72975 15.5951L1.52296 17.8481C0.553806 16.1171 0 14.1198 0 11.9946C0 10.3541 0.330153 8.79354 0.921234 7.37144L1.03838 7.23828L5.26115 9.36345C4.94165 10.1784 4.76592 11.0625 4.76592 11.9946C4.77125 13.3049 5.11737 14.5352 5.72975 15.5951Z"
            fill="#FFBC00" />
          <path
            d="M23.9932 11.9947C23.9932 12.1225 23.9879 12.2556 23.9879 12.3835C23.9879 12.4101 23.9879 12.4421 23.9825 12.4687C23.9825 12.5166 23.9772 12.5699 23.9772 12.6178C23.9772 12.6285 23.9772 12.6445 23.9719 12.6604C23.9719 12.6977 23.9666 12.7403 23.9666 12.7776C23.9666 12.8149 23.9612 12.8469 23.9612 12.8841C23.9559 12.9481 23.9506 13.0066 23.9453 13.0706C23.9399 13.1345 23.9346 13.1984 23.924 13.2623C23.924 13.2889 23.9186 13.3102 23.9133 13.3369C23.908 13.3742 23.908 13.4061 23.9027 13.4434C23.8973 13.4807 23.8973 13.518 23.892 13.5499C23.8867 13.5766 23.8867 13.6032 23.8814 13.6298C23.876 13.6671 23.8707 13.6991 23.8654 13.7363C23.8547 13.8003 23.8441 13.8642 23.8388 13.9281C23.8175 14.0559 23.7962 14.1784 23.7695 14.3063C23.7642 14.3222 23.7642 14.3435 23.7589 14.3595C23.7482 14.4074 23.7376 14.4607 23.7269 14.5086C23.7269 14.5193 23.7216 14.53 23.7216 14.5406C23.7056 14.6045 23.695 14.6684 23.679 14.7323C23.6524 14.8549 23.6204 14.9774 23.5885 15.0999C23.5778 15.1318 23.5725 15.1638 23.5619 15.1957C23.5512 15.233 23.5406 15.2703 23.5299 15.3076C23.5299 15.3182 23.5246 15.3236 23.5246 15.3342C22.9708 17.2623 21.9431 18.988 20.5745 20.3888C20.2177 20.751 19.8397 21.0919 19.4403 21.4114C17.3955 23.0306 14.8075 24 11.9959 24C7.49619 24 3.57695 21.5233 1.52148 17.8535L5.72827 15.6005C6.97434 17.7683 9.31736 19.2277 11.9959 19.2277C13.5401 19.2277 14.9726 18.743 16.1494 17.9174C16.2293 17.8642 16.3038 17.8056 16.3837 17.747C17.2251 17.1079 17.9173 16.2823 18.4125 15.3342C18.9344 14.3382 19.2273 13.2037 19.2273 12C19.2273 11.4407 19.1634 10.8975 19.0462 10.3755H23.892C23.9027 10.4394 23.908 10.5033 23.9186 10.5672C23.9293 10.6471 23.9346 10.727 23.9453 10.8069C23.9506 10.8495 23.9506 10.8921 23.9559 10.9294C23.9612 10.956 23.9612 10.9827 23.9612 11.0093C23.9666 11.0679 23.9719 11.1212 23.9719 11.1798C23.9772 11.249 23.9825 11.3182 23.9825 11.3821C23.9879 11.4461 23.9879 11.51 23.9932 11.5739C23.9985 11.6485 23.9985 11.7284 23.9985 11.8029C23.9879 11.8668 23.9932 11.9308 23.9932 11.9947Z"
            fill="#00A94B" />
          <path
            d="M5.26623 9.36352C6.31527 6.67377 8.93519 4.76698 11.9971 4.76698C13.7544 4.76698 15.3625 5.39015 16.6139 6.43409L19.9953 3.05193C17.8706 1.15579 15.0696 0 12.0024 0C7.01817 0 2.74216 3.04128 0.931641 7.37683L1.04879 7.24368L5.26623 9.36352Z"
            fill="#FF4131" />
          <path
            d="M23.9954 11.9946C23.9954 12.1224 23.9954 12.2556 23.9901 12.3834C23.9901 12.41 23.9901 12.442 23.9847 12.4686C23.9847 12.5165 23.9794 12.5698 23.9794 12.6177C23.9794 12.6284 23.9794 12.6444 23.9794 12.655C23.9794 12.6923 23.9741 12.7349 23.9741 12.7722C23.9741 12.8095 23.9688 12.8414 23.9688 12.8787C23.9634 12.9426 23.9581 13.0012 23.9528 13.0651C23.9475 13.1291 23.9421 13.193 23.9315 13.2569C23.9315 13.2835 23.9262 13.3048 23.9208 13.3315C23.9155 13.3687 23.9155 13.4007 23.9102 13.4327C23.9049 13.4699 23.9049 13.5019 23.8995 13.5392C23.8942 13.5658 23.8942 13.5924 23.8889 13.6191C23.8836 13.6564 23.8782 13.6883 23.8729 13.7256C23.8623 13.7895 23.8516 13.8534 23.8463 13.9173C23.825 14.0452 23.8037 14.173 23.7771 14.2955C23.7717 14.3115 23.7717 14.3328 23.7664 14.3488C23.7558 14.402 23.7451 14.45 23.7345 14.4979C23.7345 14.5086 23.7291 14.5192 23.7291 14.5299C23.7132 14.5938 23.7025 14.6577 23.6865 14.7216C23.6599 14.8441 23.628 14.9666 23.596 15.0891C23.5854 15.1211 23.58 15.153 23.5694 15.185C23.5587 15.2223 23.5481 15.2596 23.5374 15.2968C23.5374 15.3075 23.5321 15.3128 23.5321 15.3235H11.998V10.3647H23.8836C23.8942 10.4287 23.8995 10.4926 23.9102 10.5512C23.9208 10.6311 23.9262 10.711 23.9368 10.7908C23.9421 10.8335 23.9421 10.8761 23.9475 10.9187C23.9528 10.9453 23.9528 10.9719 23.9528 10.9986C23.9581 11.0572 23.9634 11.1104 23.9634 11.169C23.9688 11.2382 23.9741 11.3022 23.9741 11.3714C23.9794 11.4353 23.9794 11.4992 23.9847 11.5631C23.9901 11.6377 23.9901 11.7176 23.9901 11.7922C23.9901 11.8667 23.9954 11.9307 23.9954 11.9946Z"
            fill="#0085F7" />
          <path
            d="M23.9943 11.9947C23.9943 12.1225 23.9889 12.2556 23.9889 12.3835C23.9889 12.4101 23.9889 12.4421 23.9836 12.4687C23.9836 12.5166 23.9783 12.5699 23.9783 12.6178C23.9783 12.6285 23.9783 12.6445 23.973 12.6604C23.973 12.6977 23.9676 12.7403 23.9676 12.7776C23.9676 12.8149 23.9623 12.8469 23.9623 12.8841C23.957 12.9481 23.9517 13.0066 23.9463 13.0706C23.941 13.1345 23.9357 13.1984 23.925 13.2623C23.925 13.2889 23.9197 13.3102 23.9144 13.3369C23.9091 13.3742 23.9091 13.4061 23.9037 13.4434C23.8984 13.4807 23.8984 13.518 23.8931 13.5499C23.8878 13.5766 23.8878 13.6032 23.8824 13.6298C23.8771 13.6671 23.8718 13.6991 23.8665 13.7363C23.8558 13.8003 23.8452 13.8642 23.8398 13.9281C23.8185 14.0559 23.7972 14.1784 23.7706 14.3063C23.7653 14.3222 23.7653 14.3435 23.76 14.3595C23.7493 14.4074 23.7387 14.4607 23.728 14.5086C23.728 14.5193 23.7227 14.53 23.7227 14.5406C23.7067 14.6045 23.6961 14.6684 23.6801 14.7323C23.6535 14.8549 23.6215 14.9774 23.5896 15.0999C23.5789 15.1318 23.5736 15.1638 23.5629 15.1957C23.5523 15.233 23.5416 15.2703 23.531 15.3076C23.531 15.3182 23.5257 15.3236 23.5257 15.3342C22.9719 17.2623 21.9441 18.988 20.5756 20.3888L16.3848 17.747C17.2261 17.1079 17.9184 16.2823 18.4136 15.3342C18.9355 14.3382 19.2283 13.2037 19.2283 12C19.2283 11.4407 19.1644 10.8975 19.0473 10.3755H23.8931C23.9037 10.4394 23.9091 10.5033 23.9197 10.5672C23.9304 10.6471 23.9357 10.727 23.9463 10.8069C23.9517 10.8495 23.9517 10.8921 23.957 10.9294C23.9623 10.956 23.9623 10.9827 23.9623 11.0093C23.9676 11.0679 23.973 11.1212 23.973 11.1798C23.9783 11.249 23.9836 11.3182 23.9836 11.3821C23.9889 11.4461 23.9889 11.51 23.9943 11.5739C23.9996 11.6485 23.9996 11.7284 23.9996 11.8029C23.9889 11.8668 23.9943 11.9308 23.9943 11.9947Z"
            fill="#0085F7" />
        </svg>
        <div class="text-[#273266]">Đăng nhập bằng Google</div>
      </div>
      <div
        style="border-radius: 8px; background: rgba(39, 142, 255, 0.10); padding: 16px; font-size: 12px; text-align: left; color: #273266; line-height: 18px; margin-top: 12px;">
        Theo Nghị định số 13/2023/NĐ-CP về bảo vệ dữ liệu cá nhân, khách hàng có quyền hạn chế, phản đối, rút lại sự
        đồng ý xử lý dữ liệu cá nhân hoặc yêu cầu xóa dữ liệu cá nhân. <span
          style="color: #2140D2; font-weight: 500; cursor: pointer;" (click)="showGuideline()">Xem hướng dẫn chi
          tiết</span>
      </div>

    </form>

    <div class="mt-9 mb-8" id="made-by">Một sản phẩm của <svg width="74" height="19" viewBox="0 0 74 19" fill="none"
        xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd"
          d="M0.0270854 9.7318C0.0758805 10.6254 0.296047 11.5027 0.676604 12.3202C0.681089 12.2598 0.681089 12.1993 0.676604 12.139C0.159832 10.5478 0.185104 8.84076 0.748772 7.26423C1.11267 5.94051 1.99005 4.79903 3.20251 4.07193C3.47316 3.87704 3.7933 3.75478 4.12986 3.71775C4.46643 3.68073 4.80723 3.73028 5.11708 3.86131C5.42693 3.99233 5.6946 4.20007 5.89246 4.46309C6.09032 4.7261 6.2112 5.03486 6.24261 5.35748C6.44474 6.51519 6.22161 7.70412 5.61114 8.72234C5.2503 9.39531 4.86239 10.0597 4.47448 10.724C4.08657 11.3883 3.73475 12.0009 3.38293 12.648C2.59857 13.9365 2.09242 15.3623 1.89445 16.8411C1.74109 18.42 2.67929 19.2224 4.34819 18.9463H4.45644C6.02342 18.5817 7.49859 17.9217 8.79558 17.0051C12.4634 14.4674 15.5133 11.2002 17.7355 7.42816C18.6499 6.00931 19.263 4.43199 19.5397 2.78638C19.5892 2.37937 19.5892 1.96823 19.5397 1.56122C19.5247 1.3318 19.4602 1.10784 19.3502 0.903431C19.2403 0.699025 19.0873 0.518604 18.9009 0.373528C18.7144 0.228453 18.4986 0.121865 18.267 0.0604776C18.0354 -0.000910209 17.793 -0.0157688 17.5551 0.0168373C16.4074 0.150205 15.3079 0.537157 14.3436 1.14709C14.2639 1.19939 14.1695 1.22739 14.0729 1.22739C13.9763 1.22739 13.8819 1.19939 13.8023 1.14709C11.8297 0.146322 9.54527 -0.138654 7.37025 0.344696C5.1654 0.79387 3.20586 1.99294 1.85539 3.71929C0.504931 5.44565 -0.144706 7.58202 0.0270854 9.7318ZM17.4197 4.02879L17.3476 4.19272C16.6537 3.19695 15.7654 2.3386 14.7315 1.66476C15.4352 1.21866 16.2448 0.948701 17.086 0.879622C17.3252 0.879622 17.5547 0.970523 17.7239 1.13233C17.893 1.29413 17.9881 1.51358 17.9881 1.74241C17.9728 2.5443 17.766 3.33224 17.3837 4.04604L17.4197 4.02879ZM46.1519 13.39H46.6571V5.46101H45.0153V11.4142L44.8168 11.233L44.6093 11.0173C42.9224 9.24001 41.2264 7.4713 39.5304 5.69396C39.4402 5.60768 39.332 5.48689 39.2237 5.48689C38.7366 5.48689 38.2494 5.48689 37.7533 5.48689H37.2391V13.3986H38.926V7.43679L39.0433 7.54895L39.1786 7.687C40.9287 9.5161 42.6878 11.3279 44.4379 13.1484C44.5371 13.2433 44.6634 13.3727 44.7897 13.3727C45.2408 13.3727 45.6918 13.3727 46.1609 13.3727L46.1519 13.39ZM54.8663 10.4738V13.3641H53.0621V5.4265H58.7634C59.1493 5.42641 59.5338 5.46985 59.9091 5.55592C60.5179 5.67633 61.0593 6.00655 61.4302 6.48378C61.8011 6.96101 61.9757 7.55196 61.9208 8.14427C61.8962 8.71712 61.6533 9.26157 61.2378 9.67484C60.8223 10.0881 60.263 10.3416 59.6655 10.3875C59.0153 10.454 58.3618 10.4857 57.7079 10.4824H54.8663V10.4738ZM54.8663 9.03294H58.9528C59.1261 9.02461 59.2953 8.98045 59.449 8.90352C59.6417 8.84382 59.8106 8.72915 59.9332 8.57492C60.0558 8.42069 60.126 8.23428 60.1346 8.04074C60.1711 7.83597 60.1349 7.62541 60.032 7.44252C59.929 7.25962 59.7651 7.11488 59.5663 7.03128C59.2418 6.88245 58.8885 6.80016 58.5288 6.7897C57.6267 6.7897 56.8148 6.7897 55.9488 6.7897H55.1008C55.0138 6.78122 54.9262 6.78122 54.8392 6.7897L54.8663 9.03294ZM22.9406 5.40062H22.4896C22.4924 5.42931 22.4924 5.4582 22.4896 5.48689C22.4918 5.51562 22.4918 5.54445 22.4896 5.57317C23.8608 8.09826 25.223 10.632 26.5761 13.1743C26.6098 13.2545 26.6711 13.3212 26.7498 13.3636C26.8286 13.4059 26.92 13.4213 27.0092 13.4073C27.3817 13.3857 27.7552 13.3857 28.1278 13.4073C28.2477 13.4256 28.3706 13.4028 28.4747 13.3428C28.5787 13.2828 28.6571 13.1895 28.6961 13.0794C29.508 11.5724 30.3289 10.0683 31.1589 8.56704C31.3844 8.1529 31.6099 7.70425 31.8445 7.32463C32.079 6.945 32.2504 6.56538 32.4669 6.17712L32.8458 5.46964H31.5017C31.3875 5.45059 31.27 5.47036 31.1695 5.52552C31.0689 5.58067 30.9918 5.66774 30.9514 5.77161C29.932 7.6266 28.8946 9.50747 27.8391 11.4056C27.8107 11.4599 27.7775 11.5119 27.7399 11.5609V11.5609C27.7428 11.5867 27.7428 11.6127 27.7399 11.6386L27.6767 11.5178L27.5414 11.2934L26.8378 9.99063C26.0619 8.54691 25.2771 7.10893 24.4833 5.67671C24.4461 5.61128 24.3946 5.5543 24.3324 5.50963C24.2701 5.46496 24.1985 5.43366 24.1224 5.41787C23.7525 5.41787 23.3827 5.41787 22.9858 5.41787L22.9406 5.40062ZM65.1052 5.45238H74V6.81558H70.3916V13.3641H68.6776V6.85872H65.0691L65.1052 5.45238ZM9.6165 17.8333C13.8831 15.7124 17.1468 12.1191 18.7458 7.7819C19.657 12.9586 15.0923 17.8679 9.6165 17.8333Z"
          fill="#4F5570" />
      </svg>
    </div>
  </div>
</section>

<dialog #guideline id="guideline">
  <div class="header">
    <span class="text-xl font-bold leading-9">Hướng dẫn</span>
    <span class="cursor-pointer" (click)="closeGuideline()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M13.2329 11.9993L18.7414 6.4989C18.906 6.33424 18.9985 6.11091 18.9985 5.87804C18.9985 5.64517 18.906 5.42184 18.7414 5.25717C18.5767 5.09251 18.3534 5 18.1206 5C17.8877 5 17.6644 5.09251 17.4998 5.25717L12 10.7663L6.50024 5.25717C6.33559 5.09251 6.11228 5 5.87944 5C5.64659 5 5.42328 5.09251 5.25864 5.25717C5.09399 5.42184 5.00149 5.64517 5.00149 5.87804C5.00149 6.11091 5.09399 6.33424 5.25864 6.4989L10.7671 11.9993L5.25864 17.4996C5.17668 17.5809 5.11164 17.6776 5.06725 17.7842C5.02285 17.8907 5 18.005 5 18.1205C5 18.2359 5.02285 18.3502 5.06725 18.4568C5.11164 18.5633 5.17668 18.66 5.25864 18.7413C5.33992 18.8233 5.43663 18.8884 5.54318 18.9327C5.64972 18.9771 5.76401 19 5.87944 19C5.99486 19 6.10915 18.9771 6.2157 18.9327C6.32225 18.8884 6.41895 18.8233 6.50024 18.7413L12 13.2322L17.4998 18.7413C17.581 18.8233 17.6778 18.8884 17.7843 18.9327C17.8909 18.9771 18.0051 19 18.1206 19C18.236 19 18.3503 18.9771 18.4568 18.9327C18.5634 18.8884 18.6601 18.8233 18.7414 18.7413C18.8233 18.66 18.8884 18.5633 18.9328 18.4568C18.9771 18.3502 19 18.2359 19 18.1205C19 18.005 18.9771 17.8907 18.9328 17.7842C18.8884 17.6776 18.8233 17.5809 18.7414 17.4996L13.2329 11.9993Z"
          fill="#A1A5BA" />
      </svg>
    </span>
  </div>
  <div class="content">
    <div class="left">
      <div (click)="selectGuideline(1)" [ngClass]="{'selected': guidelineSelection === 1}">Yêu cầu xóa dữ liệu cá nhân
        của khách hàng</div>
      <div (click)="selectGuideline(2)" [ngClass]="{'selected': guidelineSelection === 2}">Rút lại sự đồng ý xử lý dữ
        liệu cá nhân của khách hàng</div>
    </div>
    <div *ngIf="guidelineSelection === 1" class="right">
      <div class="text-xl font-bold leading-9 mb-6">
        Xóa dữ liệu cá nhân của khách hàng
      </div>
      <div class="step-list">
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 1:
          </div>
          <div class="text">
            Khách hàng sau khi Đăng nhập thành công, tại màn hình OCR Platform, khách hàng chọn khu vực thông tin
            tài
            khoản.
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 2:
          </div>
          <div class="text">
            Khách hàng chọn chức năng "Xóa dữ liệu cá nhân", màn hình hiển thị thông tin chính sách mà Khách hàng đã
            đồng ý khi đăng ký tài sản sử dụng sản phẩm
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 3:
          </div>
          <div class="text">
            Khách hàng chọn chức năng "Xóa dữ liệu cá nhân". Màn hình hiển thị thông tin cảnh báo <span
              class="font-medium">"Theo điều 8
              mục 8.5
              tại Chính sách bảo vệ dữ liệu cá nhân của sản phẩm VNPT Smart Reader</span>, Khách hàng có quyền yêu
            cầu
            VNPT
            thực hiện xóa Dữ liệu cá nhân của Khách hàng với điều kiện là yêu cầu của Khách hàng phải phù hợp với
            quy
            định của pháp luật. <span class="font-medium">Tuy nhiên, yêu cầu xóa Dữ liệu cá nhân của Khách
              hàng
              đồng nghĩa với việc Khách hàng
              xác nhận ngừng sử dụng sản phẩm VNPT Smart Reader</span>, điều này cũng đồng nghĩa với việc VNPT có
            thể
            đơn
            phương chấm dứt hợp đồng mà không cần phải bồi thường cho Khách hàng do các điều kiện để thực hiện hợp
            đồng đã thay đổi. Do đó, VNPT khuyến nghị Khách hàng cân nhắc kĩ lưỡng trước khi yêu cầu VNPT thực hiện
            xóa Dữ liệu cá nhân.".
            Bạn có chắc chắn muốn yêu cầu xóa dữ liệu?
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 4:
          </div>
          <div class="text">
            Khách hàng chọn "Đồng ý", hệ thống sẽ thực hiện gửi mail thông báo qua email đăng ký tài khoản của bạn
            để
            xác thực.
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 5:
          </div>
          <div class="text">
            Khách hàng thực hiện xác nhận xóa dữ liệu cá nhân qua email, sau khi xác nhận hệ thống sẽ thực hiện
            ngừng
            cung cấp sản phẩm VNPT Smart Reader cho khách hàng.
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="guidelineSelection === 2" class="right">
      <div class="text-xl font-bold leading-9 mb-6">
        Rút lại sự đồng ý xử lý dữ liệu cá nhân của khách hàng
      </div>
      <div class="step-list">
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 1:
          </div>
          <div class="text">
            Khách hàng sau khi Đăng nhập thành công, tại màn hình OCR Platform, khách hàng chọn khu vực thông tin tài
            khoản.
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 2:
          </div>
          <div class="text">
            Khách hàng chọn chức năng "Chính sách", màn hình hiển thị thông tin chính sách mà Khách hàng đã đồng ý khi
            đăng ký tài sản sử dụng sản phẩm.
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 3:
          </div>
          <div class="text">
            Khách hàng chọn chức năng "Rút lại sự đồng ý". Màn hình hiển thị thông tin cảnh báo "<span
              class="font-medium">Theo
              điều 8 mục 8.4
              tại Chính sách bảo vệ dữ liệu cá nhân của sản phẩm VNPT Smart Reader, việc rút lại sự đồng ý xử lý Dữ
              liệu
              cá nhân của Khách hàng đồng nghĩa với việc Khách hàng xác nhận ngừng sử dụng sản phẩm VNPT Smart
              Reader</span>,
            điều này cũng đồng nghĩa với việc VNPT có thể đơn phương chấm dứt hợp đồng mà không cần phải bồi thường
            cho Khách hàng do các điều kiện để thực hiện hợp đồng đã thay đổi. Do đó, VNPT khuyến nghị Khách hàng cân
            nhắc kĩ lưỡng trước khi phản đối, hạn chế hoặc rút lại sự đồng ý xử lý Dữ liệu cá nhân của Khách hàng".
            Bạn có chắc chắn rút lại sự đồng ý?
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 4:
          </div>
          <div class="text">
            Khách hàng chọn "Đồng ý", hệ thống sẽ thực hiện gửi mail thông báo qua email đăng ký tài khoản của bạn
            để
            xác thực.
          </div>
        </div>
        <div class="step">
          <svg class="absolute -left-1" width="7" height="7" viewBox="0 0 7 7" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="3.5" cy="3.5" r="3" fill="white" stroke="#A1A5BA" />
          </svg>
          <div class="number">
            Bước 5:
          </div>
          <div class="text">
            Khách hàng thực hiện xác nhận việc rút lại sự đồng ý qua email, sau khi xác nhận hệ thống sẽ thực hiện
            ngừng cung cấp sản phẩm VNPT Smart Reader cho khách hàng.
          </div>
        </div>
      </div>
    </div>
  </div>
</dialog>

<dialog #notice id="notice">
  <div class="header">Thông báo</div>
  <div class="content">
    Theo Nghị định 13/2023/NĐ-CP về bảo vệ dữ liệu cá nhân. VNPT đã bổ sung chính sách bảo vệ dữ liệu
    cá nhân trong điều khoản sử dụng dịch vụ VNPT Smart Reader. Khách hàng vui lòng xác nhận đã đọc và đồng ý toàn bộ
    nội dung các điều khoản và điều kiện quy định trước khi đăng nhập sử dụng dịch vụ.
  </div>

  <div class="flex gap-3 items-start mt-4 mb-6">
    <input class="min-w-[18px] h-[18px] mt-1" [(ngModel)]="noticeAgreed" type="checkbox" id="agreed">
    <label for="agreed">Tôi đồng ý với <a [href]="landingPageUrl + 'vi/term-of-use'">Điều khoản sử dụng</a> và
      <a [href]="landingPageUrl + 'vi/policy'">Chính sách bảo mật</a> của VNPT Smart Reader
    </label>
  </div>
  <div class="login-btn" [ngClass]="{ 'agreed': noticeAgreed }" (click)="confirmNotice()">Đăng nhập</div>
  <div class="cancel-btn"><span (click)="closeNotice()">Hủy</span></div>
</dialog>

<dialog #noticeForSSO id="noticeForSSO">
  <div style="border-radius: 8px; color: #111127; width: 595px;">
    <div
      style="font-size: 16px; font-weight: 700; text-transform: uppercase; padding: 16px 24px; border-bottom: 1px solid #C9D4E4;">
      Cập Nhật thông tin
    </div>
    <form style="padding: 24px 24px 16px 24px;" [formGroup]="noticeForSSOForm" (ngSubmit)="handleNoticeForSSOSubmit()">
      <ng-container *ngIf="newUserLoginWithSSO">
        <div style="display: flex; flex-direction: column; margin-bottom: 16px;">
          <div style="display: flex; gap: 12px; align-items: baseline;">
            <label for="fullName" style="font-weight: 600;">Họ và tên <span style="color: #DA1E28;">*</span></label>
            <div *ngIf="checkInvalidField(noticeForSSOForm, 'fullName')" style="color: #DA1E28; font-size: 12px;">
              Họ và tên không hợp lệ
            </div>
          </div>
          <input formControlName="fullName" id="fullName" type="text"
            style="padding: 10px 12px; border-radius: 8px; border: 1px solid #CBCBCB;" placeholder="Nhập họ và tên">
        </div>
        <div style="display: flex; flex-direction: column; margin-bottom: 16px;">
          <div style="display: flex; gap: 12px; align-items: baseline;">
            <label for="workUnit" style="font-weight: 600;">Công ty <span style="color: #DA1E28;">*</span></label>
            <div *ngIf="checkInvalidField(noticeForSSOForm, 'workUnit')" style="color: #DA1E28; font-size: 12px;">
              Công ty không hợp lệ
            </div>
          </div>
          <input formControlName="workUnit" id="workUnit" type="text"
            style="padding: 10px 12px; border-radius: 8px; border: 1px solid #CBCBCB;" placeholder="Nhập tên công ty">
        </div>
        <div style="display: flex; flex-direction: column; margin-bottom: 16px;">
          <div style="display: flex; gap: 12px; align-items: baseline;">
            <label for="field" style="font-weight: 600;">Lĩnh vực <span style="color: #DA1E28;">*</span></label>
            <div *ngIf="checkInvalidField(noticeForSSOForm, 'field')" style="color: #DA1E28; font-size: 12px;">
              Lĩnh vực không hợp lệ
            </div>
          </div>
          <select formControlName="field" id="field"
            style="padding: 10px 12px; border-radius: 8px; border: 1px solid #CBCBCB;" placeholder="Nhập Họ và tên">
            <option value="Banking/Finance">Banking/Finance</option>
            <option value="Fintech">Fintech</option>
            <option value="Logistic">Logistic</option>
            <option value="E-commerce">E-commerce</option>
            <option value="Du lịch/Nhà hàng/Khách sạn">Du lịch/Nhà hàng/Khách sạn</option>
            <option value="Bán lẻ/Xuất nhập khẩu">Bán lẻ/Xuất nhập khẩu</option>
            <option value="Giáo dục">Giáo dục</option>
            <option value="Bất động sản">Bất động sản</option>
            <option value="Viễn thông">Viễn thông</option>
            <option value="Khác">Khác</option>
          </select>
        </div>
        <div style="display: flex; flex-direction: column; margin-bottom: 16px;">
          <div style="display: flex; gap: 12px; align-items: baseline;">
            <label for="phoneNumber" style="font-weight: 600;">Số điện thoại <span
                style="color: #DA1E28;">*</span></label>
            <div *ngIf="checkInvalidField(noticeForSSOForm, 'phoneNumber')" style="color: #DA1E28; font-size: 12px;">
              Số điện thoại không hợp lệ
            </div>
          </div>
          <input formControlName="phoneNumber" id="phoneNumber" type="text"
            style="padding: 10px 12px; border-radius: 8px; border: 1px solid #CBCBCB;" placeholder="Nhập số điện thoại">
        </div>
      </ng-container>
      <div style="display: flex; gap: 8px; align-items: baseline; margin-bottom: 8px;">
        <input formControlName="ssoAgreed" id="ssoAgreed" type="checkbox" style="-webkit-appearance: auto;">
        <label for="ssoAgreed">Tôi đã đọc và đồng ý với VNPT về <a style="color: #0F67CE; font-weight: 600;"
          [href]="landingPageUrl + 'vi/term-of-use'" target="_blank">Điều
            khoản sử dụng</a> và <a style="color: #0F67CE; font-weight: 600;"
          [href]="landingPageUrl + 'vi/policy'" target="_blank">Chính sách bảo mật</a>
          của sản phẩm <a style="color: #0F67CE; font-weight: 600;" [href]="landingPageUrl"
            target="_blank">VNPT Smart Reader</a></label>
      </div>
      <div
        style="margin: 0 -24px; border-top: 1px solid #C9D4E4; padding: 16px 0 0 0; display: flex; justify-content: center;">
        <button type="submit"
          [ngStyle]="{'background': noticeForSSOForm.controls['ssoAgreed'].value ? '#0F67CE': '#A1A5BA'}"
          [disabled]="!noticeForSSOForm.controls['ssoAgreed'].value"
          style="height: 44px; width: 213px; border-radius: 10px; background: #A1A5BA; color: #fff; font-weight: 700; border: none;">
          Lưu thông tin
        </button>
      </div>
    </form>
  </div>
</dialog>