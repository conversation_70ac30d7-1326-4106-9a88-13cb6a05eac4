:host {
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #cbcbcb;
  }

  ::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }

  ::ng-deep {
    width: 420px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background-color: #272836;

    .ant-collapse-ghost>.ant-collapse-item>.ant-collapse-content>.ant-collapse-content-box {
      padding-left: 12px;
      padding-right: 12px;
      padding-top: 4px;
    }

    button[type="button"].ant-switch {
      background-color: revert;

      &.ant-switch-checked {
        background: #009B4E;
      }
    }

    nz-form-label.ant-form-item-label label {
      font-size: 12px;
      font-weight: 500;
      color: #fff;

      .ant-form-item-tooltip {
        display: inline-flex;
        align-items: center;
        color: #fff;
        font-size: 14px;
      }
    }

    .ant-select {
      &.ant-select-status-error .ant-select-selector {
        border-color: #FF3355 !important;
      }

      .ant-select-selector {
        border-radius: 8px;
      }
    }

    .ant-form-item-explain-error {
      margin-top: 4px;
      font-size: 12px;
      color: #FF3355;
    }

  }
}