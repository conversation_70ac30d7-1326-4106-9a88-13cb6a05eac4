# API Upload File

**Chức năng**: Upload file để sử dụng trong tất cả các API OCR của VNPT Smart Reader, kết quả của API Upload file trả về mã hash của file đư<PERSON>c sử dụng làm đầu vào cho các API khác.

**Endpoint:** `<domain-name>/file-service/v1/addFile`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col>
<col>
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong><PERSON><PERSON> tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Bearer ${access_token}</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-ID</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật do VNPT cung cấp</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật do VNPT cung cấp</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col>
<col>
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file</td>
<td>file</td>
<td>x</td>
<td></td>
<td>File hệ thống xử lý</td>
</tr>
<tr>
<td></td>
<td>title</td>
<td>text</td>
<td></td>
<td></td>
<td>Tiêu đề file</td>
</tr>
<tr>
<td></td>
<td>description</td>
<td>text</td>
<td></td>
<td></td>
<td>Mô tả file</td>
</tr>
</tbody></table>

**Request response:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>fileName</td>
<td>String</td>
<td>Tên file</td>
</tr>
<tr>
<td></td>
<td>title</td>
<td>String</td>
<td>Tiêu đề của file</td>
</tr>
<tr>
<td></td>
<td>description</td>
<td>String</td>
<td>Mô tả của file</td>
</tr>
<tr>
<td></td>
<td>fileType</td>
<td>String</td>
<td>Định dạng của file gửi lên</td>
</tr>
<tr>
<td></td>
<td>hash</td>
<td>String</td>
<td>Mã hash ảnh theo</td>
</tr>
<tr>
<td></td>
<td>uploadedDate</td>
<td>String</td>
<td>Thời gian upload file</td>
</tr>
<tr>
<td></td>
<td>storageType</td>
<td>String</td>
<td>Kiểu lưu trữ do hệ thống quy định</td>
</tr>
<tr>
<td></td>
<td>tokenID</td>
<td>String</td>
<td>tokenID trả về</td>
</tr>
</tbody></table>

**Response example:**

```json
{
  "message": "IDG-00000000",
  "object": {
    "fileName": "DuocPham",
    "tokenId": "cdc6499e-b0a2-2dc6-e053-6c1b9f0af9cf",
    "description": "Hashing document",
    "storageType": "IDG01",
    "title": "Hashing document",
    "uploadedDate": "4/18/23 9:29 AM",
    "hash": "idg20241218-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_923ad070-bcf2-11ef-a64a-6d251c2710f4",
    "fileType": "pdf"
  }
}
```