import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from '@platform/app/core/services/user.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { environment } from '@platform/environment/environment';
import { get, isEmpty } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError, forkJoin, map, take, tap } from 'rxjs';
import { SubscriptionService } from '@platform/app/core/services/subscription.service';
import { HttpErrorResponse } from '@angular/common/http';
import { PaymentResponseType } from '../response-payment/types';
import { NzModalService } from 'ng-zorro-antd/modal';

enum PaymentType {
  vnptMoney = 1,
  wallet = 2,
}
@Component({
  selector: 'app-payment',
  templateUrl: './payment.component.html',
  styleUrls: ['./payment.component.scss'],
})
export class PaymentComponent implements OnInit {
  formatAmount = this.utilsService.formatAmount;
  private readonly subscriptions: {
    name: string;
    quota: string;
    time: number;
    price: number;
    uuidPlan: string;
  }[] = [
    {
      name: 'Reader_5',
      quota: '5.000',
      time: 1,
      price: 3500000,
      uuidPlan: environment.Reader_05,
    },
    {
      name: 'Reader_10',
      quota: '10.000',
      time: 1,
      price: 6850000,
      uuidPlan: environment.Reader_10,
    },
    {
      name: 'Reader_50',
      quota: '50.000',
      time: 1,
      price: 33500000,
      uuidPlan: environment.Reader_50,
    },
    {
      name: 'Reader_30 ',
      quota: '30.000',
      time: 6,
      price: 20550000,
      uuidPlan: environment.Reader_30,
    },
    {
      name: 'Reader_60 ',
      quota: '60.000',
      time: 6,
      price: 40200000,
      uuidPlan: environment.Reader_60,
    },
    {
      name: 'Reader_300 ',
      quota: '300.000',
      time: 6,
      price: 196500000,
      uuidPlan: environment.Reader_300,
    },
    {
      name: 'Reader_80',
      quota: '80.000',
      time: 12,
      price: 53600000,
      uuidPlan: environment.Reader_80,
    },
    {
      name: 'Reader_120',
      quota: '120.000',
      time: 12,
      price: 78600000,
      uuidPlan: environment.Reader_120,
    },
    {
      name: 'Reader_600',
      quota: '600.000',
      time: 12,
      price: *********,
      uuidPlan: environment.Reader_600,
    },
  ];

  selectedSubscription: {
    name: string;
    quota: string;
    time: number;
    price: number;
    uuidPlan: string;
  };
  exportBill = false;
  billForm: UntypedFormGroup = new UntypedFormGroup({
    fullName: new UntypedFormControl(null, Validators.required),
    companyName: new UntypedFormControl(null, Validators.required),
    taxCode: new UntypedFormControl(null, Validators.required),
    address: new UntypedFormControl(null, Validators.required),
    phone: new UntypedFormControl(null, [
      Validators.required,
      Validators.pattern('^((\\+91-?)|0)?[0-9]{10}$'),
    ]),
    email: new UntypedFormControl(null, [Validators.required, Validators.email]),
  });
  isIndividual = false;
  action: null | 'extend';
  uuidProject;
  uuidProjectServicePlan;
  walletDetail;
  paymentType: PaymentType = 1;
  @ViewChild('notice')
  noticeTemplate: TemplateRef<any>;
  noticeTime: Date;
  confirm = false;

  constructor(
    private router: Router,
    private subscriptionService: SubscriptionService,
    private userService: UserService,
    private toastr: ToastrService,
    private utilsService: UtilsService,
    public modalService: NzModalService
  ) {
    const uuidPlan = history.state.uuidPlan;
    this.action = history.state.action;
    if (!uuidPlan) router.navigate(['subscription/info']);
    else
      this.selectedSubscription = this.subscriptions.find(
        (item) => item.uuidPlan === uuidPlan
      );
  }

  ngOnInit(): void {
    // this.subscriptionService
    //   .getWalletDetail()
    //   .pipe(
    //     tap((walletDetail) => {
    //       this.walletDetail = walletDetail.object;
    //     }),
    //     catchError((error) => {
    //       this.toastr.warning(
    //         'Tài khoản của bạn chưa thể sử dụng ví điện tử để thanh toán',
    //         undefined,
    //         { timeOut: 10 * 1000 }
    //       );
    //       return EMPTY;
    //     })
    //   )
    //   .subscribe();

    forkJoin({
      subscriptionCheckRegister:
        this.subscriptionService.fetchSubscriptionCheckRegister(),
      userInfo: this.userService.getIdgAccount(),
    }).subscribe({
      error: () => {
        this.toastr.error('Có lỗi xảy ra');
        return this.router.navigate(['subscription']);
      },
      next: ({ subscriptionCheckRegister, userInfo: userInfoRes }) => {
        this.uuidProject = get(
          subscriptionCheckRegister,
          'object.uuidProject.uuidProject'
        );
        this.uuidProjectServicePlan = get(
          subscriptionCheckRegister,
          'object.uuidProjectServicePlan'
        );
        const userInfo = get(userInfoRes, 'object', {});

        this.billForm.patchValue({
          fullName: userInfo.fullName,
          companyName: userInfo.workUnit,
          phone: userInfo.phoneNumber,
          email: userInfo.username,
        });
      },
    });
  }

  checkInvalidField(fieldName) {
    return (
      this.billForm.controls[fieldName].touched &&
      !isEmpty(this.billForm.controls[fieldName].errors)
    );
  }

  getFieldError(fieldName, errorName) {
    return get(
      this.billForm,
      `controls.${fieldName}.errors.${errorName}`,
      false
    );
  }

  handleIndividualChange(isIndividual: boolean) {
    const taxCode = this.billForm.get('taxCode');
    if (!isIndividual) {
      taxCode.setValidators(Validators.required);
    } else {
      taxCode.removeValidators(Validators.required);
      taxCode.setErrors(null);
    }
    this.isIndividual = isIndividual;
  }

  pay() {
    if (!this.confirm) return;
    const body = {
      channelCode: environment.channelCode,
      paymentPeriod: 1,
      exportInvoice: this.exportBill,
      // paymentType: this.paymentType,
    };

    if (this.exportBill) {
      this.billForm.markAllAsTouched();
      if (this.billForm.invalid) return;
      body['addInfoInv'] = {
        ...this.billForm.value,
      };
    }

    if (this.action === 'extend') {
      body['uuidProjectServicePlan'] = this.uuidProjectServicePlan;
      body['description'] = 'Gia hạn dịch vụ RPA';
    } else {
      body['uuidProject'] = this.uuidProject;
      body['uuidPlan'] = this.selectedSubscription.uuidPlan;
      body['description'] = 'Đăng ký dịch vụ RPA';
    }
    (this.action === 'extend'
      ? this.subscriptionService.extendSubscription(body)
      : this.subscriptionService.registerSubscription(body)
    )
      .pipe(
        // map(() => { // simulate insufficient balance / error case
        //   throw new HttpErrorResponse({
        //     error: { message: 'IDG-01000021' },
        //     status: 400,
        //   });
        // }),
        tap(({ object: redirectUrl }) => {
          if (this.paymentType === PaymentType.vnptMoney)
            this.handlePaymentSuccess(redirectUrl);
          else
            this.router.navigate(['subscription', 'response-payment'], {
              state: {
                paymentResponseType: PaymentResponseType.success,
                planName: this.selectedSubscription?.name,
              },
            });
        }),
        catchError((resp: HttpErrorResponse) => {
          if (
            this.paymentType === PaymentType.wallet &&
            get(resp, 'error.message') === 'IDG-01000021'
          )
            this.router.navigate(['subscription', 'response-payment'], {
              state: {
                paymentResponseType:
                  PaymentResponseType.insufficientBalanceInWallet,
              },
            });
          else
            this.router.navigate(['subscription', 'response-payment'], {
              state: { paymentResponseType: PaymentResponseType.failed },
            });

          this.toastr.error('Đã có lỗi xảy ra!');
          return EMPTY;
        })
      )
      .subscribe();
  }

  showNotice() {
    if (this.exportBill && this.billForm.invalid) {
      for (const field in this.billForm.controls) {
        this.billForm.controls[field].markAsDirty({ onlySelf: true });
        this.billForm.controls[field].updateValueAndValidity();
      }
      return;
    }
    this.noticeTime = new Date();
    const modal = this.modalService.create({
      nzContent: this.noticeTemplate,
      nzFooter: null,
      nzMaskClosable: false,
      nzStyle: { width: '740px' },
      nzBodyStyle: { padding: '0px' },
      nzClosable: false,
    });
    modal.afterClose
      .pipe(
        take(1),
        tap(() => (this.confirm = false))
      )
      .subscribe();
  }

  private handlePaymentSuccess(redirectUrl: string) {
    // for the accured expense case, which response is unclear
    if (['success', 'falied'].includes(redirectUrl.toLowerCase()))
      return this.router.navigate(['subscription']);
    else window.location.href = redirectUrl; // redirect to vnpt money to pay
  }
}
