import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError } from 'rxjs';

@Component({
  selector: 'app-modal-create-folder',
  templateUrl: './modal-create-folder.component.html',
  styleUrls: ['./modal-create-folder.component.scss']
})
export class ModalCreateFolderComponent implements OnInit {
  readonly nzModalData: { listFolders: Folder[] } = inject(NZ_MODAL_DATA);
  listFolders = this.nzModalData.listFolders || [];
  nameFolder: string = '';

  constructor(
    private modalRef: NzModalRef,
    private kieService: KIEService,
    private toastr: ToastrService,
    private documentLayoutService: DocumentLayoutService
  ) {}

  ngOnInit(): void {}

  createFolder(): void {
    if (this.documentLayoutService.foldersCount >= 100) {
      this.toastr.error('Vượt quá số lượng tối đa 100 thư mục cho phép');
      return;
    }

    if (this.nameFolder.trim().length < 1) {
      this.toastr.error('Tên thư mục không được để trống');
      return;
    }

    if (this.nameFolder.trim().length > 255) {
      this.toastr.error('Tên thư mục không được quá 255 ký tự');
      return;
    }
    // check exist name folder

    if (this.nameFolder.trim()) {
      // Check if any document in the selected folder has the same name as this.documentSelected.title.trim()
      const isFolderExist = this.listFolders.some(
        (folder) => folder.name === this.nameFolder.trim()
      );

      if (isFolderExist) {
        this.toastr.error('Tên thư mục đã tồn tại!');
        return;
      }
    }

    this.kieService
      .createNewFolder(this.nameFolder)
      .pipe(
        catchError((error) => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
          return EMPTY;
        })
      )
      .subscribe(() => {
        this.toastr.success('Thư mục được tạo thành công');
        /*
          new folder is created and could be inserted into any position depend on current filters (eg. createdAt=ASC, name=ASC,...)
          because this behavior could lead to problem when user not seeing exact folder they had created on UI
          => then we need to set the filters to createdAt=DESC, making the last created folder always inserted on top, (at the same time only fetch the first page of folders, no need to refetched all pages)
        */
        this.documentLayoutService.loadFolders({
          loadStrategy: 'clear-and-fetch',
          filters: { orderBy: 'createdAt', orderValue: 'DESC' }
        });
        this.modalRef.close();
      });
  }
}
