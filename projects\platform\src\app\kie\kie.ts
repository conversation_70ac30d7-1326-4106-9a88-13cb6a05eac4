export enum FieldType {
  Field = 'Field',
  List = 'List',
  Table = 'Table'
}

export enum FileStatus {
  Accepted = 'accepted',
  NotAccepted = 'not_accepted'
}

export type File = {
  id: string;
  name: string;
  documentId: string;
  document: {
    name: string;
    isSelfConfig: boolean;
    templateVerNo: boolean;
    extraConfig: { name: string; color: string; is_visible: boolean; order: number }[];
  };
  fileLink: string;
  initialOcrResult: {
    [field: string]: DocumentField;
  } & { num_of_pages: number; warning_messages: string[]; warnings: string[] };
  ocrResult: {
    [field: string]: DocumentField;
  } & { num_of_pages: number; warning_messages: string[]; warnings: string[] };
  status: FileStatus;
  templateVerNo: number;
  createdAt: string;
};

type Cell = {
  bboxes: { [page: number]: number[] };
  confidence_score: number;
  warnings: string[];
  text: string;
  font_styles: FontStyle;
  type: 'Field';
  name: string;
};

export type DocumentField = {
  bboxes: { [pageNumber: number]: Bbox };
  confidence_score: number;
  text?: string;
  cells?: Cell[];
  rows?: {
    type: 'List';
    name: string;
    cells: Cell[];
  }[];
  columns?: {
    type: 'List';
    name: string;
    cells: Cell[];
  }[];
  font_styles?: FontStyle[];
  type: FieldType;
  warnings?: string[];
  /* optional FE only */
  config?: Template['config']['fields'][string];
  initialOcrResult?: Omit<DocumentField, 'initialOcrResult'>;
};

export const excludedKeyFields = [
  'num_of_pages',
  'warning_messages',
  'warnings',
  'suggestion_name',
  'suggestion_score',
  'aligned_file_hash',
  'aligned_images',
  'original_images'
];

export type Bbox = [number, number, number, number];

export type Folder = {
  id: string;
  name: string;
  createdAt: boolean;
  documents: {
    id: string;
    name: string;
    createdAt: string;
    isSelfConfig: boolean;
    filesCount: number;
  }[];
};

export type RolePermisson = 'creator' | 'editor' | 'viewer';

export enum Role {
  Creator = 'creator',
  Editor = 'editor',
  Viewer = 'viewer'
}

export type User = {
  id: string;
  name: string;
};

export type ListDocumentPermission = {
  permissionList: DocumentPermission[];
  limit: number;
  page: number;
  total: number;
};

export type DocumentPermission = {
  assignee: User;
  assigneeId: string;
  createdAt: string;
  id: string;
  role: Exclude<RolePermisson, 'creator'>;
};

export type SharedDocument = {
  id: string;
  name: string;
  templateRef: string;
  creatorId: string;
  creator: {
    id: string;
    name: string;
  };
  createdAt: string;
  isSelfConfig: boolean;
  role: RolePermisson;
};

export type ListSharedDocuments = {
  documents: SharedDocument[];
  page: number;
  total: number;
  limit: number;
};

export type FileProps = {
  id: string;
  name: string;
  documentId: string;
  fileLink: string;
  templateVerNo: number;
  status: FileStatus;
  createdAt: string;
  updatedAt: string;
  creator: {
    id: string;
    name: string;
  };
  disabled: boolean;
};

export type ParamQueryListFile = {
  startDate?: string;
  endDate?: string;
  search?: string;
  page: number;
  limit: number;
};

export type ParamQueryListSharedDocument = {
  search?: string;
  page: number;
  limit: number;
  role?: RolePermisson[];
  templateTypes?: string[];
  creatorIds?: string[];
};

export type ParamQueryListDocumentPermission = {
  page: number;
  limit: number;
  roles?: RolePermisson[];
  assigneeIds?: string[];
};

export type ListFile = {
  files: FileProps[];
  page: number;
  total: number;
  limit: number;
};

export type FileUploaded = {
  success: boolean;
  data: any;
  fileName: string;
};

export type ApiResponse<T> = {
  message: string;
  object: T;
};

export type Response<T> = {
  status: number;
  data: T;
};

export enum FontStyle {
  Bold = 'bold',
  Normal = 'normal',
  Italic = 'italic'
}

export enum OcrModel {
  LocationOnly = 'location_only',
  Default = 'default'
}

export enum TemplateConfigFieldModel {
  Default = 'default',
  Special = 'special',
  Handwriting = 'handwriting',
  NumberHandwriting = 'number_hw'
}

export type Document = {
  id: string;
  name: string;
  folderId: string;
  isSelfConfig: boolean;
  templateRef: string;
  templateRefType: 'NAME' | 'ID';
  templateVerNo: number;
  extraConfig: Partial<ExtraConfig>[];
  template: Template;
  myRole: Role;
};

export type Template = {
  id: string;
  name: string;
  file_link: string;
  config: {
    ocr_model: OcrModel;
    image_type: string;
    content_from_pdf: boolean;
    fields: {
      [fieldName: string]: {
        name: string;
        prewords: string[];
        sufwords: string[];
        is_title_or_uppercase: boolean;
        is_number: boolean;
        is_date: boolean;
        is_address: boolean;
        table: any; // TODO: need confirm
        multilines: boolean;
        location: {
          [pageNumber: string]: [number, number, number, number];
        } | null;
        paragraph: any; // TODO: need confirm
        using_regex: string; // TODO: need confirm
        model: string;
        cer_threshold: number;
        confidence_score: number;
        is_visible: boolean;
        /* optional FE only */
        extraConfig?: ExtraConfig;
      };
    };
  };
};

export type ExtraConfig = {
  name: string /* only key to map extraConfig with coresponding template.config */;
  id: string /* unique field id */;
  color: string;
  is_visible: boolean;
  order: number;
};

export type SystemTemplate = {
  id: string;
  title: string;
  icon: string;
  idgEndpoint: string;
};

export const listSystemTemplate: SystemTemplate[] = [
  {
    title: 'Bản trích lục hồ sơ bệnh binh',
    id: 'ban_trich_luc_ho_so_benh_binh',
    icon: 'assets/kie/document/icon-documents/ho_so_benh_binh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/ban-trich-luc-ho-so-benh-binh'
  },
  {
    title: 'Bằng tốt nghiệp THPT',
    id: 'bang_tot_nghiep_thpt',
    icon: 'assets/kie/document/icon-documents/bang_tot_nghiep_thpt.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/bang-tot-nghiep-thpt'
  },
  {
    title: 'Bằng tốt nghiệp đại học',
    id: 'bang_tot_nghiep_dai_hoc',
    icon: 'assets/kie/document/icon-documents/bang_tot_nghiep_dai_hoc.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/bang-tot-nghiep-dai-hoc'
  },
  {
    title: 'Giấy khai sinh',
    id: 'giay_khai_sinh',
    icon: 'assets/kie/document/icon-documents/giay_khai_sinh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/giay-khai-sinh'
  },
  {
    title: 'Giấy nộp tiền',
    id: 'giay_nop_tien',
    icon: 'assets/kie/document/icon-documents/giay_nop_tien.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/giay-nop-tien'
  },
  {
    title: 'Giấy phép xây dựng',
    id: 'giay_phep_xay_dung',
    icon: 'assets/kie/document/icon-documents/giay_phep_xay_dung.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/giay-phep-xay-dung'
  },
  {
    title: 'Giấy đăng ký Hộ kinh doanh',
    id: 'dang_ky_ho_kinh_doanh',
    icon: 'assets/kie/document/icon-documents/giay_dang_ky_ho_kinh_doanh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/dang-ky-ho-kinh-doanh'
  },
  {
    title: 'Giấy đăng ký kinh doanh',
    id: 'dang_ky_kinh_doanh',
    icon: 'assets/kie/document/icon-documents/giay_dang_ky_kinh_doanh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/dang-ky-kinh-doanh'
  },
  {
    title: 'Giấy đăng ký kết hôn',
    id: 'giay_dang_ky_ket_hon',
    icon: 'assets/kie/document/icon-documents/giay_dang_ky_ket_hon.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/giay-dang-ky-ket-hon'
  },
  {
    title: 'Hóa đơn giá trị gia tăng',
    id: 'hoa_don_gtgt',
    icon: 'assets/kie/document/icon-documents/hoa_don.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/hoa-don-gtgt'
  },
  {
    title: 'Hóa đơn bán hàng',
    id: 'hoa_don_ban_hang',
    icon: 'assets/kie/document/icon-documents/hoa_don_ban_hang.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/hoa-don-ban-hang'
  },
  {
    title: 'Phiếu thi',
    id: 'phieu_thi',
    icon: 'assets/kie/document/icon-documents/phieu_thi.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/phieu-thi'
  },
  {
    title: 'Phiếu xuất kho',
    id: 'phieu_xuat_kho',
    icon: 'assets/kie/document/icon-documents/phieu_xuat_kho.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/phieu-xuat-kho'
  },
  {
    title: 'Sổ đỏ',
    id: 'so_do',
    icon: 'assets/kie/document/icon-documents/so_do.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/so-do'
  },
  {
    title: 'Thẻ đảng viên',
    id: 'the_dang_vien',
    icon: 'assets/kie/document/icon-documents/the_dang_vien.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/the-dang-vien'
  },
  {
    title: 'Văn bản hành chính',
    id: 'van_ban_hanh_chinh',
    icon: 'assets/kie/document/icon-documents/van_ban_hanh_chinh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v1/ocr/van-ban-hanh-chinh'
  },
  {
    title: 'Văn bản hành chính nâng cao',
    id: 'van_ban_hanh_chinh_v2',
    icon: 'assets/kie/document/icon-documents/van_ban_hanh_chinh.svg',
    idgEndpoint: '/rpa-service/aidigdoc/v2/ocr/van-ban-hanh-chinh'
  }
];

export type DrawCommand = {
  fieldId: string;
  fieldName: string;
  color?: string;
  text?: string;
} | null;

export type BboxChange = {
  fieldId: string;
  page?: number;
  value?: [number, number, number, number];
};

export type FieldConfigChange = {
  type: 'create' | 'update' | 'remove';
  content: {
    id: string;
    name: string;
    // bbox: { page: number; value: Bbox } | null;
    is_visible: boolean;
    color: string;
    order: number;
    isNew?: boolean;
  };
};

export type FieldOcrResultChange = {
  fieldId: string;
  text: string;

  valueTypeListPath?: number; // cell index in type = List
  valueTypeTablePath?: {
    // column + cell index in type = Table
    columnId: number;
    cellId: number;
  };
};

export type ValueTypeField = {
  type: FieldType.Field;
  text: string;
};

export type ValueTypeList = {
  type: FieldType.List;
  cells: ValueTypeField[];
};

export type ValueTypeTable = {
  type: FieldType.Table;
  rows: ValueTypeList[];
};
