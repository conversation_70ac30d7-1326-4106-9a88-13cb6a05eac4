import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StatisticRoutingModule } from './statistic-routing.module';
import { StatisticComponent } from './statistic.component';
import { CardComponent } from './components/card/card.component';
import { ChartComponent } from './components/chart/chart.component';
import { TableComponent } from './components/table/table.component';
import { StatisticByPageComponent } from './statistic-by-page/statistic-by-page.component';
import { StatisticByDocumentComponent } from './statistic-by-document/statistic-by-document.component';
import { DateFilterComponent } from './components/date-filter/date-filter.component';
import { CustomDatePickerComponent } from './components/custom-date-picker/custom-date-picker.component';
import { NgChartsModule } from 'ng2-charts';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { FormsModule } from '@angular/forms';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzInputModule } from 'ng-zorro-antd/input';
import { PieComponent } from './components/pie/pie.component';
import { NzSelectModule } from 'ng-zorro-antd/select';

@NgModule({
  declarations: [
    StatisticComponent,
    CardComponent,
    ChartComponent,
    TableComponent,
    StatisticByPageComponent,
    StatisticByDocumentComponent,
    DateFilterComponent,
    CustomDatePickerComponent,
    PieComponent
  ],
  imports: [
    CommonModule,
    StatisticRoutingModule,
    NgChartsModule,
    NzTabsModule,
    FormsModule,
    NzDropDownModule,
    NzToolTipModule,
    NzDatePickerModule,
    NzInputModule,
    NzSelectModule
  ]
})
export class StatisticModule {}
