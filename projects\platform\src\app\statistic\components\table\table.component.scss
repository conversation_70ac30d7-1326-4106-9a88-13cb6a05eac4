:host {
  display: block;

  .rpa-table-wrapper {
    max-height: 400px;
    overflow: auto;
    text-align: left;

    table {
      width: 100%;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #cbcbcb;
    }

    &::-webkit-scrollbar {
      width: 5px;
      background-color: transparent;
    }
  }

  .rpa-top-row {
    th {
      position: sticky;
      top: 0;
      background: #eeeeee;

      &:first-child {
        border-radius: 8px 0px 0px 0px;
      }

      &:last-child {
        border-radius: 0px 8px 0px 0px;
      }
    }
  }

  .rpa-bottom-row {
    th {
      bottom: 0;
      position: sticky;
      background: #eeeeee;

      &:first-child {
        border-radius: 0px 0px 0px 8px;
      }

      &:last-child {
        border-radius: 0px 0px 8px 0px;
      }
    }
  }
}