import { environment } from '@platform/environment/environment';

export enum TemplateType {
  SoHoaCoBan = 'scan',
  SoHoaNangCao = 'scan-table',
  DangKyKinhDoanh = 'dang-ky-kinh-doanh',
  // TrangSach = 'trang-sach',
  BangTotNghiepTHPT = 'bang-tot-nghiep-thpt',
  BangTotNghiepDaiHoc = 'bang-tot-nghiep-dai-hoc',
  DangKyHoKinhDoanh = 'dang-ky-ho-kinh-doanh',
  GiayPhepXayDung = 'giay-phep-xay-dung',
  PhieuXuatKho = 'phieu-xuat-kho',
  HoaDonGTGT = 'hoa-don-gtgt',
  PhieuThi = 'phieu-thi',
  SoDo = 'so-do',
  HoaDonBanHang = 'hoa-don-ban-hang',
  VanBanHanhChinh = 'van-ban-hanh-chinh',
  VanBanHanhChinhV2 = 'van-ban-hanh-chinh-v2',
  GoiYPhongBan = 'goi-y-phong-ban',
  GiayNopTien = 'giay-nop-tien',
  GiayKhaiSinh = 'giay-khai-sinh',
  GiayDangKyKetHon = 'giay-dang-ky-ket-hon',
  BanTrichLucHoSoBenhBinh = 'ban-trich-luc-ho-so-benh-binh',
  TheDangVien = 'the-dang-vien',
  GiayToTuyThan = 'giay-to-tuy-than',
  SoYeuLyLich = 'so-yeu-ly-lich',
  GiayXacNhanTinhTrangHonNhan = 'giay-xac-nhan-tinh-trang-hon-nhan',
  PhieuLyLichTuPhap = 'phieu-ly-lich-tu-phap',
  ChungChiHanhNgheDuoc = 'chung-chi-hanh-nghe-duoc'
}

export enum FunctionType {
  SoHoaVanBan = 'so-hoa-van-ban',
  TrichXuatThongTin = 'trich-xuat-thong-tin',
  GoiYXuLyVanBan = 'goi-y-xu-ly-van-ban'
}

export enum TemplateGroup {
  GiayToPhapLyNhanThan = 'giay-to-phap-ly-nhan-than',
  HocVanChungChiChuyenMon = 'hoc-van-chung-chi-chuyen-mon',
  KinhDoanhTaiChinh = 'kinh-doanh-tai-chinh',
  HanhChinhPhapLyCong = 'hanh-chinh-phap-ly-cong',
  DangDoanTheChinhSachXaHoi = 'dang-doan-the-chinh-sach-xa-hoi'
}

export enum RestrictionType {
  WIP = 'working-in-progress',
  LDP = 'landing-page'
}

export interface Template {
  functionType: FunctionType;
  value: TemplateType;
  label: string;
  sampleFiles: { path: string; name: string }[];
  acceptType?: string;
  acceptTypeLabel?: string;
  acceptExtensions: string[];
  restrictedInLDP: boolean;
  allowDevMode: boolean;
  allowMergeImagesIntoPdfOption?: boolean;
  templateGroup?: string; // only used in Trich xuat thong tin templates
}

export const excludedKeyFields = [
  'num_of_pages',
  'warning_messages',
  'warnings',
  'suggestion_name',
  'suggestion_score',
  'aligned_file_hash',
  'aligned_images',
  'original_images'
];

export const FunctionTypes: {
  value: FunctionType;
  label: string;
  step2Label: string;
  defaultTemplate: TemplateType;
  showInLDP: boolean;
  WIP: {
    sandbox: boolean;
    production: boolean;
  };
  allowTemplateGroup: boolean;
}[] = [
  {
    value: FunctionType.SoHoaVanBan,
    label: 'Số hóa văn bản',
    step2Label: 'tính năng số hóa',
    defaultTemplate: TemplateType.SoHoaCoBan,
    showInLDP: true,
    WIP: {
      sandbox: false,
      production: false
    },
    allowTemplateGroup: false
  },
  {
    value: FunctionType.TrichXuatThongTin,
    label: 'Trích xuất thông tin',
    step2Label: 'văn bản bóc tách',
    defaultTemplate: TemplateType.DangKyKinhDoanh,
    showInLDP: true,
    WIP: {
      sandbox: false,
      production: false
    },
    allowTemplateGroup: true
  },
  {
    value: FunctionType.GoiYXuLyVanBan,
    label: 'Gợi ý xử lý văn bản',
    step2Label: 'văn bản cần gợi ý',
    defaultTemplate: TemplateType.GoiYPhongBan,
    showInLDP: false,
    WIP: {
      sandbox: false,
      production: true
    },
    allowTemplateGroup: false
  }
];

export const TemplateTypes: Template[] = [
  {
    functionType: FunctionType.SoHoaVanBan,
    value: TemplateType.SoHoaCoBan,
    label: 'Cơ bản',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/so-hoa-van-ban/co-ban/van-ban-thuan-text-co-chu-so-viet-tay.pdf',
        name: 'Văn bản thuần Text + Có chữ số viết tay'
      }
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh/van-ban-chat-luong-thap.pdf',
      //   name: 'Văn bản chất lượng thấp',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh/van-ban-photocopy.pdf',
      //   name: 'Văn bản photocopy',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: false,
    allowMergeImagesIntoPdfOption: true,
    allowDevMode: environment.envName === 'sandbox' && false
  },
  {
    functionType: FunctionType.SoHoaVanBan,
    value: TemplateType.SoHoaNangCao,
    label: 'Nâng cao',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/so-hoa-van-ban/nang-cao/giay-chung-nhan-dang-ky-doanh-nghiep-co-bang.pdf',
        name: 'Giấy chứng nhận đăng ký doanh nghiệp có bảng'
      },
      {
        path: 'assets/ocr-sample-files/so-hoa-van-ban/nang-cao/mau-co-bang.pdf',
        name: 'Mẫu có bảng'
      },
      {
        path: 'assets/ocr-sample-files/so-hoa-van-ban/nang-cao/cong-van-van-ban-nhieu-trang.pdf',
        name: 'Công văn văn bản nhiều trang'
      }
      // {
      //   path: 'assets/ocr-sample-files/so-hoa-van-ban/nang-cao/van-ban-hanh-chinh-co-bang.pdf',
      //   name: 'Văn bản hành chính có bảng',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: false,
    allowMergeImagesIntoPdfOption: true,
    allowDevMode: environment.envName === 'sandbox' && true
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.DangKyKinhDoanh,
    label: 'Giấy đăng ký kinh doanh',
    sampleFiles: [
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/dang-ky-kinh-doanh/dang-ky-kinh-doanh-1-mat.png',
      //   name: 'Đăng ký kinh doanh 1 mặt',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/dang-ky-kinh-doanh/dang-ky-kinh-doanh-1-trang.pdf',
      //   name: 'Đăng ký kinh doanh 1 trang',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/dang-ky-kinh-doanh/dang-ky-kinh-doanh-co-bang.pdf',
      //   name: 'Đăng ký kinh doanh có bảng',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/dang-ky-kinh-doanh/dang-ky-kinh-doanh-nhieu-trang.pdf',
      //   name: 'Đăng ký kinh doanh nhiều trang',
      // },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/dang-ky-kinh-doanh/dang-ky-kinh-doanh-photocopy.pdf',
        name: 'Đăng ký kinh doanh Photocopy'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: false,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.VanBanHanhChinh,
    label: 'Văn bản hành chính',
    sampleFiles: [
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh/van-ban-chat-luong-thap.pdf',
      //   name: 'Văn bản chất lượng thấp',
      // },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh/van-ban-hanh-chinh-thong-thuong.pdf',
        name: 'Văn bản hành chính thông thường'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh/van-ban-photocopy.pdf',
        name: 'Văn bản photocopy'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: false,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.HanhChinhPhapLyCong
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.VanBanHanhChinhV2,
    label: 'Văn bản hành chính (Bản thêm trường thông tin)',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/van-ban-hanh-chinh-v2/mau-1.pdf',
        name: 'Văn bản mẫu'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: false,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.HanhChinhPhapLyCong
  },
  /* { // deprecated
      value: TemplateType.TrangSach,
      label: 'Trang sách',
      sampleFileUrl: 'assets/ocr-sample-files/trang-sach.JPG',
      acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
      acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
      acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    }, */
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.BangTotNghiepTHPT,
    label: 'Bằng tốt nghiệp cấp 3',
    sampleFiles: [
      // {
      //   path: 'assets/ocr-sample-files/bang-tot-nghiep-cap-3.jpg',
      //   name: 'Bằng tốt nghiệp cấp 3',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HocVanChungChiChuyenMon
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.BangTotNghiepDaiHoc,
    label: 'Bằng tốt nghiệp Đại học',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/bang-tot-nghiep-dai-hoc.jpg',
        name: 'Bằng tốt nghiệp đại học'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HocVanChungChiChuyenMon
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.DangKyHoKinhDoanh,
    label: 'Giấy đăng ký Hộ kinh doanh',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/giay-dang-ky-ho-kinh-doanh.jpg',
        name: 'Mẫu giấy đăng ký Hộ kinh doanh'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayPhepXayDung,
    label: 'Giấy phép xây dựng',
    sampleFiles: [
      // {
      //   path: 'assets/ocr-sample-files/giay-phep-xay-dung.jpg',
      //   name: 'Giấy phép xây dựng',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HanhChinhPhapLyCong
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.PhieuThi,
    label: 'Phiếu thi',
    sampleFiles: [
      // { path: 'assets/ocr-sample-files/phieu-thi.pdf', name: 'Phiếu thi' },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HocVanChungChiChuyenMon
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.HoaDonGTGT,
    label: 'Hóa đơn giá trị gia tăng',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-gtgt/hoa-don-gtgt-1.pdf',
        name: 'Hóa đơn giá trị gia tăng 1'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-gtgt/hoa-don-gtgt-2.pdf',
        name: 'Hóa đơn giá trị gia tăng 2'
      }
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-gtgt/mau-1.pdf',
      //   name: 'Mẫu 1',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-gtgt/mau-2.pdf',
      //   name: 'Mẫu 2',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-gtgt/mau-3.pdf',
      //   name: 'Mẫu 3',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.HoaDonBanHang,
    label: 'Hóa đơn bán hàng',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-ban-hang/hoa-don-ban-hang-1.pdf',
        name: 'Hóa đơn bán hàng 1'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-ban-hang/hoa-don-ban-hang-2.pdf',
        name: 'Hóa đơn bán hàng 2'
      }
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-ban-hang/mau-1.pdf',
      //   name: 'Mẫu 1',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-ban-hang/mau-2.pdf',
      //   name: 'Mẫu 2',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/hoa-don-ban-hang/mau-3.pdf',
      //   name: 'Mẫu 3',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.PhieuXuatKho,
    label: 'Phiếu xuất kho',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/phieu-xuat-kho-1.pdf',
        name: 'Phiếu xuất kho 1'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/phieu-xuat-kho-2.pdf',
        name: 'Phiếu xuất kho 2'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/phieu-xuat-kho-3.pdf',
        name: 'Phiếu xuất kho 3'
      }
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/mau-1.pdf',
      //   name: 'Mẫu 1',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/mau-2.pdf',
      //   name: 'Mẫu 2',
      // },
      // {
      //   path: 'assets/ocr-sample-files/boc-tach-thong-tin/phieu-xuat-kho/mau-3.pdf',
      //   name: 'Mẫu 3',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.SoDo,
    label: 'Sổ đỏ',
    sampleFiles: [{ path: 'assets/ocr-sample-files/so-do.pdf', name: 'Sổ đỏ' }],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HanhChinhPhapLyCong
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayNopTien,
    label: 'Giấy nộp tiền',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/giay-nop-tien/mau-1.pdf',
        name: 'Mẫu 1'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/giay-nop-tien/mau-2.pdf',
        name: 'Mẫu 2'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/giay-nop-tien/mau-3.pdf',
        name: 'Mẫu 3'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/giay-nop-tien/mau-4.pdf',
        name: 'Mẫu 4'
      },
      {
        path: 'assets/ocr-sample-files/boc-tach-thong-tin/giay-nop-tien/mau-5.pdf',
        name: 'Mẫu 5'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.KinhDoanhTaiChinh
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayKhaiSinh,
    label: 'Giấy khai sinh',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/giay-khai-sinh.pdf',
        name: 'Mẫu giấy khai sinh'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayDangKyKetHon,
    label: 'Giấy đăng ký kết hôn',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/giay-dang-ky-ket-hon.pdf',
        name: 'Mẫu giấy đăng ký kết hôn'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.BanTrichLucHoSoBenhBinh,
    label: 'Bản trích lục hồ sơ bệnh binh',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/ho-so-benh-binh.pdf',
        name: 'Mẫu trích lục hồ sơ bệnh binh'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.DangDoanTheChinhSachXaHoi
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.TheDangVien,
    label: 'Thẻ đảng viên',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/the-dang-vien.png',
        name: 'Mẫu thẻ đảng viên'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.DangDoanTheChinhSachXaHoi
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayToTuyThan,
    label: 'Giấy tờ tùy thân',
    sampleFiles: [
      {
        path: 'assets/ocr-sample-files/giay-to-tuy-than.jpg',
        name: 'Mẫu giấy tờ tùy thân'
      }
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: true,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.SoYeuLyLich,
    label: 'Sơ yếu lý lịch',
    sampleFiles: [],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.GiayXacNhanTinhTrangHonNhan,
    label: 'Giấy xác nhận tình trạng hôn nhân',
    sampleFiles: [],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.PhieuLyLichTuPhap,
    label: 'Phiếu lý lịch tư pháp',
    sampleFiles: [],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.GiayToPhapLyNhanThan
  },
  {
    functionType: FunctionType.TrichXuatThongTin,
    value: TemplateType.ChungChiHanhNgheDuoc,
    label: 'Chứng chỉ hành nghề dược',
    sampleFiles: [],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false,
    allowMergeImagesIntoPdfOption: false,
    templateGroup: TemplateGroup.HocVanChungChiChuyenMon
  },
  {
    functionType: FunctionType.GoiYXuLyVanBan,
    value: TemplateType.GoiYPhongBan,
    label: 'Văn bản hành chính',
    sampleFiles: [
      // {
      //   path: 'assets/ocr-sample-files/goi-y-phong-ban.pdf',
      //   name: 'Văn bản hành chính',
      // },
    ],
    acceptType: 'application/pdf, image/jpeg, image/png, image/jpg',
    acceptTypeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    acceptExtensions: ['pdf', 'jpeg', 'jpg', 'png'],
    restrictedInLDP: true,
    allowDevMode: environment.envName === 'sandbox' && false
  }
];

export const TemplateGroups: { value: string; label: string }[] = [
  { value: TemplateGroup.KinhDoanhTaiChinh, label: 'Kinh doanh & tài chính' },
  { value: TemplateGroup.HanhChinhPhapLyCong, label: 'Hành chính & pháp lý công' },
  { value: TemplateGroup.GiayToPhapLyNhanThan, label: 'Giấy tờ pháp lý & nhân thân' },
  {
    value: TemplateGroup.HocVanChungChiChuyenMon,
    label: 'Học vấn & chứng chỉ chuyên môn'
  },
  {
    value: TemplateGroup.DangDoanTheChinhSachXaHoi,
    label: 'Đảng, đoàn thể & chính sách xã hội'
  }
];

export const goiYPhongBanMap = {
  HCM_VPUBNDQUAN12: 'ỦY BAN NHÂN DÂN QUẬN 12',
  HCM_VPUBNDQUAN12_0100: 'Văn phòng UBND quận',
  HCM_VPUBNDQUAN12_3600: 'Phòng nội vụ',
  HCM_VPUBNDQUAN12_0200: 'Thanh tra quận',
  HCM_VPUBNDQUAN12_0300: 'Phòng tư pháp',
  HCM_VPUBNDQUAN12_0700: 'Phòng Tài chính - kế hoạch',
  HCM_VPUBNDQUAN12_0900: 'Phòng kinh tế',
  HCM_VPUBNDQUAN12_3800: 'Chi cục thống kê',
  HCM_VPUBNDQUAN12_2100: 'Chi cục thuế',
  HCM_VPUBNDQUAN12_4000: 'Đội QLTT số 12',
  HCM_VPUBNDQUAN12_1000: 'Phòng Tài Nguyên và môi trường',
  HCM_VPUBNDQUAN12_0800: 'Phòng Quản lý đô thị',
  HCM_VPUBNDQUAN12_4500: 'Đội Quản lý trật tự Đô thị',
  HCM_VPUBNDQUAN12_1300: 'Ban Quản lý đầu tư xây dựng khu vực Quận 12',
  HCM_VPUBNDQUAN12_1200: 'Ban Bồi thường giải phóng mặt bằng',
  HCM_VPUBNDQUAN12_4100: 'Đội Thanh tra địa bàn quận',
  HCM_VPUBNDQUAN12_3400: 'Chi nhánh văn phòng đăng lý đất đai',
  HCM_VPUBNDQUAN12_1100: 'Phòng Giáo dục và Đào tạo',
  HCM_VPUBNDQUAN12_0500: 'Phòng Lao động thương binh và xã hội',
  HCM_VPUBNDQUAN12_0400: 'Phòng Văn hóa và thông tin',
  HCM_VPUBNDQUAN12_0600: 'Phòng Y tế',
  HCM_VPUBNDQUAN12_3700: 'Bệnh viện Quận 12',
  HCM_VPUBNDQUAN12_1700: 'Trung tâm Y tế',
  HCM_VPUBNDQUAN12_1500: 'Trung tâm Văn hóa - Thể thao Quận 12',
  HCM_VPUBNDQUAN12_1600: 'Trung tâm Giáo dục Nghề nghiệp - Giáo dục Thường xuyên',
  HCM_VPUBNDQUAN12_1800: 'Công an Quận',
  HCM_VPUBNDQUAN12_1900: 'Ban Chỉ huy quân sự quận',
  HCM_VPUBNDQUAN12_4300: 'Tòa án nhân dân quận',
  HCM_VPUBNDQUAN12_4400: 'Viện Kiểm soát nhân dân quận',
  HCM_VPUBNDQUAN12_4200: 'Chi cục Thi hành án dân sự quận',
  HCM_VPUBNDQUAN12_3900: 'Công ty TNHH MTV Dịch vụ công ích Quận',
  HCM_VPUBNDQUAN12_3500: 'Liên đoàn Lao động',
  HCM_VPUBNDQUAN12_4700: 'Hội Chữ thập đỏ quận',
  HCM_VPUBNDQUAN12_4800: 'Bảo hiểm Xã hội quận',
  HCM_VPUBNDQUAN12_4600: 'Quận đoàn',
  HCM_VPUBNDQUAN12_5000: 'Hội phụ nữ',
  HCM_VPUBNDQUAN12_4900: 'Ngân hàng Chính sách Xã hội quận',
  HCM_VPUBNDQUAN12_3300: 'Kho bạc nhà nước',
  HCM_VPUBNDQUAN12_2400: 'UBND phường Đông Hưng Thuận',
  HCM_VPUBNDQUAN12_2200: 'UBND phường Tân Thới Nhất',
  HCM_VPUBNDQUAN12_2300: 'UBND phường Tân Hưng Thuận',
  HCM_VPUBNDQUAN12_2500: 'UBND phường Trung Mỹ Tây',
  HCM_VPUBNDQUAN12_2600: 'UBND phường Tân Chánh Hiệp',
  HCM_VPUBNDQUAN12_2900: 'UBND phường Tân Thới Hiệp',
  HCM_VPUBNDQUAN12_2800: 'UBND phường Hiệp Thành',
  HCM_VPUBNDQUAN12_2700: 'UBND phường Thới An',
  HCM_VPUBNDQUAN12_3000: 'UBND phường Thạnh Xuân',
  HCM_VPUBNDQUAN12_3100: 'UBND phường Thạnh Lộc',
  HCM_VPUBNDQUAN12_3200: 'UBND phường An Phú Đông',
  'chutich.q12': 'Nguyễn Văn Đức',
  'pct1.q12': 'Đậu An Phúc',
  'pct2.q12': 'Võ Thị Chính',
  'pct3.q12': 'Nguyễn Minh Chánh',
  'vtvp.q12': 'Văn thư UBND Quận'
};

// export const Modes = ['landing-page', 'platform', 'demo'] as const;
// export type Mode = (typeof Modes)[number];
export enum Mode {
  LDP = 'landing-page',
  Platform = 'platform',
  Demo = 'demo'
}

// hard code the order of field by template
export const FIELD_ORDER = /* : {
  [key: string]: { [key: string]: string | { [key: string]: string }[] }[];
} */ {
  [TemplateType.DangKyKinhDoanh]: [
    {
      NOI_CAP_GIAY_PHEP: 'Nơi cấp giấy phép ĐKKD'
    },
    {
      MA_SO_DOANH_NGHIEP: 'Mã số doanh nghiệp'
    },
    {
      DANG_KY_LAN_DAU: 'Đăng ký lần đầu'
    },
    {
      DANG_KY_THAY_DOI: 'Đăng ký thay đổi'
    },
    {
      TEN_CONG_TY_TIENG_VIET: 'Tên công ty viết bằng tiếng Việt'
    },
    {
      TEN_CONG_TY_NUOC_NGOAI: 'Tên công ty viết bằng tiếng nước ngoài'
    },
    {
      TEN_CONG_TY_VIET_TAT: 'Tên công ty viết tắt'
    },
    {
      DIA_CHI_TRU_SO_CHINH: 'Địa chỉ trụ sở chính'
    },
    {
      SO_DIEN_THOAI: 'Số điện thoại'
    },
    {
      EMAIL: 'Email'
    },
    {
      WEBSITE: 'Website'
    },
    {
      VON_DIEU_LE: 'Vốn điều lệ'
    },
    {
      VON_DIEU_LE_BANG_CHU: 'Vốn điều lệ bằng chữ'
    },
    {
      NGUOI_DUNG_DAU_HO_VA_TEN: 'Thông tin người đứng đầu - Họ và tên'
    },
    {
      NGUOI_DUNG_DAU_GIOI_TINH: 'Thông tin người đứng đầu - Giới tính'
    },
    {
      NGUOI_DUNG_DAU_NGAY_SINH: 'Thông tin người đứng đầu - Ngày sinh'
    },
    {
      NGUOI_DUNG_DAU_QUOC_TICH: 'Thông tin người đứng đầu - Quốc tịch'
    },
    {
      NGUOI_DUNG_DAU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN:
        'Thông tin người đứng đầu - Loại giấy chứng thực cá nhân'
    },
    {
      NGUOI_DUNG_DAU_SO_GIAY_CHUNG_THUC_CA_NHAN:
        'Thông tin người đứng đầu - Số giấy chứng thực cá nhân'
    },
    {
      NGUOI_DUNG_DAU_NGAY_CAP: 'Thông tin người đứng đầu - Ngày cấp'
    },
    {
      NGUOI_DUNG_DAU_NOI_CAP: 'Thông tin người đứng đầu - Nơi cấp'
    },
    {
      NGUOI_DUNG_DAU_HO_KHAU_THUONG_TRU: 'Thông tin người đứng đầu - Nơi đăng ký HKTT'
    },
    {
      NGUOI_DUNG_DAU_CHO_O_HIEN_TAI: 'Thông tin người đứng đầu - Chỗ ở hiện tại'
    },
    {
      CHU_SO_HUU_HO_VA_TEN: 'Chủ sở hữu là cá nhân - Họ và tên'
    },
    {
      CHU_SO_HUU_GIOI_TINH: 'Chủ sở hữu là cá nhân - Giới tính'
    },
    {
      CHU_SO_HUU_NGAY_SINH: 'Chủ sở hữu là cá nhân - Ngày sinh'
    },
    {
      CHU_SO_HUU_QUOC_TICH: 'Chủ sở hữu là cá nhân - Quốc tịch'
    },
    {
      CHU_SO_HUU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN:
        'Chủ sở hữu là cá nhân - Loại giấy chứng thực cá nhân'
    },
    {
      CHU_SO_HUU_SO_GIAY_CHUNG_THUC_CA_NHAN:
        'Chủ sở hữu là cá nhân - Số giấy chứng thực cá nhân'
    },
    {
      CHU_SO_HUU_NGAY_CAP: 'Chủ sở hữu là cá nhân - Ngày cấp'
    },
    {
      CHU_SO_HUU_NOI_CAP: 'Chủ sở hữu là cá nhân - Nơi cấp'
    },
    {
      CHU_SO_HUU_HO_KHAU_THUONG_TRU: 'Chủ sở hữu là cá nhân - Nơi đăng ký HKTT'
    },
    {
      CHU_SO_HUU_CHO_O_HIEN_TAI: 'Chủ sở hữu là cá nhân - Chỗ ở hiện tại'
    },
    {
      TEN_TO_CHUC: 'Chủ sở hữu là tổ chức - Tên tổ chức'
    },
    {
      'MSDN/QDTL': 'Chủ sở hữu là tổ chức - Mã số doanh nghiệp/QĐTL'
    },
    {
      TC_NGAY_CAP: 'Chủ sở hữu là tổ chức - Ngày cấp'
    },
    {
      TC_NOI_CAP: 'Chủ sở hữu là tổ chức - Nơi cấp'
    },
    {
      TC_DIA_CHI_TRU_SO_CHINH: 'Chủ sở hữu là tổ chức - Địa chỉ trụ sở chính'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_VA_TEN:
        'Thông tin người đại diện pháp luật - Họ và tên'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHUC_DANH:
        'Thông tin người đại diện pháp luật - Chức danh'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_GIOI_TINH:
        'Thông tin người đại diện pháp luật - Giới tính'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_SINH:
        'Thông tin người đại diện pháp luật - Ngày sinh'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_QUOC_TICH:
        'Thông tin người đại diện pháp luật - Quốc tịch'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN:
        'Thông tin người đại diện pháp luật - Loại giấy tờ chứng thực cá nhân'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_SO_GIAY_CHUNG_THUC_CA_NHAN:
        'Thông tin người đại diện pháp luật - Số giấy tờ chứng thực cá nhân'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_CAP:
        'Thông tin người đại diện pháp luật - Ngày cấp'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NOI_CAP:
        'Thông tin người đại diện pháp luật - Nơi cấp'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHO_O_HIEN_TAI:
        'Thông tin người đại diện pháp luật - Chỗ ở hiện tại'
    },
    {
      THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_KHAU_THUONG_TRU:
        'Thông tin người đại diện pháp luật - Hộ khẩu thường trú'
    },
    {
      DANH_SACH_THANH_VIEN_GOP_VON: 'Danh sách thành viên góp vốn',
      table_columns: [
        {
          TEN_THANH_VIEN: 'Tên thành viên'
        },
        {
          NOI_DANG_KY_HKTT: 'Nơi đăng ký HKTT'
        },
        {
          GIA_TRI_VON_GOP: 'Giá trị góp vốn'
        },
        {
          TY_LE: 'Tỷ lệ %'
        },
        {
          SO_CMND: 'Số CMND'
        }
      ]
    }
  ],
  [TemplateType.HoaDonBanHang]: [
    {
      type: 'LOẠI HÓA ĐƠN'
    },
    {
      serial: 'KÝ HIỆU'
    },
    {
      invoice_number: 'SỐ'
    },
    {
      issued_date: 'NGÀY HÓA ĐƠN'
    },
    {
      seller_name: 'TÊN NGƯỜI BÁN'
    },
    {
      seller_address: 'ĐỊA CHỈ NGƯỜI BÁN'
    },
    {
      seller_tax_code: 'MÃ SỐ THUẾ NGƯỜI BÁN'
    },
    {
      seller_phone_number: 'SỐ ĐIỆN THOẠI NGƯỜI BÁN'
    },
    {
      seller_account_number: 'STK NGƯỜI BÁN'
    },
    {
      seller_bank: 'TÊN NGÂN HÀNG CỦA NGƯỜI BÁN'
    },
    {
      buyer_name: 'TÊN NGƯỜI MUA'
    },
    {
      buyer_company_name: 'TÊN ĐƠN VỊ MUA'
    },
    {
      buyer_tax_code: 'MÃ SỐ THUẾ NGƯỜI MUA'
    },
    {
      buyer_phone_number: 'SỐ ĐIỆN THOẠI NGƯỜI MUA'
    },
    {
      buyer_address: 'ĐỊA CHỈ NGƯỜI MUA'
    },
    {
      payment_method: 'HÌNH THỨC THANH TOÁN'
    },
    {
      buyer_account_number: 'STK NGƯỜI MUA'
    },
    {
      buyer_bank: 'TÊN NGÂN HÀNG CỦA NGƯỜI MUA'
    },
    {
      currency_method: 'ĐỒNG TIỀN THANH TOÁN'
    },
    {
      rates: 'TỶ GIÁ'
    },
    {
      details: 'CHI TIẾT',
      table_columns: [
        {
          Description: 'TÊN HÀNG HÓA, DỊCH VỤ'
        },
        {
          Unit: 'ĐƠN VỊ TÍNH'
        },
        {
          quantity: 'SỐ LƯỢNG'
        },
        {
          unit_price: 'ĐƠN GIÁ'
        },
        {
          amount: 'THÀNH TIỀN'
        },
        {
          total_amount: 'TỔNG TIỀN'
        }
      ]
    },
    {
      total_amount_in_words: 'SỐ TIỀN VIẾT BẰNG CHỮ'
    },
    {
      signed_by: 'KÝ BỞI'
    },
    {
      signed_date: 'NGÀY KÝ'
    },
    {
      invoice_provider: 'ĐƠN VỊ CUNG CẤP HÓA ĐƠN'
    }
  ],
  /* [TemplateType.PhieuThi]: [
    {
      candidates: 'KẾT QUẢ',
      table_columns: [
        {
          identification_number: 'SỐ CMND'
        },
        {
          exam_number: 'ĐỀ THI SỐ'
        },
        {
          candiate_name: 'TÊN THÍ SINH'
        },
        {
          results_1: 'CÂU 1'
        },
        {
          results_2: 'CÂU 2'
        },
        {
          results_3: 'CÂU 3'
        },
        {
          results_4: 'CÂU 4'
        },
        {
          results_5: 'CÂU 5'
        },
        {
          results_6: 'CÂU 6'
        },
        {
          results_7: 'CÂU 7'
        },
        {
          results_8: 'CÂU 8'
        },
        {
          results_9: 'CÂU 9'
        },
        {
          results_10: 'CÂU 10'
        },
        {
          results_11: 'CÂU 11'
        },
        {
          results_12: 'CÂU 12'
        },
        {
          results_13: 'CÂU 13'
        },
        {
          results_14: 'CÂU 14'
        },
        {
          results_15: 'CÂU 15'
        },
        {
          results_16: 'CÂU 16'
        },
        {
          results_17: 'CÂU 17'
        },
        {
          results_18: 'CÂU 18'
        },
        {
          results_19: 'CÂU 19'
        },
        {
          results_20: 'CÂU 20'
        },
        {
          results_21: 'CÂU 21'
        },
        {
          results_22: 'CÂU 22'
        },
        {
          results_23: 'CÂU 23'
        },
        {
          results_24: 'CÂU 24'
        },
        {
          results_25: 'CÂU 25'
        },
        {
          results_26: 'CÂU 26'
        },
        {
          results_27: 'CÂU 27'
        },
        {
          results_28: 'CÂU 28'
        },
        {
          results_29: 'CÂU 29'
        },
        {
          results_30: 'CÂU 30'
        },
        {
          results_31: 'CÂU 31'
        },
        {
          results_32: 'CÂU 32'
        },
        {
          results_33: 'CÂU 33'
        },
        {
          results_34: 'CÂU 34'
        },
        {
          results_35: 'CÂU 35'
        },
        {
          results_36: 'CÂU 36'
        },
        {
          results_37: 'CÂU 37'
        },
        {
          results_38: 'CÂU 38'
        },
        {
          results_39: 'CÂU 39'
        },
        {
          results_40: 'CÂU 40'
        }
      ]
    }
  ], */
  [TemplateType.VanBanHanhChinh]: [
    { base: 'Căn cứ' },
    { m_origin: 'Nơi ban hành văn bản' },
    { m_date: 'Ngày ban hành văn bản' },
    { m_month: 'Tháng ban hành văn bản' },
    { m_year: 'Năm ban hành văn bản' },
    { left_m_cited: 'Trích yếu' },
    { m_number: 'Số, ký hiệu' },
    { category: 'Loại văn bản' },
    { recipient: 'Nơi nhận' },
    { m_sign: 'Người ký' }
  ]
};
