import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { ListSharedDocuments, Role } from '@platform/app/kie/kie';

@Component({
  selector: 'app-table-shared-document',
  templateUrl: './table-shared-document.component.html',
  styleUrls: ['./table-shared-document.component.scss']
})
export class TableSharedDocumentComponent implements OnInit, OnChanges {
  @Input() listSharedDocuments: ListSharedDocuments;
  @Output() onPageChanged: EventEmitter<number> = new EventEmitter<number>();
  @Output() onLimitChanged: EventEmitter<number> = new EventEmitter<number>();

  constructor() {}

  ngOnInit(): void {}

  ngOnChanges(): void {}

  handleChangePage(newPage: number): void {
    this.onPageChanged.emit(newPage);
  }

  handleChangeLimit(limit): void {
    this.onLimitChanged.emit(limit);
  }

  viewRole(role: Role): string {
    switch (role) {
      case Role.Creator:
        return 'Ch<PERSON> sở hữu';
      case Role.Editor:
        return 'Người chỉnh sửa';
      case Role.Viewer:
        return '<PERSON><PERSON><PERSON>i xem';
    }
  }
}
