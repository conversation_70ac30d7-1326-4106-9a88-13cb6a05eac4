import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalCopyDocumentComponent } from './modal-copy-document.component';

describe('ModalCopyDocumentComponent', () => {
  let component: ModalCopyDocumentComponent;
  let fixture: ComponentFixture<ModalCopyDocumentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalCopyDocumentComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalCopyDocumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
