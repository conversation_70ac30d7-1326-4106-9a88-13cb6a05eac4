:host {
	display: flex;
	height: 1px;
	flex: auto;
	background-color: #fff;
	@apply text-text-1;

	::ng-deep markdown {
		* {
			margin-bottom: 8px;
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			margin-bottom: 12px;
			font-weight: 600;
		}

		h1 {
			font-size: 28px;
			line-height: 40px;
		}

		h2 {
			font-size: 20px;
			line-height: 28px;
		}

		p {
			font-weight: 400;
			font-size: 14px;
			line-height: 20px;
		}

		strong {
			font-weight: 600;
		}

		:not(pre)>code {
			font-weight: 500;
			color: #E83E8C;
			background-color: #EDEDED;
			padding: 2px 4px;
			border-radius: 4px;
		}

		a {
			@apply text-brand-1;
		}

		table {
			margin-bottom: 12px;
			width: 100%;
			table-layout: fixed;

			thead tr th {
				border-bottom: 1px solid #E7EBEF;
				text-align: left;
				font-weight: 600;
				padding: 8px;
				background-color: #F0F1F4;
				overflow-wrap: break-word;
			}

			tbody tr td {
				border-bottom: 1px solid #E7EBEF;
				text-align: left;
				vertical-align: baseline;
				padding: 8px;
				overflow-wrap: break-word;
			}
		}
	}
}