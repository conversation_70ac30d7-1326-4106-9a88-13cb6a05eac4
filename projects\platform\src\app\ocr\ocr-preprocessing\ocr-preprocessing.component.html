<div
  #inputFile
  class="flex relative items-center justify-center gap-3 bg-bg-3 rounded-lg p-5 border border-dashed border-line hover:border-brand-2"
  (dragover)="inputFile?.classList?.add('!border-brand-2')"
  (dragleave)="inputFile?.classList?.remove('!border-brand-2')"
  (drop)="inputFile?.classList?.remove('!border-brand-2')"
>
  <input
    multiple
    class="absolute top-0 left-0 w-full h-full z-10 opacity-0 text-[0px] cursor-pointer"
    type="file"
    [accept]="RULE_ACCEPT.accept"
    #inputFileElem
    (change)="handleFileInputChange($event, inputFileElem)"
    (cancel)="handleFileInputCancel($event, inputFileElem)"
  />
  <div class="flex flex-col items-center justify-center gap-1">
    <img class="w-11" src="/assets/kie/header-table/upload.svg" alt="icon-upload" />
    <div class="font-medium">
      <span class="text-brand-1">Chọn</span> hoặc kéo thả file tại đây
    </div>
    <div class="text-text-3">
      Định dạng file cho phép <b>pdf</b>, <b>jpg</b>, <b>jpeg</b>, <b>png</b>. Kích thước
      file tối đa <b>{{ RULE_ACCEPT.getSizeStr() }}</b>
    </div>
  </div>
</div>
<div
  *ngIf="setOfCheckedId.size > 0"
  class="bg-brand-3/10 p-[10px] rounded items-center gap-6 transition-all duration-500 flex"
>
  <div class="flex gap-2 border-r border-icon-1 pr-4 text-sm font-medium text-text-1">
    <img
      (click)="uncheckAll()"
      class="cursor-pointer"
      src="/assets/kie/header-table/cancel.svg"
      alt="cancel-icon"
    />
    <p>
      Đã chọn (<span class="text-brand-1">{{ setOfCheckedId.size }}</span
      >)
    </p>
  </div>
  <button class="flex gap-2 text-sm font-medium text-text-1" (click)="executeMany()">
    <img class="cursor-pointer" src="/assets/ocr/transfer-ocr.svg" alt="download-icon" />
    <p>Số hóa tài liệu</p>
  </button>
  <button
    class="flex gap-2 text-sm font-medium text-text-1"
    (click)="deleteCheckedItems()"
  >
    <img src="/assets/kie/header-table/delete.svg" alt="delete-icon" />
    <p>Xóa khỏi danh sách</p>
  </button>
</div>
<nz-table
  class="xl:h-[1px] flex-auto overflow-auto"
  #rowSelectionTable
  [nzTemplateMode]="true"
  [nzShowPagination]="false"
  [nzFrontPagination]="true"
  [nzData]="fileList"
  [nzPageIndex]="tablePageIndex"
  [nzPageSize]="tableLimit"
  (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
  [nzTableLayout]="'fixed'"
>
  <thead>
    <tr>
      <th
        nzWidth="40px"
        [nzDisabled]="!rowSelectionTable.data?.length"
        [nzChecked]="checked"
        [nzIndeterminate]="indeterminate"
        (nzCheckedChange)="onAllChecked($event)"
      ></th>
      <th nzWidth="60px">STT</th>
      <th>Tài liệu</th>
      <th>Phạm vi OCR</th>
      <th nzWidth="100px" nzRight></th>
    </tr>
  </thead>
  <tbody>
    <ng-container *ngFor="let file of rowSelectionTable.data; let i = index">
      <ng-template
        *ngTemplateOutlet="
          tableRow;
          context: { file: file, fileIndex: (tablePageIndex - 1) * tableLimit + i }
        "
      ></ng-template>
    </ng-container>
    <ng-template #tableRow let-file="file" let-fileIndex="fileIndex">
      <tr [ngClass]="{ active: file.id === activeFile?.id }">
        <td
          [nzChecked]="setOfCheckedId.has(file.id)"
          (nzCheckedChange)="onItemChecked(file.id, $event)"
        ></td>
        <td>{{ fileIndex + 1 }}</td>
        <td>
          <div>
            <div
              (click)="selectFile(file)"
              class="text-text-1 text-sm font-medium truncate break-words hover:text-brand-2 hover:cursor-pointer"
              nz-tooltip
              [nzTooltipTitle]="file.name.length > 30 ? file.name : ''"
            >
              {{ file.name }}
            </div>
            <div class="text-text-3 text-xs">
              {{ file.size }} - {{ file.numPages || 'N/A' }} trang
            </div>
          </div>
        </td>
        <td>
          <div
            class="flex items-center gap-2"
            [formGroup]="fileListFormArray.controls[fileIndex]"
          >
            <nz-select
              formControlName="pageSelectionType"
              nzSize="large"
              class="min-w-[135px]"
              (nzFocus)="selectFile(file)"
              (ngModelChange)="handlePageSelectionTypeChange($event, fileIndex)"
            >
              <nz-option nzValue="all" nzLabel="Tất cả trang"></nz-option>
              <nz-option nzValue="custom" nzLabel="Tùy chỉnh"></nz-option>
            </nz-select>
            <div *ngIf="showPageSelectionTextInput(fileIndex)" class="relative">
              <input
                [nzStatus]="
                  fileListFormArray.controls[fileIndex].controls['pageSelectionText']
                    .invalid
                    ? 'error'
                    : null
                "
                nz-input
                formControlName="pageSelectionText"
                placeholder="Vd: 1-3,8,9"
                nzSize="large"
                class="rounded-lg pr-7"
                (focus)="selectFile(file)"
              />
              <span
                class="cursor-pointer absolute right-2 top-1/2 -translate-y-1/2"
                nz-icon
                [nzType]="
                  fileListFormArray.controls[fileIndex].controls['pageSelectionText']
                    .invalid
                    ? 'close-circle'
                    : 'check-circle'
                "
                nzTheme="twotone"
                [nzTwotoneColor]="
                  fileListFormArray.controls[fileIndex].controls['pageSelectionText']
                    .invalid
                    ? 'red'
                    : '#52c41a'
                "
                nz-tooltip
                [nzTooltipColor]="'red'"
                [nzTooltipTitle]="
                  getPageSelectionTextError(fileIndex, 'invalidPageSelection')
                "
              ></span>
            </div>
          </div>
        </td>
        <td>
          <div class="flex gap-2">
            <button
              nz-tooltip
              nzTooltipTitle="Xem và chỉnh sửa"
              (click)="selectFile(file)"
            >
              <ng-template [ngTemplateOutlet]="viewIcon"></ng-template>
            </button>
            <button nz-tooltip nzTooltipTitle="Số hóa tài liệu" (click)="execute(file)">
              <ng-template [ngTemplateOutlet]="executeIcon"></ng-template>
            </button>
            <button nz-tooltip nzTooltipTitle="Xóa" (click)="deleteFile(file)">
              <ng-template [ngTemplateOutlet]="deleteIcon"></ng-template>
            </button>
          </div>
        </td>
      </tr>
    </ng-template>
  </tbody>
</nz-table>
<div
  *ngIf="!fileList.length"
  class="w-full min-h-[500px] flex flex-col gap-5 items-center justify-center bg-[linear-gradient(0deg,rgba(255,255,255,1)50%,rgba(251,251,252,1)100%)]"
>
  <img src="assets/kie/document/empty-table.svg" />
</div>
<nz-pagination
  class=""
  [nzShowTotal]="rangeTemplate"
  [nzTotal]="fileList?.length || 0"
  [nzPageSize]="tableLimit"
  [(nzPageIndex)]="tablePageIndex"
  [nzItemRender]="renderItemTemplate"
>
</nz-pagination>
<ng-template #rangeTemplate let-range="range" let-total>
  <div class="flex justify-between items-center">
    <div>
      Tổng số: <span class="font-medium">{{ total }}</span> file / Tối đa:
      <span class="font-medium">{{ MaxPreprocessingFileListCount }}</span> file
    </div>
    <div>
      Số file mỗi trang
      <nz-select
        class="w-[65px]"
        [ngModel]="tableLimit"
        (ngModelChange)="onLimitChange($event)"
      >
        <nz-option [nzValue]="5" nzLabel="5"></nz-option>
        <nz-option [nzValue]="10" nzLabel="10"></nz-option>
        <nz-option [nzValue]="20" nzLabel="20"></nz-option>
        <!-- <nz-option [nzValue]="30" nzLabel="30"></nz-option> -->
      </nz-select>
    </div>
  </div>
</ng-template>
<ng-template #renderItemTemplate let-type let-page="page">
  <ng-container [ngSwitch]="type">
    <a *ngSwitchCase="'page'">{{ page }}</a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'prev'">
      <img src="/assets/kie/header-table/previous_page.svg" alt="prev-icon" />
    </a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'next'">
      <img src="/assets/kie/header-table/next_page.svg" alt="next-icon" />
    </a>
    <a class="btn-dot" *ngSwitchCase="'prev_5'">...</a>
    <a class="btn-dot" *ngSwitchCase="'next_5'">...</a>
  </ng-container>
</ng-template>
<ngx-spinner
  [name]="'ocr-preprocessing-loading'"
  bdColor="rgba(0, 0, 0, 0.2)"
  size="medium"
  color="#fff"
  type="ball-scale-multiple"
  [fullScreen]="false"
  class="contents"
>
  <div class="font-semibold text-text-2">Đang xử lý file và tải lên</div>
</ngx-spinner>

<ng-template #viewIcon>
  <svg
    class="group"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      class="stroke-status-success"
      d="M12.9833 10.0009C12.9833 11.6509 11.6499 12.9842 9.99993 12.9842C8.34993 12.9842 7.0166 11.6509 7.0166 10.0009C7.0166 8.35091 8.34993 7.01758 9.99993 7.01758C11.6499 7.01758 12.9833 8.35091 12.9833 10.0009Z"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-status-success"
      d="M9.99987 16.8913C12.9415 16.8913 15.6832 15.1579 17.5915 12.1579C18.3415 10.9829 18.3415 9.00794 17.5915 7.83294C15.6832 4.83294 12.9415 3.09961 9.99987 3.09961C7.0582 3.09961 4.31654 4.83294 2.4082 7.83294C1.6582 9.00794 1.6582 10.9829 2.4082 12.1579C4.31654 15.1579 7.0582 16.8913 9.99987 16.8913Z"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>
<ng-template #executeIcon>
  <svg
    class="group"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      class="stroke-brand-1"
      d="M16.6447 15.4564H12.3872C12.305 15.4518 12.2758 15.4243 12.2643 15.4114C12.2461 15.391 12.2292 15.3542 12.2292 15.3054C12.2292 15.2565 12.2462 15.2197 12.2644 15.1992C12.276 15.1862 12.3052 15.1588 12.3871 15.1543H16.6447H17.822C17.8319 15.2546 17.8319 15.3561 17.822 15.4564H16.6447ZM17.4088 14.2808C17.6399 14.5006 17.7757 14.7966 17.8164 15.1059L16.9954 14.2979L16.1439 13.4599C16.1439 13.4599 16.1439 13.4599 16.1439 13.4599C16.0844 13.4013 16.0837 13.3057 16.1422 13.2463C16.2008 13.1868 16.2964 13.1861 16.3558 13.2445L16.3558 13.2445L17.4027 14.2749L17.4027 14.275L17.4088 14.2808ZM16.2499 17.4095C16.21 17.4095 16.1719 17.3946 16.1422 17.3644L16.1421 17.3644C16.0837 17.3049 16.0844 17.2093 16.1439 17.1508L16.9954 16.3127L17.8164 15.5047C17.7759 15.8136 17.6405 16.109 17.4103 16.3283L17.4103 16.3282L17.4045 16.3339L16.3558 17.3661L16.3557 17.3661C16.3262 17.3952 16.2889 17.4095 16.2499 17.4095ZM13.7025 12.9716L13.7025 12.9716L12.6555 11.9412L12.6556 11.9411L12.6495 11.9353C12.4184 11.7155 12.2826 11.4195 12.2419 11.1103L13.0628 11.9182L13.9144 12.7563C13.9144 12.7563 13.9144 12.7563 13.9144 12.7563C13.9738 12.8148 13.9746 12.9105 13.9161 12.9699C13.8864 13 13.8482 13.015 13.8084 13.015C13.7694 13.015 13.732 13.0006 13.7025 12.9716ZM13.4135 11.0618H12.2363C12.2264 10.9615 12.2263 10.8601 12.2363 10.7598H13.4135H17.671C17.7532 10.7643 17.7825 10.7918 17.794 10.8047C17.8122 10.8252 17.8291 10.862 17.8291 10.9108C17.829 10.9596 17.8121 10.9965 17.7939 11.017C17.7823 11.0299 17.7531 11.0573 17.6711 11.0618H13.4135ZM13.0628 9.9034L12.2418 10.7114C12.2824 10.4025 12.4178 10.1071 12.6479 9.88785L12.648 9.8879L12.6538 9.8822L13.7024 8.85008C13.7025 8.85007 13.7025 8.85006 13.7025 8.85004C13.762 8.79155 13.8575 8.79232 13.9161 8.85173C13.9746 8.91121 13.9738 9.00683 13.9144 9.06534C13.9144 9.06535 13.9144 9.06536 13.9144 9.06536L13.0628 9.9034ZM11.3235 17.8327H4.27067C3.11088 17.8327 2.1665 16.8883 2.1665 15.7285V4.27018C2.1665 3.11039 3.11088 2.16602 4.27067 2.16602H12.2749C13.4347 2.16602 14.379 3.11039 14.379 4.27018V6.6354C14.3745 6.71758 14.347 6.74682 14.3341 6.75833C14.3136 6.77652 14.2769 6.79342 14.228 6.79341C14.1792 6.7934 14.1423 6.77647 14.1219 6.75823C14.1089 6.74667 14.0815 6.71746 14.077 6.63548V4.27018C14.077 3.27607 13.269 2.4681 12.2749 2.4681H4.27067C3.27656 2.4681 2.46859 3.27607 2.46859 4.27018V15.7285C2.46859 16.7226 3.27656 17.5306 4.27067 17.5306H11.3234C11.4056 17.5352 11.4348 17.5626 11.4463 17.5756C11.4645 17.596 11.4814 17.6328 11.4814 17.6816C11.4814 17.7305 11.4645 17.7673 11.4462 17.7878C11.4347 17.8008 11.4054 17.8282 11.3235 17.8327ZM7.50741 11.5378C7.49586 11.5508 7.46664 11.5782 7.38467 11.5827H4.92919C4.847 11.5781 4.81776 11.5506 4.80626 11.5377C4.78806 11.5173 4.77116 11.4805 4.77118 11.4317C4.77119 11.3828 4.78811 11.346 4.80636 11.3255C4.81791 11.3125 4.84713 11.2851 4.9291 11.2806H7.38458C7.46677 11.2852 7.49601 11.3126 7.50751 11.3256C7.52571 11.346 7.54261 11.3828 7.54259 11.4316C7.54258 11.4805 7.52566 11.5173 7.50741 11.5378ZM10.3795 8.97852H4.92919C4.847 8.97396 4.81776 8.94647 4.80626 8.93355C4.78806 8.91311 4.77116 8.87633 4.77118 8.82749C4.77119 8.77865 4.78811 8.74181 4.80636 8.72133C4.81791 8.70836 4.84713 8.68094 4.9291 8.67643H10.3794C10.4616 8.68099 10.4908 8.70848 10.5023 8.7214C10.5205 8.74184 10.5374 8.77862 10.5374 8.82746C10.5374 8.8763 10.5205 8.91314 10.5022 8.93362C10.4907 8.94659 10.4614 8.97401 10.3795 8.97852ZM11.6128 6.37435H4.92919C4.847 6.36979 4.81776 6.3423 4.80626 6.32938C4.78806 6.30894 4.77116 6.27216 4.77118 6.22333C4.77119 6.17448 4.78811 6.13765 4.80636 6.11716C4.81791 6.10419 4.84713 6.07678 4.9291 6.07227H11.6127C11.6949 6.07682 11.7242 6.10431 11.7357 6.11724C11.7539 6.13767 11.7708 6.17445 11.7708 6.22329C11.7707 6.27213 11.7538 6.30897 11.7356 6.32945C11.724 6.34242 11.6948 6.36984 11.6128 6.37435Z"
      fill="#2B2D3B"
      stroke="#989BB3"
    />
  </svg>
</ng-template>
<ng-template #deleteIcon>
  <svg
    class="group"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      class="stroke-status-error"
      d="M17.5 4.98307C14.725 4.70807 11.9333 4.56641 9.15 4.56641C7.5 4.56641 5.85 4.64974 4.2 4.81641L2.5 4.98307"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-status-error"
      d="M7.0835 4.14102L7.26683 3.04935C7.40016 2.25768 7.50016 1.66602 8.9085 1.66602H11.0918C12.5002 1.66602 12.6085 2.29102 12.7335 3.05768L12.9168 4.14102"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      class="stroke-status-error"
      d="M15.7082 7.61719L15.1665 16.0089C15.0748 17.3172 14.9998 18.3339 12.6748 18.3339H7.32484C4.99984 18.3339 4.92484 17.3172 4.83317 16.0089L4.2915 7.61719"
      stroke="#989BB3"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>
