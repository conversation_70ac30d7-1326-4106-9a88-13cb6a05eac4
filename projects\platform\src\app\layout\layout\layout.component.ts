import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { UserInfoModalComponent } from '../user-info-modal/user-info-modal.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ChangePasswordModalComponent } from '../change-password-modal/change-password-modal.component';
import { UserService } from '@platform/app/core/services/user.service';
import { RevokeConsentModalComponent } from '../revoke-consent-modal/revoke-consent-modal.component';
import { RequestRemoveDataModalComponent } from '../request-remove-data-modal/request-remove-data-modal.component';
import UtilsService from '@platform/app/core/services/utils.service';
import { AuthService } from '@platform/app/core/services/auth.service';
import { SingleSignOnService } from '@platform/app/core/services/sso.service';
import { PlatformV2CountdownModalComponent } from '../platform-v2-countdown-modal/platform-v2-countdown-modal.component';
import { environment } from '@platform/environment/environment';
import { NzDrawerRef, NzDrawerService } from 'ng-zorro-antd/drawer';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';

// LAYOUT component must be declared in app.module
@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit, OnDestroy {
  menus = [
    {
      path: '/ocr-experience',
      name: 'Trải nghiệm',
      icon: 'assets/img/rpa/sidebar-icon/tab-experience.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/tab-experience-active.svg',
      subMenus: [
        {
          path: '/demo/ocr-experience',
          name: 'Demo Preview'
        },
        {
          path: '/ocr-experience/demo-dong-ho-nuoc',
          name: 'Demo đồng hồ nước',
          excludedEnvironments: ['production']
        }
      ].filter((menu) => {
        // hide Demo đồng hồ nước sub menu on production
        if (menu.excludedEnvironments)
          return !menu.excludedEnvironments.includes(environment.envName);
        else return true;
      })
    },
    {
      path: '/ocr',
      name: 'Nhận dạng ký tự',
      icon: 'assets/img/rpa/sidebar-icon/ocr.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/ocr-active.svg'
    },
    {
      path: '/key-information-extractor',
      name: 'Bóc tách thông tin',
      icon: 'assets/img/rpa/sidebar-icon/kie.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/kie-active.svg'
    },
    {
      path: '/demo/llm',
      name: 'Demo LLM',
      icon: 'assets/img/rpa/sidebar-icon/llm.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/llm-active.svg',
      excludedEnvironments: ['production']
    },
    {
      path: '/docs',
      name: 'Tích hợp',
      icon: 'assets/img/rpa/sidebar-icon/docs.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/docs-active.svg'
    },
    {
      path: '/statistic',
      name: 'Thống kê',
      icon: 'assets/img/rpa/sidebar-icon/statistic.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/statistic-active.svg'
    }
  ].filter((menu) => {
    // hide Demo LLM menu on production
    if (menu.excludedEnvironments)
      return !menu.excludedEnvironments.includes(environment.envName);
    else return true;
  });
  user;
  shouldUseMobileHeader = false;

  @ViewChild('menuDrawerTempl', { static: false })
  menuDrawerTempl?: TemplateRef<{
    $implicit: typeof this.cachedPreviewParams;
    drawerRef: NzDrawerRef<string>;
  }>;

  constructor(
    public router: Router,
    private authService: AuthService,
    private ssoService: SingleSignOnService,
    private modalService: NzModalService,
    private utilsService: UtilsService,
    private drawerService: NzDrawerService,
    private responsive: BreakpointObserver
  ) {
    this.user = this.utilsService.getCurrentUser();
    if (!this.user) this.handleLogout();
  }

  ngOnInit(): void {
    this.responsive
      .observe([Breakpoints.XSmall, Breakpoints.Small, Breakpoints.Medium])
      .subscribe((result) => {
        this.shouldUseMobileHeader = result.matches; // max-wdith <= 1279.98px => use mobileHeader
      });
  }

  showPlatformV2CountdownModal() {
    this.modalService.create({
      nzContent: PlatformV2CountdownModalComponent,
      nzFooter: null,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '0px' },
      nzStyle: { width: '700px' },
      nzClosable: false
    });
  }

  navigateRoute(route) {
    this.router.navigate([route]);
  }

  handleLogout() {
    this.authService.logout();
    this.ssoService.logout();
    this.utilsService.onLogout$.next(null);
    this.router.navigate(['/login']);
  }

  openUserInfoModal() {
    const modalRef = this.modalService.create({
      nzContent: UserInfoModalComponent,
      nzFooter: null,
      nzMaskClosable: false,
      nzClassName: 'custom-ant-modal-common-styles'
    });
    modalRef.componentInstance.account = this.user?.account;
  }

  openChangePasswordModal() {
    this.modalService.create({
      nzContent: ChangePasswordModalComponent,
      nzFooter: null,
      nzMaskClosable: false,
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  openRevokeConsentModal() {
    this.modalService.create({
      nzContent: RevokeConsentModalComponent,
      nzFooter: null,
      nzMaskClosable: false,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '0px' },
      nzClosable: false
    });
  }

  openRequestRemoveDataModal() {
    this.modalService.create({
      nzContent: RequestRemoveDataModalComponent,
      nzFooter: null,
      nzMaskClosable: false,
      nzBodyStyle: { padding: '0px' },
      nzClosable: false,
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  showMenuDrawer() {
    this.drawerService.create({
      nzMask: true,
      nzMaskClosable: true,
      nzPlacement: 'left',
      nzTitle: null,
      nzFooter: null,
      nzContent: this.menuDrawerTempl,
      nzClosable: true,
      nzBodyStyle: { padding: 0 }
    });
  }

  ngOnDestroy(): void {}
}
