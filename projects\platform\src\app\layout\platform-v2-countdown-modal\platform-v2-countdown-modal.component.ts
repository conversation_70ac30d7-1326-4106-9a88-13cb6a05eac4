import { Component, OnDestroy, OnInit } from '@angular/core';
import { PlatformV2InfoModalComponent } from '../platform-v2-info-modal/platform-v2-info-modal.component';
import { differenceInSeconds, isAfter, parseISO } from 'date-fns';
import { interval, startWith, Subject, takeUntil, tap } from 'rxjs';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-platform-v2-countdown-modal',
  templateUrl: './platform-v2-countdown-modal.component.html',
  styleUrls: ['./platform-v2-countdown-modal.component.scss']
})
export class PlatformV2CountdownModalComponent implements OnInit, OnDestroy {
  platformV2Understood = false;
  releaseDate = parseISO('2024-09-09');
  days = '00';
  hours = '00';
  mins = '00';
  seconds = '00';
  destroy$ = new Subject<void>();

  constructor(
    public modal: NzModalRef,
    private modalService: NzModalService
  ) {
    this.platformV2Understood = !!localStorage.getItem('platformV2Understood');
  }

  ngOnInit(): void {
    interval(1000)
      .pipe(
        takeUntil(this.destroy$),
        startWith(0),
        tap(() => {
          if (isAfter(new Date(), this.releaseDate))
            return this.openPlatformV2InfoModal();

          const diffInSecond = differenceInSeconds(this.releaseDate, new Date());
          this.days = Math.floor(diffInSecond / (60 * 60 * 24))
            .toString()
            .padStart(2, '0');
          this.hours = Math.floor((diffInSecond % (60 * 60 * 24)) / (60 * 60))
            .toString()
            .padStart(2, '0');
          this.mins = Math.floor((diffInSecond % (60 * 60)) / 60)
            .toString()
            .padStart(2, '0');
          this.seconds = (diffInSecond % 60).toString().padStart(2, '0');
        })
      )
      .subscribe();
  }

  changePlatformV2Understood() {
    if (this.platformV2Understood) localStorage.setItem('platformV2Understood', 'true');
    else localStorage.removeItem('platformV2Understood');
  }

  openPlatformV2InfoModal() {
    this.modal.close();
    this.modalService.create({
      nzContent: PlatformV2InfoModalComponent,
      nzFooter: null,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '0px' },
      nzStyle: { width: '700px' },
      nzClosable: false
    });
  }

  close() {
    this.modalService.closeAll();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
