<div
	class="relative flex flex-col justify-center bg-[#f3f6f6] min-h-[100vh] bg-[url('assets/img/rpa/login/bg-blur.png')] bg-cover"
	*transloco="let t; read: 'ocrExperience.wrapper'">
	<button (click)="openConfigModal()"
		class="absolute top-4 right-4 rounded-[4px] bg-[#0F67CE] text-white py-1 px-2 flex gap-1 items-center font-semibold">
		<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
			<mask id="path-1-inside-1_4426_309" fill="white">
				<path
					d="M14.2134 6.36665L12.9534 5.94665L13.5467 4.75998C13.6069 4.63577 13.627 4.49596 13.6043 4.35983C13.5816 4.2237 13.5172 4.09797 13.42 3.99998L12 2.57998C11.9015 2.48137 11.7746 2.41612 11.6371 2.3934C11.4996 2.37068 11.3584 2.39163 11.2334 2.45332L10.0467 3.04665L9.62671 1.78665C9.58235 1.65531 9.49816 1.54107 9.38584 1.45981C9.27352 1.37856 9.13866 1.33434 9.00004 1.33332H7.00004C6.86028 1.33295 6.72394 1.37653 6.61029 1.45788C6.49664 1.53923 6.41143 1.65423 6.36671 1.78665L5.94671 3.04665L4.76004 2.45332C4.63583 2.39317 4.49602 2.37305 4.35989 2.39574C4.22376 2.41843 4.09803 2.4828 4.00004 2.57998L2.58004 3.99998C2.48143 4.09849 2.41618 4.22543 2.39346 4.36295C2.37074 4.50047 2.39169 4.64166 2.45338 4.76665L3.04671 5.95332L1.78671 6.37332C1.65537 6.41767 1.54113 6.50187 1.45987 6.61419C1.37862 6.7265 1.33441 6.86136 1.33338 6.99998V8.99998C1.33302 9.13974 1.37659 9.27608 1.45794 9.38973C1.53929 9.50338 1.6543 9.58859 1.78671 9.63331L3.04671 10.0533L2.45338 11.24C2.39323 11.3642 2.37311 11.504 2.3958 11.6401C2.41849 11.7763 2.48286 11.902 2.58004 12L4.00004 13.42C4.09855 13.5186 4.22549 13.5838 4.36301 13.6066C4.50053 13.6293 4.64172 13.6083 4.76671 13.5466L5.95338 12.9533L6.37338 14.2133C6.4181 14.3457 6.50331 14.4607 6.61696 14.5421C6.73061 14.6234 6.86695 14.667 7.00671 14.6666H9.00671C9.14647 14.667 9.28281 14.6234 9.39646 14.5421C9.51011 14.4607 9.59532 14.3457 9.64004 14.2133L10.06 12.9533L11.2467 13.5466C11.3701 13.6053 11.5086 13.6246 11.6433 13.6019C11.7781 13.5793 11.9026 13.5157 12 13.42L13.42 12C13.5187 11.9015 13.5839 11.7745 13.6066 11.637C13.6293 11.4995 13.6084 11.3583 13.5467 11.2333L12.9534 10.0466L14.2134 9.62665C14.3447 9.58229 14.459 9.4981 14.5402 9.38578C14.6215 9.27346 14.6657 9.1386 14.6667 8.99998V6.99998C14.6671 6.86022 14.6235 6.72388 14.5421 6.61023C14.4608 6.49658 14.3458 6.41137 14.2134 6.36665ZM13.3334 8.51998L12.5334 8.78665C12.3494 8.84632 12.1806 8.9453 12.0387 9.07671C11.8969 9.20813 11.7853 9.36884 11.7117 9.5477C11.6381 9.72656 11.6043 9.91928 11.6127 10.1125C11.621 10.3057 11.6713 10.4948 11.76 10.6666L12.14 11.4266L11.4067 12.16L10.6667 11.76C10.4957 11.6748 10.3085 11.6274 10.1176 11.6208C9.92669 11.6143 9.73662 11.6489 9.56025 11.7222C9.38388 11.7956 9.22533 11.9059 9.09532 12.0459C8.96531 12.1858 8.86689 12.352 8.80671 12.5333L8.54004 13.3333H7.48004L7.21338 12.5333C7.1537 12.3494 7.05473 12.1806 6.92331 12.0387C6.79189 11.8968 6.63118 11.7852 6.45232 11.7116C6.27347 11.638 6.08074 11.6043 5.88753 11.6126C5.69431 11.621 5.50522 11.6713 5.33338 11.76L4.57338 12.14L3.84004 11.4066L4.24004 10.6666C4.32877 10.4948 4.37906 10.3057 4.38741 10.1125C4.39576 9.91928 4.36197 9.72656 4.2884 9.5477C4.21483 9.36884 4.10323 9.20813 3.96134 9.07671C3.81945 8.9453 3.65067 8.84632 3.46671 8.78665L2.66671 8.51998V7.47998L3.46671 7.21331C3.65067 7.15364 3.81945 7.05466 3.96134 6.92325C4.10323 6.79183 4.21483 6.63112 4.2884 6.45226C4.36197 6.2734 4.39576 6.08068 4.38741 5.88746C4.37906 5.69425 4.32877 5.50516 4.24004 5.33331L3.86004 4.59331L4.59338 3.85998L5.33338 4.23998C5.50522 4.32871 5.69431 4.37899 5.88753 4.38735C6.08074 4.3957 6.27347 4.36191 6.45232 4.28834C6.63118 4.21477 6.79189 4.10317 6.92331 3.96128C7.05473 3.81939 7.1537 3.65061 7.21338 3.46665L7.48004 2.66665H8.52004L8.78671 3.46665C8.84638 3.65061 8.94536 3.81939 9.07678 3.96128C9.20819 4.10317 9.36891 4.21477 9.54776 4.28834C9.72662 4.36191 9.91934 4.3957 10.1126 4.38735C10.3058 4.37899 10.4949 4.32871 10.6667 4.23998L11.4267 3.85998L12.16 4.59331L11.76 5.33331C11.6749 5.50428 11.6274 5.69155 11.6209 5.88244C11.6144 6.07334 11.649 6.2634 11.7223 6.43977C11.7956 6.61614 11.906 6.77469 12.0459 6.9047C12.1859 7.03471 12.3521 7.13313 12.5334 7.19331L13.3334 7.45998V8.51998ZM8.00004 5.33331C7.47263 5.33331 6.95705 5.48971 6.51852 5.78273C6.07999 6.07575 5.7382 6.49222 5.53636 6.97949C5.33453 7.46676 5.28172 8.00294 5.38461 8.52022C5.48751 9.0375 5.74148 9.51266 6.11442 9.8856C6.48736 10.2585 6.96252 10.5125 7.4798 10.6154C7.99708 10.7183 8.53326 10.6655 9.02053 10.4637C9.5078 10.2618 9.92428 9.92003 10.2173 9.4815C10.5103 9.04297 10.6667 8.5274 10.6667 7.99998C10.6667 7.29274 10.3858 6.61446 9.88566 6.11436C9.38556 5.61427 8.70729 5.33331 8.00004 5.33331ZM8.00004 9.33331C7.73633 9.33331 7.47855 9.25512 7.25928 9.10861C7.04002 8.9621 6.86912 8.75386 6.7682 8.51023C6.66729 8.26659 6.64088 7.9985 6.69233 7.73986C6.74378 7.48122 6.87076 7.24364 7.05723 7.05717C7.2437 6.8707 7.48128 6.74371 7.73992 6.69227C7.99856 6.64082 8.26665 6.66722 8.51029 6.76814C8.75392 6.86906 8.96216 7.03996 9.10867 7.25922C9.25518 7.47849 9.33337 7.73627 9.33337 7.99998C9.33337 8.3536 9.1929 8.69274 8.94285 8.94279C8.6928 9.19284 8.35366 9.33331 8.00004 9.33331Z" />
			</mask>
			<path
				d="M14.2134 6.36665L12.9534 5.94665L13.5467 4.75998C13.6069 4.63577 13.627 4.49596 13.6043 4.35983C13.5816 4.2237 13.5172 4.09797 13.42 3.99998L12 2.57998C11.9015 2.48137 11.7746 2.41612 11.6371 2.3934C11.4996 2.37068 11.3584 2.39163 11.2334 2.45332L10.0467 3.04665L9.62671 1.78665C9.58235 1.65531 9.49816 1.54107 9.38584 1.45981C9.27352 1.37856 9.13866 1.33434 9.00004 1.33332H7.00004C6.86028 1.33295 6.72394 1.37653 6.61029 1.45788C6.49664 1.53923 6.41143 1.65423 6.36671 1.78665L5.94671 3.04665L4.76004 2.45332C4.63583 2.39317 4.49602 2.37305 4.35989 2.39574C4.22376 2.41843 4.09803 2.4828 4.00004 2.57998L2.58004 3.99998C2.48143 4.09849 2.41618 4.22543 2.39346 4.36295C2.37074 4.50047 2.39169 4.64166 2.45338 4.76665L3.04671 5.95332L1.78671 6.37332C1.65537 6.41767 1.54113 6.50187 1.45987 6.61419C1.37862 6.7265 1.33441 6.86136 1.33338 6.99998V8.99998C1.33302 9.13974 1.37659 9.27608 1.45794 9.38973C1.53929 9.50338 1.6543 9.58859 1.78671 9.63331L3.04671 10.0533L2.45338 11.24C2.39323 11.3642 2.37311 11.504 2.3958 11.6401C2.41849 11.7763 2.48286 11.902 2.58004 12L4.00004 13.42C4.09855 13.5186 4.22549 13.5838 4.36301 13.6066C4.50053 13.6293 4.64172 13.6083 4.76671 13.5466L5.95338 12.9533L6.37338 14.2133C6.4181 14.3457 6.50331 14.4607 6.61696 14.5421C6.73061 14.6234 6.86695 14.667 7.00671 14.6666H9.00671C9.14647 14.667 9.28281 14.6234 9.39646 14.5421C9.51011 14.4607 9.59532 14.3457 9.64004 14.2133L10.06 12.9533L11.2467 13.5466C11.3701 13.6053 11.5086 13.6246 11.6433 13.6019C11.7781 13.5793 11.9026 13.5157 12 13.42L13.42 12C13.5187 11.9015 13.5839 11.7745 13.6066 11.637C13.6293 11.4995 13.6084 11.3583 13.5467 11.2333L12.9534 10.0466L14.2134 9.62665C14.3447 9.58229 14.459 9.4981 14.5402 9.38578C14.6215 9.27346 14.6657 9.1386 14.6667 8.99998V6.99998C14.6671 6.86022 14.6235 6.72388 14.5421 6.61023C14.4608 6.49658 14.3458 6.41137 14.2134 6.36665ZM13.3334 8.51998L12.5334 8.78665C12.3494 8.84632 12.1806 8.9453 12.0387 9.07671C11.8969 9.20813 11.7853 9.36884 11.7117 9.5477C11.6381 9.72656 11.6043 9.91928 11.6127 10.1125C11.621 10.3057 11.6713 10.4948 11.76 10.6666L12.14 11.4266L11.4067 12.16L10.6667 11.76C10.4957 11.6748 10.3085 11.6274 10.1176 11.6208C9.92669 11.6143 9.73662 11.6489 9.56025 11.7222C9.38388 11.7956 9.22533 11.9059 9.09532 12.0459C8.96531 12.1858 8.86689 12.352 8.80671 12.5333L8.54004 13.3333H7.48004L7.21338 12.5333C7.1537 12.3494 7.05473 12.1806 6.92331 12.0387C6.79189 11.8968 6.63118 11.7852 6.45232 11.7116C6.27347 11.638 6.08074 11.6043 5.88753 11.6126C5.69431 11.621 5.50522 11.6713 5.33338 11.76L4.57338 12.14L3.84004 11.4066L4.24004 10.6666C4.32877 10.4948 4.37906 10.3057 4.38741 10.1125C4.39576 9.91928 4.36197 9.72656 4.2884 9.5477C4.21483 9.36884 4.10323 9.20813 3.96134 9.07671C3.81945 8.9453 3.65067 8.84632 3.46671 8.78665L2.66671 8.51998V7.47998L3.46671 7.21331C3.65067 7.15364 3.81945 7.05466 3.96134 6.92325C4.10323 6.79183 4.21483 6.63112 4.2884 6.45226C4.36197 6.2734 4.39576 6.08068 4.38741 5.88746C4.37906 5.69425 4.32877 5.50516 4.24004 5.33331L3.86004 4.59331L4.59338 3.85998L5.33338 4.23998C5.50522 4.32871 5.69431 4.37899 5.88753 4.38735C6.08074 4.3957 6.27347 4.36191 6.45232 4.28834C6.63118 4.21477 6.79189 4.10317 6.92331 3.96128C7.05473 3.81939 7.1537 3.65061 7.21338 3.46665L7.48004 2.66665H8.52004L8.78671 3.46665C8.84638 3.65061 8.94536 3.81939 9.07678 3.96128C9.20819 4.10317 9.36891 4.21477 9.54776 4.28834C9.72662 4.36191 9.91934 4.3957 10.1126 4.38735C10.3058 4.37899 10.4949 4.32871 10.6667 4.23998L11.4267 3.85998L12.16 4.59331L11.76 5.33331C11.6749 5.50428 11.6274 5.69155 11.6209 5.88244C11.6144 6.07334 11.649 6.2634 11.7223 6.43977C11.7956 6.61614 11.906 6.77469 12.0459 6.9047C12.1859 7.03471 12.3521 7.13313 12.5334 7.19331L13.3334 7.45998V8.51998ZM8.00004 5.33331C7.47263 5.33331 6.95705 5.48971 6.51852 5.78273C6.07999 6.07575 5.7382 6.49222 5.53636 6.97949C5.33453 7.46676 5.28172 8.00294 5.38461 8.52022C5.48751 9.0375 5.74148 9.51266 6.11442 9.8856C6.48736 10.2585 6.96252 10.5125 7.4798 10.6154C7.99708 10.7183 8.53326 10.6655 9.02053 10.4637C9.5078 10.2618 9.92428 9.92003 10.2173 9.4815C10.5103 9.04297 10.6667 8.5274 10.6667 7.99998C10.6667 7.29274 10.3858 6.61446 9.88566 6.11436C9.38556 5.61427 8.70729 5.33331 8.00004 5.33331ZM8.00004 9.33331C7.73633 9.33331 7.47855 9.25512 7.25928 9.10861C7.04002 8.9621 6.86912 8.75386 6.7682 8.51023C6.66729 8.26659 6.64088 7.9985 6.69233 7.73986C6.74378 7.48122 6.87076 7.24364 7.05723 7.05717C7.2437 6.8707 7.48128 6.74371 7.73992 6.69227C7.99856 6.64082 8.26665 6.66722 8.51029 6.76814C8.75392 6.86906 8.96216 7.03996 9.10867 7.25922C9.25518 7.47849 9.33337 7.73627 9.33337 7.99998C9.33337 8.3536 9.1929 8.69274 8.94285 8.94279C8.6928 9.19284 8.35366 9.33331 8.00004 9.33331Z"
				fill="white" stroke="white" stroke-width="2" mask="url(#path-1-inside-1_4426_309)" />
		</svg>
		Cài đặt
	</button>
	<div class="flex gap-4 items-center py-4 px-8"
		[ngStyle]="{'justify-content': cacheInfoForm.controls['alignment'].value}">
		<div *ngIf="cacheInfoForm.controls['title'].value" class="font-extrabold text-2xl text-[#273266]"
			[ngStyle]="{'font-size': cacheInfoForm.controls['titleFontSize'].value === 'large' ? '32px' : cacheInfoForm.controls['titleFontSize'].value === 'small' ? '18px' : '24px' }">
			{{ cacheInfoForm.controls['title'].value }}
		</div>
		<img *ngIf="logoObjectUrl" [src]="this.sanitizer.bypassSecurityTrustResourceUrl(logoObjectUrl)" class="w-[128px]"
			[ngStyle]="{'width': cacheInfoForm.controls['logoSize'].value === 'large' ? '256px' : cacheInfoForm.controls['logoSize'].value === 'small' ? '64px' : '128px' }">
	</div>
	<router-outlet></router-outlet>
</div>
<ng-template #configModal>
	<div class="p-6">
		<div class="text-[#111127] text-center text-xl font-semibold mb-5">
			Thông tin doanh nghiệp
		</div>

		<form [formGroup]="cacheInfoForm" (ngSubmit)="saveConfigToCache()">
			<label class="flex flex-col gap-1 mb-2">
				<div class="font-semibold">Tên tổ chức</div>
				<input class="py-1 px-2 rounded-[4px] border border-[#CBCBCB]" type="text" formControlName="title"
					placeholder="Nhập tên tổ chức">
			</label>
			<div class="flex flex-col gap-1 mb-2">
				<div class="font-semibold">Cỡ chữ</div>
				<div class="flex justify-between items-center">
					<label>
						<input type="radio" formControlName="titleFontSize" name="titleFontSize" value="small">
						Nhỏ
					</label>
					<label>
						<input type="radio" formControlName="titleFontSize" name="titleFontSize" value="medium">
						Trung bình
					</label>
					<label>
						<input type="radio" formControlName="titleFontSize" name="titleFontSize" value="large">
						Lớn
					</label>
				</div>
			</div>
			<label class="flex flex-col gap-1 mb-2">
				<div class="font-semibold">Logo</div>
				<div class="flex relative items-center justify-between h-8 rounded-[4px] border border-[#AFAFAF]">
					<input class="absolute top-0 left-0 w-full h-full opacity-0 text-[0px] cursor-pointer"
						accept="image/svg+xml, image/jpeg, image/png" type="file" (change)="handleLogoInputChange($event)">
					<div class="flex-1 pl-2 text-ellipsis whitespace-nowrap overflow-hidden">{{
						logoFile?.name || cacheInfoForm.controls['logo'].value || 'Chọn logo định dạng jpeg, jpg, png, svg' }}</div>
					<div
						class="border-l border-l-[#AFAFAF] bg-[#CBCBCB] py-[5px] px-2 rounded-tr-[4px] rounded-br-[4px] text-[#333333]">
						Chọn file</div>
				</div>
			</label>
			<div class="flex flex-col gap-1 mb-2">
				<div class="font-semibold">Kích cỡ logo</div>
				<div class="flex justify-between items-center">
					<label>
						<input type="radio" formControlName="logoSize" name="logoSize" value="small">
						Nhỏ
					</label>
					<label>
						<input type="radio" formControlName="logoSize" name="logoSize" value="medium">
						Trung bình
					</label>
					<label>
						<input type="radio" formControlName="logoSize" name="logoSize" value="large">
						Lớn
					</label>
				</div>
			</div>
			<div class="flex flex-col gap-1 mb-2">
				<div class="font-semibold">Vị trí thông tin</div>
				<div class="flex justify-between items-center">
					<label>
						<input type="radio" formControlName="alignment" name="alignment" value="left">
						Trái
					</label>
					<label>
						<input type="radio" formControlName="alignment" name="alignment" value="center">
						Giữa
					</label>
					<!-- <label>
						<input  type="radio" formControlName="alignment" name="alignment"
							value="right">
						Phải
					</label> -->
				</div>
			</div>
			<div class="flex items-center gap-6 justify-center mt-5">
				<button class="h-9 rounded-lg bg-[#0F67CE] text-white py-1 px-2 flex gap-1 items-center font-medium"
					type="submit">
					<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M5.00004 3.33333H13.3334L16.6667 6.66666V15C16.6667 15.442 16.4911 15.8659 16.1786 16.1785C15.866 16.4911 15.4421 16.6667 15 16.6667H5.00004C4.55801 16.6667 4.13409 16.4911 3.82153 16.1785C3.50897 15.8659 3.33337 15.442 3.33337 15V5C3.33337 4.55797 3.50897 4.13404 3.82153 3.82148C4.13409 3.50892 4.55801 3.33333 5.00004 3.33333"
							stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M10 13.3333C10.9205 13.3333 11.6667 12.5871 11.6667 11.6667C11.6667 10.7462 10.9205 10 10 10C9.07957 10 8.33337 10.7462 8.33337 11.6667C8.33337 12.5871 9.07957 13.3333 10 13.3333Z"
							stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
						<path d="M11.6666 3.33333V6.66666H6.66663V3.33333" stroke="white" stroke-width="1.5" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
					Lưu
				</button>
				<button type="button"
					class="h-9 border border-[#2140D2] rounded-lg bg-white text-[#2140D2] p-[4px_8px] flex gap-1 items-center font-medium"
					(click)="clearConfigAndResetPage()">
					<svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M4.52255 1.91075C4.36627 1.75447 4.15431 1.66667 3.93329 1.66667C3.71228 1.66667 3.50032 1.75447 3.34404 1.91075C3.18776 2.06703 3.09996 2.27899 3.09996 2.50001V6.25001C3.09996 6.47102 3.18776 6.68298 3.34404 6.83926C3.50032 6.99554 3.71228 7.08334 3.93329 7.08334H7.68329C7.90431 7.08334 8.11627 6.99554 8.27255 6.83926C8.42883 6.68298 8.51663 6.47102 8.51663 6.25001C8.51663 6.02899 8.42883 5.81703 8.27255 5.66075C8.11627 5.50447 7.90431 5.41667 7.68329 5.41667H6.40316C6.27934 5.52673 6.15969 5.64203 6.04455 5.76235L5.68329 5.41667V4.91667H6.21728L4.52255 1.91075ZM4.52255 1.91075L4.16899 2.2643C4.10648 2.20179 4.0217 2.16667 3.93329 2.16667C3.84489 2.16667 3.7601 2.20179 3.69759 2.2643C3.63508 2.32682 3.59996 2.4116 3.59996 2.50001V6.25001C3.59996 6.33841 3.63508 6.4232 3.69759 6.48571C3.7601 6.54822 3.84489 6.58334 3.93329 6.58334H7.68329C7.7717 6.58334 7.85648 6.54822 7.91899 6.48571C7.98151 6.42319 8.01663 6.33841 8.01663 6.25001C8.01663 6.1616 7.98151 6.07682 7.91899 6.0143C7.85648 5.95179 7.7717 5.91667 7.68329 5.91667H5.68329H4.51281L5.32204 5.07099C6.30966 4.03887 7.5832 3.32546 8.97919 3.02232C10.3752 2.71917 11.8299 2.84014 13.1566 3.36968C14.4834 3.89922 15.6216 4.81318 16.4252 5.99424C17.2288 7.1753 17.6611 8.56957 17.6666 9.99807L17.6666 10C17.6666 10.0884 17.7017 10.1732 17.7643 10.2357C17.8268 10.2982 17.9116 10.3333 18 10.3333C18.0884 10.3333 18.1732 10.2982 18.2357 10.2357C18.2982 10.1732 18.3333 10.0884 18.3333 10C18.3333 8.97132 18.1307 7.9527 17.737 7.00232C17.3434 6.05193 16.7664 5.18839 16.039 4.461C15.3116 3.73361 14.448 3.15661 13.4976 2.76295C12.5475 2.36938 11.5291 2.16677 10.5007 2.16667C8.49277 2.17258 6.56378 2.94932 5.11205 4.3365L4.26663 5.14434V3.97501V2.50001C4.26663 2.4116 4.23151 2.32681 4.169 2.2643L4.52255 1.91075ZM15.6779 14.9374L16.4871 14.0917H15.3166H13.3166C13.2282 14.0917 13.1434 14.0566 13.0809 13.994C13.0184 13.9315 12.9833 13.8467 12.9833 13.7583C12.9833 13.6699 13.0184 13.5851 13.0809 13.5226C13.1434 13.4601 13.2282 13.425 13.3166 13.425H17.0824C17.1664 13.429 17.2458 13.4645 17.3048 13.5247C17.3646 13.5858 17.3987 13.6675 17.4 13.7529V17.5C17.4 17.5884 17.3648 17.6732 17.3023 17.7357C17.2398 17.7982 17.155 17.8333 17.0666 17.8333C16.9782 17.8333 16.8934 17.7982 16.8309 17.7357C16.7684 17.6732 16.7333 17.5884 16.7333 17.5V16.025V14.862L15.8893 15.6622C14.7792 16.7146 13.3852 17.419 11.8794 17.6884C10.3736 17.9577 8.82192 17.7804 7.41577 17.1781C6.00963 16.5758 4.8106 15.575 3.96665 14.2992C3.12285 13.0236 2.67093 11.5289 2.66663 9.99947C2.66677 9.91126 2.70187 9.82669 2.76426 9.7643C2.82677 9.70179 2.91155 9.66667 2.99996 9.66667C3.08836 9.66667 3.17315 9.70179 3.23566 9.7643C3.29817 9.82682 3.33329 9.9116 3.33329 10L3.33329 10.0013C3.33704 11.4306 3.76805 12.8261 4.57097 14.0085C5.37389 15.191 6.51202 16.1063 7.83914 16.637C9.16625 17.1676 10.6217 17.2893 12.0185 16.9865C13.4154 16.6836 14.6897 15.97 15.6779 14.9374Z"
							fill="black" stroke="#2140D2" />
					</svg>
					Làm mới
				</button>
			</div>
		</form>
	</div>
</ng-template>