`Domain name:` [https://api.idg.vnpt.vn](https://api.idg.vnpt.vn)

# API bóc tách Văn bản hành chính

**- Chức năng**: <PERSON><PERSON><PERSON> tách và trích xuất thông tin giấy tờ là Văn bản hành chính được trả về từ API `/file-service/addFile`

**- Chi tiết API**:

**Endpoint:** `<domain-name>/rpa-service/aidigdoc/v1/ocr/van-ban-hanh-chinh`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>G<PERSON><PERSON> trị</strong></th>
<th><strong><PERSON><PERSON><PERSON> buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td></td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>Boolean</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

Đối với Văn bản hành chính, các trường thông tin bóc tách bao gồm các trường thông tin bên dưới và được thể hiện các vị trí bóc tách một số trường thông tin trong ảnh đính kèm.

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>m_origin</td>
<td>Tên cơ quan ban hành văn bản</td>
<td>ĐẢNG ỦY KHỐI DOANH NGHIỆP TRUNG ƯƠNG</td>
</tr>
<tr>
<td>2</td>
<td>m_number</td>
<td>Số và kí hiệu văn bản</td>
<td>Số 16 - QĐi/ĐUK</td>
</tr>
<tr>
<td>3</td>
<td>m_date</td>
<td>Ngày ban hành văn bản</td>
<td>04</td>
</tr>
<tr>
<td>4</td>
<td>m_month</td>
<td>Tháng ban hành văn bản</td>
<td>5</td>
</tr>
<tr>
<td>5</td>
<td>m_year</td>
<td>Năm ban hành văn bản</td>
<td>2018</td>
</tr>
<tr>
<td>6</td>
<td>category</td>
<td>Loại văn bản</td>
<td>QUY ĐỊNH</td>
</tr>
<tr>
<td>7</td>
<td>left_m_cited</td>
<td>Trích yếu nội dung văn bản</td>
<td>Về chế độ báo cáo với Đảng ủy Khối Doanh nghiệp Trung ương</td>
</tr>
</tbody></table>

![img-1.png](img-1.png)

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
  "details": false
}
```

Response success

```json
{
  "file_hash": "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
  "message": "IDG-00000000",
  "object": {
    "category": "QUY ĐỊNH",
    "left_m_cited": "Về chế độ báo cáo với Đảng ủy Khối Doanh nghiệp Trung ương",
    "m_date": "04",
    "m_month": "5",
    "m_number": "Số 16 - QĐi/ĐUK",
    "m_origin": "ĐẢNG ỦY KHỐI DOANH NGHIỆP TRUNG ƯƠNG",
    "m_year": "2018",
    "num_of_pages": 1,
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230418/IDG01_d05ae3e1-dd90-11ed-a112-9505f188c05b",
  "details": true
}
```

Response success

```json
{
  "file_hash": "idg20250317-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_5c34d432-02d9-11f0-a7f8-d9fc76a2c333",
  "message": "IDG-00000000",
  "object": {
    "category": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.13666666666666666, 0.0871559633027523, 0.4116666666666667, 0.11811926605504587
        ]
      },
      "confidence_score": 0.930417001247406,
      "font_styles": ["normal"],
      "text": "QUYẾT ĐỊNH",
      "type": "Field",
      "warnings": []
    },
    "left_m_cited": {
      "bbox_conf_score": 0,
      "bboxes": {},
      "confidence_score": 0.0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": []
    },
    "m_date": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.5366666666666666, 0.06422018348623854, 0.9383333333333334, 0.09403669724770643
        ]
      },
      "confidence_score": 0.9641017317771912,
      "font_styles": ["normal"],
      "text": "04",
      "type": "Field",
      "warnings": []
    },
    "m_month": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.5366666666666666, 0.06422018348623854, 0.9383333333333334, 0.09403669724770643
        ]
      },
      "confidence_score": 0.9641017317771912,
      "font_styles": ["normal"],
      "text": "15",
      "type": "Field",
      "warnings": []
    },
    "m_number": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.13666666666666666, 0.0871559633027523, 0.4116666666666667, 0.11811926605504587
        ]
      },
      "confidence_score": 0.930417001247406,
      "font_styles": ["normal"],
      "text": "16-/QĐ/ĐUK",
      "type": "Field",
      "warnings": []
    },
    "m_origin": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.08666666666666667, 0.02522935779816514, 0.48833333333333334,
          0.06536697247706422
        ]
      },
      "confidence_score": 0.9907231032848358,
      "font_styles": ["normal"],
      "text": "ĐẢNG UỶ KHỐI DOANH NGHIỆP TRUNG ƯƠNG",
      "type": "Field",
      "warnings": []
    },
    "m_year": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.5366666666666666, 0.06422018348623854, 0.9383333333333334, 0.09403669724770643
        ]
      },
      "confidence_score": 0.9641017317771912,
      "font_styles": ["normal"],
      "text": "2018",
      "type": "Field",
      "warnings": []
    },
    "num_of_pages": 1,
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>Bảng 4</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

# API bóc tách Văn bản hành chính (Bản thêm trường thông tin)

**- Chức năng**: Bóc tách và trích xuất thông tin giấy tờ là Văn bản hành chính (Bản thêm trường thông tin) được trả về từ API `/file-service/addFile`. Các thông tin được bổ sung bao gồm: Căn cứ văn bản, Người ký văn bản, Chức vụ người ký, Nơi nhận văn bản, Nhóm văn bản, Thông tin về số công báo (đối với văn bản dạng Công báo).


**- Chi tiết API**:

**Endpoint:** `<domain-name>/rpa-service/aidigdoc/v2/ocr/van-ban-hanh-chinh`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td></td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>Boolean</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

Đối với Văn bản hành chính (Bản thêm trường thông tin), các trường thông tin bóc tách bao gồm các trường thông tin bên dưới và được thể hiện các vị trí bóc tách một số trường thông tin trong ảnh đính kèm.

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 25%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
<th><strong>Ví dụ kết quả trả về với hình ảnh ví dụ</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>m_origin</td>
<td>Tên cơ quan ban hành văn bản</td>
<td>ỦY BAN NHÂN DÂN PHƯỜNG TÂN HƯNG THUẬN</td>
</tr>
<tr>
<td>2</td>
<td>m_number</td>
<td>Số và ký hiệu văn bản</td>
<td>05/TTr-UBND</td>
</tr>
<tr>
<td>3</td>
<td>m_date</td>
<td>Ngày ban hành văn bản</td>
<td>08</td>
</tr>
<tr>
<td>4</td>
<td>m_month</td>
<td>Tháng ban hành văn bản</td>
<td>11</td>
</tr>
<tr>
<td>5</td>
<td>m_year</td>
<td>Năm ban hành văn bản</td>
<td>2022</td>
</tr>
<tr>
<td>6</td>
<td>category</td>
<td>Loại văn bản</td>
<td>TỜ TRÌNH</td>
</tr>
<tr>
<td>7</td>
<td>left_m_cited</td>
<td>Trích yếu nội dung văn bản</td>
<td>Phê duyệt kế hoạch lựa chọn nhà thầu Dịch vụ sự nghiệp công: Duy tu mặt đường hẻm 27, khu phố 2, phường Tân Hưng Thuận</td>
</tr>
<tr>
<td>8</td>
<td>base</td>
<td>Căn cứ văn bản</td>
<td>Luật tổ chức chính quyền địa phương ngày 19/6/2015.<br>
Luật sửa đổi, bổ sung một số điều của Luật Tổ chức Chính phủ và Luật Tổ chức chính quyền địa phương ngày 22/11/2019.<br>
Luật Đấu thầu ngày 26/11/2013.<br>
Nghị định 63/2014/NĐ-CP ngày 26/6/2014 của Chính phủ.<br>
Nghị định số 32/2019/NĐ-CP ngày 10/4/2019 của Chính phủ.<br>
Quyết định số 343/QĐ-UBND ngày 11/7/2021.<br>
Quyết định số 6247/QĐ-UBND-TC ngày 28/12/2021 của Chủ tịch ban nhân dân Quận 12.<br>
Quyết định số 10734/QĐ-UBND-ĐT ngày 28/10/2022 của Chủ tịch ban nhân dân quận 12.
</td>
</tr>
<tr>
<td>9</td>
<td>m_sign</td>
<td>Người ký văn bản</td>
<td>Nguyễn Ngọc Ấn</td>
</tr>
<tr>
<td>10</td>
<td>recipient</td>
<td>Nơi nhận văn bản</td>
<td>Như trên; <br>
Lưu: VT, ĐCXD(1b).Q
</td>
</tr>
<tr>
<td>11</td>
<td>group</td>
<td>Nhóm văn bản 
(Đối với văn bản dạng Công báo => Thể hiện khu vực nhóm văn bản)
</td>
<td>Chỉ bóc với văn bản hành chính dạng công báo
</td>
</tr>
<tr>
<td>12</td>
<td>official_gazette</td>
<td>Thông tin về số công báo
</td>
<td>Chỉ bóc với văn bản hành chính dạng công báo
</td>
</tr>
<tr>
<td>13</td>
<td>signer_position</td>
<td>Chức vụ người ký 
</td>
<td>PHÓ CHỦ TỊCH
</td>
</tr>
</tbody></table>

![img-2.jpg](img-2.jpg)

![img-3.jpg](img-3.jpg)

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "dg20241218-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_923ad070-bcf2-11ef-a64a-6d251c2710f4",
  "details": false
}
```

Response success

```json
{
   "file_hash": "idg20241218-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_923ad070-bcf2-11ef-a64a-6d251c2710f4",
   "message": "IDG-00000000",
   "object": {
       "base": [
           "Luật tổ chức chính quyền địa phương ngày 19/6/2015",
           "Luật sửa đổi, bổ sung một số điều của Luật Tổ chức Chính phủ và Luật Tổ chức chính quyền địa phương ngày 22/11/2019",
           "Luật Đấu thầu ngày 26/11/2013",
           "Nghị định 63/2014/NĐ-CP ngày 26/6/2014 của Chính phủ",
           "Nghị định số 32/2019/NĐ-CP ngày 10/4/2019 của Chính phủ",
           "Quyết định số 343/QĐ-UBND ngày 11/7/2021",
           "Quyết định số 6247/QĐ-UBND-TC ngày 28/12/2021 của Chủ tịch ban nhân dân Quận 12",
           "Quyết định số 10734/QĐ-UBND-ĐT ngày 28/10/2022 của Chủ tịch ban nhân dân quận 12"
       ],
       "category": "TỜ TRÌNH",
       "group": "",
       "left_m_cited": "Phê duyệt kế hoạch lựa chọn nhà thầu Dịch vụ sự nghiệp công: Duy tu mặt đường hẻm 27, khu phố 2, phường Tân Hưng Thuận",
       "m_date": "08",
       "m_month": "11",
       "m_number": "05/TTr-UBND",
       "m_origin": "ỦY BAN NHÂN DÂN PHƯỜNG TÂN HƯNG THUẬN",
       "m_sign": "Nguyễn Ngọc Ấn",
       "m_year": "2022",
       "signer_position":"PHÓ CHỦ TỊCH"
       "num_of_pages": 4,
       "official_gazette": "",
       "recipient": [
           "Như trên",
           "Lưu: VT, ĐCXD(1b).Q"
       ],
       "warning_messages": [],
       "warnings": []
   },
   "server_version": "1.3.17",
   "status": "OK",
   "statusCode": 200
}

```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20241218-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_923ad070-bcf2-11ef-a64a-6d251c2710f4",
  "details": true
}
```

Response success

```json
{
  "file_hash": "idg20250317-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_959d8921-02de-11f0-b56d-bfdc00a6507b",
  "message": "IDG-00000000",
  "object": {
    "base": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9989798527497512,
          "font_styles": ["normal"],
          "name": "0",
          "text": "Luật tổ chức chính quyền địa phương ngày 19/6/2015",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9991150272303614,
          "font_styles": ["normal"],
          "name": "1",
          "text": "Luật sửa đổi, bổ sung một số điều của Luật Tổ chức Chính phủ và Luật Tổ chức chính quyền địa phương ngày 22/11/2019",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9986765384674072,
          "font_styles": ["normal"],
          "name": "2",
          "text": "Luật Đấu thầu ngày 26/11/2013",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9987915919886695,
          "font_styles": ["normal"],
          "name": "3",
          "text": "Nghị định 63/2014/NĐ-CP ngày 26/6/2014 của Chính phủ",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9983246044108742,
          "font_styles": ["normal"],
          "name": "4",
          "text": "Nghị định số 32/2019/NĐ-CP ngày 10/4/2019 của Chính phủ",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9989935159683228,
          "font_styles": ["normal"],
          "name": "5",
          "text": "Quyết định số 343/QĐ-UBND ngày 11/7/2021",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9991044677220858,
          "font_styles": ["normal"],
          "name": "6",
          "text": "Quyết định số 6247/QĐ-UBND-TC ngày 28/12/2021 của Chủ tịch ban nhân dân Quận 12",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {},
          "confidence_score": 0.9990527079655573,
          "font_styles": ["normal"],
          "name": "7",
          "text": "Quyết định số 10734/QĐ-UBND-ĐT ngày 28/10/2022 của Chủ tịch ban nhân dân quận 12",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "category": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.4695599758890898, 0.20473118279569893, 0.5979505726341169, 0.22193548387096773
        ]
      },
      "confidence_score": 0.9941835403442383,
      "font_styles": ["normal"],
      "text": "TỜ TRÌNH",
      "type": "Field",
      "warnings": []
    },
    "group": {
      "bbox_conf_score": 0.0,
      "bboxes": {},
      "confidence_score": 1.0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": []
    },
    "left_m_cited": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.2224231464737794, 0.23483870967741935, 0.8444846292947559, 0.28817204301075267
        ]
      },
      "confidence_score": 0.9868709643681844,
      "font_styles": ["normal"],
      "text": "Phê duyệt kế hoạch lựa chọn nhà thầu Dịch vụ sự nghiệp công: Duy tu mặt đường hẻm 27, khu phố 2, phường Tân Hưng Thuận",
      "type": "Field",
      "warnings": []
    },
    "m_date": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.515370705244123, 0.14709677419354839, 0.9385171790235082, 0.16473118279569893
        ]
      },
      "confidence_score": 0.9865571856498718,
      "font_styles": ["normal"],
      "text": "08",
      "type": "Field",
      "warnings": []
    },
    "m_month": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.515370705244123, 0.14709677419354839, 0.9385171790235082, 0.16473118279569893
        ]
      },
      "confidence_score": 0.9865571856498718,
      "font_styles": ["normal"],
      "text": "11",
      "type": "Field",
      "warnings": []
    },
    "m_number": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [0.20795660036166366, 0.1410752688172043, 0.3899939722724533, 0.16]
      },
      "confidence_score": 0.9400916695594788,
      "font_styles": ["normal"],
      "text": "05/TTr-UBND",
      "type": "Field",
      "warnings": []
    },
    "m_origin": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.16455696202531644, 0.08344086021505376, 0.46172393007836043,
          0.11526881720430107
        ]
      },
      "confidence_score": 0.9742092192173004,
      "font_styles": ["normal"],
      "text": "ỦY BAN NHÂN DÂN PHƯỜNG TÂN HƯNG THUẬN",
      "type": "Field",
      "warnings": []
    },
    "m_sign": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "4": [
          0.6618357487922706, 0.23166023166023167, 0.8285024154589372, 0.24924924924924924
        ]
      },
      "confidence_score": 0.9633073806762695,
      "font_styles": ["normal"],
      "text": "Nguyễn Ngọc Ấn",
      "type": "Field",
      "warnings": []
    },
    "m_year": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.515370705244123, 0.14709677419354839, 0.9385171790235082, 0.16473118279569893
        ]
      },
      "confidence_score": 0.9865571856498718,
      "font_styles": ["normal"],
      "text": "2022",
      "type": "Field",
      "warnings": []
    },
    "signer_position": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.7153707052441234, 0.14709677419354867, 0.938517179023505, 0.16473118279569888
        ]
      },
      "confidence_score": 0.986557185649898,
      "font_styles": ["normal"],
      "text": "PHÓ CHỦ TỊCH",
      "type": "Field",
      "warnings": []
    },
    "num_of_pages": 4,
    "official_gazette": {
      "bbox_conf_score": 0.0,
      "bboxes": {},
      "confidence_score": 1.0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": []
    },
    "recipient": {
      "bbox_conf_score": 1.0,
      "bboxes": {
        "4": [
          0.1358695652173913, 0.12226512226512226, 0.31219806763285024,
          0.16859716859716858
        ]
      },
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "4": [
              0.13647342995169082, 0.13942513942513943, 0.21256038647342995,
              0.1522951522951523
            ]
          },
          "confidence_score": 0.9998878836631775,
          "font_styles": ["normal"],
          "name": "1",
          "text": "Như trên",
          "type": "Field",
          "warnings": []
        },
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "4": [
              0.13828502415458938, 0.1583011583011583, 0.31219806763285024,
              0.16859716859716858
            ]
          },
          "confidence_score": 0.998967707157135,
          "font_styles": ["normal"],
          "name": "2",
          "text": "Lưu: VT, ĐCXD(1b).Q",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 0.999537467956543,
      "type": "List",
      "warnings": []
    },
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>[Bảng mô tả kết quả trả về của các trường thông tin](#bang-mo-ta-ket-qua)</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

# API bóc tách Giấy đăng ký kinh doanh

**- Chức năng**: Bóc tách và trích xuất thông tin giấy Đăng ký kinh doanh được trả về từ API `/file-service/addFile`

**- Chi tiết API**:

**Endpoint:** `<domain-name>/rpa-service/aidigdoc/v1/ocr/dang-ky-kinh-doanh`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td></td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>Boolean</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
<tr>
<td></td>
<td>original_images = [hash-page1, hash-page2, ...]</td>
<td>Array</td>
<td>Đường link của file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>aligned_file_hash</td>
<td>Array</td>
<td>Thông số hash file của file sau khi được xoay đối với trường hợp ảnh bị nghiêng</td>
</tr>
</tbody></table>

Đối với Giấy đăng ký kinh doanh, các trường thông tin bóc tách bao gồm các trường thông tin bên dưới và được thể hiện các vị trí bóc tách một số trường thông tin trong ảnh đính kèm.

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 25%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
<th><strong>Ví dụ kết quả trả về với hình ảnh ví dụ</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>NOI_CAP_GIAY_PHEP</td>
<td>Nơi cấp giấy phép</td>
<td>SỞ KẾ HOẠCH VÀ ĐẦU TƯ TỈNH THANH HOÁ</td>
</tr>
<tr>
<td>2</td>
<td>MA_SO_DOANH_NGHIEP</td>
<td>Mã số doanh nghiệp</td>
<td>2800796</td>
</tr>
<tr>
<td>3</td>
<td>DANG_KY_LAN_DAU</td>
<td>Đăng ký lần đầu</td>
<td>ngày 13 tháng 04 năm 2004</td>
</tr>
<tr>
<td>4</td>
<td>DANG_KY_THAY_DOI</td>
<td>Đăng ký thay đổi lần thứ</td>
<td>lần thứ: 7, ngày 29 tháng 03 năm 2018</td>
</tr>
<tr>
<td>5</td>
<td>TEN_CONG_TY_TIENG_VIET</td>
<td>Tên công ty bằng tiếng Việt</td>
<td>CÔNG TY TNHH THÁI HƯNG</td>
</tr>
<tr>
<td>6</td>
<td>TEN_CONG_TY_NUOC_NGOAI</td>
<td>Tên công ty (chi nhánh) viết bằng tiếng nước ngoài </td>
<td></td>
</tr>
<tr>
<td>7</td>
<td>TEN_CONG_TY_VIET_TAT</td>
<td>Tên công ty viết tắt</td>
<td></td>
</tr>
<tr>
<td>8</td>
<td>DIA_CHI_TRU_SO_CHINH</td>
<td>Địa chỉ trụ sở chính</td>
<td>Địa chỉ trụ sở chính
</td>
</tr>
<tr>
<td>9</td>
<td>SO_DIEN_THOAI</td>
<td>Số điện thoại</td>
<td>0373.820.000</td>
</tr>
<tr>
<td>10</td>
<td>EMAIL</td>
<td>Email</td>
<td></td>
</tr>
<tr>
<td>11</td>
<td>WEBSITE</td>
<td>Website</td>
<td></td>
</tr>
<tr>
<td>12</td>
<td>VON_DIEU_LE</td>
<td>Vốn điều lệ
</td>
<td>9.000.000.000 đồng
</td>
</tr>
<tr>
<td>13</td>
<td>VON_DIEU_LE_BANG_CHU</td>
<td>Vốn điều lệ bằng chữ
</td>
<td>Chín tỷ đồng
</td>
</tr>
<tr>
<td>14</td>
<td>NGUOI_DUNG_DAU_HO_VA_TEN</td>
<td>Họ và tên của người đứng đầu
</td><td>
</td>
</tr>
<tr>
<td>15</td>
<td>NGUOI_DUNG_DAU_GIOI_TINH</td>
<td>Giới tính của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>16</td>
<td>NGUOI_DUNG_DAU_NGAY_SINH</td>
<td>Ngày sinh của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>17</td>
<td>NGUOI_DUNG_DAU_QUOC_TICH</td>
<td>Quốc tịch của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>18</td>
<td>NGUOI_DUNG_DAU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN</td>
<td>Loại giấy tờ chứng thực cá nhân của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>19</td>
<td>NGUOI_DUNG_DAU_SO_GIAY_CHUNG_THUC_CA_NHAN</td>
<td>Số giấy chứng thực cá nhân của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>20</td>
<td>NGUOI_DUNG_DAU_NGAY_CAP</td>
<td>Ngày cấp giấy tờ chứng thực cá nhân của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>21</td>
<td>NGUOI_DUNG_DAU_NOI_CAP</td>
<td>Nơi cấp
</td>
<td>
</td>
</tr>
<tr>
<td>22</td>
<td>NGUOI_DUNG_DAU_HO_KHAU_THUONG_TRU</td>
<td>Hộ khẩu thường trú của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>23</td>
<td>NGUOI_DUNG_DAU_CHO_O_HIEN_TAI</td>
<td>Chỗ ở hiện tại của người đứng đầu
</td>
<td>
</td>
</tr>
<tr>
<td>24</td>
<td>CHU_SO_HUU_HO_VA_TEN</td>
<td>Họ và tên chủ sở hữu
</td>
<td>LÊ THÁI HƯNG
</td>
</tr>
<tr>
<td>25</td>
<td>CHU_SO_HUU_GIOI_TINH</td>
<td>Giới tính chủ sở hữu
</td>
<td>Nam
</td>
</tr>
<tr>
<td>26</td>
<td>CHU_SO_HUU_NGAY_SINH</td>
<td>Ngày sinh chủ sở hữu
</td>
<td>18/02/1998
</td>
</tr>
<tr>
<td>27</td>
<td>CHU_SO_HUU_QUOC_TICH</td>
<td>Quốc tịch của chủ sở hữu
</td>
<td>Việt Nam
</td>
</tr>
<tr>
<td>28</td>
<td>CHU_SO_HUU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN</td>
<td>Loại giấy tờ chứng thực cá nhân của chủ sở hữu
</td>
<td>Chứng minh nhân dân
</td>
</tr>
<tr>
<td>29</td>
<td>CHU_SO_HUU_SO_GIAY_CHUNG_THUC_CA_NHAN</td>
<td>Số giấy tờ chứng thực cá nhân của chủ sở hữu
</td>
<td>171522466
</td>
</tr>
<tr>
<td>30</td>
<td>CHU_SO_HUU_NGAY_CAP</td>
<td>Ngày cấp số giấy chứng thực cá nhân của chủ sở hữu
</td>
<td>19/01/2015
</td>
</tr>
<tr>
<td>31</td>
<td>CHU_SO_HUU_NOI_CAP</td>
<td>Nơi cấp giấy chứng thực cá nhân của chủ sở hữu
</td>
<td>Công an tỉnh Thanh Hoá
</td>
</tr>
<tr>
<td>32</td>
<td>CHU_SO_HUU_HO_KHAU_THUONG_TRU</td>
<td>Hộ khẩu thường trú chủ sở hữu
</td>
<td>Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam
</td>
</tr>
<tr>
<td>33</td>
<td>CHU_SO_HUU_CHO_O_HIEN_TAI</td>
<td>Chỗ ở hiện tại chủ sở hữu
</td>
<td>Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam
</td>
</tr>
<tr>
<td>34</td>
<td>TEN_TO_CHUC</td>
<td>Tên tổ chức (Đối với trường hợp chủ sở hữu là Tổ chức)
</td>
<td>
</td>
</tr>
<tr>
<td>35</td>
<td>MSDN/QDTL</td>
<td>Mã số doanh nghiệp/Quyết định thành lập
</td>
<td>
</td>
</tr>
<tr>
<td>36</td>
<td>TC_NGAY_CAP</td>
<td>Ngày cấp (Đối với trường hợp chủ sở hữu là Tổ chức)
</td>
<td>
</td>
</tr>
<tr>
<td>37</td>
<td>TC_NOI_CAP</td>
<td>Nơi cấp (Đối với trường hợp chủ sở hữu là Tổ chức)
</td>
<td>
</td>
</tr>
<tr>
<td>38</td>
<td>TC_DIA_CHI_TRU_SO_CHINH</td>
<td>Địa chỉ trụ sở chính (Đối với trường hợp chủ sở hữu là Tổ chức)
</td>
<td></td>
</tr>
<tr>
<td>39</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_VA_TEN</td>
<td>Họ và tên của người đại diện pháp luật
</td>
<td>LÊ THÁI HƯNG</td>
</tr>
<tr>
<td>40</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHUC_DANH</td>
<td>Chức danh của người đại diện pháp luật
</td>
<td>Giám đốc</td>
</tr>
<tr>
<td>41</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_GIOI_TINH</td>
<td>Giới tính của người đại diện pháp luật
</td>
<td>Nam</td>
</tr>
<tr>
<td>42</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_SINH</td>
<td>Ngày sinh của người đại diện pháp luật
</td>
<td>18/02/1998</td>
</tr>
<tr>
<td>43</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_QUOC_TICH</td>
<td>Quốc tịch của người đại diện pháp luật
</td>
<td>Việt Nam</td>
</tr>
<tr>
<td>44</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN</td>
<td>Loại giấy tờ chứng thực cá nhân của người đại diện pháp luật
</td>
<td>Chứng minh nhân dân</td>
</tr>
<tr>
<td>45</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_SO_GIAY_CHUNG_THUC_CA_NHAN</td>
<td>Số giấy tờ chứng thực cá nhân của người đại diện pháp luật
</td>
<td>171522466</td>
</tr>
<tr>
<td>46</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_CAP</td>
<td>Ngày cấp giấy tờ chứng thực cá nhân của người đại diện pháp luật
</td>
<td>19/01/2015</td>
</tr>
<tr>
<td>47</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NOI_CAP</td>
<td>Nơi cấp giấy tờ chứng thực cá nhân của người đại diện pháp luật
</td>
<td>Công an tỉnh Thanh Hoá</td>
</tr>
<tr>
<td>48</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHO_O_HIEN_TAI</td>
<td>Chỗ ở hiện tại của người đại diện pháp luật
</td>
<td>Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam</td>
</tr>
<tr>
<td>49</td>
<td>THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_KHAU_THUONG_TRU</td>
<td>Hộ khẩu thường trú của người đại diện pháp luật
</td>
<td>Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam</td>
</tr>
<tr>
<td>50</td>
<td>DANH_SACH_THANH_VIEN_GOP_VON</td>
<td>Danh sách thành viên góp vốn
</td>
<td></td>
</tr>
<tr>
<td>50.1</td>
<td>TEN_THANH_VIEN</td>
<td>Tên thành viên
</td>
<td></td>
</tr>
<tr>
<td>50.2</td>
<td>NOI_DANG_KY_HKTT</td>
<td>Nơi đăng ký hộ khẩu thường trú
</td>
<td></td>
</tr>
<tr>
<td>50.3</td>
<td>GIA_TRI_VON_GOP</td>
<td>Giá trị vốn góp
</td>
<td></td>
</tr>
<tr>
<td>50.4</td>
<td>TY_LE</td>
<td>Tỷ lệ góp vốn
</td>
<td></td>
</tr>
<tr>
<td>50.5</td>
<td>SO_CMND</td>
<td>Số CMND
</td>
<td></td>
</tr>
</tbody></table>

![img-4.png](img-4.png)

![img-5.png](img-5.png)

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "dg20230418/IDG01_8ae0627a-ddba-11ed-a112-ad22e146ae89i",
  "details": false
}
```

Response success

```json
{
  "file_hash": "idg20230418/IDG01_8ae0627a-ddba-11ed-a112-ad22e146ae89",
  "message": "IDG-00000000",
  "object": {
    "CHU_SO_HUU_CHO_O_HIEN_TAI": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam",
    "CHU_SO_HUU_GIOI_TINH": "Nam",
    "CHU_SO_HUU_HO_KHAU_THUONG_TRU": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam",
    "CHU_SO_HUU_HO_VA_TEN": "LÊ THÁI HƯNG",
    "CHU_SO_HUU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": "Chứng minh nhân dân",
    "CHU_SO_HUU_NGAY_CAP": "19/01/2015",
    "CHU_SO_HUU_NGAY_SINH": "18/02/1998",
    "CHU_SO_HUU_NOI_CAP": "Công an tỉnh Thanh Hoá",
    "CHU_SO_HUU_QUOC_TICH": "Việt Nam",
    "CHU_SO_HUU_SO_GIAY_CHUNG_THUC_CA_NHAN": "171522466",
    "DANG_KY_LAN_DAU": "ngày 13 tháng 04 năm 2004",
    "DANG_KY_THAY_DOI": "lần thứ: 7, ngày 29 tháng 03 năm 2018",
    "DANH_SACH_THANH_VIEN_GOP_VON": {
      "GIA_TRI_VON_GOP": [],
      "NOI_DANG_KY_HKTT": [],
      "SO_CMND": [],
      "TEN_THANH_VIEN": [],
      "TY_LE": []
    },
    "DIA_CHI_TRU_SO_CHINH": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam",
    "EMAIL": "",
    "MA_SO_DOANH_NGHIEP": "2800796",
    "MSDN/QDTL": "",
    "NGUOI_DUNG_DAU_CHO_O_HIEN_TAI": "",
    "NGUOI_DUNG_DAU_GIOI_TINH": "",
    "NGUOI_DUNG_DAU_HO_KHAU_THUONG_TRU": "",
    "NGUOI_DUNG_DAU_HO_VA_TEN": "",
    "NGUOI_DUNG_DAU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": "",
    "NGUOI_DUNG_DAU_NGAY_CAP": "",
    "NGUOI_DUNG_DAU_NGAY_SINH": "",
    "NGUOI_DUNG_DAU_NOI_CAP": "",
    "NGUOI_DUNG_DAU_QUOC_TICH": "",
    "NGUOI_DUNG_DAU_SO_GIAY_CHUNG_THUC_CA_NHAN": "",
    "NOI_CAP_GIAY_PHEP": "SỞ KẾ HOẠCH VÀ ĐẦU TƯ TỈNH THANH HOÁ",
    "SO_DIEN_THOAI": "0373.820.000",
    "TC_DIA_CHI_TRU_SO_CHINH": "",
    "TC_NGAY_CAP": "",
    "TC_NOI_CAP": "",
    "TEN_CONG_TY_NUOC_NGOAI": "",
    "TEN_CONG_TY_TIENG_VIET": "CÔNG TY TNHH THÁI HƯNG",
    "TEN_CONG_TY_VIET_TAT": "",
    "TEN_TO_CHUC": "",
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHO_O_HIEN_TAI": [
      "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam"
    ],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHUC_DANH": ["Giám đốc"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_GIOI_TINH": ["Nam"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_KHAU_THUONG_TRU": [
      "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam"
    ],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_VA_TEN": ["LÊ THÁI HƯNG"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": [
      "Chứng minh nhân dân"
    ],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_CAP": ["19/01/2015"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_SINH": ["18/02/1998"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NOI_CAP": ["Công an tỉnh Thanh Hoá"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_QUOC_TICH": ["Việt Nam"],
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_SO_GIAY_CHUNG_THUC_CA_NHAN": ["171522466"],
    "VON_DIEU_LE": "9.000.000.000 đồng",
    "VON_DIEU_LE_BANG_CHU": "Chín tỷ đồng",
    "WEBSITE": "",
    "aligned_file_hash": [],
    "num_of_pages": 2,
    "original_images": [],
    "warning_messages": [
      "Trường thông tin có độ chính xác thấp",
      "Trường thông tin không có kết quả"
    ],
    "warnings": [
      "truong_thong_tin_co_do_chinh_xac_thap",
      "truong_thong_tin_khong_co_ket_qua"
    ]
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230327-cf2a903b-5cfd-b2b3-e053-6c1b9f0a8568/IDG01_4e06dc80-cc47-11ed-bb53-c3191952e55e",
  "details": true
}
```

Response success

```json
{
  "file_hash": "idg20230327-cf2a903b-5cfd-b2b3-e053-6c1b9f0a8568/IDG01_4e06dc80-cc47-11ed-bb53-c3191952e55e",
  "message": "IDG-00000000",
  "object": {
    "CHU_SO_HUU_CHO_O_HIEN_TAI": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11608222490931076, 0.7759726378794357, 0.8972188633615478, 0.8110303548525011
        ]
      },
      "confidence_score": 0.8999771750581729,
      "font_styles": ["normal"],
      "text": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "CHU_SO_HUU_GIOI_TINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11426844014510278, 0.6233433091064557, 0.8071342200725514, 0.6374519025224454
        ]
      },
      "confidence_score": 0.9744994747328848,
      "font_styles": ["normal"],
      "text": "Nam",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_HO_KHAU_THUONG_TRU": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11426844014510278, 0.728516460025652, 0.9111245465538089, 0.7627191107310817
        ]
      },
      "confidence_score": 0.9227874907410129,
      "font_styles": ["normal"],
      "text": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hoà Bình, Tỉnh Hoà Bình, Việt Nam",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "CHU_SO_HUU_HO_VA_TEN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11426844014510278, 0.6233433091064557, 0.8071342200725514, 0.6374519025224454
        ]
      },
      "confidence_score": 0.9744994747328848,
      "font_styles": ["normal"],
      "text": "LÊ THÁI HƯNG",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11366384522370013, 0.6652415562206071, 0.6644498186215235, 0.6802052159042326
        ]
      },
      "confidence_score": 0.9439423058534073,
      "font_styles": ["normal"],
      "text": "Chứng minh nhân dân",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "CHU_SO_HUU_NGAY_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11366384522370013, 0.7058572039333049, 0.7503022974607013, 0.7216759298845661
        ]
      },
      "confidence_score": 0.9858472189423286,
      "font_styles": ["normal"],
      "text": "19/01/2015",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_NGAY_SINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.1124546553808948, 0.6442924326635314, 0.8476420798065296, 0.6592560923471569
        ]
      },
      "confidence_score": 0.9646889318330316,
      "font_styles": ["normal"],
      "text": "18/02/1998",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_NOI_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11366384522370013, 0.7058572039333049, 0.7503022974607013, 0.7216759298845661
        ]
      },
      "confidence_score": 0.9858472189423286,
      "font_styles": ["normal"],
      "text": "Công an tỉnh Thanh Hoá",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_QUOC_TICH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.1124546553808948, 0.6442924326635314, 0.8476420798065296, 0.6592560923471569
        ]
      },
      "confidence_score": 0.9646889318330316,
      "font_styles": ["normal"],
      "text": "Việt Nam",
      "type": "Field",
      "warnings": []
    },
    "CHU_SO_HUU_SO_GIAY_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.1124546553808948, 0.6840530141085934, 0.5199516324062878, 0.7015818725951262
        ]
      },
      "confidence_score": 0.9000090024623356,
      "font_styles": ["normal"],
      "text": "171522466",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "DANG_KY_LAN_DAU": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.3210399032648126, 0.2573749465583583, 0.7496977025392987, 0.27105600684053016
        ]
      },
      "confidence_score": 0.8899954359965682,
      "font_styles": ["normal"],
      "text": "ngày 13 tháng 04 năm 2004",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "DANG_KY_THAY_DOI": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.2678355501813785, 0.2817443351859769, 0.7998790810157195, 0.2967079948696024
        ]
      },
      "confidence_score": 0.9395681314813762,
      "font_styles": ["normal"],
      "text": "lần thứ: 7, ngày 29 tháng 03 năm 2018",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "DANH_SACH_THANH_VIEN_GOP_VON": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [],
      "columns": [
        {
          "bbox_conf_score": 1.0,
          "bboxes": {},
          "cells": [],
          "confidence_score": 1.0,
          "name": "TEN_THANH_VIEN",
          "type": "List",
          "warnings": []
        },
        {
          "bbox_conf_score": 1.0,
          "bboxes": {},
          "cells": [],
          "confidence_score": 1.0,
          "name": "NOI_DANG_KY_HKTT",
          "type": "List",
          "warnings": []
        },
        {
          "bbox_conf_score": 1.0,
          "bboxes": {},
          "cells": [],
          "confidence_score": 1.0,
          "name": "GIA_TRI_VON_GOP",
          "type": "List",
          "warnings": []
        },
        {
          "bbox_conf_score": 1.0,
          "bboxes": {},
          "cells": [],
          "confidence_score": 1.0,
          "name": "TY_LE",
          "type": "List",
          "warnings": []
        },
        {
          "bbox_conf_score": 1.0,
          "bboxes": {},
          "cells": [],
          "confidence_score": 1.0,
          "name": "SO_CMND",
          "type": "List",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "html": "",
      "page_id": 1,
      "paragraph_id": 0,
      "rows": [],
      "text": "--------------- | ----------------- | ---------------- | ------ | --------\n--------------- | ----------------- | ---------------- | ------ | --------\n",
      "type": "Table",
      "warnings": []
    },
    "DIA_CHI_TRU_SO_CHINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11487303506650544, 0.4698589140658401, 0.8996372430471584, 0.5049166310389055
        ]
      },
      "confidence_score": 0.9286291934793957,
      "font_styles": ["normal"],
      "text": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "EMAIL": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0.9999548564974613,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": ["truong_thong_tin_khong_co_ket_qua"]
    },
    "MA_SO_DOANH_NGHIEP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.38331318016928656, 0.2291577597263788, 0.6819830713422007, 0.2479692176143651
        ]
      },
      "confidence_score": 0.8947945678377439,
      "font_styles": ["normal"],
      "text": "2800796",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "MSDN/QDTL": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_CHO_O_HIEN_TAI": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_GIOI_TINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_HO_KHAU_THUONG_TRU": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_HO_VA_TEN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_NGAY_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_NGAY_SINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_NOI_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_QUOC_TICH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NGUOI_DUNG_DAU_SO_GIAY_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "NOI_CAP_GIAY_PHEP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.16142684401451027, 0.09961522017956391, 0.405683192261185, 0.12997007268063274
        ]
      },
      "confidence_score": 0.9637651070980979,
      "font_styles": ["normal"],
      "text": "SỞ KẾ HOẠCH VÀ ĐẦU TƯ TỈNH THANH HOÁ",
      "type": "Field",
      "warnings": []
    },
    "SO_DIEN_THOAI": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11608222490931076, 0.5083368961094484, 0.6457073760580411, 0.5280034202650705
        ]
      },
      "confidence_score": 0.8805529552599123,
      "font_styles": ["normal"],
      "text": "0373.820.000",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "TC_DIA_CHI_TRU_SO_CHINH": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "TC_NGAY_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "TC_NOI_CAP": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "TEN_CONG_TY_NUOC_NGOAI": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0.9517577654729827,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": ["truong_thong_tin_khong_co_ket_qua"]
    },
    "TEN_CONG_TY_TIENG_VIET": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.1185006045949214, 0.3822146216331766, 0.7382103990326482, 0.39589568191534846
        ]
      },
      "confidence_score": 0.9124049808574802,
      "font_styles": ["normal"],
      "text": "CÔNG TY TNHH THÁI HƯNG",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "TEN_CONG_TY_VIET_TAT": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0.946528801251561,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "TEN_TO_CHUC": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": [
        "truong_thong_tin_khong_co_ket_qua",
        "truong_thong_tin_co_do_chinh_xac_thap"
      ]
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHO_O_HIEN_TAI": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09068923821039904, 0.2578024796921761, 0.8651753325272068,
              0.2937152629328773
            ]
          },
          "confidence_score": 0.8772863296860767,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHO_O_HIEN_TAI_0",
          "text": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHUC_DANH": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09189842805320435, 0.10645575032064986, 0.31499395405078595,
              0.1218469431380932
            ]
          },
          "confidence_score": 0.9691856402077217,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_CHUC_DANH_0",
          "text": "Giám đốc",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_GIOI_TINH": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09189842805320435, 0.08208636169303121, 0.841596130592503,
              0.09918768704574604
            ]
          },
          "confidence_score": 0.9844242984420928,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_GIOI_TINH_0",
          "text": "Nam",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_KHAU_THUONG_TRU": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09189842805320435, 0.2159042325780248, 0.8887545344619106,
              0.25053441641727237
            ]
          },
          "confidence_score": 0.8645168563908968,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_KHAU_THUONG_TRU_0",
          "text": "Số nhà 161, phố Tây Sơn, Phường An Hưng, Thành phố Hòa Bình, Tỉnh Hòa Bình, Việt Nam",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_VA_TEN": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09189842805320435, 0.08208636169303121, 0.841596130592503,
              0.09918768704574604
            ]
          },
          "confidence_score": 0.9844242984420928,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_HO_VA_TEN_0",
          "text": "LÊ THÁI HƯNG",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09068923821039904, 0.15220179563916203, 0.6444981862152358,
              0.1658828559213339
            ]
          },
          "confidence_score": 0.8972507133532591,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN_0",
          "text": "Chứng minh nhân dân",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_CAP": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09068923821039904, 0.1936725096194955, 0.7357920193470375,
              0.20906370243693886
            ]
          },
          "confidence_score": 0.9866117904257428,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_CAP_0",
          "text": "19/01/2015",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_SINH": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09008464328899637, 0.129115006412997, 0.8216444981862152,
              0.14493373236425822
            ]
          },
          "confidence_score": 0.9720748103501169,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NGAY_SINH_0",
          "text": "18/02/1998",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NOI_CAP": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09068923821039904, 0.1936725096194955, 0.7357920193470375,
              0.20906370243693886
            ]
          },
          "confidence_score": 0.9866117904257428,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_NOI_CAP_0",
          "text": "Công an tỉnh Thanh Hoá",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_QUOC_TICH": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.09008464328899637, 0.129115006412997, 0.8216444981862152,
              0.14493373236425822
            ]
          },
          "confidence_score": 0.9720748103501169,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_QUOC_TICH_0",
          "text": "Việt Nam",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_SO_GIAY_CHUNG_THUC_CA_NHAN": {
      "bbox_conf_score": 1.0,
      "bboxes": {},
      "cells": [
        {
          "bbox_conf_score": 0.9999,
          "bboxes": {
            "2": [
              0.08887545344619105, 0.17058572039333048, 0.5084643288996372,
              0.18854211201368107
            ]
          },
          "confidence_score": 0.8970532905096573,
          "font_styles": ["normal"],
          "name": "THONG_TIN_NGUOI_DAI_DIEN_PHAP_LUAT_SO_GIAY_CHUNG_THUC_CA_NHAN_0",
          "text": "171522466",
          "type": "Field",
          "warnings": []
        }
      ],
      "confidence_score": 1.0,
      "type": "List",
      "warnings": []
    },
    "VON_DIEU_LE": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11064087061668681, 0.5485250106883284, 0.5259975816203144, 0.5677640017101325
        ]
      },
      "confidence_score": 0.9092182499822714,
      "font_styles": ["normal"],
      "text": "9.000.000.000 đồng",
      "type": "Field",
      "warnings": ["truong_thong_tin_co_do_chinh_xac_thap"]
    },
    "VON_DIEU_LE_BANG_CHU": {
      "bbox_conf_score": 0.9999,
      "bboxes": {
        "1": [
          0.11426844014510278, 0.5741769987174006, 0.34522370012091896, 0.5921333903377511
        ]
      },
      "confidence_score": 0.981379364537496,
      "font_styles": ["normal"],
      "text": "Chín tỷ đồng",
      "type": "Field",
      "warnings": []
    },
    "WEBSITE": {
      "bbox_conf_score": 0.9999,
      "bboxes": {},
      "confidence_score": 0.9999548564974613,
      "font_styles": ["normal"],
      "text": "",
      "type": "Field",
      "warnings": ["truong_thong_tin_khong_co_ket_qua"]
    },
    "aligned_file_hash": [],
    "num_of_pages": 2,
    "original_images": [],
    "warning_messages": [
      "Trường thông tin có độ chính xác thấp",
      "Trường thông tin không có kết quả"
    ],
    "warnings": [
      "truong_thong_tin_co_do_chinh_xac_thap",
      "truong_thong_tin_khong_co_ket_qua"
    ]
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>String json</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>[Bảng mô tả kết quả trả về của các trường thông tin](#bang-mo-ta-ket-qua)</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
<tr>
<td></td>
<td>original_images = [hash-page1, hash-page2, ...]</td>
<td>Array</td>
<td>Đường link của file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>aligned_file_hash</td>
<td>Array</td>
<td>Thông số hash file của file sau khi được xoay đối với trường hợp ảnh bị nghiêng</td>
</tr>
</tbody></table>

# API bóc tách Hoá đơn giá trị gia tăng

**- Chức năng**: Bóc tách và trích xuất thông tin của Hóa đơn giá trị gia tăng được trả về từ API /file-service/addFile

**- Chi tiết API**:

**Endpoint:** `<domain-name>/rpa-service/aidigdoc/v1/ocr/hoa-don-gtgt`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td></td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn. Mặc định: "client_session": "00-14-22-01-23-45-1548211589291"</td>
</tr>
<tr>
<td></td>
<td>file_type</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Định dạng file upload lên server (PDF &amp; Image - JPG, PNG, HEIC)</td>
</tr>
<tr>
<td></td>
<td>details</td>
<td>Boolean</td>
<td></td>
<td></td>
<td>Mặc định <strong>details=false</strong>. Trong trường hợp người dùng mong muốn nhận chi tiết kết quả trả về của trường thông tin, chọn <strong>details=true</strong>. Mô tả trường hợp details=true được thể hiện ở mục <strong>Phụ lục 1</strong>.</td>
</tr>
</tbody></table>

**Request response:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

Đối với Hoá đơn giá trị gia tăng, các trường thông tin bóc tách bao gồm các trường thông tin bên dưới và được thể hiện các vị trí bóc tách một số trường thông tin trong ảnh đính kèm.

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 25%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Kết quả API trả về</strong></th>
<th><strong>Trường thông tin bóc tách</strong></th>
<th><strong>Ví dụ kết quả trả về với hình ảnh ví dụ</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>type</td>
<td>Tên loại hóa đơn</td>
<td>HÓA ĐƠN GIÁ TRỊ GIA TĂNG</td>
</tr>
<tr>
<td>2</td>
<td>serial</td>
<td>Ký hiệu (Sign)</td>
<td>1C21AAA</td>
</tr>
<tr>
<td>3</td>
<td>invoice_number</td>
<td>Số hóa đơn </td>
<td>00000001</td>
</tr>
<tr>
<td>4</td>
<td>issued_date</td>
<td>Ngày hóa đơn</td>
<td>31/12/2021</td>
</tr>
<tr>
<td>5</td>
<td>seller_name</td>
<td>Họ tên người bán (Đơn vị bán)</td>
<td>CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ LTH VIỆT NAM</td>
</tr>
<tr>
<td>6</td>
<td>seller_address</td>
<td>Địa chỉ người bán</td>
<td>Tầng L7 Vincom Center, Số 72, Đường Lê Thánh Tôn, Phường Bến Nghé, Quận 7, Thành phố Hải Phòng</td>
</tr>
<tr>
<td>7</td>
<td>seller_tax_code </td>
<td>Mã số thuế người bán</td>
<td></td>
</tr>
<tr>
<td>8</td>
<td>buyer_name</td>
<td>Họ tên người mua</td>
<td></td>
</tr>
<tr>
<td>9</td>
<td>buyer_company_name</td>
<td>Tên đơn vị mua</td>
<td>LTH C&T CORPORATION</td>
</tr>
<tr>
<td>10</td>
<td>buyer_tax_code</td>
<td>Mã số thuế người mua</td>
<td></td>
</tr>
<tr>
<td>11</td>
<td>buyer_address</td>
<td>Địa chỉ người mua
</td>
<td>SỐ 10, ĐƯỜNG NGUYỄN TRÃI, HÀ ĐÔNG, HÀ NỘI, VIỆT NAM
</td>
</tr>
<tr>
<td>12</td>
<td>payment_method</td>
<td>Hình thức thanh toán
</td>
<td>CK
</td>
</tr>
<tr>
<td>13</td>
<td>rates</td>
<td>Tỷ giá
</td>
<td></td>
</tr>
<tr>
  <td colspan="4">
    <strong><span style="color:red;">Trường thông tin trong bảng (mục details)</span></strong>
  </td>
</tr>
<tr>
<td>14</td>
<td>Description</td>
<td>Tên hàng hóa dịch vụ
</td>
<td>Xuất điều chỉnh giảm hàng nhập theo Debit Note số SSC-HQ 31122021, ngày 31/12/2021; Dung Môi Methyl Ethyl Ketone (MEK)
</td>
</tr>
<tr>
<td>15</td>
<td>Unit</td>
<td>Đơn vị tính
</td>
<td>Tấn</td>
</tr>
<tr>
<td>16</td>
<td>Quantity</td>
<td>Số lượng
</td>
<td>799,999
</td>
</tr>
<tr>
<td>17</td>
<td>Unit_price</td>
<td>Đơn giá
</td>
<td>130,00</td>
</tr>
<tr>
<td>18</td>
<td>amount</td>
<td>Tiền trước thuế/Thành tiền
</td>
<td>103.999,87
</td>
</tr>
<tr>
<td>19</td>
<td>tax_percentage</td>
<td>Thuế suất
</td>
<td></td>
</tr>
<tr>
<td>20</td>
<td>tax_amount</td>
<td>Tiền thuế
</td>
<td></td>
</tr>
<tr>
<td>21</td>
<td>total_amount</td>
<td>Tổng tiền sau thuế
</td>
<td>
</td>
</tr>
<tr>
<td>22</td>
<td>grand_total_before_tax</td>
<td>Tổng cộng tiền trước thuế
</td>
<td>103.999,87</td>
</tr>
<tr>
<td>23</td>
<td>general_tax_rates</td>
<td>Thuế suất chung
</td>
<td>0
</td>
</tr>
<tr>
<td>24</td>
<td>total_tax_amount</td>
<td>Tổng tiền thuế
</td>
<td>0</td>
</tr>
<tr>
<td>25</td>
<td>grand_total_after_tax</td>
<td>Tổng cộng tiền sau thuế
</td>
<td>103.999,87</td>
</tr>
<tr>
<td>26</td>
<td>grand_total_after_tax_in_text</td>
<td>Số tiền bằng chữ
</td>
<td>Một trăm linh ba nghìn chín trăm chín mươi chín đô la Mỹ tám mươi bảy cents
</td>
</tr>
<tr>
<td>27</td>
<td>signed_by</td>
<td>Tên đơn vị bán
</td>
<td>CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ SAMSUNG VIỆT NAM</td>
</tr>
<tr>
<td>28</td>
<td>signed_date </td>
<td>Ký ngày
</td>
<td>31/12/2021</td>
</tr>
</tbody></table>

![img-6.jpg](img-6.jpg)

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230406/IDG01_bf1b7e9a-d422-11ed-9aa8-379f4176bf24",
  "details": false
}
```

Response success

```json
{
  "file_hash": "idg20230406/IDG01_bf1b7e9a-d422-11ed-9aa8-379f4176bf24",
  "message": "IDG-00000000",
  "object": {
    "buyer_address": "SỐ 10, ĐƯỜNG NGUYỄN TRÃI, HÀ ĐÔNG, HÀ NỘI, VIỆT NAM",
    "buyer_company_name": "LTH C&T CORPORATION",
    "buyer_name": "",
    "buyer_tax_code": "",
    "details": {
      "Description": [
        "Xuất điều chỉnh giảm hàng nhập theo Debit Note số SSC-HQ 31122021, ngày 31/12/2021; Dung Môi Methyl Ethyl Ketone (MEK)"
      ],
      "Quantity": ["799,999"],
      "Unit": ["Tấn"],
      "Unit_price": ["130,00"],
      "amount": ["103.999,87"],
      "tax_amount": [],
      "tax_percentage": [],
      "total_amount": []
    },
    "general_tax_rates": "0",
    "grand_total_after_tax": "103.999,87",
    "grand_total_after_tax_in_text": "Một trăm linh ba nghìn chín trăm chín mươi chín đô la Mỹ tám mươi bảy cents",
    "grand_total_before_tax": "103.999,87",
    "invoice_number": "00000001",
    "issued_date": "31/12/2021",
    "num_of_pages": 1,
    "payment_method": "CK",
    "rates": "",
    "seller_address": "Tầng L7 Vincom Center, Số 72, Đường Lê Thánh Tôn, Phường Bền Nghé, Quận 7, Thành Phố Hải Phòng",
    "seller_name": "CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ LTH VIỆT NAM",
    "seller_tax_code": "",
    "serial": "1C21AAA",
    "signed_by": "CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ SAMSUNG VIỆT NAM",
    "signed_date": "31/12/2021",
    "total_tax_amount": "0",
    "type": "HÓA ĐƠN GIÁ TRỊ GIA TĂNG",
    "warning_messages": [],
    "warnings": []
  },
  "server_version": "1.3.17",
  "status": "OK",
  "statusCode": 200
}
```

**Phụ lục 1. Mô tả trường hợp chọn details=true**

Khi lựa chọn details:true, người dùng có thể xem được thông tin chi tiết của kết quả trả về. Ví dụ như sau:

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "file_type": "pdf",
  "file_hash": "idg20230327-cf2a903b-5cfd-b2b3-e053-6c1b9f0a8568/IDG01_4e06dc80-cc47-11ed-bb53-c3191952e55e",
  "details": true
}
```

Response success

```json
{
   "file_hash": "idg20250317-0d9d0c18-1226-3a5b-e063-62199f0af83a/IDG01_df04de96-02e5-11f0-b455-0906095c0e82",
   "message": "IDG-00000000",
   "object": {
       "buyer_address": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
                "1": [
                    0.3641863278886872,
                    0.30825138948268493,
                    0.7810042347247429,
                    0.3180846515604959
                ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "SỐ 10, ĐƯỜNG NGUYỄN TRÃI, HÀ ĐÔNG, HÀ NỘI, VIỆT NAM",
           "type": "Field",
           "warnings": []
       },
       "buyer_company_name": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.36358136721113127,
                   0.27105600684053016,
                   0.5251058681185723,
                   0.28388200085506626
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "LTH C&T CORPORATION",
           "type": "Field",
           "warnings": []
       },
       "buyer_name": {
           "bbox_conf_score": 0.9999,
           "bboxes": {},
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
        "text": "",
           "type": "Field",
           "warnings": []
       },
       "buyer_tax_code": {
           "bbox_conf_score": 0.9999,
           "bboxes": {},
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "",
           "type": "Field",
           "warnings": []
       },
       "details": {
           "bbox_conf_score": 1.0,
           "bboxes": {},
           "cells": [],
           "columns": [
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 0.9999,
                           "bboxes": {
                               "1": [
                                   0.1385359951603146,
                                   0.3988884138520735,
                                   0.4615849969751966,
                                   0.4330910645575032
                               ]
                           },
                           "confidence_score": 1.0,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Description",
                           "text": "Xuất điều chỉnh giảm hàng nhập theo Debit Note số SSC-HQ 31122021, ngày 31/12/2021; Dung Môi Methyl Ethyl Ketone (MEK)",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "Description",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 0.9999,
                           "bboxes": {
                               "1": [
                                   0.5136116152450091,
                                   0.41000427533133815,
                                   0.5353901996370236,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 1.0,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Unit",
                           "text": "Tấn",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "Unit",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 0.9999,
                           "bboxes": {
                               "1": [
                                   0.6025408348457351,
                                   0.41000427533133815,
                                   0.6497277676950998,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 1.0,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Quantity",
                           "text": "799,999",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "Quantity",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 0.9999,
                           "bboxes": {
                               "1": [
                                   0.7338173018753781,
                                   0.41000427533133815,
                                   0.7743496672716274,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 1.0,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Unit_price",
                           "text": "130,00",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "Unit_price",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 0.9999,
                           "bboxes": {
                               "1": [
                                   0.8445251058681186,
                                   0.41000427533133815,
                                   0.9104658197217181,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 1.0,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "amount",
                           "text": "103.999,87",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "amount",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [],
                   "confidence_score": 1.0,
                   "name": "tax_percentage",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [],
                   "confidence_score": 1.0,
                   "name": "tax_amount",
                   "type": "List",
                   "warnings": []
               },
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [],
                   "confidence_score": 1.0,
                   "name": "total_amount",
                   "type": "List",
                   "warnings": []
               }
           ],
           "confidence_score": 1.0,
           "html": "",
           "page_id": 1,
           "paragraph_id": 0,
           "rows": [
               {
                   "bbox_conf_score": 1.0,
                   "bboxes": {},
                   "cells": [
                       {
                           "bbox_conf_score": 1.0,
                           "bboxes": {
                               "1": [
                                   0.1385359951603146,
                                   0.3988884138520735,
                                   0.4615849969751966,
                                   0.4330910645575032
                               ]
                           },
                           "confidence_score": 0.9999,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Description",
                           "text": "Xuất điều chỉnh giảm hàng nhập theo Debit Note số SSC-HQ 31122021, ngày 31/12/2021; Dung Môi Methyl Ethyl Ketone (MEK)",
                           "type": "Field",
                           "warnings": []
                       },
                       {
                           "bbox_conf_score": 1.0,
                           "bboxes": {
                               "1": [
                                   0.5136116152450091,
                                   0.41000427533133815,
                                   0.5353901996370236,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 0.9999,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Unit",
                           "text": "Tấn",
                           "type": "Field",
                           "warnings": []
                       },
                       {
                           "bbox_conf_score": 1.0,
                           "bboxes": {
                               "1": [
                                   0.6025408348457351,
                                   0.41000427533133815,
                                   0.6497277676950998,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 0.9999,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Quantity",
                           "text": "799,999",
                           "type": "Field",
                           "warnings": []
                       },
                       {
                           "bbox_conf_score": 1.0,
                           "bboxes": {
                               "1": [
                                   0.7338173018753781,
                                   0.41000427533133815,
                                   0.7743496672716274,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 0.9999,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "Unit_price",
                           "text": "130,00",
                           "type": "Field",
                           "warnings": []
                       },
                       {
                           "bbox_conf_score": 1.0,
                           "bboxes": {
                               "1": [
                                   0.8445251058681186,
                                   0.41000427533133815,
                                   0.9104658197217181,
                                   0.42154766994442067
                               ]
                           },
                           "confidence_score": 0.9999,
                           "font_styles": [
                               "normal"
                           ],
                           "name": "amount",
                           "text": "103.999,87",
                           "type": "Field",
                           "warnings": []
                       }
                   ],
                   "confidence_score": 1.0,
                   "name": "Description",
                   "type": "List",
                   "warnings": []
               }
           ],
           "text": "----------------------------------------------------------------------------------------------------------------------- | ----------------- | ----- | --------- | ----------- | ----------- | --------------- | ----------- | -------------\n                                                                                                            Description  |  Unit |  Quantity |  Unit_price |      amount |  tax_percentage |  tax_amount |  total_amount\n--------------------------------------------------------------------------------------------------------------
           --------- | ----------------- | ----- | --------- | ----------- | ----------- | --------------- | ----------- | -------------\n Xuất điều chỉnh giảm hàng nhập theo Debit Note số SSC-HQ 31122021, ngày 31/12/2021; Dung Môi Methyl Ethyl Ketone (MEK) |               Tấn | 799,999 |    130,00 |  103.999,87\n----------------------------------------------------------------------------------------------------------------------- | ----------------- | ----- | --------- | ----------- | ----------- | --------------- | ----------- | -------------\n",
           "type": "Table",
           "warnings": []
       },
       "general_tax_rates": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.09437386569872959,
                   0.6105173*********,
                   0.3115547489413188,
                   0.6220607097050022
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "0",
           "type": "Field",
           "warnings": []
       },
       "grand_total_after_tax": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.853599516031458,
                   0.628901239846088,
                   0.9195402298850575,
                   0.6404446344591705
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "103.999,87",
           "type": "Field",
           "warnings": []
       },
       "grand_total_after_tax_in_text": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.3502722323049002,
                   0.6468576314664386,
                   0.8106473079249849,
                   0.6584010260795211
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "Một trăm linh ba nghìn chín trăm chín mươi chín đô la Mỹ tám mươi bảy cents",
           "type": "Field",
           "warnings": []
       },
       "grand_total_before_tax": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.853599516031458,
                   0.5925609234715691,
                   0.9195402298850575,
                   0.6041043180846516
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "103.999,87",
           "type": "Field",
           "warnings": []
       },
       "invoice_number": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.7120387174833636,
                   0.1013253527148354,
                   0.8566243194192378,
                   0.11928174433518597
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "00000001",
           "type": "Field",
           "warnings": []
       },
       "issued_date": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.31094978826376285,
                   0.10303548525010689,
                   0.6581972171808832,
                   0.1145788798631894
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "31/12/2021",
           "type": "Field",
           "warnings": []
       },
       "num_of_pages": 1,
       "payment_method": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.3641863278886872,
                   0.3232150491663104,
                   0.38294010889292196,
                   0.334330910645575
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "CK",
           "type": "Field",
           "warnings": []
       },
       "rates": {
           "bbox_conf_score": 0.9999,
           "bboxes": {},
           "confidence_score": 0,
           "font_styles": [
               "normal"
           ],
           "text": "",
           "type": "Field",
           "warnings": []
       },
       "seller_address": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.2571082879612825,
                   0.19752030782385635,
                   0.8729582577132486,
                   0.20863616930312098
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "Tầng L7 Vincom Center, Số 72, Đường Lê Thánh Tôn, Phường Bền Nghé, Quận 7, Thành Phố Hải Phòng",
           "type": "Field",
           "warnings": []
       },
       "seller_name": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.09437386569872959,
                   0.15604959384352288,
                   0.7241379310344828,
                   0.17229585292860197
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ LTH VIỆT NAM",
           "type": "Field",
           "warnings": []
       },
       "seller_tax_code": {
           "bbox_conf_score": 0.9999,
           "bboxes": {},
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "",
           "type": "Field",
           "warnings": []
       },
       "serial": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.7120387174833636,
                   0.07823856348867037,
                   0.8995765275257108,
                   0.0940572894399316
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "1C21AAA",
           "type": "Field",
           "warnings": []
       },
       "signed_by": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.6533575317604355,
                   0.7229585292860197,
                   0.8886872353297036,
                   0.7443351859769132
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "CÔNG TY TNHH HÓA CHẤT CÔNG NGHỆ SAMSUNG VIỆT NAM",
           "type": "Field",
           "warnings": []
       },
       "signed_date": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.6533575317604355,
                   0.7443351859769132,
                   0.822141560798548,
                   0.75502351432236
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "31/12/2021",
           "type": "Field",
           "warnings": []
       },
       "total_tax_amount": {
           "bbox_conf_score": 0.9999,
           "bboxes": {
               "1": [
                   0.911070780399274,
                   0.6105173*********,
                   0.9195402298850575,
                   0.6220607097050022
               ]
           },
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "0",
           "type": "Field",
           "warnings": []
       },
       "type": {
           "bbox_conf_score": 0.9999,
           "bboxes": {},
           "confidence_score": 1.0,
           "font_styles": [
               "normal"
           ],
           "text": "HÓA ĐƠN GIÁ TRỊ GIA TĂNG",
           "type": "Field",
           "warnings": []
       },
       "warning_messages": [],
       "warnings": []
   },
   "server_version": "1.3.17",
   "status": "OK",
   "statusCode": 200
}

```

**Trong đó:**

Http code: 200 - thành công

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>file_hash</td>
<td>String</td>
<td>Thông số hash file gửi lên trả về từ api /addFile</td>
</tr>
<tr>
<td></td>
<td>message</td>
<td>String</td>
<td>Thông báo mã lỗi kết quả. Ví dụ “IDG-00000000”</td>
</tr>
<td></td>
<td>status</td>
<td>String</td>
<td>Trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>statusCode</td>
<td>Integer</td>
<td>Mã trạng thái trả về</td>
</tr>
<tr>
<td></td>
<td>object</td>
<td>Object</td>
<td>Object chứa kết quả bóc tách trả ra từ hệ thống. Kết quả bóc tách thông tin được thể hiện chi tiết ở <strong>[Bảng mô tả kết quả trả về của các trường thông tin](#bang-mo-ta-ket-qua)</strong>.</td>
</tr>
</tbody></table>

Các trường thông tin trong Object:

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 15%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Kiểu dữ liệu</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>OBJECT</td>
<td>warnings</td>
<td>Array</td>
<td>Thông tin cảnh báo các trường thông tin<br>VD: List [ "anh_dau_vao_mat_goc", "anh_dau_vao_nghieng", ...]</td>
</tr>
<tr>
<td></td>
<td>warning_messages</td>
<td>Array</td>
<td>List các message cảnh báo tương ứng với từng mã cảnh báo ở warning, ở format mà người có thể đọc được<br>ví dụ: List [ "Giấy tờ bị mất góc", "Giấy tờ bị nghiêng", ...]</td>
</tr>
<tr>
<td></td>
<td>num_of_pages</td>
<td>Integer</td>
<td>Số trang của file đã thực hiện bóc tách</td>
</tr>
</tbody></table>

# Bảng mô tả kết quả trả về của các trường thông tin {#bang-mo-ta-ket-qua}

<!-- start using html to help with <table> colspan and rolspan rendering -->
<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Thông tin</strong></th>
<th><strong>Ý nghĩa</strong></th>
</tr>
</thead>
<tbody><tr>
<td colspan="3"><strong>Đối với thông tin ngoài bảng</strong></td>
</tr>
<tr>
<td>1</td>
<td><strong>bboxes</strong></td>
<td>Tọa độ vị trí của trường thông tin bóc tách Tọa độ của trường thông tin bóc tách (là một dict ghi tọa độ tương đối của 4 đỉnh bounding box, cụ thể là X min, Y min, X max, Y max, hai số đầu là góc trên bên trái, hai số cuối là góc dưới bên phải)</td>
</tr>
<tr>
<td>2</td>
<td><strong>confidence_score</strong></td>
<td>Độ chính xác của trường bóc tách</td>
</tr>
<tr>
<td>3</td>
<td><strong>bbox_conf_score</strong></td>
<td>Độ tự tin của toạ độ vị trí của trường thông tin bóc tách</td>
</tr>
<tr>
<td>4</td>
<td><strong>warnings</strong></td>
<td>Thông tin cảnh báo</td>
</tr>
<tr>
<td>5</td>
<td><strong>name</strong></td>
<td>Tên trường thông tin</td>
</tr>
<tr>
<td>6</td>
<td><strong>text</strong></td>
<td>Kết quả bóc tách thông tin</td>
</tr>
<tr>
<td>7</td>
<td><strong>type</strong></td>
<td>Kiểu OCR (nhận 1 trong các giá trị Line, Phrase, Paragraph, List)</td>
</tr>
<tr>
<td>8</td>
<td><strong>font_styles</strong></td>
<td>Kiểu dáng của thông tin bóc tách</td>
</tr>
<tr>
<td colspan="3"><strong>Đối với thông tin trong bảng (Tương tự các thông tin ngoài bảng, thêm các thông tin sau)</strong></td>
</tr>
<tr>
<td>9</td>
<td><strong>columns</strong></td>
<td>Các trường thông tin OCR theo cột (từ trái qua phải) trong bảng. Khởi đầu bóc tách ô đầu tiên trong cột bằng ký tự [, kết thúc ô cuối cùng trong cột bằng ký tự ]. Hiển thị kết quả khi OCR thông tin hết các ô theo các cột trong bảng</td>
</tr>
<tr>
<td>10</td>
<td><strong>rows</strong></td>
<td>Các trường thông tin OCR theo hàng (từ trái qua phải) trong bảng. Khởi đầu bóc tách ô đầu tiên trong hàng bằng ký tự [, kết thúc ô cuối cùng trong hàng bằng ký tự ]. Hiển thị kết quả khi OCR thông tin hết các ô theo hàng trong bảng</td>
</tr>
<tr>
<td>11</td>
<td><strong>cell</strong></td>
<td>Thể hiện khi OCR cụm từ trong các ô theo thứ tự từ trên xuống dưới, theo từng cột (từ trái qua phải) trong bảng</td>
</tr>
</tbody></table>
