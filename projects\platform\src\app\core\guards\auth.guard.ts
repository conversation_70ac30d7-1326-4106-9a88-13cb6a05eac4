import { Injectable, inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree
} from '@angular/router';
import UtilsService from '@platform/app/core/services/utils.service';
import { ToastrService } from 'ngx-toastr';
// import { environment } from '@environment/environment';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard {
  constructor(
    private router: Router,
    private utilsService: UtilsService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}
  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const isLoggedIn =
      !!this.utilsService.getCurrentUser() && !!this.utilsService.getAccessToken();
    const isSessionExpired = isLoggedIn && this.utilsService.isAccessTokenExpired();

    if (isSessionExpired) {
      this.authService.logout();
      const message =
        'Phiên làm việc đã hết hạn, vui lòng đăng nhập để tiếp tục sử dụng.';
      const dup = this.toastr.findDuplicate(
        undefined,
        message,
        false, // resetOnDuplicate
        false // countDuplicates
      );
      if (!dup) this.toastr.info(message);
    }

    if (!isLoggedIn || isSessionExpired) {
      this.router.navigate(['login'], { queryParams: { returnUrl: _state.url } });
      return false;
    }
    return true;
  }
}
