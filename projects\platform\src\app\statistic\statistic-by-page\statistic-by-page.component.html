<div class="grid grid-cols-4 gap-6 statistic-by-page-container">
  <app-pie
    class="col-span-full xl:col-span-1"
    [dataset]="pageStatisticStatus"
    unit="trang"
    centerIcon="assets/statistic/page.svg"
  ></app-pie>
  <app-chart
    class="col-span-full xl:col-span-3"
    [dataset]="pageStatisticByTime"
    [labelKey]="'time'"
    [dataKey]="'page'"
    [chartTitle]="'Biểu đồ tổng số trang văn bản bóc tách'"
    [yAxisTitle]="'Số trang'"
    [xAxisTitle]="dateFilter.xAxisTitleForChart"
    [tooltipLabel]="'Trang'"
  ></app-chart>
  <app-table
    class="col-span-full"
    [columns]="pageStatisticByAPI.columns"
    [dataSource]="pageStatisticByAPI.data"
    [title]="'Thống kê số trang theo tần suất API'"
    [hasTotalRow]="pageStatisticByAPI.hasTotalRow"
    [dateFilter]="dateFilter"
    (refetchDataEvent)="setPageStatisticByAPI($event)"
    [hideSearch]="false"
  ></app-table>
</div>

<ng-template #successTooltipTmpl>
  <div class="text-base font-bold mb-2">Thành công</div>
  <div>Số trang văn bản được xử lý và trả kết quả thành công</div>
</ng-template>

<ng-template #invalidTooltipTmpl>
  <div class="text-base font-bold mb-2">Không hợp lệ</div>
  <div>Số trang văn bản lỗi do đầu vào không hợp lệ</div>
</ng-template>
