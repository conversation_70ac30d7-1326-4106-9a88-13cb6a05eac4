import { Component, Input, OnInit, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-detail-layout',
  templateUrl: './detail-layout.component.html',
  styleUrls: ['./detail-layout.component.scss']
})
export class DetailLayoutComponent implements OnInit {
  @Input() title?: string;
  @Input() contentTemplate: TemplateRef<any>;
  @Input() actionsTemplate?: TemplateRef<any>;
  @Input() goBackCommands: string[];

  constructor(private router: Router) {}

  ngOnInit(): void {}

  goBack() {
    this.router.navigate(this.goBackCommands?.length ? this.goBackCommands : ['.', '.']);
  }
}
