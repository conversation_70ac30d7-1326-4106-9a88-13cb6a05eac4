<div class="flex flex-col gap-2">
  <div class="font-medium">Sắp xếp thư mục theo:</div>
  <nz-radio-group nzButtonStyle="solid" [(ngModel)]="orderBy">
    <label nz-radio-button nzValue="name">Tên</label>
    <label nz-radio-button nzValue="createdAt">Thời gian <PERSON></label>
    <label nz-radio-button nzValue="updatedAt">Thời gian chỉnh sửa</label>
  </nz-radio-group>
  <div class="font-medium">Thứ tự:</div>
  <nz-radio-group nzButtonStyle="solid" [(ngModel)]="orderValue">
    <label nz-radio-button nzValue="ASC">
      {{ orderBy === 'name' ? 'A-Z' : 'Tăng dần' }}
    </label>
    <label nz-radio-button nzValue="DESC">
      {{ orderBy === 'name' ? 'Z-A' : 'Giảm dần' }}
    </label>
  </nz-radio-group>
  <div class="flex items-center justify-center gap-4 mt-2">
    <button
      class="rounded-lg border border-brand-1 text-brand-1 px-7 py-[6px] font-medium flex items-center gap-2"
      (click)="cancel()"
    >
      <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5.5 15L15.5 5"
          stroke="#1E5FD5"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M15.5 15L5.5 5"
          stroke="#1E5FD5"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Hủy
    </button>
    <button
      class="rounded-lg bg-brand-1 border border-brand-1 text-white px-7 py-[6px] font-medium flex items-center gap-2"
      (click)="apply()"
    >
      Áp dụng
    </button>
  </div>
</div>
