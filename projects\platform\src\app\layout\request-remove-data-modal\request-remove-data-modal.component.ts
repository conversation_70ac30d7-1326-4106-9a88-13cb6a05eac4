import { Component, OnInit } from '@angular/core';
import { UserService } from '@platform/app/core/services/user.service';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError, tap } from 'rxjs';

@Component({
  selector: 'app-request-remove-data-modal',
  templateUrl: './request-remove-data-modal.component.html',
  styleUrls: ['./request-remove-data-modal.component.scss'],
})
export class RequestRemoveDataModalComponent implements OnInit {
  step: 1 | 2 = 1;
  confirmed = false;
  account = {
    fullName: '',
    username: '',
    phoneNumber: '',
    workUnit: '',
  };
  message = '';

  constructor(
    public modal: NzModalRef,
    private toastr: ToastrService,
    private userService: UserService
  ) {
    this.userService
      .getIdgAccount()
      .pipe(
        tap((result) => {
          this.account = result.object;
        }),
        catchError(() => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
          this.modal.close();
          return EMPTY;
        })
      )
      .subscribe();
  }

  ngOnInit(): void {}

  goToStep2() {
    if (this.confirmed) this.step = 2;
  }

  sendRemoveDataRequest() {
    if (!this.confirmed || !this.message?.trim()) return;
    this.userService
      .sendRemoveDataRequest(this.message)
      .pipe(
        tap(() => {
          this.toastr.success('Gửi yêu cầu xóa dữ liệu cá nhân thành công');
          this.modal.close();
        }),
        catchError(() => {
          this.toastr.error('Gửi yêu cầu xóa dữ liệu cá nhân thất bại');
          return EMPTY;
        })
      )
      .subscribe();
  }
}
