import { HttpParams } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { UserService } from '@platform/app/core/services/user.service';
import { ToastrService } from 'ngx-toastr';
import {
  EMPTY,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';
import {
  ListSharedDocuments,
  RolePermisson,
  User,
  listSystemTemplate,
  ParamQueryListSharedDocument,
  Role
} from '../kie';
import { DocumentLayoutService } from '../services/document-layout.service';
import { isArray } from 'lodash';
import { UntypedFormBuilder, Validators } from '@angular/forms';

@Component({
  selector: 'app-shared-documents',
  templateUrl: './shared-documents.component.html',
  styleUrls: ['./shared-documents.component.scss']
})
export class SharedDocumentsComponent implements OnInit {
  dataSharedDocuments: ListSharedDocuments = {
    documents: [],
    page: 1,
    total: 0,
    limit: 10
  };

  readonly listSystemTemplate = [
    ...listSystemTemplate,
    {
      title: 'Văn bản tự tạo',
      id: 'isSelfConfig',
      icon: 'assets/kie/document/icon-documents/ho_so_benh_binh.svg'
    }
  ];

  listUser: User[] = [];

  readonly listRoles = [
    { id: 'all', name: 'Tất cả vai trò', value: [Role.Viewer, Role.Editor] },
    { id: Role.Viewer, name: 'Người xem', value: [Role.Viewer] },
    { id: Role.Editor, name: 'Người chỉnh sửa', value: [Role.Editor] }
  ];

  private creatorSearchSubject = new Subject<string>();
  filterForm = this.formBuilder.group({
    limit: this.formBuilder.control(10, [
      Validators.required,
      Validators.max(30),
      Validators.min(10)
    ]),
    page: this.formBuilder.control(1, [Validators.required, Validators.min(1)]),
    creatorIds: this.formBuilder.control([]),
    roleId: this.formBuilder.control('all'),
    templateTypes: this.formBuilder.control(null)
  });
  private destroy$ = new Subject<void>();

  constructor(
    private kieService: KIEService,
    private toastr: ToastrService,
    private documentLayoutService: DocumentLayoutService,
    private userService: UserService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.documentLayoutService.selectedDocumentId$.next('shared');
    this.fetchSharedDocument$().subscribe();

    this.filterForm.valueChanges
      .pipe(takeUntil(this.destroy$), switchMap(this.fetchSharedDocument$.bind(this)))
      .subscribe();

    this.creatorSearchSubject
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Wait for 300ms pause in events
        distinctUntilChanged(),
        filter((text) => !!text.trim()),
        switchMap((text) =>
          this.userService.searchUser(text).pipe(
            tap((res) => {
              this.listUser = res.users;
            }),
            catchError((err) => {
              this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
              return EMPTY;
            })
          )
        )
      )
      .subscribe();
  }

  fetchSharedDocument$() {
    if (this.filterForm.invalid) {
      console.log('invalid filterForm', this.filterForm);
      return EMPTY;
    }
    const filters = Object.assign({}, this.filterForm.value);
    filters['role'] = this.listRoles.find((item) => item.id === filters['roleId'])[
      'value'
    ];
    delete filters.roleId;

    let params = new HttpParams();
    for (const key in filters) {
      if (!filters[key]) continue;
      if (isArray(filters[key]))
        filters[key].forEach((value) => {
          params = params.append(`${key}[]`, value);
        });
      else params = params.append(key, filters[key]);
    }
    return this.kieService.getListSharedDocuments(params).pipe(
      tap((res) => {
        this.dataSharedDocuments = res.data;
      }),
      catchError((err) => {
        this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
        return EMPTY;
      })
    );
  }

  handleChangePage(page: number) {
    this.filterForm.patchValue({ page });
  }

  handleChangeLimit(limit: number) {
    this.filterForm.patchValue({ limit, page: 1 });
  }

  searchUser(text: string): void {
    this.creatorSearchSubject.next(text);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
