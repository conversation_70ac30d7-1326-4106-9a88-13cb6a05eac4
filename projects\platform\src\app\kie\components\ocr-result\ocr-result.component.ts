import {
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  TemplateRef
} from '@angular/core';
import { DocumentField, FieldType, File, excludedKeyFields } from '@platform/app/kie/kie';
import { get, isEqual } from 'lodash';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';

enum ScoreBgColor {
  Low = '#FF3355',
  Medium = '#FFA100',
  High = '#009B4E'
}
@Component({
  selector: 'app-ocr-result',
  templateUrl: './ocr-result.component.html',
  styleUrls: ['./ocr-result.component.scss']
})
export class OcrResultComponent implements OnInit, OnChanges {
  readonly ScoreBgColor = ScoreBgColor;

  /* extract-file => with initialValue */
  /* default => readonly ocr data, without initialValue */
  @Input()
  mode: 'extract-file' | 'default' = 'default'; // switch mode to render confidence_score or not

  /* this component ONLY displays ocr result */
  /* any action (view and logic) should be passed and handled from parent */
  @Input() actionsTemplate?: TemplateRef<any>;

  ocrResultFieldList: {
    order: number;
    isNew?: boolean;
    /*  */
    isVisible: boolean;
    type: FieldType;
    name: string;
    color: string;
    text?: string;
    score?: string;
    scoreBgColor?: ScoreBgColor;
    isEdited?: boolean;
    initialValue?: string;
    list?: {
      text: string;
      score?: string;
      scoreBgColor?: string;
      isEdited?: boolean;
      initialValue?: string;
    }[];
    table?: {
      isEdited?: boolean;
      initialValue?: string;
      headers: { colId: string; label?: string }[];
      rows: {
        [colId: string]: {
          text: string;
          isEdited?: boolean;
          initialValue?: string;
        };
      }[];
    };
  }[] = [];

  // ocrResultChangeCount = 0;
  private _ocrResult: File['ocrResult'];
  @Input()
  set ocrResult(ocrResult: File['ocrResult']) {
    // console.log(++this.ocrResultChangeCount, { old: this.ocrResult, new: ocrResult });
    this._ocrResult = ocrResult;
    if (!ocrResult) {
      this.ocrResultFieldList = []; // reset
      return;
    }
    this.ocrResultFieldList = Object.entries(ocrResult)
      .filter(([key, field]) => !excludedKeyFields.includes(key) && field)
      .map(([key, field]: [string, DocumentField]) => {
        let data: (typeof this.ocrResultFieldList)[number] = {
          name: get(field, 'config.name', key),
          type: field.type,
          color: get(field, 'config.extraConfig.color', ScoreBgColor.High),
          order: get(field, 'config.extraConfig.order', 0),
          isNew: get(field, 'config.extraConfig.isNew'),
          isVisible:
            this.mode === 'extract-file'
              ? get(field, 'config.extraConfig.is_visible', true)
              : true
        };

        switch (field.type) {
          case 'Field': {
            const initialValue = get(
              field,
              `${this.mode === 'extract-file' ? 'initialOcrResult.' : ''}text`
            );
            const isEdited = !isEqual(field.text, initialValue);

            data = {
              ...data,
              isEdited,
              initialValue,
              ...(isEdited
                ? {}
                : {
                    score: this.getScorePercent(field.confidence_score),
                    scoreBgColor: this.getScoreBgColor(field.confidence_score)
                  }),
              text: field.text
            };
            break;
          }
          case 'List': {
            data['list'] = field.cells.map((cell, cellId) => {
              const initialValue = get(
                field,
                `${this.mode === 'extract-file' ? 'initialOcrResult.' : ''}cells.${cellId}.text`
              );
              const isEdited = !isEqual(cell.text, initialValue);

              return {
                isEdited,
                initialValue,
                ...(isEdited
                  ? {}
                  : {
                      score: this.getScorePercent(cell.confidence_score),
                      scoreBgColor: this.getScoreBgColor(cell.confidence_score)
                    }),
                text: cell.text
              };
            });
            break;
          }
          case 'Table': {
            /* render by cols */
            const headers = field.columns.map((col) => {
              return { colId: col.name, label: col.name };
            });
            let isEdited = false;
            let rows = field.rows.map((row, rowId) => {
              const initialRowValue = get(
                field,
                `${this.mode === 'extract-file' ? 'initialOcrResult.' : ''}rows.${rowId}`
              );
              const tableRow = {};
              row.cells.forEach((cell, cellId) => {
                const initialCellValue = get(initialRowValue, `cells.${cellId}.text`);
                const isCellEdited = !isEqual(initialCellValue, cell.text);
                isCellEdited && (isEdited = true);
                tableRow[cell.name] = {
                  text: cell.text,
                  initialValue: initialCellValue,
                  isEdited: isCellEdited
                };
              });
              return tableRow;
            });
            if (!rows.length) rows = [null]; /* table no data case */
            data = {
              ...data,
              score: this.getScorePercent(field.confidence_score),
              scoreBgColor: this.getScoreBgColor(field.confidence_score),
              isEdited,
              table: { headers, rows }
            };
            break;
          }

          default:
            break;
        }
        return data;
      })
      .sort((a, b) => get(a, 'order', 0) - get(b, 'order', 0));
  }
  get ocrResult(): File['ocrResult'] {
    return this._ocrResult;
  }

  displayingTableField?: (typeof this.ocrResultFieldList)[number];
  tableDrawerHeight = 300;
  tableDrawerAnimationId = -1;

  constructor() {}

  ngOnInit(): void {}

  count = 0;
  ngOnChanges(changes: SimpleChanges): void {}

  showTableDrawer(field: (typeof this.ocrResultFieldList)[number]) {
    console.log(field);
    this.displayingTableField = field;
  }

  hideTableDrawer() {
    this.displayingTableField = null;
  }

  handleTableDrawerResize({ height }: NzResizeEvent): void {
    cancelAnimationFrame(this.tableDrawerAnimationId);
    this.tableDrawerAnimationId = requestAnimationFrame(() => {
      this.tableDrawerHeight = height!;
    });
  }

  private getScorePercent(score: number) {
    let value = (score * 100).toFixed(1);
    if (value.split('.').pop().charAt(0) === '0')
      value = value.slice(0, value.indexOf('.'));
    return value + '%';
  }

  private getScoreBgColor(score: number) {
    score *= 100;
    if (score >= 85) return ScoreBgColor.High;
    if (score >= 50) return ScoreBgColor.Medium;
    return ScoreBgColor.Low;
  }
}
