import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from './layout/layout.component';
import { LdpLayoutComponent } from './ldp-layout/ldp-layout.component';
import { UserInfoModalComponent } from './user-info-modal/user-info-modal.component';
import { ChangePasswordModalComponent } from './change-password-modal/change-password-modal.component';
import { RevokeConsentModalComponent } from './revoke-consent-modal/revoke-consent-modal.component';
import { RequestRemoveDataModalComponent } from './request-remove-data-modal/request-remove-data-modal.component';
import { RevokeConsentComponent } from './revoke-consent/revoke-consent.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { RouterModule } from '@angular/router';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { TranslocoModule } from '@ngneat/transloco';
import { OldLayoutComponent } from './old-layout/old-layout.component';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { DemoLayoutComponent } from './demo-layout/demo-layout.component';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { PlatformV2CountdownModalComponent } from './platform-v2-countdown-modal/platform-v2-countdown-modal.component';
import { PlatformV2InfoModalComponent } from './platform-v2-info-modal/platform-v2-info-modal.component';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';

@NgModule({
  declarations: [
    OldLayoutComponent,
    LayoutComponent,
    LdpLayoutComponent,
    DemoLayoutComponent,
    UserInfoModalComponent,
    ChangePasswordModalComponent,
    RevokeConsentModalComponent,
    RequestRemoveDataModalComponent,
    RevokeConsentComponent,
    PlatformV2CountdownModalComponent, // consider refactoring to other place
    PlatformV2InfoModalComponent // consider refactoring to other place
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NzPopoverModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzDropDownModule,
    NzCheckboxModule,
    TranslocoModule,
    NzDrawerModule,
    NzIconModule
  ]
  // exports: [LayoutComponent] // TODO: LayoutComponent NOT being export but still being able to use in AppRoutingModule
})
export class LayoutModule {}
