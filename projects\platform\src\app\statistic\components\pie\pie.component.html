<div
  class="col-span-full xl:col-span-1 bg-white rounded-lg shadow-[0px_4px_12px_0px_rgba(64,75,68,0.05)] p-6 flex gap-6 flex-col justify-between items-center"
>
  <div class="max-w-[300px] max-h-[300px] relative">
    <canvas
      baseChart
      class="chart"
      [data]="doughnutChartData"
      [options]="doughnutChartOptions"
      [type]="'doughnut'"
    >
    </canvas>
    <div
      class="flex flex-col items-center gap-2 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
    >
      <img class="w-12 h-12" [src]="centerIcon" alt="" />
      <span class="font-bold text-xl text-[#111127]">{{ getTotal() }} {{ unit }}</span>
      <span class="font-bold text-xs text-[#757575]">TỔNG CỘNG</span>
    </div>
  </div>
  <div class="w-full flex flex-col gap-6">
    <div *ngFor="let item of dataset">
      <div class="flex gap-[10px] items-center text-[#111127] font-semibold mb-2">
        <div>{{ item.label }}</div>
        <img
          *ngIf="item.tooltip"
          src="assets/statistic/tooltip.svg"
          alt="tooltip"
          nz-tooltip
          [nzTooltipTitle]="item.tooltip"
          nzTooltipPlacement="right"
          nzTooltipOverlayClassName="statistic-tooltip"
        />
        <div class="flex-1 text-right font-bold">{{ item.value }} {{ unit }}</div>
      </div>
      <div class="h-[6px] w-full bg-[#EEEEEE] rounded-[16px]">
        <div
          [ngStyle]="{
            width: getPercentage(item.key) + '%',
            'background-color': item.color
          }"
          class="h-full rounded-[16px]"
        ></div>
      </div>
    </div>
  </div>
</div>
