import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import UtilsService from '@platform/app/core/services/utils.service';
import { environment as env } from '@platform/environment/environment';

@Injectable({
  providedIn: 'root'
})
export class StatisticService {
  idgBackendUrl = `${env.backendUrl}idg-api/report-service/page`;
  kieStatisticUrl = `${env.backendUrl}key-information-extractor/statistic`;

  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  fetchPageStatisticStatus(dateFilter: string) {
    return this.http.get<{
      message: string;
      object: { totalPage: number; success: number; invalid: number };
    }>(`${this.idgBackendUrl}/status`, {
      headers: this.utilsService.headers,
      params: { time: dateFilter }
    });
  }

  fetchPageStatisticByTime(dateFilter: string) {
    return this.http.get<{
      message: string;
      object: { time: string; page: number }[];
    }>(`${this.idgBackendUrl}/time`, {
      headers: this.utilsService.headers,
      params: { time: dateFilter }
    });
  }

  fetchPageStatisticByAPI(dateFilter: string) {
    return this.http.get<{
      message: string;
      object: {
        api: string;
        total: number;
        success: number;
        invalid: number;
      }[];
    }>(`${this.idgBackendUrl}/api`, {
      headers: this.utilsService.headers,
      params: { time: dateFilter }
    });
  }

  fetchUserDocumentCount(dateFilter: string) {
    return this.http.get<{ status: number; data: { owned: number; shared: number } }>(
      `${this.kieStatisticUrl}/document-count-total`,
      {
        headers: this.utilsService.headers,
        params: { time: dateFilter }
      }
    );
  }

  fetchUserDocumentStatByTime(dateFilter: string) {
    return this.http.get<{
      status: number;
      data: { document: { owned?: number; shared?: number }; time: number | string }[];
    }>(`${this.kieStatisticUrl}/document-count-by-time`, {
      headers: this.utilsService.headers,
      params: { time: dateFilter }
    });
  }

  fetchOwnedDocumentStat(dateFilter: string, documentTypeFilter?: string) {
    const params = { time: dateFilter };
    if (documentTypeFilter) params['type'] = documentTypeFilter;
    return this.http.get<{
      status: number;
      data: {
        id: string;
        name: string;
        createdAt: string;
        fileCount: number;
        sharedUserCount: number;
        isSelfConfig: boolean;
      }[];
    }>(`${this.kieStatisticUrl}/owned-document`, {
      headers: this.utilsService.headers,
      params
    });
  }

  fetchSharedDocumentStat(dateFilter: string, documentTypeFilter?: string) {
    const params = { time: dateFilter };
    if (documentTypeFilter) params['type'] = documentTypeFilter;
    return this.http.get<{
      status: number;
      data: {
        id: string;
        name: string;
        sharedAt: string;
        myRole: string;
        owner: string;
        fileCount: number;
        isSelfConfig: boolean;
      }[];
    }>(`${this.kieStatisticUrl}/shared-document`, {
      headers: this.utilsService.headers,
      params
    });
  }
}
