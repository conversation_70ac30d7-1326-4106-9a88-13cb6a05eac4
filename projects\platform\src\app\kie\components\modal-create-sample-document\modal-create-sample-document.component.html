<div class="border-b border-line flex justify-between gap-4 items-center p-4">
  <span class="font-semibold">T<PERSON>o mớ<PERSON> văn bản</span>
  <img
    src="assets/kie/document/cancel.svg"
    class="p-2 pr-0 cursor-pointer"
    (click)="handleCancel()"
  />
</div>
<div class="flex flex-col items-center gap-4 p-4">
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="name-folder"
      >Tên văn bản <span class="text-red-500">*</span></label
    >
    <input
      [autofocus]="true"
      id="name-folder"
      nzSize="large"
      nz-input
      placeholder="Tên văn bản"
      class="rounded-lg"
      [(ngModel)]="nameDocument"
    />
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="folderSelect"
      >T<PERSON><PERSON> mục <span class="text-red-500">*</span></label
    >
    <nz-select
      id="folderSelect"
      nzSize="large"
      [(ngModel)]="selectedFolderId"
      nzPlaceHolder="Chọn một thư mục"
    >
      <nz-option
        *ngFor="let folder of listFolders"
        [nzValue]="folder.id"
        [nzLabel]="folder.name"
      ></nz-option>
    </nz-select>
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="configTemplate"
      >Config template <span class="text-red-500">*</span></label
    >
    <nz-select
      id="configTemplate"
      nzSize="large"
      [(ngModel)]="selectedConfig"
      nzPlaceHolder="Chọn một thư mục"
    >
      <nz-option
        *ngFor="let config of configs"
        [nzValue]="config.value"
        [nzLabel]="config.label"
      ></nz-option>
    </nz-select>
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="upload"
      >Tải file <span class="text-red-500">*</span></label
    >
    <app-upload-files
      [fileList]="fileList"
      (onUploadFilesChange)="handleUploadFilesChange($event)"
    ></app-upload-files>
  </div>
</div>

<div class="border-t border-line flex justify-center items-center p-4">
  <button
    (click)="createNewSelfDocument()"
    class="flex items-center gap-2 rounded-lg bg-brand-1 text-white font-medium px-4 py-2 hover:bg-brand-2 focus:bg-brand-2"
  >
    <i nz-icon nzType="plus" nzTheme="outline"></i>
    Tạo mới
  </button>
</div>
