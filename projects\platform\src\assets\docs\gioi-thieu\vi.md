# Đối tượng sử dụng

Tài liệu dành cho AI Partner - các đơn vị muốn tích hợp VNPT Smart Reader vào sản phẩm để cung cấp dịch vụ Nhận dạng ký tự quang học (OCR) và Bóc tách thông tin trong văn bản (KIE) cho khách hàng cuối.

# Dịch vụ cung cấp

<table>
<colgroup>
<col style="width: 20%;">
<col>
<col style="width: 20%;">
</colgroup>
<thead>
<tr>
<th><strong>Công nghệ</strong></th>
<th><strong><PERSON><PERSON> tả</strong></th>
<th><strong>Dịch vụ cung cấp</strong></th>
</tr>
</thead>
<tbody><tr>
<td>Nhận dạng ký tự quang học (OCR)</td>
<td>Chuyển đổi toàn bộ thông tin dạng hình ảnh chữ đánh máy trong văn bản thành dạng text có thể chỉnh sửa được và cho biết vị trí của đoạn text này trong văn bản.</td>
<td>• OCR cơ bản<br>• OCR nâng cao</td>
</tr>
<tr>
<td>Bóc tách thông tin trong văn bản (KIE)</td>
<td>Dựa trên các thông tin dạng text (đã được OCR), tìm kiếm và bóc tách các trường thông tin theo yêu cầu của từng loại văn bản. VNPT Smart Reader hỗ trợ bóc tách thông tin cho tài liệu đầu vào là file scan và ảnh chụp từ điện thoại.</td>
<td></td>
</tr>
<tr>
<td></td>
<td><strong>Thư viện bóc tách</strong>: nền tảng cung cấp cho người dùng một thư viện các mẫu văn bản với các trường thông tin đã được cấu hình sẵn, giúp người dùng nhanh chóng bóc tách thông tin với độ chính xác cao.</td>
<td>Danh sách các mẫu văn bản có sẵn trong thư viện bóc tách xem tại Bảng 1</td>
</tr>
<tr>
<td></td>
<td><strong>Xây dựng mẫu bóc tách mới</strong>: Đối với các văn bản đặc thù không có trong thư viện, nền tảng cung cấp cho người dùng chức năng tự xây dựng mô hình bóc tách cho nhiều loại văn bản, giúp cho người dùng chủ động sử dụng với các nguồn tài liệu hiện có. Người dùng sau khi thực hiện xây dựng mô hình bóc tách văn bản có thể sử dụng trực tiếp trên platform hoặc tương tác qua API.</td>
<td>Tự xây dựng mẫu bóc tách văn bản</td>
</tr>
<tr>
<td>Document understanding (LLM)</td>
<td>Sử dụng mô hình ngôn ngữ lớn để nâng cao khả năng hiểu và xử lý ngữ cảnh của văn bản, hỗ trợ tự động phân loại, tổng hợp thông tin, chuẩn hóa dữ liệu và trả lời câu hỏi dựa trên nội dung tài liệu.</td>
<td>Đang phát triển</td>
</tr>
</tbody></table>

**Bảng 1: Danh sách các mẫu văn bản có sẵn trong thư viện bóc tách:**

<table>
<colgroup>
<col style="width: 10%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>STT</strong></th>
<th><strong>Mẫu văn bản</strong></th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>Bản trích lục hồ sơ bệnh binh</td>
</tr>
<tr>
<td>2</td>
<td>Bằng tốt nghiệp cấp 3</td>
</tr>
<tr>
<td>3</td>
<td>Giấy khai sinh</td>
</tr>
<tr>
<td>4</td>
<td>Giấy nộp tiền</td>
</tr>
<tr>
<td>5</td>
<td>Giấy phép xây dựng</td>
</tr>
<tr>
<td>6</td>
<td>Giấy tờ tùy thân</td>
</tr>
<tr>
<td>7</td>
<td>Giấy đăng ký hộ kinh doanh</td>
</tr>
<tr>
<td>8</td>
<td>Giấy đăng ký kinh doanh</td>
</tr>
<tr>
<td>9</td>
<td>Giấy đăng ký kết hôn</td>
</tr>
<tr>
<td>10</td>
<td>Hoá đơn bán hàng</td>
</tr>
<tr>
<td>11</td>
<td>Hoá đơn giá trị gia tăng</td>
</tr>
<tr>
<td>12</td>
<td>Phiếu thi</td>
</tr>
<tr>
<td>13</td>
<td>Phiếu xuất kho</td>
</tr>
<tr>
<td>14</td>
<td>Sơ yếu lý lịch</td>
</tr>
<tr>
<td>15</td>
<td>Sổ đỏ</td>
</tr>
<tr>
<td>16</td>
<td>Thẻ đảng viên</td>
</tr>
<tr>
<td>17</td>
<td>Văn bản hành chính</td>
</tr>
<tr>
<td>18</td>
<td>Văn bản hành chính (Bản thêm trường thông tin)</td>
</tr>
</tbody></table>

# Yêu cầu đầu vào

<table>
<colgroup>
<col style="width: 25%;">
<col>
</colgroup>
<thead>
<tr>
<th><strong>Loại tài liệu đầu vào</strong></th>
<th><strong>Yêu cầu</strong></th>
</tr>
</thead>
<tbody><tr>
<td>Chung</td>
<td>• Văn bản bao gồm chữ in, không có chữ viết tay, chữ trong văn bản phải đảm bảo có thể nhìn thấy và nhận diện được dễ dàng bằng mắt thường<br>• Đối với văn bản có bảng, yêu cầu bảng phải có đủ hàng, đủ cột, đủ dòng kẻ và bảng nằm trong 1 trang, đường kẻ không bị méo<br>• Văn bản dạng scan/ảnh không bị mờ nhòe, không bị mất góc, không bị loá sáng, không quá tối<br>• Độ tương phản cao giữa chữ và nền (chữ đen trên nền trắng là tốt nhất)<br>• Font chữ rõ ràng, tránh font chữ nghệ thuật, không phải chữ viết tay<br>• Cỡ chữ tối thiểu khoảng 10pt trở lên (tốt nhất ≥ 12pt)</td>
</tr>
<tr>
<td>Tài liệu scan (dạng PDF)</td>
<td>• Độ phân giải (DPI): ≥ 150 DPI</td>
</tr>
<tr>
<td>Tài liệu ảnh (dạng JPG, JPEG, PNG, HEIC)</td>
<td>• Văn bản dạng ảnh không được chụp gián tiếp từ một thiết bị khác, có độ phân giải nhỏ nhất là Full HD (1920×1280) với chiều cao của ảnh tối thiểu là 1280</td>
</tr>
</tbody></table>