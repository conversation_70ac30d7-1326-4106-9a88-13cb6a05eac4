{"name": "smartreader", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^16.2.14", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/localize": "^16.2.12", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@ngneat/transloco": "^4.3.0", "@ngx-translate/core": "^14.0.0", "@popperjs/core": "^2.10.2", "angular-oauth2-oidc": "^16.0.0", "angular-oauth2-oidc-jwks": "^16.0.0", "chart.js": "^3.6.0", "cheerio": "^1.0.0-rc.12", "date-fns": "^2.30.0", "docx": "^7.6.0", "exceljs": "^4.4.0", "exif-auto-rotate": "^0.3.5", "fabric": "^5.5.2", "file-saver": "^2.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "ng-zorro-antd": "^16.2.2", "ng2-charts": "^3.0.11", "ng2-pdf-viewer": "^10.3.4", "ngx-captcha": "^13.0.0", "ngx-markdown": "^16.0.0", "ngx-spinner": "^16.0.2", "ngx-toastr": "^17.0.2", "node-forge": "^1.3.1", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.6.82", "prismjs": "^1.29.0", "rxjs": "~7.4.0", "tslib": "^2.3.0", "xlsx": "^0.18.2", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.16", "@angular/cli": "^16.2.16", "@angular/compiler-cli": "^16.2.12", "@types/fabric": "^5.3.10", "@types/file-saver": "^2.0.7", "@types/jasmine": "~3.10.0", "@types/lodash": "^4.14.178", "@types/node-forge": "^1.0.4", "autoprefixer": "^10.4.15", "jasmine-core": "~3.10.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "postcss": "^8.4.29", "prettier": "3.2.4", "tailwindcss": "^3.3.3", "typescript": "~5.1.6"}}