import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OcrInputComponent } from './ocr-input/ocr-input.component';
import { OcrResultComponent } from './ocr-result/ocr-result.component';
import { DongHoNuocComponent } from './dong-ho-nuoc/dong-ho-nuoc.component';

const routes: Routes = [
  { path: '', component: OcrInputComponent },
  { path: 'result', component: OcrResultComponent },
  { path: 'demo-dong-ho-nuoc', component: DongHoNuocComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OcrExperienceRoutingModule {}
