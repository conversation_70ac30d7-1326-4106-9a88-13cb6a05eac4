import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OCRRoutingModule } from './ocr-routing.module';
import { DocumentViewerComponent } from '@platform/app/components/document-viewer/document-viewer.component';
import { DocumentReproductionComponent } from '@platform/app/components/document-reproduction/document-reproduction.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { OcrLayoutComponent } from './components/ocr-layout/ocr-layout.component';
import { OcrPreprocessingComponent } from './ocr-preprocessing/ocr-preprocessing.component';
import { OcrInProgressComponent } from './ocr-in-progress/ocr-in-progress.component';
import { OcrFinishedComponent } from './ocr-finished/ocr-finished.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ExportModalComponent } from './components/export-modal/export-modal.component';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { OcrResultComponent } from './components/ocr-result/ocr-result.component';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

@NgModule({
  declarations: [
    OcrLayoutComponent,
    OcrPreprocessingComponent,
    OcrInProgressComponent,
    OcrFinishedComponent,
    ExportModalComponent,
    OcrResultComponent
  ],
  imports: [
    CommonModule,
    OCRRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NzInputModule,
    NzTabsModule,
    NzTableModule,
    NzPaginationModule,
    NzSelectModule,
    NzToolTipModule,
    NzCheckboxModule,
    NzIconModule,
    NzProgressModule,
    NzBadgeModule,
    DocumentViewerComponent,
    NgxSpinnerModule,
    DocumentReproductionComponent,
    NzRadioModule,
    NzPopoverModule
  ]
})
export class OCRModule {}
