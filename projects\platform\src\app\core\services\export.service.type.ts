import { PageOrientation } from 'docx';

export type FontStyle = 'bold' | 'normal' | 'italic';

export type Bbox = [RelativeUnit, RelativeUnit, RelativeUnit, RelativeUnit];

/* Type definition describes input OCR Result */
export type OcrResult = {
  object: {
    num_of_pages: number;
    phrases: {
      cells: {
        text: string;
        font_styles: FontStyle[];
        type: 'Phrase' | 'Header' | 'Title' | 'Footer' | 'Footnote';
        bboxes: { [key: number]: Bbox };
        paragraph_id?: number;
        line_id?: number;
        /* ... */
      }[];
    }[];
    paragraphs: {
      cells: {
        text: string;
        type:
          | 'Paragraph'
          | 'Table'
          | 'Figure'
          | 'Header'
          | 'Title'
          | 'Footer'
          | 'Footnote';
        bboxes: { [key: number]: Bbox };
        page_id: number;
        paragraph_id: number;
        /* Type Table Only */
        columns?: {
          cells: {
            text: string;
            font_styles: FontStyle[];
            bboxes: { [key: number]: Bbox };
          }[];
        }[];
        rows?: {
          cells: {
            text: string;
            font_styles: FontStyle[];
            bboxes: { [key: number]: Bbox };
          }[];
        }[];
        /* ... */
        html?: string;
        cells?: {
          bboxes: { [key: number]: Bbox };
          font_styles: FontStyle[];
          text: string;
        }[];
      }[];
    }[];
    lines: {
      cells: {
        text: string;
        type: 'Phrase' | 'Header' | 'Title' | 'Footer' | 'Footnote';
        paragraph_id: number;
        line_id: number;
        bboxes: { [key: number]: Bbox };
      }[];
    }[];
  };
};

/* Type definition used within exporting docx logic, extended from input OCR Result */
export type Phrase = OcrResult['object']['phrases'][number]['cells'][number];

export type Line = {
  phrases: Phrase[];
  bboxes: { [key: number]: Bbox };
  text: string;
};

export type Paragraph = OcrResult['object']['paragraphs'][number]['cells'][number] & {
  lines: Line[];
};

export type PagesDimension = {
  // original dimension
  width: Pixel;
  height: Pixel;
  orientation: PageOrientation;
  // fixed A4 dimension
  A4Width: Pixel; // x-axis, horizontal wise
  A4Height: Pixel; // y-axis, vertical wise
}[];

export type RenderedPageImage = {
  base64Url: string;
  renderScale: number;
};

export type PageMargin = {
  leftMargin: RelativeUnit;
  rightMargin: RelativeUnit;
  topMargin: RelativeUnit;
  bottomMargin: RelativeUnit;
};

/* Scale unit */
export type RelativeUnit = number; // 0 <-> 1
export type Twip = number;
export type Pixel = number;
export type Inch = number;
export type Point = number;
export type HalfPoint = number;

export const supportedFileTypeList = ['pdf', 'jpg', 'jpeg', 'png'] as const;

export type SupportedFileType = (typeof supportedFileTypeList)[number];

export const supportedMimetypeList = [
  'application/octet-stream',
  'application/pdf',
  'image/jpeg',
  'image/png'
] as const;

export type SupportedMimetype = (typeof supportedMimetypeList)[number];
