import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-change-document-name',
  templateUrl: './modal-change-document-name.component.html',
  styleUrls: ['./modal-change-document-name.component.scss']
})
export class ModalChangeDocumentNameComponent implements OnInit {
  readonly nzModalData: { document: Folder['documents'][number] } = inject(NZ_MODAL_DATA);
  document = this.nzModalData.document;
  newDocumentName: string;

  constructor(
    private kieService: KIEService,
    private toastr: ToastrService,
    private modalRef: NzModalRef,
    private documentLayoutService: DocumentLayoutService
  ) {}

  ngOnInit(): void {
    this.newDocumentName = this.document.name;
  }

  cancel() {
    this.modalRef.close();
  }

  save() {
    if (this.newDocumentName.trim().length < 1)
      return this.toastr.error('Tên văn bản không được để trống');

    if (this.newDocumentName.trim().length > 255)
      return this.toastr.error('Tên văn bản không được quá 255 ký tự');

    for (const folder of this.documentLayoutService.folders) {
      if (
        folder.documents.findIndex((doc) => doc.id === this.document.id) !== -1 &&
        folder.documents
          .filter((doc) => doc.id !== this.document.id)
          .map((doc) => doc.name.trim())
          .includes(this.newDocumentName.trim())
      )
        return this.toastr.error('Tên văn bản đã tồn tại!');
    }

    this.kieService
      .updateDocumentDetail(this.document.id, {
        name: this.newDocumentName
      })
      .pipe(
        tap(() => {
          this.toastr.success('Đổi tên văn bản thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
