import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalConfigFiltersForFoldersListComponent } from './modal-config-filters-for-folders-list.component';

describe('ModalConfigFiltersForFoldersListComponent', () => {
  let component: ModalConfigFiltersForFoldersListComponent;
  let fixture: ComponentFixture<ModalConfigFiltersForFoldersListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalConfigFiltersForFoldersListComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalConfigFiltersForFoldersListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
