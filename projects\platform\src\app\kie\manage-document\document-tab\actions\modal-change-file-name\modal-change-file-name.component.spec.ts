import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalChangeFileNameComponent } from './modal-change-file-name.component';

describe('ModalChangeFileNameComponent', () => {
  let component: ModalChangeFileNameComponent;
  let fixture: ComponentFixture<ModalChangeFileNameComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalChangeFileNameComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalChangeFileNameComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
