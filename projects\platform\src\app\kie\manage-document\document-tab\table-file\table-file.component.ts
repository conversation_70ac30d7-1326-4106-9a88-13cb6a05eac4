import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  TemplateRef
} from '@angular/core';
import { Router } from '@angular/router';
import { ExportService } from '@platform/app/core/services/export.service';
import { KIEService } from '@platform/app/core/services/kie.service';
import { File, FileProps, FileStatus, ListFile } from '@platform/app/kie/kie';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import {
  catchError,
  concatMap,
  delay,
  filter,
  finalize,
  from,
  map,
  take,
  tap,
  throwError,
  toArray
} from 'rxjs';
import { ModalChangeFileNameComponent } from '../actions/modal-change-file-name/modal-change-file-name.component';
import { ModalDeleteFileComponent } from '../actions/modal-delete-file/modal-delete-file.component';
import { get, isArray } from 'lodash';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import UtilsService from '@platform/app/core/services/utils.service';

@Component({
  selector: 'app-table-file',
  templateUrl: './table-file.component.html',
  styleUrls: ['./table-file.component.scss']
})
export class TableFileComponent implements OnInit, OnChanges {
  _documentId: string;
  @Input()
  set documentId(id: string) {
    this._documentId = id;
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }
  get documentId(): string {
    return this._documentId;
  }
  @Input() roleInDocument: 'creator' | 'editor' | 'viewer';
  @Input() listFiles: ListFile;
  @Input() uploadBtn?: TemplateRef<any>;
  @Output() pageChange: EventEmitter<number> = new EventEmitter<number>();
  @Output() limitChange: EventEmitter<number> = new EventEmitter<number>();
  @Output() fileActionSuccess: EventEmitter<void> = new EventEmitter<void>();

  checked = false;
  indeterminate = false;
  listOfCurrentPageData: readonly FileProps[] = [];
  setOfCheckedId = new Set<string>();

  constructor(
    private modal: NzModalService,
    private router: Router,
    private kieService: KIEService,
    private exportService: ExportService,
    private toastr: ToastrService,
    private documentLayoutService: DocumentLayoutService,
    private utils: UtilsService
  ) {}

  ngOnInit(): void {}

  ngOnChanges(): void {}

  // handler pagination
  onPageChange(pageIndex: number): void {
    this.pageChange.emit(pageIndex);
  }

  onLimitChange(limit): void {
    this.limitChange.emit(limit);
  }

  // handler active list checkbox
  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onCurrentPageDataChange(listOfCurrentPageData: readonly FileProps[]): void {
    this.listOfCurrentPageData = listOfCurrentPageData;
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    const listOfEnabledData = this.listOfCurrentPageData.filter(
      ({ disabled }) => !disabled
    );
    if (!listOfEnabledData.length) {
      this.checked = false;
      this.indeterminate = false;
      return;
    }
    this.checked = listOfEnabledData.every(({ id }) => this.setOfCheckedId.has(id));
    this.indeterminate =
      listOfEnabledData.some(({ id }) => this.setOfCheckedId.has(id)) && !this.checked;
  }

  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.listOfCurrentPageData
      .filter(({ disabled }) => !disabled)
      .forEach(({ id }) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  uncheckAll() {
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }

  viewFileDetail(fileId) {
    if (!this.checkActionPermission('view')) return;
    this.router.navigate(['key-information-extractor', 'file', fileId]);
  }

  downloadExportOcrResult(file: File) {
    if (!this.checkActionPermission('download')) return;
    if (file.status === FileStatus.NotAccepted)
      return this.toastr.warning('Vui lòng xác nhận văn bản trước khi tải xuống');

    this.kieService
      .getFileDetail(file.id)
      .pipe(
        map((resp) => resp.data.ocrResult),
        tap((ocrResult) => this.exportService.exportXlsxTemplateOcr(ocrResult, file.name))
      )
      .subscribe();
  }

  downloadExportOcrResultMany() {
    if (!this.checkActionPermission('download')) return;

    const listFileIds = Array.from(this.setOfCheckedId);
    if (!listFileIds.length) return;
    if (
      listFileIds.some(
        (fileId) =>
          /* filter out any NotAccepted file in current page listFiles.files first */
          this.listFiles.files.findIndex(
            (file) => file.id === fileId && file.status === FileStatus.NotAccepted
          ) !== -1
      )
    )
      return this.toastr.warning(
        'Vui lòng chỉ chọn những tài liệu ở trạng thái đã xác nhận trước khi tải xuống'
      );

    const listFileIdsInCurrentPage = listFileIds.filter((id) =>
      this.listFiles.files.map((file) => file.id).includes(id)
    );
    const listFileIdsNotInCurrentPage = listFileIds.filter(
      (id) => !listFileIdsInCurrentPage.includes(id)
    );

    from(listFileIdsNotInCurrentPage.concat(listFileIdsInCurrentPage))
      .pipe(
        concatMap((fileId, index) =>
          this.kieService.getFileDetail(fileId).pipe(
            map((resp) => {
              if (resp.data.status === FileStatus.NotAccepted) {
                this.toastr.warning(
                  'Vui lòng chỉ chọn những tài liệu ở trạng thái đã xác nhận trước khi tải xuống'
                );
                throw new Error(`file ${resp.data.name}'s status is not_accepted`);
              }
              return resp.data;
            }),
            tap(() => setTimeout(() => this.utils.toggleAppLoadingSpinner(true))), // keep showing spinner for the delay(300) below in each getFileDetail() request
            delay(300),
            catchError((err) => {
              console.log(err);
              return throwError(() => err); // rethrow error to stop execution at that point
            })
          )
        ),
        toArray(), // accumulate all files detail
        tap((listFilesDetail) => {
          listFilesDetail.sort(
            (f1, f2) =>
              new Date(f2.createdAt).valueOf() - new Date(f1.createdAt).valueOf()
          );
          let exportFileName = 'Tổng hợp kết quả OCR';
          this.documentLayoutService.folders.some((folder) => {
            const document = folder.documents.find((doc) => doc.id === this.documentId);
            if (!document) return false;
            exportFileName = document.name + ' - ' + exportFileName;
            return true;
          });
          this.exportService.exportXlsxTemplateOcrMergeMany(
            listFilesDetail.map((file) => ({
              name: file.name,
              ocrResult: file.ocrResult
            })),
            exportFileName
          );
        }),
        finalize(() => setTimeout(() => this.utils.toggleAppLoadingSpinner(false, true))) // force turn off last ongoing spinner
      )
      .subscribe();
  }

  changeFileName(file: File) {
    if (!this.checkActionPermission('change-name')) return;
    const modal = this.modal.create({
      nzTitle: 'Đổi tên',
      nzContent: ModalChangeFileNameComponent,
      nzData: { file },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '16px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        tap((result) => {
          if (!!result) this.fileActionSuccess.next();
        })
      )
      .subscribe();
  }

  deleteFile(file: File) {
    if (!this.checkActionPermission('delete')) return;
    const modal = this.modal.create({
      nzTitle: null,
      nzContent: ModalDeleteFileComponent,
      nzData: { files: [file] },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => isArray(result) && result.length > 0),
        tap(() => {
          this.fileActionSuccess.next();
          this.updateCheckedSet(file.id, false);
          this.refreshCheckedStatus();
        })
      )
      .subscribe();
  }

  deleteFileMany() {
    if (!this.checkActionPermission('delete')) return;
    const selectedFileIds = Array.from(this.setOfCheckedId);
    if (!selectedFileIds.length) return;
    const modal = this.modal.create({
      nzTitle: null,
      nzContent: ModalDeleteFileComponent,
      nzData: { files: selectedFileIds.map((id) => ({ id })) },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => isArray(result) && result.length > 0),
        tap((result) => {
          this.fileActionSuccess.next();
          result.forEach((fileId) => this.updateCheckedSet(fileId, false));
          this.refreshCheckedStatus();
        })
      )
      .subscribe();
  }

  checkActionPermission(action: 'view' | 'change-name' | 'download' | 'delete') {
    switch (this.roleInDocument) {
      case 'creator':
        return true;

      case 'editor': {
        return true;
      }

      case 'viewer': {
        return ['view', 'download'].includes(action);
      }
    }
  }
}
