import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import {
  Observable,
  Subject,
  debounceTime,
  delay,
  exhaustMap,
  filter,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs';
import { ModalDeleteDocumentComponent } from './actions/modal-delete-document/modal-delete-document.component';
import { ModalChangeDocumentNameComponent } from './actions/modal-change-document-name/modal-change-document-name.component';
import { ModalMoveDocumentComponent } from './actions/modal-move-document/modal-move-document.component';
import { ModalCopyDocumentComponent } from './actions/modal-copy-document/modal-copy-document.component';
import { ModalChangeFolderNameComponent } from './actions/modal-change-folder-name/modal-change-folder-name.component';
import { ModalDeleteFolderComponent } from './actions/modal-delete-folder/modal-delete-folder.component';
import { CdkScrollable, ScrollDispatcher } from '@angular/cdk/scrolling';
import { ModalConfigFiltersForFoldersListComponent } from './actions/modal-config-filters-for-folders-list/modal-config-filters-for-folders-list.component';
import { sortBy } from 'lodash';

@Component({
  selector: 'app-document-layout',
  templateUrl: './document-layout.component.html',
  styleUrls: ['./document-layout.component.scss']
})
export class DocumentLayoutComponent implements OnInit, OnDestroy {
  lastScrollTop = 0;
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private documentLayoutService: DocumentLayoutService,
    private modalService: NzModalService,
    private scrollDispatcher: ScrollDispatcher
  ) {}

  NAME_MAX_LENGTH = 30;
  folders: (Folder & { isActive: boolean })[] = [];
  foldersCount = 0;
  selectedDocumentId = null;
  isAscending: boolean = true;
  sharedDocumentsCount: number = 0;
  orderByLabel;
  orderValueLabel;
  destroyed$ = new Subject<void>();

  ngOnInit(): void {
    this.documentLayoutService.store$
      .pipe(
        takeUntil(this.destroyed$), // equal to manually unsubscribe() in ngOnDestroy(), prevent memory leak
        tap(({ folders, sharedDocumentsCount, foldersCount }) => {
          this.foldersCount = foldersCount;
          this.folders = folders.map((currentFolder) => {
            const prevFolderIndex = this.folders.findIndex(
              (item) => item.id === currentFolder.id
            );
            return {
              ...currentFolder,
              isActive:
                prevFolderIndex !== -1 ? this.folders[prevFolderIndex].isActive : false
            };
          });
          this.sharedDocumentsCount = sharedDocumentsCount;
        })
      )
      .subscribe();

    this.documentLayoutService.selectedDocumentId$
      .pipe(
        takeUntil(this.destroyed$), // equal to manually unsubscribe() in ngOnDestroy(), prevent memory leak
        delay(0), // fix NG0100: ExpressionChangedAfterItHasBeenCheckedError
        tap((value) => {
          this.selectedDocumentId = value;
          // active only folder with selected document
          this.folders.forEach((folder) => {
            const isActive = folder.documents.some((doc) => doc.id === value);
            folder.isActive = isActive;
          });
        })
      )
      .subscribe();

    this.scrollDispatcher
      .scrolled()
      .pipe(
        takeUntil(this.destroyed$), // equal to manually unsubscribe() in ngOnDestroy(), prevent memory leak
        filter((cdkScrollable: CdkScrollable) => {
          const scrollTop = cdkScrollable.getElementRef().nativeElement.scrollTop;
          const isScrollingDown = scrollTop > this.lastScrollTop;
          this.lastScrollTop = scrollTop;
          return (
            /* check target by id */
            cdkScrollable.getElementRef().nativeElement.id === 'infinite-scroll-target' &&
            /* check is target is scrolling down */
            isScrollingDown &&
            /* check if the scrolling is almost reached to bottom */
            cdkScrollable.measureScrollOffset('bottom') === 0
          );
        }),
        debounceTime(300),
        exhaustMap(
          () =>
            /* only load more with already set filters */
            this.documentLayoutService.loadFolders({
              loadStrategy: 'fetch-next-page',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();

    this.documentLayoutService.loadFoldersFilters$
      .pipe(
        takeUntil(this.destroyed$),
        tap(({ orderBy, orderValue }) => {
          this.orderByLabel = {
            name: 'Tên',
            createdAt: 'Thời gian tạo',
            updatedAt: 'Thời gian chỉnh sửa'
          }[orderBy];
          this.orderValueLabel = {
            ASC: 'Tăng dần',
            DESC: 'Giảm dần'
          }[orderValue];
        })
      )
      .subscribe();

    if (
      this.documentLayoutService.folders.length === 0 // if no data in store$
    )
      // only then do first load
      this.documentLayoutService.loadFolders({
        loadStrategy: 'clear-and-fetch', // first load
        returnAsObservable: false,
        filters: {
          // using default filters
          orderBy: 'createdAt',
          orderValue: 'DESC'
        }
      });
  }

  trackBy(index, item) {
    return item?.id;
  }

  loadMoreFolders() {
    /* only load more with already set filters */
    this.documentLayoutService.loadFolders({ loadStrategy: 'fetch-next-page' });
  }

  configFolderOrderBy() {
    const modal = this.modalService.create({
      nzTitle: null,
      nzContent: ModalConfigFiltersForFoldersListComponent,
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '400px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        switchMap(
          (result) =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'clear-and-fetch', // discard all previous fetched folders
              returnAsObservable: true,
              filters: {
                // apply user selected filters
                orderBy: result.orderBy,
                orderValue: result.orderValue
              }
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  deleteDocument(document: Folder['documents'][number]) {
    const modal = this.modalService.create({
      nzTitle: null,
      nzContent: ModalDeleteDocumentComponent,
      nzData: { document },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => {
          if (document.id === this.selectedDocumentId) this.router.navigate(['/']);
        }),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  renameDocument(document: Folder['documents'][number]) {
    const modal = this.modalService.create({
      nzTitle: 'Đổi tên',
      nzContent: ModalChangeDocumentNameComponent,
      nzData: { document },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '16px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  moveDocument(document: Folder['documents'][number]) {
    const modal = this.modalService.create({
      nzTitle: 'Chuyển vào thư mục',
      nzContent: ModalMoveDocumentComponent,
      nzData: {
        document,
        folders: this.folders
          .filter(
            (folder) => !folder.documents.map((doc) => doc.id).includes(document.id)
          )
          .map((folder) => ({ id: folder.id, name: folder.name }))
      },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '16px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  copyDocument(document: Folder['documents'][number]) {
    const folder = this.folders.find((folder) =>
      folder.documents.map((doc) => doc.id).includes(document.id)
    );
    const modal = this.modalService.create({
      nzTitle: null,
      nzContent: ModalCopyDocumentComponent,
      nzData: { sourceDocumentId: document.id, sourceFolder: folder },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  renameFolder(folder: Folder) {
    const modal = this.modalService.create({
      nzTitle: 'Đổi tên',
      nzContent: ModalChangeFolderNameComponent,
      nzData: { folder },
      nzFooter: null,
      nzMaskClosable: true,
      nzClosable: true,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '16px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  deleteFolder(folder: Folder) {
    const modal = this.modalService.create({
      nzTitle: null,
      nzContent: ModalDeleteFolderComponent,
      nzData: { folder },
      nzFooter: null,
      nzMaskClosable: false,
      nzClosable: false,
      nzClassName: 'modal-menu-create-common',
      nzBodyStyle: { padding: '24px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => {
          if (folder.documents.map((doc) => doc.id).includes(this.selectedDocumentId))
            this.router.navigate(['/']);
        }),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        )
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
