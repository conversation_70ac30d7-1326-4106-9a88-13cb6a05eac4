import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '@platform/app/core/guards/auth.guard';
import { LayoutComponent } from './layout/layout/layout.component';
import { LdpLayoutComponent } from './layout/ldp-layout/ldp-layout.component';
import { RevokeConsentComponent } from './layout/revoke-consent/revoke-consent.component';
import { DemoLayoutComponent } from './layout/demo-layout/demo-layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: 'ocr-experience',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./ocr-experience/ocr-experience.module').then(
            (m) => m.OcrExperienceModule
          )
      },
      {
        path: 'statistic',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./statistic/statistic.module').then((m) => m.StatisticModule)
      },
      {
        path: 'docs',
        canActivate: [AuthGuard],
        loadChildren: () => import('./docs/docs.module').then((m) => m.DocsModule)
      },
      {
        path: 'token-info',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./token-info/token-info.module').then((m) => m.TokenInfoModule)
      },
      {
        path: 'subscription',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./subscription/subscription.module').then((m) => m.SubscriptionModule)
      },
      {
        path: 'payment-history',
        canActivate: [AuthGuard],
        loadChildren: () =>
          import('./payment-history/payment-history.module').then(
            (m) => m.PaymentHistoryModule
          )
      },
      {
        path: 'revoke-consent/confirm',
        canActivate: [AuthGuard],
        component: RevokeConsentComponent
      },
      {
        path: '',
        redirectTo: '/ocr-experience',
        pathMatch: 'full'
      }
    ]
  },
  {
    path: 'key-information-extractor',
    canActivate: [AuthGuard],
    loadChildren: () => import('./kie/kie.module').then((m) => m.KIEModule)
  },
  {
    path: 'ocr',
    canActivate: [AuthGuard],
    loadChildren: () => import('./ocr/ocr.module').then((m) => m.OCRModule)
  },
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then((m) => m.LoginModule)
  },
  {
    path: 'reset-password',
    loadChildren: () =>
      import('./reset-password/reset-password.module').then((m) => m.ResetPasswordModule)
  },
  {
    path: 'ldp',
    component: LdpLayoutComponent,
    children: [
      {
        path: 'ocr-experience',
        loadChildren: () =>
          import('./ocr-experience/ocr-experience.module').then(
            (m) => m.OcrExperienceModule
          )
      },
      {
        path: '**',
        redirectTo: 'ocr-experience'
      }
    ]
  },
  {
    path: 'demo',
    children: [
      {
        canActivate: [AuthGuard],
        component: DemoLayoutComponent,
        path: 'ocr-experience',
        loadChildren: () =>
          import('./ocr-experience/ocr-experience.module').then(
            (m) => m.OcrExperienceModule
          )
      },
      {
        canActivate: [AuthGuard],
        component: LayoutComponent,
        path: 'llm',
        loadChildren: () => import('./llm/llm.module').then((m) => m.LLMModule)
      },
      {
        path: '**',
        redirectTo: 'ocr-experience'
      }
    ]
  },
  {
    path: '**',
    redirectTo: 'ocr-experience'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
