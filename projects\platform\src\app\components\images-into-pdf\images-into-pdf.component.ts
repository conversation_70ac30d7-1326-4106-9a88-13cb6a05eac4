import { Component, EventEmitter, Output, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { get, set } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import UtilsService from '@platform/app/core/services/utils.service';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NgxSpinnerModule, NgxSpinnerService } from 'ngx-spinner';
import { NzDrawerModule, NzDrawerRef, NzDrawerService } from 'ng-zorro-antd/drawer';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-images-into-pdf',
  standalone: true,
  imports: [
    CommonModule,
    NzToolTipModule,
    NzIconModule,
    NzDrawerModule,
    DragDropModule,
    NgxSpinnerModule,
    NzInputModule,
    FormsModule
  ],
  templateUrl: './images-into-pdf.component.html',
  styleUrls: ['./images-into-pdf.component.scss']
})
export class ImagesIntoPdfComponent {
  readonly STEPS = ['collect-images', 'merge', 'output-pdf'] as const; // act as a typing and order of step
  step: (typeof this.STEPS)[number] = this.STEPS[0];
  readonly RULE_ACCEPT = {
    mimetypes: ['image/jpeg', 'image/png'], // .heic is currently not supported
    accept: '.jpeg, .jpg, .png',
    typeLabel: '*.jpeg, *.jpg, *.png',
    extensions: ['jpeg', 'jpg', 'png'],
    size: 10 * 1024 * 1024,
    getSizeStr() {
      return this.size / (1024 * 1024) + 'MB';
    },
    minFileCount: 1,
    maxFileCount: 5
  };
  imageList: File[] = [];
  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('images-into-pdf-loading');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('images-into-pdf-loading');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }
  @ViewChild('previewDrawerTmpl', { static: false })
  previewDrawerTmpl?: TemplateRef<{
    $implicit: typeof this.cachedPreviewParams;
    drawerRef: NzDrawerRef<string>;
  }>;
  createdPdf: { file: File; name: string; url: string };
  isEditingCreatedPdfName = false;
  @Output() onPdfCreated = new EventEmitter<File | null>();

  constructor(
    private toastrService: ToastrService,
    private utils: UtilsService,
    private spinner: NgxSpinnerService,
    private drawerService: NzDrawerService,
    private pdfService: PdfService
  ) {}

  convertBytesToMB = this.utils.convertBytesToMB;

  handleFileInputChange(e, inputFileElem: HTMLInputElement) {
    if (this.imageList.length >= this.RULE_ACCEPT.maxFileCount) return;

    let files: File[] = Array.from(get(e, 'target.files', []));
    if (!files.length) return;
    // if (files.length + this.fileList.length > this.RULE_ACCEPT.maxFileCount) // reject all batch files select
    //   return this.toastrService.warning(
    //     `Số lượng tải lên vượt quá mức cho phép. Tổng số ảnh không vượt quá ${this.RULE_ACCEPT.maxFileCount}`
    //   );

    files = files
      .map((file) => {
        // Extracting the file extension from the file name
        const extension = file.name.split('.').pop().toLowerCase();

        // Check if the MIME type is in the accepted list and the file extension is in the accepted list
        if (
          !this.RULE_ACCEPT.mimetypes.includes(file.type) ||
          !this.RULE_ACCEPT.extensions.includes(extension)
        ) {
          this.toastrService.error(
            `File ${file.name} không đúng định dạng cho phép (Hỗ trợ file ${this.RULE_ACCEPT.extensions.join(', ')})`
          );
          return;
        }

        // Check file size
        if (file.size > this.RULE_ACCEPT.size) {
          this.toastrService.error(
            `File ${file.name} vượt quá dung lượng cho phép (Dung lượng tối đa cho phép ${this.RULE_ACCEPT.getSizeStr()})`
          );
          return;
        }

        return file;
      })
      .filter((file) => !!file);

    if (!files.length) return;

    let newAddedFiles = files;
    if (files.length + this.imageList.length > this.RULE_ACCEPT.maxFileCount) {
      // accept only a number of files that fit into the maxFileCount
      this.toastrService.error(
        `Chỉ cho phép ghép tối đa ${this.RULE_ACCEPT.maxFileCount} ảnh`
      );
      if (this.RULE_ACCEPT.maxFileCount - this.imageList.length <= 0) return;
      newAddedFiles = files.slice(
        0,
        this.RULE_ACCEPT.maxFileCount - this.imageList.length
      );
    }

    this.imageList.push(...newAddedFiles);

    // reset input file element
    if (inputFileElem) inputFileElem.value = null;
  }

  handleFileInputCancel(e, inputFileElem: HTMLInputElement) {}

  removeFile(fileIndex) {
    this.imageList = this.imageList.filter((_, i) => fileIndex !== i);
  }

  showPreviewImage(file: File) {
    const drawerRef = this.drawerService.create({
      nzTitle: null,
      nzFooter: null,
      // nzMask: false,
      nzContent: this.previewDrawerTmpl,
      nzClosable: false,
      nzWidth: 650,
      nzWrapClassName: 'images-into-pdf-preview-drawer',
      nzMaskStyle: { background: 'transparent' },
      nzContentParams: {
        name: file.name,
        previewLink: URL.createObjectURL(file)
      }
    });
  }

  fileDropped(e: CdkDragDrop<string>) {
    moveItemInArray(this.imageList, e.previousIndex, e.currentIndex);
  }

  async mergeImagesIntoPdf() {
    if (
      this.step !== 'collect-images' ||
      this.imageList?.length < this.RULE_ACCEPT.minFileCount
    )
      return;

    /* step1: collect-images => step2: merge */
    this.loading = true;
    this.step = this.STEPS[1];
    await new Promise((res) => setTimeout(res, 1000)); // FIXME: conditional add (in case of the execution of creating pdf is too quick to even see the loading screen) or not add delay to the execution time

    const pdfFileName = this.imageList[0].name.slice(
      0,
      this.imageList[0].name.lastIndexOf('.') !== -1 // find the last '.' as in the extension, eg '.png'
        ? this.imageList[0].name.lastIndexOf('.') // then omit the extension
        : this.imageList[0].name.length // no extension found, use all string
    );

    let file;
    try {
      file = await this.pdfService.createPdfFileFromImageList(
        this.imageList,
        pdfFileName
      );
    } catch (e) {
      console.error(e);
      this.toastrService.error(
        'Đã có lỗi xảy ra khi ghép file, hãy đảm bảo file đầu vào đúng định dạng cho phép và không bị lỗi cấu trúc'
      );
      return;
    } finally {
      this.createdPdf = null;
      this.loading = false;
      this.step = this.STEPS[0];
    }

    this.createdPdf = {
      file,
      name: pdfFileName,
      url: URL.createObjectURL(file)
    };

    this.loading = false;
    this.step = this.STEPS[2];
  }

  cancelMerge() {
    if (this.step === this.STEPS[1]) {
      /* stop the on going execution */
      this.onPdfCreated.emit(null);
    }
  }

  toggleEditingCreatedPdfName() {
    if (this.step === this.STEPS[2] && this.createdPdf?.name)
      this.isEditingCreatedPdfName = !this.isEditingCreatedPdfName;
  }

  async emitCreatedPdf() {
    if (this.step === this.STEPS[2] && this.createdPdf) {
      if (this.createdPdf.file.name !== this.createdPdf.name + '.pdf')
        // rename created pdf if changed
        this.createdPdf.file = new File(
          [this.createdPdf.file],
          this.createdPdf.name + '.pdf',
          {
            type: 'application/pdf'
          }
        );
      this.onPdfCreated.emit(this.createdPdf.file);
    }
  }

  discardCreatedPdf() {
    if (this.step === this.STEPS[2]) {
      this.isEditingCreatedPdfName = false;
      this.createdPdf = null; // clear createdPdf
      this.step = this.STEPS[0]; // then move back to step 1
      this.onPdfCreated.emit(null); // output null, let parent component decide what to do next
    }
  }
}
