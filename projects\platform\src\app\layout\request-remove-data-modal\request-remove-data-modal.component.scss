.wrapper {
	position: relative; // min-width: 557px;
	// min-height: 343px;

	.close-btn {
		position: absolute;
		top: 16px;
		right: 16px;
		padding: 0;
	}

	.header {
		color: #545454;
		text-align: center;
		font-size: 20px;
		font-weight: 700;
		line-height: 24px;
		padding: 28px 0;
	}

	.content {
		padding: 0 36px;

		div {
			color: #111127;
			text-align: justify;
			font-size: 14px;
			font-weight: 400;
			line-height: 21px;

			a {
				font-weight: 500;
				color: #0F67CE;
			}
		}

		div.form {
			div {
				margin-bottom: 12px;
				display: flex;
				gap: 12px;
				align-items: center;

				label {
					margin: 0;
					width: 110px;
				}

				input {
					flex: 1;
				}
			}
		}
	}

	.confirm-btn {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 16px 0;

		button {
			@apply h-8 rounded-[4px] w-[120px] bg-brand-1 text-white;

			&[disabled] {
				@apply bg-[#CBCBCB] text-white border-[#CBCBCB];
			}
		}
	}
}

:host ::ng-deep {
	input.ant-input {
		@apply h-10 rounded-[4px];
	}
}