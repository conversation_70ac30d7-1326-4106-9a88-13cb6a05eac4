import { Component, Input, OnChanges, Output, EventEmitter } from '@angular/core';
import { TableColumn, TableRow } from '../../statistic.interface';
import { isArray } from 'lodash';
import { debounceTime, distinctUntilChanged, map, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss']
})
export class TableComponent implements OnChanges {
  @Input() columns: TableColumn[] = [];
  @Input() dataSource: TableRow[] = [];
  @Input() title = '';
  @Input() hasTotalRow = false;
  @Input() dateFilter;
  @Input() hideSearch: boolean;
  totalRow: (number | string | boolean)[] = [];
  private destroy$ = new Subject<void>();
  searchTerm: string = '';
  private searchTerm$ = new Subject<string>();
  @Output() refetchDataEvent = this.searchTerm$.pipe(
    takeUntil(this.destroy$),
    map((searchTerm) => searchTerm.trim()),
    debounceTime(300),
    distinctUntilChanged(),
    map((searchTerm) => ({ search: searchTerm }))
  );

  constructor() {}

  ngOnChanges(): void {
    this.calculateTotalRow();
  }

  calculateTotalRow(): void {
    if (this.hasTotalRow) {
      this.totalRow = this.columns.map((col) => {
        if (col.canSum) {
          const listDataByIndex = this.dataSource.map((item) => item[col.index]);
          return listDataByIndex.reduce((a: number, b: number) => a + b, 0);
        } else return '';
      });
    }
  }

  renderCell(col: TableColumn, row: TableRow) {
    return col.render && typeof col.render === 'function'
      ? col.render(row[col.index], row, col.index)
      : row[col.index];
  }

  getRouterLink(col: TableColumn, row: TableRow) {
    if (!col.routerLink) return;

    if (typeof col.routerLink === 'function')
      return col.routerLink(row[col.index], row, col.index);

    if (isArray(col.routerLink)) return col.routerLink;
  }

  handleSearch(): void {
    this.searchTerm$.next(this.searchTerm);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
