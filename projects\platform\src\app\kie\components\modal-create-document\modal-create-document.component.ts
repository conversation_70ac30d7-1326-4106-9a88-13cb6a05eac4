import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder, SystemTemplate, listSystemTemplate } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, Observable, catchError, switchMap, tap } from 'rxjs';

export type Option = {
  label: string;
  value: string;
};
@Component({
  selector: 'app-modal-create-document',
  templateUrl: './modal-create-document.component.html',
  styleUrls: ['./modal-create-document.component.scss']
})
export class ModalCreateDocumentComponent implements OnInit {
  readonly nzModalData: {
    designatedSystemTemplateId?: SystemTemplate['id'];
    listFolders: Folder[];
  } = inject(NZ_MODAL_DATA);
  designatedSystemTemplateId = this.nzModalData.designatedSystemTemplateId;
  listFolders = this.nzModalData.listFolders || [];

  selectedFolderId: string;
  selectedTemplateId: string = '';
  nameDocument: string = '';

  listSystemTemplate: Option[] = listSystemTemplate.map((item) => ({
    label: item.title,
    value: item.id
  }));

  constructor(
    private modalRef: NzModalRef,
    private kieService: KIEService,
    private toastr: ToastrService,
    private documentLayoutService: DocumentLayoutService,
    private router: Router
  ) {}

  ngOnInit(): void {
    if (this.designatedSystemTemplateId) {
      this.selectedTemplateId = this.designatedSystemTemplateId;
    }
    if (this.listFolders.length > 0) {
      this.selectedFolderId = this.listFolders[0].id;
    }
  }

  createDocument(): void {
    if (this.selectedTemplateId.trim().length < 1) {
      this.toastr.error('Vui lòng chọn một loại văn bản');
      return;
    }

    // validate name document
    if (this.nameDocument.trim().length < 1) {
      this.toastr.error('Tên văn bản không được để trống');
      return;
    }
    if (this.nameDocument.trim().length > 255) {
      this.toastr.error('Tên văn bản không được quá 255 ký tự');
      return;
    }

    // check exist name documents
    const selectedFolder = this.listFolders.find(
      (folder) => folder.id === this.selectedFolderId
    );

    if (selectedFolder) {
      if (selectedFolder.documents.length >= 100) {
        this.toastr.error(
          'Vượt quá số lượng tối đa 100 văn bản cho phép trong 1 thư mục'
        );
        return;
      }

      // Check if any document in the selected folder has the same name as this.documentSelected.title.trim()
      const isDocumentExist = selectedFolder.documents.some(
        (doc) => doc.name === this.nameDocument.trim()
      );

      if (isDocumentExist) {
        this.toastr.error('Tên văn bản đã tồn tại!');
        return;
      }
    }

    const payload = {
      name: this.nameDocument,
      folderId: this.selectedFolderId,
      systemTemplateName: this.selectedTemplateId
    };

    this.kieService
      .createNewSystemDocument(payload)
      .pipe(
        tap(() => {
          this.toastr.success('Tạo mới văn bản thành công');
          this.modalRef.close();
        }),
        catchError((error) => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
          return EMPTY;
        }),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        ),
        tap(() => {
          const selectedFolder = this.documentLayoutService.folders.find(
            (folder) => folder.id === this.selectedFolderId
          );
          const newestDoc = selectedFolder.documents[selectedFolder.documents.length - 1]; // last document === newest one
          if (newestDoc?.id)
            this.router.navigate([
              '/',
              'key-information-extractor',
              'document',
              newestDoc.id
            ]);
        })
      )
      .subscribe();
  }
}
