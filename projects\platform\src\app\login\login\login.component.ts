import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { get, isEmpty } from 'lodash';
import { UserService, AgreementCode } from '@platform/app/core/services/user.service';
import {
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import { environment } from '@platform/environment/environment';
import { ReCaptcha2Component } from 'ngx-captcha';
import UtilsService from '@platform/app/core/services/utils.service';
import {
  Observable,
  EMPTY,
  catchError,
  iif,
  switchMap,
  tap,
  take,
  of,
  forkJoin,
  finalize
} from 'rxjs';
import { AuthService } from '@platform/app/core/services/auth.service';
import { SingleSignOnService } from '@platform/app/core/services/sso.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  readonly AgreementCode = AgreementCode;
  @ViewChild('guideline', { static: true })
  guidelineDialog: ElementRef;
  @ViewChild('notice', { static: true })
  noticeDialog: ElementRef;
  @ViewChild('noticeForSSO', { static: true })
  noticeForSSODialog: ElementRef;
  noticeAgreed: boolean = false;
  agreementCode: AgreementCode;
  public lgForm: UntypedFormGroup;
  passwordType: 'password' | 'text' = 'password';
  returnUrl = '/';
  newUserLoginWithSSO = false;
  noticeForSSOForm = new UntypedFormGroup({
    fullName: new UntypedFormControl(null, [Validators.required]),
    workUnit: new UntypedFormControl(null, [Validators.required]),
    field: new UntypedFormControl(null, [Validators.required]),
    phoneNumber: new UntypedFormControl(null, [
      Validators.required,
      Validators.maxLength(10),
      Validators.minLength(10)
    ]),
    ssoAgreed: new UntypedFormControl(false)
  });

  constructor(
    private userService: UserService,
    private authService: AuthService,
    private ssoService: SingleSignOnService,
    private router: Router,
    private route: ActivatedRoute,
    private toastr: ToastrService,
    private formBuilder: UntypedFormBuilder,
    private utilsService: UtilsService
  ) {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/';
    if (this.utilsService.getAccessToken()) {
      this.router.navigate([this.returnUrl]);
    }
    this.loginFailedCount = +localStorage.getItem('loginFailedCount') || 0;
    this.handleLoginTimeout(+localStorage.getItem('loginFailedTimeout') || 0);
    // clearInterval(this.countDownInterval)
    this.lgForm = this.formBuilder.group({
      username: ['', Validators.required],
      password: ['', Validators.required],
      recaptcha: ['', Validators.required]
    });
  }

  error = '';
  readonly maxLoginFailedCount = 5;
  loginFailedCount: number = 0;
  loginFailedTimeout: number = 0; // in second
  private countDownInterval;
  public siteKey = environment.recaptchaSiteKey;
  public registerUrl = environment.registerUrl;
  public landingPageUrl = environment.landingPageUrl;

  public theme: 'light';
  public size: 'compact' | 'normal' = 'normal';
  public lang = 'en';
  public type: 'audio';
  guidelineSelection: 1 | 2 = 1;

  ngOnInit(): void {
    let ssoProfile = null;
    this.ssoService.oAuthAccessToken$
      .pipe(
        take(1),
        // tap((accessToken) => {
        //   this.toastr.show('login with google single sign on');
        //   console.log(accessToken);
        // }),
        switchMap(({ profile }) => {
          ssoProfile = profile;
          return this.userService.getAccountServicePermission(ssoProfile.email);
        }),
        catchError((error) => {
          // safely return error for the next switchMap() pipe to handle
          return of(error);
        }),
        switchMap((agreementRes) => {
          if (agreementRes instanceof HttpErrorResponse && agreementRes.status === 404) {
            // handle case new user login with sso for the first time
            this.newUserLoginWithSSO = true;
            this.toastr.success('Chào mừng bạn tới hệ thống VNPT Smart Reader');
            this.noticeForSSOForm.patchValue({ fullName: ssoProfile?.name });
            this.noticeForSSODialog.nativeElement.showModal();
            return EMPTY;
          }

          // console.log('handle agreement case');
          switch (agreementRes['object']?.agreementCode) {
            case AgreementCode.UserAgreed: {
              return this.getLogin({ withSSO: true });
            }
            case AgreementCode.UserNotAgreed: {
              this.agreementCode = AgreementCode.UserNotAgreed;
              this.ssoService.logout();
              return EMPTY;
            }
            case AgreementCode.OldUserNeedsAgreement: {
              /* show modal accept agreement only */
              for (const key in this.noticeForSSOForm.controls) {
                if (key !== 'ssoAgreed')
                  this.noticeForSSOForm.controls[key].disable({
                    onlySelf: true,
                    emitEvent: false
                  });
              }

              this.noticeForSSODialog.nativeElement.showModal();
              return EMPTY;
            }
          }

          console.log('abnormal cases won be handled');
          this.toastr.error('Đã có lỗi xảy ra, vui lòng đăng nhập lại');
          this.ssoService.logout();
          return EMPTY; // abnormal cases won be handled
        })
      )
      .subscribe();
  }

  @ViewChild('captchaElem', { static: false }) captchaElem: ReCaptcha2Component;
  captcha: string;
  handleReset() {
    this.captcha = null;
    console.log();
  }
  handleError() {
    this.captcha = null;
    console.log();
  }
  handleExpire() {
    this.captcha = null;
    console.log();
  }

  handleSuccess(data: string) {
    this.captcha = data;
  }
  handleLoad() {
    console.log();
  }

  get f() {
    return this.lgForm.controls;
  }

  ssoWithGoogle() {
    this.ssoService.initLogin();
  }

  getLogin = ({ withSSO }: { withSSO: boolean }) => {
    let login$: Observable<any>;
    if (withSSO) {
      login$ = this.ssoService.loginWithSSO().pipe(
        catchError(() => {
          this.toastr.error('Single Sign On with Google OAuth2 had failed');
          this.ssoService.logout();
          return EMPTY;
        })
      );
    } else {
      if (this.loginFailedCount >= this.maxLoginFailedCount) return EMPTY;
      this.error = '';
      if (this.lgForm.invalid) return EMPTY;
      const formData = this.lgForm.value;
      const body = {
        username: formData.username,
        password: formData.password,
        captcha: this.captcha
      };

      login$ = this.authService.login(body).pipe(
        catchError((err) => {
          this.lgForm.patchValue({ password: '', recaptcha: null });
          this.captchaElem.resetCaptcha();
          this.loginFailedCount++;
          this.error = get(
            err,
            'error.message',
            'Tên đăng nhập hoặc mật khẩu không chính xác.'
          );
          this.toastr.error(this.error);
          localStorage.setItem('loginFailedCount', `${this.loginFailedCount}`);
          if (this.loginFailedCount >= this.maxLoginFailedCount) {
            this.toastr.error(
              `Bạn đã đăng nhập sai ${this.maxLoginFailedCount} lần, vui lòng đợi 5 phút trước khi thử lại`,
              undefined,
              { progressBar: true, timeOut: 5 * 60 * 1000 }
            );
            this.handleLoginTimeout(5 * 60); // 5 minutes timeout
          }
          return EMPTY;
        })
      );
    }

    if (!login$) return EMPTY;
    return login$.pipe(
      tap((res) => {
        this.loginFailedCount = 0;
        localStorage.removeItem('loginFailedCount');
        localStorage.setItem('accessToken', res.accessToken);
      }),
      switchMap(() => {
        return this.userService.getUserInfor();
      }),
      tap((user: {}) => {
        if (!isEmpty(user)) {
          localStorage.setItem('currentUser', JSON.stringify(user));
          this.utilsService.onLogin$.next(null);
          this.router.navigate([this.returnUrl], {});
        }
      }),
      catchError((err) => {
        console.log(err);
        this.authService.logout();
        this.ssoService.logout();
        return EMPTY;
      })
    );
  };

  handleLoginBtnClicked = () => {
    if (this.loginFailedCount === this.maxLoginFailedCount || this.loginFailedTimeout)
      return;
    this.lgForm.controls['username'].markAsDirty();
    this.lgForm.controls['password'].markAsDirty();
    this.lgForm.controls['recaptcha'].markAsDirty();
    if (this.lgForm.valid)
      this.userService
        .getAccountServicePermission(this.lgForm.value?.username)
        .pipe(
          tap(({ object: { agreementCode } }) => {
            this.agreementCode = agreementCode;
            if (agreementCode === AgreementCode.OldUserNeedsAgreement)
              this.noticeDialog.nativeElement.showModal();
          }),
          catchError((e) => {
            this.toastr.error(get(e, 'error.error', "Something's wrong"));
            return EMPTY;
          }),
          switchMap(({ object: { agreementCode } }) =>
            iif(
              () => agreementCode === AgreementCode.UserAgreed,
              this.getLogin({ withSSO: false }),
              EMPTY
            )
          )
        )
        .subscribe();
  };

  private handleLoginTimeout(timeout) {
    if (!timeout) return;
    this.loginFailedTimeout = timeout;
    clearInterval(this.countDownInterval);
    this.countDownInterval = setInterval(() => {
      this.loginFailedTimeout--;
      localStorage.setItem('loginFailedTimeout', `${this.loginFailedTimeout}`);
      if (this.loginFailedTimeout <= 0) {
        clearInterval(this.countDownInterval);
        this.loginFailedCount = 0;
        localStorage.removeItem('loginFailedCount');
        localStorage.removeItem('loginFailedTimeout');
      }
    }, 1000);
  }

  showHidePassword() {
    if (this.passwordType === 'password') this.passwordType = 'text';
    else this.passwordType = 'password';
  }

  showGuideline() {
    this.guidelineDialog.nativeElement.showModal();
  }

  closeGuideline() {
    this.guidelineDialog.nativeElement.close();
    this.guidelineSelection = 1;
  }

  selectGuideline(selection) {
    this.guidelineSelection = selection;
  }

  closeNotice() {
    this.noticeAgreed = false;
    this.noticeDialog.nativeElement.close();
  }

  confirmNotice() {
    if (this.noticeAgreed) {
      this.noticeAgreed = false;
      this.noticeDialog.nativeElement.close();
      this.getLogin({ withSSO: false })
        .pipe(
          switchMap(() => this.userService.changeAccountServicePermission()),
          catchError(() => {
            this.authService.logout();
            return EMPTY;
          })
        )
        .subscribe();
    }
  }

  handleNoticeForSSOSubmit() {
    /* 
      handle cases:
        - AccountServicePermission returns 404: user is not found => user is new to the system and login with SSO
        - AgreementCode.OldUserNeedsAgreement: old user created before ND-13 need to agree with terms and conditions before proceed
    */
    if (!this.noticeForSSOForm.controls['ssoAgreed'].value)
      this.toastr.error('Hãy chấp thuận các điều khoản');

    if (!this.noticeForSSOForm.valid) {
      this.noticeForSSOForm.markAllAsTouched();
      return;
    }

    this.getLogin({ withSSO: true })
      .pipe(
        switchMap(() => {
          const requests = {
            changePermission: this.userService.changeAccountServicePermission()
          };
          if (this.newUserLoginWithSSO) {
            const userInfo = { ...this.noticeForSSOForm.value };
            delete userInfo.ssoAgreed;
            requests['saveUserInfo'] = this.userService.updateIdgAccount(userInfo);
          }
          return forkJoin(requests);
        }),
        catchError((error) => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng đăng nhập lại');
          this.authService.logout();
          this.ssoService.logout();
          console.log(error);
          return EMPTY;
        }),
        finalize(() => this.noticeForSSODialog.nativeElement.close())
      )
      .subscribe();
  }

  getFieldError(form: UntypedFormGroup, fieldName, errorName) {
    return get(form, `controls.${fieldName}.errors.${errorName}`, false);
  }

  checkInvalidField(form: UntypedFormGroup, fieldName) {
    return form.controls[fieldName].touched && !isEmpty(form.controls[fieldName].errors);
  }
}
