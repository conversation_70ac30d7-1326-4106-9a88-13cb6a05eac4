import { Component, OnInit } from '@angular/core';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { Subject, takeUntil, tap } from 'rxjs';

@Component({
  selector: 'app-modal-config-filters-for-folders-list',
  templateUrl: './modal-config-filters-for-folders-list.component.html',
  styleUrls: ['./modal-config-filters-for-folders-list.component.scss']
})
export class ModalConfigFiltersForFoldersListComponent implements OnInit {
  orderBy: 'name' | 'createdAt' | 'updatedAt' = 'createdAt';
  orderValue: 'ASC' | 'DESC' = 'ASC';
  destroyed$ = new Subject<void>();

  constructor(
    private modalRef: NzModalRef,
    private documentLayoutService: DocumentLayoutService
  ) {}

  ngOnInit(): void {
    this.documentLayoutService.loadFoldersFilters$
      .pipe(
        takeUntil(this.destroyed$),
        tap(({ orderBy, orderValue }) => {
          this.orderBy = orderBy;
          this.orderValue = orderValue;
        })
      )
      .subscribe();
  }

  cancel() {
    this.modalRef.close();
  }

  apply() {
    this.modalRef.close({ orderBy: this.orderBy, orderValue: this.orderValue });
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
