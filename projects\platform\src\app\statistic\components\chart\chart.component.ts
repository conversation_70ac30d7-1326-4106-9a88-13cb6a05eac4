import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { ChartConfiguration, ChartEvent, ChartType } from 'chart.js';
import { format } from 'date-fns';
import { set } from 'lodash';

@Component({
  selector: 'app-chart',
  templateUrl: './chart.component.html',
  styleUrls: ['./chart.component.scss'],
})
export class ChartComponent implements OnChanges {
  public lineChartType: ChartType = 'line';
  @Input() dataset: any[];
  @Input() dataKey: string;
  @Input() labelKey: string;
  @Input() chartTitle: string;
  @Input() xAxisTitle: string;
  @Input() yAxisTitle: string;
  @Input() tooltipLabel: string;

  lastUpdate: string;

  public lineChartData: ChartConfiguration['data'];

  public lineChartOptions: ChartConfiguration['options'] = {
    plugins: { legend: { display: false } },
    maintainAspectRatio: false,
    elements: {
      line: {
        tension: 0.5,
      },
    },
    scales: {
      y: {
        grid: { display: false },
        ticks: { stepSize: 1 },
        title: {
          text: 'y',
          color: '#141414',
          display: true,
          font: {
            family: "'Open Sans', sans-serif",
            weight: '600',
          },
        },
      },
      x: {
        title: {
          text: 'x',
          color: '#141414',
          display: true,
          font: {
            family: "'Open Sans', sans-serif",
            weight: '600',
          },
        },
      },
    },
  };

  public chartClicked({
    event,
    active,
  }: {
    event?: ChartEvent;
    active?: {}[];
  }): void {
    // console.log(event, active);
  }

  public chartHovered({
    event,
    active,
  }: {
    event?: ChartEvent;
    active?: {}[];
  }): void {
    // console.log(event, active);
  }
  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    this.setLineChartData();
    this.lastUpdate = format(new Date(), 'hh:mm dd/MM/yyyy');
    set(this.lineChartOptions, 'scales.y.title.text', this.yAxisTitle);
    set(this.lineChartOptions, 'scales.x.title.text', this.xAxisTitle);
  }

  setLineChartData(): void {
    if (!this.dataset || !this.dataKey || !this.labelKey) return;
    this.lineChartData = {
      datasets: [
        {
          label: this.tooltipLabel,
          data: this.dataset.map((item) => item[this.dataKey]),
          backgroundColor: '#0062FF',
          borderColor: '#0062FF',
          pointRadius: 7,
          pointBackgroundColor: '#17C48A',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(148,159,177,0.8)',
          // fill: 'origin',
        },
      ],
      labels: this.dataset.map((item) => item[this.labelKey]),
    };
  }
}
