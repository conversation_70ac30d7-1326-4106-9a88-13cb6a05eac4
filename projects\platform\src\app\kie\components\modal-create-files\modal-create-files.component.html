<div class="border-b border-line flex justify-between gap-4 items-center p-4">
  <span class="font-semibold">Upload file</span>
  <img
    src="assets/kie/document/cancel.svg"
    class="p-2 pr-0 cursor-pointer"
    (click)="handleCancel()"
  />
</div>
<app-upload-files
  class="p-4"
  [maxFileCount]="3"
  [fileList]="fileList"
  (onUploadFilesChange)="handleUploadFilesChange($event)"
></app-upload-files>
<div class="border-t border-line flex justify-center items-center p-4">
  <button
    (click)="saveFiles()"
    class="flex items-center gap-2 rounded-lg bg-brand-1 text-white font-medium px-4 py-2"
    [ngClass]="
      fileList.length === 0
        ? 'opacity-70 cursor-not-allowed'
        : 'hover:bg-brand-2 focus:bg-brand-2'
    "
  >
    <img src="assets/kie/document/save.svg" />
    <PERSON><PERSON><PERSON>
  </button>
</div>
