<div class="flex items-center justify-between gap-2 p-4">
  <span class="text-base font-semibold">T<PERSON><PERSON> về</span>
  <img
    (click)="handleCancel()"
    class="inline cursor-pointer"
    src="assets/ocr-experience/cancel.svg"
  />
</div>
<div
  class="p-4 grid grid-cols-[80px_1fr] items-center gap-y-4 border-y border-line font-medium"
>
  <div class="col-span-1">Lo<PERSON><PERSON> tệp</div>
  <nz-select
    class="col-span-1"
    nzSize="large"
    [nzCustomTemplate]="defaultTemplate"
    [nzOptionHeightPx]="35"
    [(ngModel)]="outputType"
  >
    <nz-option nzCustomContent nzLabel="Word" nzValue="word">
      <div class="flex items-center gap-2">
        <img class="inline w-[22px]" src="assets/ocr-experience/doc-icon.svg" />
        <span class="font-medium">Word</span>
      </div>
    </nz-option>
    <nz-option nzCustomContent nzLabel="Excel" nzValue="excel">
      <div class="flex items-center gap-2">
        <img class="inline w-[22px]" src="assets/ocr-experience/xls-icon.svg" />
        <span class="font-medium">Excel</span>
      </div>
    </nz-option>
    <nz-option nzCustomContent nzLabel="PDF" nzValue="pdf" nzDisabled>
      <div class="flex items-center gap-2">
        <img class="inline w-[22px]" src="assets/ocr-experience/pdf-icon.svg" />
        <span class="font-medium">PDF</span>
      </div>
    </nz-option>
  </nz-select>
  <ng-template #defaultTemplate let-selected>
    <div class="flex items-center gap-2">
      <ng-containter [ngSwitch]="selected.nzValue">
        <img
          *ngSwitchCase="'pdf'"
          class="inline w-5"
          src="assets/ocr-experience/pdf-icon.svg"
        />
        <img
          *ngSwitchCase="'word'"
          class="inline w-5"
          src="assets/ocr-experience/doc-icon.svg"
        />
        <img
          *ngSwitchCase="'excel'"
          class="inline w-5"
          src="assets/ocr-experience/xls-icon.svg"
        />
      </ng-containter>
      <span class="font-medium">{{ selected.nzLabel }}</span>
    </div>
  </ng-template>
  <div class="col-span-full border-t border-dashed border-text-4"></div>
  <div class="col-span-1">Trang</div>
  <nz-select class="col-span-1" nzSize="large" [(ngModel)]="pageSelectionType">
    <nz-option nzLabel="Tất cả các trang" nzValue="all"></nz-option>
    <nz-option nzLabel="Tùy chỉnh" nzValue="custom"></nz-option>
  </nz-select>
  <div class="col-span-1"></div>
  <div class="col-span-1 flex flex-col" *ngIf="pageSelectionType === 'custom'">
    <input
      nz-input
      class="rounded-lg bg-bg-1"
      nzSize="large"
      (ngModelChange)="handlePageSelection($event)"
      [ngModel]="undefined"
    />
    <div
      *ngIf="!!pageSelection && !pageSelection.valid"
      class="text-status-error text-xs mt-1"
    >
      Lựa chọn trang không hợp lệ, lựa chọn trong khoảng từ 1 tới
      {{ numPages || 1 }}
    </div>
  </div>
</div>
<div class="flex items-center justify-center p-4">
  <button
    (click)="handleExport()"
    class="bg-[#1E5FD5] rounded-lg text-white font-medium p-2"
  >
    Tải về
  </button>
</div>
