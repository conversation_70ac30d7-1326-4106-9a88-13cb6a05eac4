<div class="flex flex-col items-center justify-between">
  <div
    class="py-4 px-6 flex justify-between items-center w-full border-b border-[#e7ebef]"
  >
    <div class="font-semibold">G<PERSON><PERSON><PERSON> thi<PERSON>u phiên bản mới</div>
    <svg
      (click)="close()"
      class="cursor-pointer"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 12L12 4"
        stroke="#989BB3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 12L4 4"
        stroke="#989BB3"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </div>
  <div>
    <ng-container *ngTemplateOutlet="activeSlide"></ng-container>
    <ng-template #slide>
      <div class="p-6 flex flex-col items-center justify-between gap-3">
        <video
          class="border-[12px] border-[#202225] rounded-lg w-[652px] h-[322px] object-cover"
          controls
          [src]="'https://storage-cic.vnpt.vn/rpa-media/test-files/Video%201.mp4'"
        ></video>
        <div class="text-base font-semibold">Quản lý và sử dụng văn bản hệ thống</div>
        <div class="text-center">
          Cho phép người dùng quản lý và sử dụng văn bản theo thư mục, theo văn bản, hỗ
          trợ người dùng dễ dàng quản lý, sử dụng tài liệu bóc tách thông tin
        </div>
      </div>
    </ng-template>
    <ng-template #slide>
      <div class="p-6 flex flex-col items-center justify-between gap-3">
        <video
          class="border-[12px] border-[#202225] rounded-lg w-[652px] h-[322px] object-cover"
          controls
          [src]="'https://storage-cic.vnpt.vn/rpa-media/test-files/Video%202.mp4'"
        ></video>
        <div class="text-base font-semibold">Cấu hình bóc tách thông tin</div>
        <div class="text-center">
          Cho phép người dùng dễ dàng cấu hình bóc tách thông tin đối với các văn bản
          <br /><br />
        </div>
      </div>
    </ng-template>
    <ng-template #slide>
      <div class="p-6 flex flex-col items-center justify-between gap-3">
        <video
          class="border-[12px] border-[#202225] rounded-lg w-[652px] h-[322px] object-cover"
          controls
          [src]="'https://storage-cic.vnpt.vn/rpa-media/test-files/Video%203.mp4'"
        ></video>
        <div class="text-base font-semibold">Xem và chỉnh sửa kết quả bóc tách</div>
        <div class="text-center">
          Hỗ trợ người dùng xem và hiệu chỉnh kết quả bóc tách <br /><br />
        </div>
      </div>
    </ng-template>
    <ng-template #slide>
      <div class="p-6 flex flex-col items-center justify-between gap-3">
        <video
          class="border-[12px] border-[#202225] rounded-lg w-[652px] h-[322px] object-cover"
          controls
          [src]="'https://storage-cic.vnpt.vn/rpa-media/test-files/Video%204.mp4'"
        ></video>
        <div class="text-base font-semibold">Xác nhận kết quả bóc tách</div>
        <div class="text-center">
          Cho phép người dùng xác nhận kết quả bóc tách sau khi kết quả được hiệu chỉnh
          <br /><br />
        </div>
      </div>
    </ng-template>
  </div>
  <div class="flex items-center justify-center gap-2 pb-6">
    <div
      [ngStyle]="{
        'background-color': activeId === 0 ? '#0667E1' : '#e7ebef'
      }"
      class="rounded-full bg-[#e7ebef] w-2 h-2"
    ></div>
    <div
      [ngStyle]="{
        'background-color': activeId === 1 ? '#0667E1' : '#e7ebef'
      }"
      class="rounded-full bg-[#e7ebef] w-2 h-2"
    ></div>
    <div
      [ngStyle]="{
        'background-color': activeId === 2 ? '#0667E1' : '#e7ebef'
      }"
      class="rounded-full bg-[#e7ebef] w-2 h-2"
    ></div>
    <div
      [ngStyle]="{
        'background-color': activeId === 3 ? '#0667E1' : '#e7ebef'
      }"
      class="rounded-full bg-[#e7ebef] w-2 h-2"
    ></div>
  </div>
  <div
    class="py-4 px-6 border-t border-[#e7ebef] flex items-center justify-between w-full"
  >
    <label
      nz-checkbox
      [(ngModel)]="platformV2Understood"
      (ngModelChange)="changePlatformV2Understood()"
      >Đã hiểu, không hiện lại trong phiên đăng nhập này
    </label>
    <div class="flex items-center justify-between gap-2">
      <button
        (click)="prev()"
        class="border border-[#0667e1] text-[#0667e1] font-medium flex items-end gap-2 py-2 px-[14px] rounded-sm"
      >
        <svg
          class="w-5 h-5"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9.57 5.92969L3.5 11.9997L9.57 18.0697"
            stroke="#0667E1"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M20.4999 12H3.66992"
            stroke="#0667E1"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        Quay lại
      </button>
      <button
        (click)="next()"
        class="border border-[#0667e1] bg-[#0667e1] text-white font-medium flex items-end gap-2 py-2 px-[14px] rounded-sm"
      >
        Tiếp theo
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.0254 4.94141L17.0837 9.99974L12.0254 15.0581"
            stroke="white"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2.91699 10H16.942"
            stroke="white"
            stroke-width="1.5"
            stroke-miterlimit="10"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
  </div>
</div>
