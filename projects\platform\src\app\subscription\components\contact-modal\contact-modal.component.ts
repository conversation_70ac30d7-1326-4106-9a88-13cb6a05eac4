import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { UserService } from '@platform/app/core/services/user.service';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { isEmpty } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { SubscriptionService } from '@platform/app/core/services/subscription.service';
import { EMPTY, catchError, tap } from 'rxjs';

@Component({
  selector: 'app-contact-modal',
  templateUrl: './contact-modal.component.html',
  styleUrls: ['./contact-modal.component.scss'],
})
export class ContactModalComponent implements OnInit {
  contactForm: UntypedFormGroup;
  fieldOptions = [
    'Ngân hàng',
    'Chứng khoán',
    '<PERSON><PERSON>o hiểm',
    '<PERSON><PERSON><PERSON><PERSON> thông',
    'Fintech',
    'Logistic',
    'e-Commerce',
    'Khác',
  ];
  @Input() planName;

  constructor(
    public modal: NzModalRef,
    private userService: UserService,
    private subscriptionService: SubscriptionService,
    private toastr: ToastrService
  ) {
    this.contactForm = new UntypedFormGroup({
      fullname: new UntypedFormControl('', Validators.required),
      phone: new UntypedFormControl('', [
        Validators.required,
        Validators.pattern('^((\\+91-?)|0)?[0-9]{10}$'),
      ]),
      email: new UntypedFormControl(
        { value: '', disabled: true },
        Validators.required
      ),
      company: new UntypedFormControl('', Validators.required),
      field: new UntypedFormControl('', Validators.required),
      content: new UntypedFormControl('', Validators.required),
    });
  }

  ngOnInit(): void {
    this.userService
      .getIdgAccount()
      .pipe(
        tap((result) => {
          const account = result.object;
          this.contactForm.setValue({
            fullname: account.fullName,
            phone: account.phoneNumber,
            email: account.username,
            field: account.field,
            company: account.workUnit,
            content: '',
          });
        }),
        catchError(() => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại');
          this.modal.close();
          return EMPTY;
        })
      )
      .subscribe();
  }

  checkInvalidField(fieldName) {
    return (
      !this.contactForm.controls[fieldName].pristine &&
      !isEmpty(this.contactForm.controls[fieldName].errors)
    );
  }

  handleSubmit() {
    if (this.contactForm.valid) {
      const body = this.contactForm.value;
      body.email = this.contactForm.controls['email'].value;
      body.planName = this.planName;
      this.subscriptionService.sendMessage(body).subscribe((res) => {
        this.modal.close();
        this.toastr.success('Gửi tin nhắn thành công');
      });
    } else {
      for (const field in this.contactForm.controls) {
        this.contactForm.controls[field].markAsDirty({ onlySelf: true });
        this.contactForm.controls[field].updateValueAndValidity();
      }
    }
  }
}
