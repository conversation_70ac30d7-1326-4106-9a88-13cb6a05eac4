import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { fabric } from 'fabric';
import { Canvas } from 'fabric/fabric-impl';
import { flatten, get, isEmpty, isNumber } from 'lodash';
import UtilsService from '@platform/app/core/services/utils.service';
import { ToastrService } from 'ngx-toastr';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { OcrResult } from '@platform/app/core/services/export.service.type';
import { PageOrientation } from 'docx';
import { PDFDocument } from 'pdf-lib';
import { NzModalService } from 'ng-zorro-antd/modal';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';
import { loadImage } from 'canvas';

@Component({
  selector: 'app-document-reproduction',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './document-reproduction.component.html',
  styleUrls: ['./document-reproduction.component.scss']
})
export class DocumentReproductionComponent implements OnDestroy {
  @ViewChild('drawingCanvasWrapper')
  drawingCanvasWrapperElem: ElementRef<HTMLDivElement>;

  @ViewChild('drawingCanvas')
  drawingCanvasElem: ElementRef<HTMLCanvasElement>;

  drawingCanvas: Canvas;

  pagesDimension: any; // only for pdf case
  listImageURL;

  _documentReproductionInputs: { file: File; ocrResult: OcrResult['object'] };
  @Input()
  set documentReproductionInputs(inputs: { file: File; ocrResult: OcrResult['object'] }) {
    if (!inputs || !inputs.file || !inputs.ocrResult || isEmpty(inputs.ocrResult)) {
      this._documentReproductionInputs = null;
      return;
    }
    this._documentReproductionInputs = inputs;
    this.handleInputsChanged();
  }
  get file() {
    return this._documentReproductionInputs?.file;
  }
  get ocrResult() {
    return this._documentReproductionInputs?.ocrResult;
  }

  _currentPage = 1;
  @Input()
  set currentPage(page: number) {
    if (!isNumber(page)) return;
    this._currentPage = page;
    if (!this.drawingCanvas || !this.pagesDimension || !this.pagesDimension[page - 1])
      return;

    this.resizeAndDrawCanvas();
  }
  get currentPage() {
    return this._currentPage;
  }

  _isShowingBbox: {
    paragraph: boolean;
    line: boolean;
    phrase: boolean;
  };
  @Input()
  set isShowingBbox(inputs: { paragraph: boolean; line: boolean; phrase: boolean }) {
    this._isShowingBbox = inputs;
    this.drawTextOnCanvas();
  }
  get isShowingParagraphBbox() {
    return this._isShowingBbox?.paragraph;
  }
  get isShowingLineBbox() {
    return this._isShowingBbox?.line;
  }
  get isShowingPhraseBbox() {
    return this._isShowingBbox?.phrase;
  }

  @Output() afterFileLoaded = new EventEmitter<{ numPages: number }>();

  pdfDocument: PDFDocument; // pdfLib: for reading pages dimension and pdf creation
  pdfjs: PDFDocumentProxy; // pdf.js: for rending pdf pages into images

  constructor(
    private utils: UtilsService,
    private toastrService: ToastrService,
    private modalService: NzModalService,
    private pdfService: PdfService
  ) {}

  /* set file and setup all file-related properties: file, fileName, fileLink, originalFileLink, pagesDimension, pdfDocument, pdfjs */
  private async handleInputsChanged() {
    /* cleanup from previous inputs */
    this.pdfjs?.cleanup();
    this.pdfjs?.destroy();
    this.pagesDimension = null;
    this.listImageURL = [];
    const file = this.file;

    const pagesDimension: {
      width: number;
      height: number;
      orientation: PageOrientation;
    }[] = [];
    if (file.type === 'application/pdf') {
      const fileArrayBuffer = await this.file.arrayBuffer();
      try {
        this.pdfjs = await this.pdfService.pdfjsDist.getDocument({
          url: URL.createObjectURL(new Blob([fileArrayBuffer]))
        }).promise;
        this.afterFileLoaded.next({ numPages: this.pdfjs.numPages });
        for (let pageNo = 1; pageNo <= this.pdfjs.numPages; pageNo++) {
          const page = await this.pdfjs.getPage(pageNo);
          const viewport = page.getViewport({ scale: 1.0 });
          const orientation =
            viewport.width / viewport.height >= 1
              ? PageOrientation.LANDSCAPE
              : PageOrientation.PORTRAIT;
          pagesDimension.push({
            height: viewport.height,
            width: viewport.width,
            orientation
          });
        }
      } catch (error) {
        // notify user about potentially corrupted pdf file
        this.modalService.error({
          nzTitle: 'Đã có lỗi xảy khi đọc file PDF',
          nzContent: `
            File PDF này có thể bị hỏng hoặc bị lỗi định dạng. Vui lòng sử dụng các công cụ sửa chữa (ví dụ như <a href="https://www.ilovepdf.com/repair-pdf" target="_blank"><b>ilovepdf.com/repair-pdf</b></a>) để khôi phục file PDF trước khi thử lại!
          `
        });
        throw error; // rethrow error
      }
    } else {
      const { height, width } = await new Promise<{
        width: number;
        height: number;
      }>((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(this.file);
        reader.onload = (e) => {
          const image = new Image();
          image.src = e.target.result.toString();
          image.onload = (e) => {
            const height = get(e, 'target.height', 0);
            const width = get(e, 'target.width', 0);
            resolve({ height, width });
            return true;
          };
          image.onerror = reject;
        };
      });
      pagesDimension.push({
        width,
        height,
        orientation:
          width / height >= 1 ? PageOrientation.LANDSCAPE : PageOrientation.PORTRAIT
      });
      this.afterFileLoaded.next({ numPages: 1 });
    }
    this.pagesDimension = pagesDimension;

    this.resizeAndDrawCanvas();
  }

  private resizeAndDrawCanvas() {
    this.drawingCanvas?.dispose();
    this.drawingCanvas = null;
    /* always resize drawing canvas elem and its wrapper first */
    this.drawingCanvasElem.nativeElement.width = 0;
    this.drawingCanvasElem.nativeElement.height = 0;

    const drawingCanvasWrapper = this.drawingCanvasWrapperElem.nativeElement;
    const pageDimension = this.pagesDimension[this.currentPage - 1];

    let drawingCanvasWidth = drawingCanvasWrapper.offsetWidth;
    let drawingCanvasHeight =
      drawingCanvasWrapper.offsetWidth / (pageDimension.width / pageDimension.height);
    /* a solution to the problem of the drawing canvas width and height sometime not fitting its wrapper.
       once drawing canvas height is calculated and greater than current wrapper height
       set the height of the wrapper to the calculated canvas height (via style), this resize opeartion will cause scrollbar to appear
       scrollbar appearence will cause the wrapper width to shrink (a little bit)
       use that exact wrapper width to re-calculated canvas height
       remove the wrapper height setting (via style), return wrapper style to intial state
    */
    const willDrawingCanvasWrapperHeightBeExceeded =
      drawingCanvasHeight > drawingCanvasWrapper.offsetHeight;

    if (willDrawingCanvasWrapperHeightBeExceeded) {
      drawingCanvasWrapper.style.height = Math.ceil(drawingCanvasHeight) + 'px';
      drawingCanvasWidth = Math.floor(drawingCanvasWrapper.offsetWidth);
      drawingCanvasHeight =
        drawingCanvasWrapper.offsetWidth / (pageDimension.width / pageDimension.height);
    }
    drawingCanvasWrapper.style.height = null;

    // return;

    this.drawingCanvas = new fabric.Canvas(this.drawingCanvasElem.nativeElement, {
      selection: false,
      width: drawingCanvasWidth,
      height: drawingCanvasHeight,
      imageSmoothingEnabled: false // help (a litle bit) with blurry canvas, especially tiny scaled text
    });
    this.drawingCanvas
      .on('mouse:down', (e) => {})
      .on('mouse:up', () => {
        this.drawingCanvas.getObjects().forEach((obj) => {
          if (obj.type === 'text') obj.set('backgroundColor', null);
        });
        this.drawingCanvas.renderAll();
      });

    this.drawTextOnCanvas();
  }

  private async drawTextOnCanvas() {
    if (!this.drawingCanvas) return;

    const paragraphs = get(
      this.ocrResult,
      `paragraphs.${this.currentPage - 1}.cells`,
      []
    );

    /* clear prev page */
    this.drawingCanvas.clear();

    /* draw text */
    const phrases = get(this.ocrResult, `phrases.${this.currentPage - 1}.cells`, []);
    phrases.forEach((phrase) => {
      const [x, y, bboxWidth, bboxHeight] = this.utils.getDrawingBoundingBox(
        phrase.bboxes[this.currentPage], // bboxes array is stored in obj with key as corresponding current page (redundance since phrases array in response is organized to reflect pages)
        this.drawingCanvas.getWidth(),
        this.drawingCanvas.getHeight()
      );

      let paragraphWhichPhraseBelongsTo;
      isNumber(phrase?.paragraph_id) &&
        (paragraphWhichPhraseBelongsTo = paragraphs.find(
          (pr) => pr?.paragraph_id === phrase?.paragraph_id
        ));

      const isDrawingTextBbox = false;
      if (isDrawingTextBbox) {
        this.drawingCanvas.add(
          new fabric.Rect({
            left: x,
            top: y,
            width: bboxWidth,
            height: bboxHeight,
            fill: 'transparent',
            stroke: 'green',
            hoverCursor: 'default',
            selectable: false
          })
        );
      }
      // ! using bounding box to scale size of text === same size texts could result in non-uniformed size due to varied bbox size !
      const text = new fabric.Text(phrase.text, {
        left: x,
        top: y,
        fontSize: 14,
        fontStyle: phrase?.font_styles?.includes('italic') ? 'italic' : 'normal',
        fontWeight: phrase?.font_styles?.includes('bold') ? 700 : 'normal',
        fontFamily: 'Times New Roman',
        selectable: false,
        hoverCursor: 'text',
        type: `para_${phrase?.paragraph_id}`
      });
      text
        .on('mousedown', () => {
          if (paragraphWhichPhraseBelongsTo) {
            const phrasesBelongToPara = this.drawingCanvas.getObjects(
              `para_${paragraphWhichPhraseBelongsTo?.paragraph_id}`
            );
            phrasesBelongToPara.forEach((item) =>
              item.set('backgroundColor', 'lightgrey')
            );
          } else {
            text.set('backgroundColor', 'lightgrey');
          }
          this.drawingCanvas.renderAll();
        })
        .on('mouseup', () => {
          if (paragraphWhichPhraseBelongsTo) {
            const phrasesBelongToPara = this.drawingCanvas.getObjects(
              `para_${paragraphWhichPhraseBelongsTo?.paragraph_id}`
            );
            phrasesBelongToPara.forEach((item) =>
              item.set('backgroundColor', 'transparent')
            );
          } else {
            text.set('backgroundColor', 'transparent');
          }
          this.drawingCanvas.renderAll();
        })
        .on('mousedblclick', () => {
          const text = paragraphWhichPhraseBelongsTo
            ? paragraphWhichPhraseBelongsTo.text
            : phrase.text;
          navigator.clipboard.writeText(text);
          this.toastrService.clear();
          this.toastrService.info(`"${text}" đã được copy`);
        });

      const textScaleFactor = Math.min(bboxWidth / text.width, bboxHeight / 14);
      text.scale(textScaleFactor);
      this.drawingCanvas.add(text);
    });

    /* draw paragraph type Figure (stamps) */
    const figures = paragraphs.filter((paragraph) => paragraph.type === 'Figure'); // phrases.filter((phrase) => phrase.text.includes('stamp123@_'));
    if (figures.length) {
      const currentPageNumberWhenRenderPageImgStart = this.currentPage;
      await this.getImageURLByPageNumber(this.currentPage);
      // this code take a while to reach, once reached check if current page number still match with stamp's page number
      if (currentPageNumberWhenRenderPageImgStart !== this.currentPage) return;
      const img = await fabric.Image['fromURLAsync'](
        this.listImageURL[this.currentPage - 1]
      );
      figures.forEach(async (figure) => {
        const [x, y, bboxWidth, bboxHeight] = this.utils.getDrawingBoundingBox(
          figure.bboxes[this.currentPage],
          this.drawingCanvas.getWidth(),
          this.drawingCanvas.getHeight()
        );
        const figureURL = img.toDataURL({
          left: x,
          top: y,
          width: bboxWidth,
          height: bboxHeight
        });
        const figureImg = await fabric.Image['fromURLAsync'](figureURL, {
          left: x,
          top: y,
          width: bboxWidth,
          height: bboxHeight,
          selectable: false,
          opacity: 1,
          // stroke: 'green',
          // strokeWidth: 1,
          hoverCursor: 'default'
        });
        // this code take a while to reach, once reached check if current page number still match with stamp's page number
        if (currentPageNumberWhenRenderPageImgStart !== this.currentPage) return;
        this.drawingCanvas.add(figureImg);
        this.drawingCanvas.sendToBack(figureImg);
        this.drawingCanvas.renderAll();
        return;
      });
    }

    /* draw box for table cell */
    let tables = [];
    paragraphs.forEach((item) => {
      if (item.type === 'Table') tables.push(item);
    });
    tables.forEach((table) => {
      let parsedTableRows = [];
      try {
        parsedTableRows = this.utils.parseTableHtml(table['html']);
      } catch (error) {
        return; // skip invalid table
      }
      const cells = flatten(parsedTableRows).filter(
        (cell) =>
          cell['filled'] &&
          !cell['padded'] &&
          !isNumber(cell['original_x']) &&
          !isNumber('original_y')
      );
      cells.forEach((cell, index) => {
        const [x, y, width, height] = this.utils.getDrawingBoundingBox(
          table.cells[index].bboxes[table.page_id],
          this.drawingCanvas.getWidth(),
          this.drawingCanvas.getHeight()
        );
        const cellRect = new fabric.Rect({
          left: x,
          top: y,
          width,
          height,
          fill: 'transparent',
          // opacity: 1,
          stroke: 'black',
          strokeWidth: 1,
          selectable: false,
          hoverCursor: 'default'
        });
        cellRect
          .on('mouseover', (e) => {
            cellRect.set('hoverCursor', 'cell');
            cellRect.set('backgroundColor', 'black');
            cellRect.set('opacity', 0.15);
            this.drawingCanvas.renderAll();
          })
          .on('mouseout', (e) => {
            cellRect.set('hoverCursor', 'default');
            cellRect.set('backgroundColor', null);
            cellRect.set('opacity', 1);
            this.drawingCanvas.renderAll();
          })
          .on('mousedblclick', () => {
            navigator.clipboard.writeText(cell.text);
            this.toastrService.clear();
            this.toastrService.info(`"${cell.text}" đã được copy`);
          });
        this.drawingCanvas.add(cellRect);
      });
    });

    /* draw bounding boxes */
    if (this.isShowingParagraphBbox) {
      paragraphs.forEach((paragraph) => {
        switch (paragraph.type) {
          case 'Paragraph':
          case 'Header':
          case 'Title':
          case 'Footer':
          case 'Footnote':
          case 'Figure': {
            const [left, top, width, height] = this.utils.getDrawingBoundingBox(
              get(paragraph, `bboxes.${this.currentPage}`, [0, 0, 0, 0]),
              this.drawingCanvas.getWidth(),
              this.drawingCanvas.getHeight()
            );
            this.drawingCanvas.add(
              this.createBoundingBox({
                type: 'paragraph',
                left,
                top,
                width,
                height,
                debugText: `type: ${paragraph.type}\nparagraph_id: ${paragraph?.paragraph_id}`
              })
            );
            break;
          }
          case 'Table': {
            const topLeftX = Math.min(
              ...paragraph.cells.map((cell) => cell.bboxes[paragraph.page_id][0])
            );
            const topLeftY = Math.min(
              ...paragraph.cells.map((cell) => cell.bboxes[paragraph.page_id][1])
            );
            const bottomRightX = Math.max(
              ...paragraph.cells.map((cell) => cell.bboxes[paragraph.page_id][2])
            );
            const bottomRightY = Math.max(
              ...paragraph.cells.map((cell) => cell.bboxes[paragraph.page_id][3])
            );
            const [left, top, width, height] = this.utils.getDrawingBoundingBox(
              [topLeftX, topLeftY, bottomRightX, bottomRightY],
              this.drawingCanvas.getWidth(),
              this.drawingCanvas.getHeight()
            );
            this.drawingCanvas.add(
              this.createBoundingBox({
                type: 'paragraph',
                left: left - 2,
                top: top - 2,
                width: width + 4,
                height: height + 4,
                debugText: `type: ${paragraph.type}\nparagraph_id: ${paragraph?.paragraph_id}`
              })
            );
            break;
          }
        }
      });
    }
    if (this.isShowingLineBbox) {
      const lines = get(this.ocrResult, `lines.${this.currentPage - 1}.cells`, []);
      lines.forEach((line) => {
        const [left, top, width, height] = this.utils.getDrawingBoundingBox(
          get(line, `bboxes.${this.currentPage}`, [0, 0, 0, 0]),
          this.drawingCanvas.getWidth(),
          this.drawingCanvas.getHeight()
        );
        this.drawingCanvas.add(
          this.createBoundingBox({
            type: 'line',
            left,
            top,
            width,
            height,
            debugText: `type: ${line.type}\nparagraph_id: ${line?.paragraph_id}`
          })
        );
      });
    }
    if (this.isShowingPhraseBbox) {
      const phrases = get(this.ocrResult, `phrases.${this.currentPage - 1}.cells`, []);
      phrases.forEach((phrase) => {
        const [left, top, width, height] = this.utils.getDrawingBoundingBox(
          get(phrase, `bboxes.${this.currentPage}`, [0, 0, 0, 0]),
          this.drawingCanvas.getWidth(),
          this.drawingCanvas.getHeight()
        );
        this.drawingCanvas.add(
          this.createBoundingBox({
            type: 'phrase',
            left,
            top,
            width,
            height,
            debugText: `type: ${phrase.type}\nparagraph_id: ${phrase?.paragraph_id}`
          })
        );
      });
    }
  }

  private async getImageURLByPageNumber(pageNumber) {
    const i = pageNumber - 1;
    if (this.listImageURL[i] || (this.file.type === 'application/pdf' && !this.pdfjs))
      return; // already rendered to dataURL

    const drawingCanvasWrapper = this.drawingCanvasWrapperElem.nativeElement;
    const canvasFactory = new NodeCanvasFactory();
    const { canvas, context } = canvasFactory.create(
      drawingCanvasWrapper.offsetWidth,
      drawingCanvasWrapper.offsetWidth /
        (this.pagesDimension[i].width / this.pagesDimension[i].height)
    );
    if (this.file.type === 'application/pdf') {
      const page = await this.pdfjs.getPage(i + 1);
      const viewport = page.getViewport({ scale: window.devicePixelRatio });
      const renderContext = {
        canvasContext: context,
        viewport,
        transform: [
          canvas.height / viewport.height,
          0,
          0,
          canvas.width / viewport.width,
          0,
          0
        ]
      };
      // render pdf page into image
      await page.render(renderContext as any).promise;
      this.listImageURL[i] = canvas.toDataURL('image/png');
      page.cleanup();
    } else {
      const image = await loadImage(URL.createObjectURL(this.file)); // cannot use loadImage(Buffer.from(fileArrayBuffer)) like in exporter, browser does NOT have native Buffer support
      context.drawImage(image, 0, 0, canvas.width, canvas.height);
      this.listImageURL[i] = canvas.toDataURL('image/png');
    }
    canvasFactory.destroy({ canvas, context });
  }

  private createBoundingBox({
    type,
    left,
    top,
    width,
    height,
    debugText
  }: {
    type: 'paragraph' | 'line' | 'phrase';
    left: number;
    top: number;
    width: number;
    height: number;
    debugText: string;
  }) {
    let stroke;
    switch (type) {
      case 'paragraph':
        stroke = 'red';
        break;
      case 'line':
        stroke = 'green';
        break;
      case 'phrase':
        stroke = 'blue';
        break;
      default:
        stroke = 'yellow';
        break;
    }

    return new fabric.Rect({
      fill: 'transparent',
      stroke,
      selectable: false,
      hoverCursor: 'default',
      left,
      top,
      width,
      height
    })
      .off('mouseover')
      .off('mouseout')
      .on('mouseover', (e) => {
        if (!e.target) return;
        this.drawingCanvas.add(
          new fabric.Text(debugText, {
            left,
            top,
            hoverCursor: 'default',
            fill: 'white',
            backgroundColor: 'black',
            fontWeight: '600',
            selectable: false,
            fontSize: 14,
            type: 'debug_hovering_text'
          })
        );
        e.target.bringToFront(); // prevent flickering
        this.drawingCanvas.requestRenderAll();
        console.debug(debugText);
      })
      .on('mouseout', (e) => {
        this.drawingCanvas
          .getObjects('debug_hovering_text')
          .forEach((hoveringText) => this.drawingCanvas.remove(hoveringText));
        this.drawingCanvas.requestRenderAll();
      });
  }

  ngOnDestroy(): void {
    this.pdfjs?.cleanup();
    this.pdfjs?.destroy();
    this.drawingCanvas?.dispose();
  }
}
