<div class="py-3 px-4 flex items-center justify-between gap-4">
  <div class="flex-1 font-semibold truncate text-base">Chỉnh sửa {{ imgTitle }}</div>
  <button (click)="cancelEditing()">
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 12L12 4"
        stroke="#6C7093"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 12L4 4"
        stroke="#6C7093"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </button>
</div>
<div class="py-3 px-4 flex items-center gap-2 border-y border-line">
  <button
    nz-tooltip
    [nzTooltipTitle]="rotationDeg + '&deg;'"
    class="flex items-center gap-1 font-medium"
    (click)="rotate(-90)"
  >
    <ng-container
      *ngTemplateOutlet="rotateIcon; context: { flipHorizontal: true }"
    ></ng-container>
    Xoay trái
  </button>
  <!-- <div class="text-center min-w-[55px]">{{ rotationDeg }}&deg;</div> -->
  <button
    nz-tooltip
    [nzTooltipTitle]="rotationDeg + '&deg;'"
    class="flex items-center gap-1 font-medium"
    (click)="rotate(90)"
  >
    <ng-container *ngTemplateOutlet="rotateIcon"></ng-container>
    Xoay phải
  </button>
  <div class="">|</div>
  <button class="flex items-center gap-1 font-medium" (click)="flip('horizontal')">
    <ng-container *ngTemplateOutlet="flipIcon"></ng-container>
    Lật ngang
  </button>
  <button class="flex items-center gap-1 font-medium" (click)="flip('vertical')">
    <ng-container
      *ngTemplateOutlet="flipIcon; context: { rotate90: true }"
    ></ng-container>
    Lật dọc
  </button>
  <div>|</div>
  <button
    class="flex items-center gap-1 font-medium"
    *ngIf="!cropRect && !isCropping"
    (click)="setupDrawBboxEvent()"
  >
    <ng-container *ngTemplateOutlet="cropIcon"></ng-container>
    Cắt
  </button>
  <button
    class="flex items-center gap-1 font-medium"
    *ngIf="!cropRect && isCropping"
    (click)="removeSetupDrawBboxEvent()"
  >
    <ng-container *ngTemplateOutlet="cropIcon"></ng-container>
    Hủy cắt
  </button>
  <button
    class="flex items-center gap-1 font-medium"
    *ngIf="!!cropRect"
    (click)="removeCropRect()"
  >
    <ng-container *ngTemplateOutlet="cropIcon"></ng-container>
    Xóa vùng cắt
  </button>
  <div>|</div>
  <button
    class="flex items-center gap-1 font-medium"
    nz-popover
    nzPopoverTrigger="click"
    [nzPopoverContent]="tiltAction"
    (nzPopoverVisibleChange)="$event ? toggleGrid('on') : toggleGrid('off')"
    nzPopoverPlacement="top"
  >
    <ng-container *ngTemplateOutlet="tiltIcon"></ng-container>
    Căn thẳng
  </button>
  <nz-switch
    [disabled]="false"
    nzSize="default"
    [ngModel]="gridEnabled"
    nzCheckedChildren="Lưới"
    nzUnCheckedChildren="Lưới"
    [nzControl]="true"
    (click)="toggleGrid()"
  ></nz-switch>
  <div class="ml-auto flex gap-3 items-center">
    <button class="flex items-center gap-1 font-medium" (click)="reset()">
      <ng-container *ngTemplateOutlet="resetIcon"></ng-container>
      Đặt lại
    </button>
    <button class="flex items-center gap-1 font-medium text-brand-1" (click)="ok()">
      <ng-container *ngTemplateOutlet="saveIcon"></ng-container>
      Lưu
    </button>
  </div>
</div>
<div
  #imageEditorWrapper
  id="image-editor-wrapper"
  class="flex-1 relative h-[750px] w-full bg-[#202225]"
>
  <canvas style="width: inherit; height: inherit" id="image-editor">
    Your browser does not support the canvas element
  </canvas>
</div>
<!-- <div class="h-3 relative border-t border-line">
  <div
    class="absolute -top-8 left-1/2 -translate-x-1/2 flex items-center gap-2 px-3 py-[6px] rounded-lg bg-white border border-line text-xs"
  >
    <button class="p-1">
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M3.3335 8H12.6668"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
    <div class="min-w-[50px] text-center bg-bg-1 rounded-[4px] font-semibold p-1">
      100%
    </div>
    <button class="p-1">
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M3.3335 8H12.6668"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M8 12.6673V3.33398"
          stroke="#6C7093"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
</div> -->

<ng-template #tiltAction>
  <div class="bg-bg-3 rounded-2xl border border-line shadow-md p-2">
    <div class="text-center font-semibold">{{ sliderValue }}&deg;</div>
    <div class="flex gap-3 font-medium">
      <button
        class="text-xs text-white p-1 rounded-lg bg-brand-1"
        (click)="tilt(sliderValue - sliderStep)"
      >
        -0.5&deg;
      </button>
      <nz-slider
        class="w-[720px]"
        [nzMax]="sliderValueMax"
        [nzMin]="sliderValueMin"
        [nzStep]="sliderStep"
        [nzIncluded]="false"
        nzTooltipVisible="never"
        [ngModel]="sliderValue"
        (ngModelChange)="tilt($event)"
      ></nz-slider>
      <button
        class="text-xs text-white p-1 rounded-lg bg-brand-1"
        (click)="tilt(sliderValue + sliderStep)"
      >
        +0.5&deg;
      </button>
    </div>
  </div>
</ng-template>

<ng-template #rotateIcon let-flipHorizontal="flipHorizontal">
  <svg
    [ngClass]="{ '-scale-x-100': flipHorizontal }"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.1665 14.666H8.1665C5.6665 14.666 4.6665 13.666 4.6665 11.166V8.16602C4.6665 5.66602 5.6665 4.66602 8.1665 4.66602H11.1665C13.6665 4.66602 14.6665 5.66602 14.6665 8.16602V11.166C14.6665 13.666 13.6665 14.666 11.1665 14.666Z"
      stroke="#6C7093"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M1.3335 6.00065C1.3335 3.42065 3.42016 1.33398 6.00016 1.33398L5.30016 2.50065"
      stroke="#6C7093"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #flipIcon let-rotate90="rotate90">
  <svg
    [ngClass]="{ 'rotate-90': rotate90 }"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.855 3.14409C1.78469 3.07471 1.6954 3.02772 1.59841 3.00903C1.50141 2.99034 1.40106 3.0008 1.31 3.03909C1.21869 3.0766 1.14053 3.1403 1.08536 3.22216C1.03019 3.30402 1.00049 3.40038 1 3.49909V12.4991C1.00049 12.5978 1.03019 12.6942 1.08536 12.776C1.14053 12.8579 1.21869 12.9216 1.31 12.9591C1.36934 12.9871 1.43441 13.0008 1.5 12.9991C1.5658 12.9995 1.63103 12.9869 1.69195 12.962C1.75287 12.9371 1.80828 12.9004 1.855 12.8541L6.355 8.35409C6.40186 8.30761 6.43906 8.25231 6.46445 8.19138C6.48983 8.13045 6.5029 8.0651 6.5029 7.99909C6.5029 7.93309 6.48983 7.86773 6.46445 7.80681C6.43906 7.74588 6.40186 7.69058 6.355 7.64409L1.855 3.14409ZM2 11.2941V4.70409L5.295 7.99909L2 11.2941Z"
      fill="#6C7093"
    />
    <path
      d="M14.69 3.03909C14.5989 3.0008 14.4986 2.99034 14.4016 3.00903C14.3046 3.02772 14.2153 3.07471 14.145 3.14409L9.64497 7.64409C9.59811 7.69058 9.56091 7.74588 9.53552 7.80681C9.51014 7.86773 9.49707 7.93309 9.49707 7.99909C9.49707 8.0651 9.51014 8.13045 9.53552 8.19138C9.56091 8.25231 9.59811 8.30761 9.64497 8.35409L14.145 12.8541C14.1917 12.9004 14.2471 12.9371 14.308 12.962C14.3689 12.9869 14.4342 12.9995 14.5 12.9991C14.5656 13.0008 14.6306 12.9871 14.69 12.9591C14.7813 12.9216 14.8594 12.8579 14.9146 12.776C14.9698 12.6942 14.9995 12.5978 15 12.4991V3.49909C14.9995 3.40038 14.9698 3.30402 14.9146 3.22216C14.8594 3.1403 14.7813 3.0766 14.69 3.03909ZM14 11.2941L10.705 7.99909L14 4.70409V11.2941Z"
      fill="#6C7093"
    />
    <path
      d="M8 1C7.86739 1 7.74021 1.05268 7.64645 1.14645C7.55268 1.24021 7.5 1.36739 7.5 1.5V2.5C7.5 2.63261 7.55268 2.75979 7.64645 2.85355C7.74021 2.94732 7.86739 3 8 3C8.13261 3 8.25979 2.94732 8.35355 2.85355C8.44732 2.75979 8.5 2.63261 8.5 2.5V1.5C8.5 1.36739 8.44732 1.24021 8.35355 1.14645C8.25979 1.05268 8.13261 1 8 1Z"
      fill="#6C7093"
    />
    <path
      d="M8 4C7.86739 4 7.74021 4.05268 7.64645 4.14645C7.55268 4.24021 7.5 4.36739 7.5 4.5V5.5C7.5 5.63261 7.55268 5.75979 7.64645 5.85355C7.74021 5.94732 7.86739 6 8 6C8.13261 6 8.25979 5.94732 8.35355 5.85355C8.44732 5.75979 8.5 5.63261 8.5 5.5V4.5C8.5 4.36739 8.44732 4.24021 8.35355 4.14645C8.25979 4.05268 8.13261 4 8 4Z"
      fill="#6C7093"
    />
    <path
      d="M8 7C7.86739 7 7.74021 7.05268 7.64645 7.14645C7.55268 7.24021 7.5 7.36739 7.5 7.5V8.5C7.5 8.63261 7.55268 8.75979 7.64645 8.85355C7.74021 8.94732 7.86739 9 8 9C8.13261 9 8.25979 8.94732 8.35355 8.85355C8.44732 8.75979 8.5 8.63261 8.5 8.5V7.5C8.5 7.36739 8.44732 7.24021 8.35355 7.14645C8.25979 7.05268 8.13261 7 8 7Z"
      fill="#6C7093"
    />
    <path
      d="M8 10C7.86739 10 7.74021 10.0527 7.64645 10.1464C7.55268 10.2402 7.5 10.3674 7.5 10.5V11.5C7.5 11.6326 7.55268 11.7598 7.64645 11.8536C7.74021 11.9473 7.86739 12 8 12C8.13261 12 8.25979 11.9473 8.35355 11.8536C8.44732 11.7598 8.5 11.6326 8.5 11.5V10.5C8.5 10.3674 8.44732 10.2402 8.35355 10.1464C8.25979 10.0527 8.13261 10 8 10Z"
      fill="#6C7093"
    />
    <path
      d="M8 13C7.86739 13 7.74021 13.0527 7.64645 13.1464C7.55268 13.2402 7.5 13.3674 7.5 13.5V14.5C7.5 14.6326 7.55268 14.7598 7.64645 14.8536C7.74021 14.9473 7.86739 15 8 15C8.13261 15 8.25979 14.9473 8.35355 14.8536C8.44732 14.7598 8.5 14.6326 8.5 14.5V13.5C8.5 13.3674 8.44732 13.2402 8.35355 13.1464C8.25979 13.0527 8.13261 13 8 13Z"
      fill="#6C7093"
    />
  </svg>
</ng-template>

<ng-template #cropIcon>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.60016 12.6673H12.6668V6.60065C12.6668 4.00065 12.0002 3.33398 9.40016 3.33398H3.3335V9.40065C3.3335 12.0007 4.00016 12.6673 6.60016 12.6673Z"
      stroke="#6C7093"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M3.3335 3.33398V1.33398"
      stroke="#6C7093"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M3.3335 3.33398H1.3335"
      stroke="#6C7093"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M12.6665 12.666V14.666"
      stroke="#6C7093"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M12.6665 12.666H14.6665"
      stroke="#6C7093"
      stroke-miterlimit="10"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #tiltIcon>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.68773 8.54755H1.33356V7.50588H2.68773V8.54755ZM3.76368 6.46421L5.11897 2.71939C5.19567 2.50759 5.39607 2.37576 5.60929 2.37576C5.66798 2.37576 5.7277 2.38573 5.78599 2.40689L13.2813 5.1195C13.4121 5.1669 13.5167 5.26232 13.5757 5.38836C13.6347 5.5143 13.6411 5.6557 13.5938 5.78652L13.3486 6.46431H14.4564L14.5733 6.14103C14.8665 5.33089 14.4459 4.43326 13.6358 4.14009L6.1405 1.42737C5.33036 1.1341 4.43273 1.55473 4.13956 2.36487L2.65589 6.46421H3.76368ZM12.2175 9.58922L10.8811 13.2819C10.7834 13.5518 10.4842 13.692 10.2141 13.5944L2.71886 10.8817C2.44878 10.784 2.3086 10.4847 2.40636 10.2147L2.6327 9.58922H1.52491L1.42684 9.86022C1.13367 10.6703 1.5542 11.568 2.36435 11.8612L9.85959 14.5739C10.0347 14.6371 10.2137 14.6672 10.3898 14.6672C11.0293 14.6672 11.6307 14.2714 11.8606 13.6364L13.3253 9.58922H12.2175ZM5.08347 7.50588H3.7294V8.54755H5.08347V7.50588ZM7.47931 7.50588H6.12514V8.54755H7.47931V7.50588ZM9.87515 7.50588H8.52098V8.54755H9.87515V7.50588ZM12.271 7.50588H10.9168V8.54755H12.271V7.50588ZM14.6668 7.50588H13.3127V8.54755H14.6668V7.50588Z"
      fill="#6C7093"
    />
  </svg>
</ng-template>

<ng-template #resetIcon>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.6668 8.00065C14.6668 11.6807 11.6802 14.6673 8.00016 14.6673C4.32016 14.6673 2.0735 10.9607 2.0735 10.9607M2.0735 10.9607H5.08683M2.0735 10.9607V14.294M1.3335 8.00065C1.3335 4.32065 4.2935 1.33398 8.00016 1.33398C12.4468 1.33398 14.6668 5.04065 14.6668 5.04065M14.6668 5.04065V1.70732M14.6668 5.04065H11.7068"
      stroke="#6C7093"
      stroke-width="1.5"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
</ng-template>

<ng-template #saveIcon>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_13513_50017)">
      <path
        d="M4.33301 12.332V8.33203H12.333V12.332"
        stroke="#0667E1"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M13.6665 14.9993H2.99984C2.64622 14.9993 2.30708 14.8589 2.05703 14.6088C1.80698 14.3588 1.6665 14.0196 1.6665 13.666V2.99935C1.6665 2.64573 1.80698 2.30659 2.05703 2.05654C2.30708 1.80649 2.64622 1.66602 2.99984 1.66602H11.6665L14.9998 4.99935V13.666C14.9998 14.0196 14.8594 14.3588 14.6093 14.6088C14.3593 14.8589 14.0201 14.9993 13.6665 14.9993Z"
        stroke="#0667E1"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.333 4.33203V5.66536"
        stroke="#0667E1"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_13513_50017">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
</ng-template>
