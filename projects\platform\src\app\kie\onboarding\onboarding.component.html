<!-- style="background-image: url('assets/kie/document/bg-sidebar.png')" -->
<div class="mt-12">
  <div class="mx-auto max-w-[650px] flex flex-col items-center gap-5">
    <h2 class="text-text-1 text-2xl font-semibold">Chào mừng bạn đến với OCR Platform</h2>
    <div>
      <img src="assets/img/onboarding.png" alt="img-onboarding" />
    </div>
    <div>
      <p class="text-text-1 text-sm font-normal px-3 text-center">
        Gi<PERSON><PERSON> ph<PERSON><PERSON> gi<PERSON><PERSON> c<PERSON> quan, doanh nghiệp xử lý bài toán số hóa và bóc tách thông tin
        từ văn bản hoặc hình ảnh, gi<PERSON>p đẩy nhanh quá trình chuyển đổi số trong các tổ
        chức, nâng cao hi<PERSON>u quả và giảm thời gian xử lý công việc.
      </p>
    </div>
    <div class="flex justify-center gap-8">
      <button
        (click)="showModalCreateFolder()"
        class="rounded-lg flex items-center px-3 gap-2 py-2 bg-brand-1 text-white text-sm font-medium"
      >
        <i nz-icon nzType="plus" nzTheme="outline"></i>
        Tạo mới thư mục
      </button>
      <button
        [class]="
          listFolders.length
            ? 'rounded-lg flex items-center px-3 gap-2 py-2 bg-brand-1 text-white text-sm font-medium'
            : 'rounded-lg flex items-center px-3 gap-2 py-2 bg-bg-1 text-text-4 cursor-not-allowed text-sm font-medium'
        "
        nz-dropdown
        nzTrigger="click"
        [nzDropdownMenu]="listFolders.length ? menu : emptyMenu"
      >
        <i nz-icon nzType="plus" nzTheme="outline"></i>
        Tạo mới văn bản
        <span nz-icon nzType="down"></span>
      </button>
      <nz-dropdown-menu #menu="nzDropdownMenu">
        <ul nz-menu class="rounded-lg shadow-lg">
          <li nz-menu-item (click)="listFolders.length && showModalCreateDocument()">
            <div class="flex gap-2 items-center">
              <img
                src="assets/kie/document/new-doc-template.svg"
                alt="icon-new-folder"
              />
              <span class="text-sm text-text-1 font-normal">
                Tạo văn bản theo mẫu có sẵn
              </span>
            </div>
          </li>
          <li
            nz-menu-item
            (click)="listFolders.length && showModalCreateSampleDocument()"
          >
            <div class="flex gap-2 items-center">
              <img src="assets/kie/document/new-doc-owner.svg" alt="icon-new-folder" />
              <span class="text-sm text-text-1 font-normal">Tự tạo giấy tờ</span>
            </div>
          </li>
        </ul>
      </nz-dropdown-menu>
      <nz-dropdown-menu #emptyMenu="nzDropdownMenu"></nz-dropdown-menu>
    </div>
  </div>
</div>
