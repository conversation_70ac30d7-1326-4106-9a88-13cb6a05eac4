<app-detail-layout
  [contentTemplate]="content"
  [title]="template?.name"
  [goBackCommands]="goBackCommands"
></app-detail-layout>
<ng-template #content>
  <app-config-fields
    [templateConfig]="template?.config"
    [bboxChangeSubject]="bboxChangeSubject"
    [drawCommandSubject]="drawCommandSubject"
    (onTemplateConfigSave)="handleSaveConfigTemplate($event)"
    (onFieldConfigChange)="handleFieldConfigChange($event)"
  ></app-config-fields>
  <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
  <app-document-viewer
    [featureFlags]="{ DisplayOcrResult: true }"
    [fileLink]="file?.fileLink"
    [ocrResult]="file?.ocrResult"
    [bboxChangeSubject]="bboxChangeSubject"
    [drawCommandSubject]="drawCommandSubject"
  ></app-document-viewer>
  <app-ocr-result [ocrResult]="file?.ocrResult"></app-ocr-result>
</ng-template>
<ng-template #actions>
  <button>Làm mới</button>
  <button>Lưu lại</button>
</ng-template>
