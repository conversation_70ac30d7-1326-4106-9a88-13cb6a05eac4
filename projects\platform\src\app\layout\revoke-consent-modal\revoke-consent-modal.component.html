<div class="wrapper">
  <button type="button" class="close-btn" (click)="close()">
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.81227 3.81325C4.04668 3.57891 4.36457 3.44727 4.69602 3.44727C5.02748 3.44727 5.34536 3.57891 5.57977 3.81325L9.99977 8.23325L14.4198 3.81325C14.6555 3.58555 14.9713 3.45956 15.299 3.46241C15.6268 3.46525 15.9403 3.59672 16.172 3.82848C16.4038 4.06024 16.5353 4.37375 16.5381 4.7015C16.541 5.02924 16.415 5.345 16.1873 5.58075L11.7673 10.0007L16.1873 14.4207C16.415 14.6565 16.541 14.9723 16.5381 15.3C16.5353 15.6277 16.4038 15.9413 16.172 16.173C15.9403 16.4048 15.6268 16.5362 15.299 16.5391C14.9713 16.5419 14.6555 16.4159 14.4198 16.1882L9.99977 11.7682L5.57977 16.1882C5.34402 16.4159 5.02827 16.5419 4.70052 16.5391C4.37277 16.5362 4.05926 16.4048 3.8275 16.173C3.59574 15.9413 3.46428 15.6277 3.46143 15.3C3.45858 14.9723 3.58457 14.6565 3.81227 14.4207L8.23227 10.0007L3.81227 5.58075C3.57793 5.34634 3.44629 5.02845 3.44629 4.697C3.44629 4.36554 3.57793 4.04766 3.81227 3.81325Z"
        fill="#757575"
      />
    </svg>
  </button>
  <ng-container [ngSwitch]="step">
    <ng-container *ngSwitchCase="1">
      <ng-template *ngTemplateOutlet="step1"></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="2">
      <ng-template *ngTemplateOutlet="step2"></ng-template>
    </ng-container>
    <ng-container *ngSwitchCase="3">
      <ng-template *ngTemplateOutlet="step3"></ng-template>
    </ng-container>
  </ng-container>
</div>

<ng-template #step1>
  <div class="header">Hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu</div>
  <div class="content">
    <div>
      Tài khoản của quý khách sẽ bị ngắt dịch vụ và không thể truy tập vào hệ thống VNPT
      Smart Reader và trang Website
      <a target="_blank" href="https://vnptai.io/ldp/smartreader">https://vnptai.io/ldp/smartreader</a
      >, khi quý khách xác nhận hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu
    </div>
    <div>
      Do đó, VNPT khuyến nghị Khách hàng đọc thông tin chi tiết tại: Điều 8 mục 8.4 tại
      <a target="_blank" href="https://vnptai.io/ldp/smartreader/vi/personal-data-policy#8"
        >Chính sách bảo vệ dữ liệu cá nhân</a
      >
      của VNPT
    </div>
    <!-- <div style="padding: 12px 0 24px 0; border-bottom: 1px solid #E2E2E2;">
			Tôi đã đọc và đồng ý xóa dữ liệu cá nhân
		</div> -->
    <div class="flex gap-3 items-center pt-3 pb-6 border-b border-[#E2E2E2]">
      <input
        class="w-[18px] h-[18px]"
        [(ngModel)]="confirmed"
        type="checkbox"
        id="agreed"
      />
      <label for="agreed">
        Tôi đã đọc và đồng ý Hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu cá nhân
      </label>
    </div>
  </div>
  <div class="confirm-btn">
    <button (click)="confirmRevokeConsent()" [disabled]="!confirmed" class="">
      Xác nhận
    </button>
  </div>
</ng-template>

<ng-template #step2>
  <div class="flex justify-center text-[#111127] pt-8 pb-5">
    <svg
      width="68"
      height="56"
      viewBox="0 0 68 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M37.8892 35.3333C36.7474 36.1151 35.3827 36.505 34.0003 36.4444C32.6179 36.505 31.2532 36.1151 30.1114 35.3333L0.666992 19.5V47.5555C0.666992 49.7657 1.54496 51.8853 3.10777 53.4481C4.67057 55.0109 6.79018 55.8888 9.00032 55.8888H59.0003C61.2104 55.8888 63.33 55.0109 64.8928 53.4481C66.4556 51.8853 67.3336 49.7657 67.3336 47.5555V19.5L37.8892 35.3333Z"
        fill="#0F67CE"
      />
      <path
        d="M59.0003 0.333984H9.00036C6.79022 0.333984 4.67061 1.21196 3.10781 2.77476C1.54501 4.33756 0.667033 6.45717 0.667033 8.66731V11.4451C0.664379 11.946 0.790771 12.4391 1.03403 12.877C1.27729 13.3149 1.62923 13.6827 2.05592 13.9451L32.6114 30.6117C33.0379 30.8363 33.5203 30.9327 34.0003 30.8895C34.4804 30.9327 34.9627 30.8363 35.3892 30.6117L65.9447 13.9451C66.3714 13.6827 66.7234 13.3149 66.9666 12.877C67.2099 12.4391 67.3363 11.946 67.3336 11.4451V8.66731C67.3336 6.45717 66.4557 4.33756 64.8929 2.77476C63.3301 1.21196 61.2104 0.333984 59.0003 0.333984Z"
        fill="#0F67CE"
      />
    </svg>
  </div>

  <div class="text-center text-base font-bold uppercase">Xác nhận</div>
  <div class="text-center py-2 px-4">
    Quý khách vui lòng truy cập vào email
    <span class="text-[#0F67CE]">{{ account?.username }}</span> để xác nhận
  </div>
  <div class="confirm-btn">
    <button (click)="close()" class="">Đóng</button>
  </div>
</ng-template>

<ng-template #step3>
  <div class="py-7">
    <div class="flex justify-center pb-4">
      <svg
        width="80"
        height="80"
        viewBox="0 0 80 80"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M40 75C49.2826 75 58.185 71.3125 64.7487 64.7487C71.3125 58.185 75 49.2826 75 40C75 30.7174 71.3125 21.815 64.7487 15.2513C58.185 8.68749 49.2826 5 40 5C30.7174 5 21.815 8.68749 15.2513 15.2513C8.68749 21.815 5 30.7174 5 40C5 49.2826 8.68749 58.185 15.2513 64.7487C21.815 71.3125 30.7174 75 40 75ZM40 80C50.6087 80 60.7828 75.7857 68.2843 68.2843C75.7857 60.7828 80 50.6087 80 40C80 29.3913 75.7857 19.2172 68.2843 11.7157C60.7828 4.21427 50.6087 0 40 0C29.3913 0 19.2172 4.21427 11.7157 11.7157C4.21427 19.2172 0 29.3913 0 40C0 50.6087 4.21427 60.7828 11.7157 68.2843C19.2172 75.7857 29.3913 80 40 80Z"
          fill="#24A148"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M54.8503 24.8504C55.55 24.1568 56.4947 23.7664 57.48 23.7637C58.4652 23.761 59.412 24.1461 60.1156 24.8359C60.8191 25.5257 61.223 26.4646 61.2398 27.4498C61.2565 28.4349 60.885 29.3871 60.2053 30.1004L40.2453 55.0504C39.9023 55.4199 39.4882 55.7164 39.028 55.9223C38.5677 56.1281 38.0707 56.239 37.5666 56.2483C37.0625 56.2576 36.5617 56.1652 36.0941 55.9766C35.6265 55.788 35.2018 55.507 34.8453 55.1504L21.6203 41.9204C21.2519 41.5771 20.9564 41.1631 20.7514 40.7031C20.5464 40.2431 20.4362 39.7466 20.4273 39.243C20.4185 38.7395 20.5111 38.2394 20.6997 37.7724C20.8883 37.3055 21.169 36.8813 21.5251 36.5252C21.8812 36.1691 22.3054 35.8884 22.7723 35.6998C23.2393 35.5112 23.7394 35.4186 24.2429 35.4275C24.7464 35.4364 25.243 35.5466 25.703 35.7515C26.163 35.9565 26.577 36.252 26.9203 36.6204L37.3903 47.0854L54.7553 24.9604C54.7863 24.9217 54.8197 24.885 54.8553 24.8504H54.8503Z"
          fill="#24A148"
        />
      </svg>
    </div>
    <div class="text-[#111127] text-center text-base font-bold uppercase">
      xác nhận Thành công
    </div>
  </div>
</ng-template>
