import { Component, OnDestroy, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OcrFinishedComponent } from '@platform/app/ocr/ocr-finished/ocr-finished.component';
import { OcrInProgressComponent } from '@platform/app/ocr/ocr-in-progress/ocr-in-progress.component';
import { OcrPreprocessingComponent } from '@platform/app/ocr/ocr-preprocessing/ocr-preprocessing.component';
import { isEqual } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import {
  BehaviorSubject,
  distinctUntilChanged,
  Observable,
  Subject,
  takeUntil,
  tap
} from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';
import { OcrService } from '@platform/app/core/services/ocr.service';

enum OcrTab {
  Preprocessing,
  InProgress,
  Finished
}

enum TabFinishedViewMode {
  Original,
  Reproduced
}

@Component({
  selector: 'app-ocr-layout',
  templateUrl: './ocr-layout.component.html',
  styleUrls: ['./ocr-layout.component.scss']
})
export class OcrLayoutComponent implements OnDestroy {
  readonly OcrTab = OcrTab;
  readonly TabFinishedViewMode = TabFinishedViewMode;
  readonly tabs: {
    key: 'preprocessing' | 'in-progress' | 'finished';
    title: string;
    documentViewerFF: any;
    confirmLeaveMessage?: string;
    badgeOverflowCount?: number;
  }[] = [
    {
      key: 'preprocessing',
      title: 'Tài liệu tải lên',
      documentViewerFF: OcrPreprocessingComponent.documentViewerFF,
      confirmLeaveMessage: OcrPreprocessingComponent.confirmLeaveMessage,
      badgeOverflowCount: 99
    },
    {
      key: 'in-progress',
      title: 'Tài liệu đang xử lý',
      documentViewerFF: OcrInProgressComponent.documentViewerFF,
      confirmLeaveMessage: OcrInProgressComponent.confirmLeaveMessage,
      badgeOverflowCount: 10
    },
    {
      key: 'finished',
      title: 'Tài liệu đã ocr',
      documentViewerFF: OcrFinishedComponent.documentViewerFF,
      confirmLeaveMessage: OcrFinishedComponent.confirmLeaveMessage,
      badgeOverflowCount: 99
    }
  ];
  selectedTabIndex = 0;
  tabFinishedViewMode: TabFinishedViewMode = TabFinishedViewMode.Reproduced;

  get documentViewerFF() {
    return this.tabs[this.selectedTabIndex].documentViewerFF;
  }
  fileSubject = new BehaviorSubject<{
    fileId: string;
    fileLink: string;
    fileName: string;
    pages?: number[]; // ocr-preprocessing tab
    exportedInJSONLink?: string; // ocr-finished tab
  }>(null);
  afterLoadCompleteFileInfoSubject = new Subject<any>();
  afterLoadCompleteFileInfo$ = this.afterLoadCompleteFileInfoSubject.asObservable();
  editedPageImageSubject = new Subject<{
    data: {
      pageIndex: number;
      editedPageImageLink: string;
    };
    fileId: string;
  }>();
  editedPageImage$ = this.editedPageImageSubject.asObservable();
  @ViewChild('tabLeaveModal')
  tabLeaveModal: TemplateRef<any>;
  fileListCount = {
    preprocessing: 0,
    'in-progress': 0,
    finished: 0
  };
  destroy$ = new Subject<void>();

  constructor(
    private toastr: ToastrService,
    private activatedRoute: ActivatedRoute,
    private modal: NzModalService,
    private ocrService: OcrService,
    private router: Router
  ) {
    this.activatedRoute.paramMap.subscribe((val) => {
      this.selectedTabIndex = this.tabs.findIndex(
        (tab) => tab.key === this.activatedRoute.snapshot.paramMap.get('tabKey')
      );
      if (this.selectedTabIndex === -1) this.router.navigate(['preprocessing']);
      this.fileSubject.next(null);
    });
    this.ocrService.fileListCount$
      .pipe(
        tap((fileListCount) => (this.fileListCount = fileListCount)),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  ngOnInit(): void {
    this.fileSubject
      .pipe(
        distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  onSpecifiedPagesValidation(validation) {
    !validation.valid && this.toastr.error(validation?.errorMsg);
  }

  afterLoadComplete(e: { fileLink: string; numPages: number }) {
    if (!this.fileSubject.value) return;
    this.afterLoadCompleteFileInfoSubject.next({
      info: e,
      fileId: this.fileSubject.value.fileId
    });
  }

  onEditPageImage(e: { pageIndex: number; editedPageImageLink: string }) {
    if (!this.fileSubject.value) return;
    this.editedPageImageSubject.next({
      data: e,
      fileId: this.fileSubject.value.fileId
    });
  }

  canDeactivate(): Observable<boolean> | Promise<boolean> | boolean {
    if (!this.tabs[this.selectedTabIndex]) return;
    const { confirmLeaveMessage, key } = this.tabs[this.selectedTabIndex];
    const showCanDeactivateNotice = this.ocrService.getShowCanDeactivateNotice(key);
    if (showCanDeactivateNotice && confirmLeaveMessage)
      return new Observable((observer) => {
        const modalRef = this.modal.create({
          nzTitle: null,
          nzContent: this.tabLeaveModal,
          nzData: {
            message: confirmLeaveMessage,
            ok: () => modalRef.triggerOk()
          },
          nzFooter: null,
          nzMaskClosable: false,
          nzClosable: true,
          nzClassName: 'custom-ant-modal-common-styles',
          nzBodyStyle: { padding: '20px' },
          nzStyle: { width: '500px' },
          nzOnOk: () => {
            this.ocrService.toggleShowCanDeactivateNotice(key);
            observer.next(true);
            observer.complete();
          },
          nzOnCancel: () => {
            observer.next(false);
            observer.complete();
          }
        });
      });
    return true;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.fileSubject.complete();
    this.afterLoadCompleteFileInfoSubject.complete();
    this.editedPageImageSubject.complete();
  }
}
