import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { cloneDeep, get, omit, pick, set } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { OcrExperienceService } from '@platform/app/core/services/ocr-experience.service';
import { catchError, of, switchMap, tap } from 'rxjs';
import { fabric } from 'fabric';

type Image = {
  path: string | SafeResourceUrl;
  name: string;
  size?: string;
  file?: File;
  isSample?: boolean;
  result?: {
    number: {
      text: string;
      textArr: string[];
      confidence_score: number;
      imageSnip: string;
    };
  };
};

@Component({
  selector: 'app-dong-ho-nuoc',
  templateUrl: './dong-ho-nuoc.component.html',
  styleUrls: ['./dong-ho-nuoc.component.scss'],
})
export class DongHoNuocComponent implements OnInit {
  image: Image;
  readonly sampleList: Image[] = [
    {
      path: 'assets/img/rpa/ocr-experience/demo/image32.png',
      name: 'image32',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image33.png',
      name: 'image33',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image65.png',
      name: 'image65',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image110.png',
      name: 'image110',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image113.png',
      name: 'image113',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image120.png',
      name: 'image120',
      isSample: true,
    },
    {
      path: 'assets/img/rpa/ocr-experience/demo/image125.png',
      name: 'image125',
      isSample: true,
    },
  ];

  constructor(
    private toastrService: ToastrService,
    private sanitizer: DomSanitizer,
    private ocrExperienceService: OcrExperienceService
  ) {}

  ngOnInit(): void {}

  selectSampleFile(img: Image) {
    if (img.path === this.image?.path) return;
    const selected = this.sampleList.find((item) => item.path === img.path);
    if (selected && !selected.file) {
      this.ocrExperienceService
        .fetchFile(selected.path)
        .pipe(
          tap((fileContent) => {
            if (typeof selected.path === 'string') {
              const fileName = selected.path.split('/')?.pop();
              selected.file = new File([fileContent], fileName, {
                type: fileContent.type,
              });
              selected.size =
                (selected.file.size / (1024 * 1024)).toFixed(2) + 'Mb';
              this.image = cloneDeep(selected);
              console.log(this.image, this.sampleList);
            }
          })
        )
        .subscribe();
    } else {
      this.image = cloneDeep(img);
      console.log(this.image, this.sampleList);
    }
  }

  refresh() {
    this.image = null;
  }

  handleChangeFile(event) {
    const fileMimetype = get(event, 'target.files.0.type', '');
    const fileExtension = get(event, 'target.files.0.name', '')
      .split('.')
      .pop()
      .toLowerCase();
    if (
      !'image/jpeg, image/png, image/jpg'
        .split(',')
        .map((item) => item.trim())
        .includes(fileMimetype) ||
      !['jpeg', 'png', 'jpg'].includes(fileExtension)
    ) {
      return this.toastrService.error(
        'File không đúng định dạng. Vui lòng kiểm tra lại!'
      );
    }

    const file: File = event.target.files[0];
    this.image = {
      size: (file.size / (1024 * 1024)).toFixed(2) + 'Mb',
      name: file.name,
      path: this.sanitizer.bypassSecurityTrustResourceUrl(
        URL.createObjectURL(file)
      ),
      file: file,
    };
    console.log(this.image);
  }

  ocr() {
    if (!this.image?.file) return;

    const formData = new FormData();
    formData.append('file', this.image.file);
    formData.append('title', '');
    formData.append('description', '');
    const addFileInput = {
      body: formData,
      isSplitting: false,
      isLandingPageMode: false,
    };

    this.ocrExperienceService
      .addFile(addFileInput)
      .pipe(
        switchMap((addFileRes) => {
          if (!('length' in addFileRes.object))
            return this.ocrExperienceService.ocrDongHoNuoc({
              fileHash: addFileRes.object.hash,
              fileType: addFileRes.object.fileType,
            });
        }),
        tap((ocrRes) => {
          const imageResult = pick(ocrRes.object, ['number']);
          set(imageResult, 'number.type', 'Field');
          set(
            imageResult,
            'number.textArr',
            get(imageResult, 'number.text', '').trim().split('')
          );
          console.log(this.image);
          fabric.Image.fromURL(
            typeof this.image.path === 'string'
              ? this.image.path
              : this.image.path['changingThisBreaksApplicationSecurity'],
            (img) => {
              // console.log(img);

              const [x1, y1, x2, y2] = get(
                imageResult,
                'number.bboxes.1',
                [0, 0, 0, 0]
              );

              const [x, y, bboxWidth, bboxHeight] = [
                x1 * (img.width || 0),
                y1 * (img.height || 0),
                (x2 - x1) * (img.width || 0),
                (y2 - y1) * (img.height || 0),
              ];

              const imageSnip = img.toDataURL({
                left: x,
                top: y,
                width: bboxWidth,
                height: bboxHeight,
              });
              set(imageResult, 'number.imageSnip', imageSnip);
            }
          );

          this.image.result = imageResult;
        }),
        catchError((error) => {
          this.toastrService.error('Bóc tách thất bại!');
          console.log(error);
          return of(error);
        })
      )
      .subscribe();
  }

  getPercentage(num: number, fraction) {
    return `${(num * 100).toFixed(fraction)}%`;
  }
}
