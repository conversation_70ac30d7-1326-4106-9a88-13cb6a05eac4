<app-detail-layout
  [title]="file?.document?.name"
  [contentTemplate]="content"
  [goBackCommands]="goBackCommands"
  [actionsTemplate]="statusBadge"
></app-detail-layout>
<ng-template #content>
  <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
  <app-document-viewer
    [featureFlags]="{
      DisplayOcrResult: true,
      EditOcrResult: true
    }"
    [fileName]="file?.name"
    [fileLink]="file?.fileLink"
    [ocrResult]="file?.ocrResult"
    (onEditOcrResult)="handleEditingOcrResult($event)"
  ></app-document-viewer>
  <app-ocr-result
    [ocrResult]="file?.ocrResult"
    mode="extract-file"
    [actionsTemplate]="
      file?.status === FileStatus.NotAccepted ? actionButtons : undefined
    "
  ></app-ocr-result>
</ng-template>

<ng-template #actionButtons>
  <div class="flex items-center justify-between w-full">
    <button
      class="rounded-lg border border-white px-7 py-2 font-medium flex items-center gap-2"
      [ngClass]="{
        'opacity-50 cursor-not-allowed': !editingFieldSet.size || !checkActionsPermission
      }"
      (click)="handleSaveFileOcrResult()"
    >
      <img class="inline-block" src="assets/kie/document/save.svg" alt="" />
      Lưu
    </button>
    <button
      class="rounded-lg bg-brand-1 border border-brand-1 p-2 font-medium flex items-center gap-2"
      [ngClass]="{
        'opacity-50 cursor-not-allowed': !checkActionsPermission
      }"
      (click)="openModalAcceptFileOcrResult(acceptModal)"
    >
      <img class="inline-block" src="assets/kie/document/check.svg" alt="" />
      Xác nhận
    </button>
  </div>
</ng-template>

<ng-template #statusBadge>
  <div
    class="flex items-center gap-1 rounded-[62px] font-medium py-2 px-4 border border-[#E7FFF380] bg-status-process"
    [ngClass]="{
      '!bg-status-success': file?.status === FileStatus.Accepted
    }"
  >
    <img
      *ngIf="file?.status !== FileStatus.Accepted"
      class="inline-block"
      src="assets/kie/document/clock.svg"
      alt=""
    />
    <img
      *ngIf="file?.status === FileStatus.Accepted"
      class="inline-block"
      src="assets/kie/document/check.svg"
      alt=""
    />
    {{ file?.status === FileStatus.Accepted ? 'Đã xác nhận' : 'Chờ xác nhận' }}
  </div>
</ng-template>

<ng-template #acceptModal>
  <div class="flex flex-col gap-5 items-center justify-center p-5">
    <svg
      width="81"
      height="80"
      viewBox="0 0 81 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="40.5" cy="40" r="40" fill="#FFF8E7" />
      <ellipse cx="40.5" cy="40" rx="27" ry="27" fill="url(#paint0_linear_12089_39775)" />
      <rect x="37.5" y="25" width="6" height="21" rx="3" fill="white" />
      <circle cx="40.5" cy="52" r="3" fill="white" />
      <defs>
        <linearGradient
          id="paint0_linear_12089_39775"
          x1="32.7273"
          y1="15.2689"
          x2="63.6947"
          y2="64.232"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FCC22D" />
          <stop offset="1" stop-color="#FF7A00" />
        </linearGradient>
      </defs>
    </svg>
    <div class="text-center">
      <span class="font-semibold text-lg text-[#282D57] mb-1 inline-block">Lưu ý</span>
      <br />
      <span class="text-text-1"
        >Thông tin sau khi xác nhận sẽ được chuyển sang trạng thái đã xác nhận và không
        thể chỉnh sửa. Bạn có muốn tiếp tục xác nhận giấy tờ này không?
      </span>
    </div>
    <div class="flex items-center justify-center gap-5">
      <button
        (click)="handleAcceptFileOcrResult()"
        class="py-2 rounded-lg w-[126px] border border-brand-1 bg-brand-1 text-white font-medium"
      >
        Xác nhận
      </button>
      <button
        (click)="handleCancelModalAcceptFileOcrResult()"
        class="py-2 rounded-lg w-[126px] border border-brand-1 text-brand-1 font-medium"
      >
        Không
      </button>
    </div>
  </div>
</ng-template>
