import { Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ExportService } from '@platform/app/core/services/export.service';
import {
  FunctionType,
  Template,
  TemplateType,
  excludedKeyFields,
  TemplateTypes,
  goiYPhongBanMap,
  Mode,
  FIELD_ORDER
} from '../ocr-experience';
import { OcrExperienceService } from '@platform/app/core/services/ocr-experience.service';
import { ToastrService } from 'ngx-toastr';
import { get } from 'lodash';
import { KeyValue } from '@angular/common';
import { fabric } from 'fabric';
import { PDFDocument } from 'pdf-lib';
import { catchError, EMPTY, firstValueFrom, switchMap, tap } from 'rxjs';
import { environment } from '@platform/environment/environment';
import { ReCaptchaV3Service, ScriptService } from 'ngx-captcha';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ScanTableExportModalComponent } from '../components/scan-table-export-modal/scan-table-export-modal.component';
import UtilsService from '@platform/app/core/services/utils.service';
import { PageOrientation } from 'docx';

@Component({
  selector: 'app-ocr-result',
  templateUrl: './ocr-result.component.html',
  styleUrls: ['./ocr-result.component.scss']
})
export class OcrResultComponent implements OnInit, OnDestroy {
  readonly Mode = Mode;
  mode: Mode = Mode.Platform; // default
  readonly excludedKeyFields = excludedKeyFields;
  fileHash: string & string[];
  fileType: string;
  templateType: Template;
  TemplateType = TemplateType;
  FunctionType = FunctionType;
  isUsingSampleFile: boolean;
  ocrResult: any = {};
  devModeEnabled;
  tableFields: { [fieldName: string]: { rows: any[]; columns: any[] } } = {};
  currentPage = 1;
  isShowingParagraphBbox = true;
  isShowingLineBbox = false;
  isShowingPhraseBbox = false;
  pageCategories = [];
  categorizedPageRanges = [];
  siteKey = environment.recaptchaV3SiteKey;
  tab: 'ocr-result' | 'doc-suggestion' | null = 'ocr-result';
  suggestionName = [];
  @ViewChild('submitSuccessModal')
  submitSuccessModal: TemplateRef<any>;
  navigateInput = 'ocr-experience';
  shouldContinueRendering = false; // prevent whole view being rendered while input validation logic is running in ngOnInit
  numPages: number = 0;
  file: File & { pdfCreatedFromImages?: boolean };
  fileName: string;
  fileLink: string;
  originalFileLink: string;
  pagesDimension: any; // only for pdf case
  pdfDocument: PDFDocument; // pdfLib: for reading pages dimension and pdf creation

  constructor(
    public router: Router,
    private ocrExperienceService: OcrExperienceService,
    private exportService: ExportService,
    private toastrService: ToastrService,
    private reCaptchaV3Service: ReCaptchaV3Service,
    private scriptService: ScriptService,
    public modal: NzModalService,
    private utils: UtilsService
  ) {
    // console.log('good morning', history.state);
    this.mode = this.router.url.includes('/ldp/ocr-experience')
      ? Mode.LDP
      : this.router.url.includes('/demo/ocr-experience')
        ? Mode.Demo
        : Mode.Platform;

    this.navigateInput =
      (this.mode === Mode.LDP ? 'ldp/' : this.mode === Mode.Demo ? 'demo/' : '') +
      'ocr-experience';

    fabric.Object.prototype.objectCaching = false;
  }

  async ngOnInit() {
    /* validate input from input page */
    this.shouldContinueRendering =
      (history.state['fileHash'] || history.state['fileHashes']) &&
      history.state['fileType'] &&
      history.state['templateType'] &&
      history.state['file'] &&
      typeof history.state['isSplitting'] === 'boolean' &&
      typeof history.state['isUsingSampleFile'] === 'boolean';

    if (!this.shouldContinueRendering) {
      await this.router.navigate([this.navigateInput]);
      return;
    }
    this.fileHash = history.state['isSplitting']
      ? history.state['fileHashes']
      : history.state['fileHash'];
    this.fileType = history.state['fileType'];
    this.templateType = history.state['templateType'];
    this.isUsingSampleFile = history.state['isUsingSampleFile'];
    this.devModeEnabled = history.state.devModeEnabled;
    try {
      await this.handleFileChanged(history.state['file']);
    } catch (error) {
      return this.router.navigate([this.navigateInput], {
        state: { selectedTemplate: this.templateType.value }
      });
    }

    if (this.mode === Mode.LDP) {
      const captcha = await this.getCaptchaV3();
      this.ocr(captcha);
    } else this.ocr();
  }

  /* set file and setup all file-related properties: file, fileName, fileLink, originalFileLink, pagesDimension, pdfDocument, pdfjs */
  private async handleFileChanged(file: File) {
    this.file = file;
    this.fileLink = URL.createObjectURL(file);
    !this.originalFileLink && (this.originalFileLink = this.fileLink);
    const fileName = file.name.slice(
      0,
      file.name.lastIndexOf('.') !== -1 ? file.name.lastIndexOf('.') : file.name.length
    );
    this.fileName = fileName;
    const pagesDimension: {
      width: number;
      height: number;
      orientation: PageOrientation;
    }[] = [];
    if (file.type === 'application/pdf') {
      const fileArrayBuffer = await this.file.arrayBuffer();
      try {
        this.pdfDocument = await PDFDocument.load(fileArrayBuffer);
      } catch (error) {
        // notify user about potentially corrupted pdf file
        this.modal.error({
          nzTitle: 'Đã có lỗi xảy khi đọc file PDF',
          nzContent: `
            File PDF này có thể bị hỏng hoặc bị lỗi định dạng. Vui lòng sử dụng các công cụ sửa chữa (ví dụ như <a href="https://www.ilovepdf.com/repair-pdf" target="_blank"><b>ilovepdf.com/repair-pdf</b></a>) để khôi phục file PDF trước khi thử lại!
          `
        });
        throw error; // rethrow error
      }
      const pdfPages = this.pdfDocument.getPages();
      pdfPages.forEach((page) => {
        // console.log(page.getRotation(), page.getSize(), page.getCropBox());
        /* should use page.getCropBox() instead of page.getSize() */
        const { width: pageWidth, height: pageHeight } = page.getCropBox(); // this return in 'point' as unit
        let orientation,
          w = pageWidth,
          h = pageHeight;

        // 90, 270, ...
        if ((page.getRotation().angle / 90) % 2 === 1) {
          // reverse width <-> height
          w = pageHeight;
          h = pageWidth;
        } else {
          // 0, 180, 360, ...
        }
        orientation = w / h >= 1 ? PageOrientation.LANDSCAPE : PageOrientation.PORTRAIT;
        pagesDimension.push({
          width: w,
          height: h,
          orientation
        });
      });
    } else {
      const { height, width } = await new Promise<{
        width: number;
        height: number;
      }>((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(this.file);
        reader.onload = (e) => {
          const image = new Image();
          image.src = e.target.result.toString();
          image.onload = (e) => {
            const height = get(e, 'target.height', 0);
            const width = get(e, 'target.width', 0);
            resolve({ height, width });
            return true;
          };
          image.onerror = reject;
        };
      });
      pagesDimension.push({
        width,
        height,
        orientation:
          width / height >= 1 ? PageOrientation.LANDSCAPE : PageOrientation.PORTRAIT
      });
    }
    this.pagesDimension = pagesDimension;
  }

  private ocr(captcha?: string) {
    this.ocrExperienceService
      .ocr({
        fileHash: this.fileHash,
        fileType: this.fileType,
        templateType: this.templateType,
        devModeEnabled: this.devModeEnabled,
        count: this.isUsingSampleFile ? 1 : 2,
        isLandingPageMode: this.mode === Mode.LDP,
        captcha
      })
      .pipe(
        tap((result) => {
          let ocrResult = result.object;

          /* start hardcode result for template "phieu-thi" */
          /* if (this.templateType.value === TemplateType.PhieuThi) {
            const columns = get(ocrResult, 'candidates.columns', []);
            const fixedAnswers = {
              results_6: 'D',
              results_7: '',
              results_8: 'A',
              results_10: 'A',
              results_12: 'C',
              results_13: 'C'
            };
            columns.forEach((column) => {
              const cell = get(column, 'cells.0');
              if (Object.keys(fixedAnswers).includes(cell?.name)) {
                cell['text'] = fixedAnswers[cell.name];
              }
            });

            set(result, 'object.candidates.columns', columns);
          } */
          /* end hardcode result */

          /* TemplateType.GoiYPhongBan custom logic */
          if (this.templateType.value === TemplateType.GoiYPhongBan) {
            const suggestion_name = get(result, 'object.suggestion_name', []).map(
              (item) => ({
                code: item,
                name: goiYPhongBanMap[item] || 'N/A'
              })
            );
            // map name with code
            this.suggestionName = suggestion_name;
          }

          /* order field in ocrResult */
          if (
            this.templateType.value in FIELD_ORDER &&
            FIELD_ORDER[this.templateType.value]
          ) {
            // lowercase all key in object ocrResult first
            ocrResult = Object.fromEntries(
              Object.entries(ocrResult).map(([key, value]) => [key.toLowerCase(), value])
            );

            // reordering field in ocrResult object with fixed order from FIELD_ORDER
            const orderedOcrResult = {};
            const fieldOrder = FIELD_ORDER[this.templateType.value];
            fieldOrder.forEach((field) => {
              const [key, value] = Object.entries(field)
                .filter(([key]) => !['table_columns'].includes(key))
                .pop();
              orderedOcrResult[key.toLowerCase()] = {
                ...ocrResult[key.toLowerCase()],
                label: value
              };
            });
            ocrResult = orderedOcrResult;
          }

          this.ocrResult = ocrResult;
          this.processTableFields();
          this.categorizePages();
        }),
        catchError((e) => {
          console.error(e);
          this.toastrService.error('Bóc tách thất bại, đã có lỗi xảy ra.');
          this.router.navigate([this.navigateInput], {
            state: { selectedTemplate: this.templateType.value }
          });
          return EMPTY; // prevent further execution chain
        }),
        switchMap(async (result) => {
          // if (!this.templateType.allowMergeImagesIntoPdfOption) return;

          let alignedFile, alignedFileLink;
          switch (this.fileType.trim().toLocaleLowerCase()) {
            case 'pdf-captured':
            case 'jpg':
            case 'jpeg':
            case 'png': {
              alignedFileLink = get(result, 'object.aligned_file_hash.0');
              break;
            }
            case 'pdf':
              /* skip */
              break;
            default: {
              console.warn(`fileType ${this.fileType} is not supported`);
              break;
            }
          }
          /* found the aligned file link */
          if (alignedFileLink) {
            // turn alignedFileLink into file
            const blob = await firstValueFrom(this.utils.fetchFile(alignedFileLink));
            if (!blob?.type || !blob?.size)
              throw new Error(
                `Empty or corrupted blob returned from fetchFile(${alignedFileLink})`
              );
            alignedFile = new File(
              [blob],
              `${this.file?.name + ' (đã được căn chỉnh)' || 'file'}`,
              { type: blob.type }
            );
            await this.handleFileChanged(alignedFile);
          } else this.originalFileLink = null; // no provided aligned file link => keep using original file + continue execution
        }),
        catchError((e) => {
          console.error(e);
          this.toastrService.error('Xử lý file được căn chỉnh thất bại');
          this.router.navigate([this.navigateInput], {
            state: { selectedTemplate: this.templateType.value }
          });
          return EMPTY;
        }),
        tap(() => {
          this.toastrService.success('Bóc tách thành công');
        })
      )
      .subscribe();
  }

  private async addFile(formData, templateType, splittedPdfFile) {
    let captcha;
    if (this.mode === Mode.LDP) {
      captcha = await this.getCaptchaV3();
    }
    this.ocrExperienceService
      .addFile({
        body: formData,
        isLandingPageMode: this.mode === Mode.LDP,
        captchaV3: captcha
      })
      .pipe(
        tap((result) => {
          const fileHash = get(result, 'object.hash');
          this.reload(fileHash, templateType, splittedPdfFile);
        })
      )
      .subscribe();
  }

  handleCurrentPageChange(currentPage = 1) {
    this.currentPage = currentPage;
  }

  afterLoadComplete(e: { fileLink: string; numPages: number }) {
    this.numPages = e.numPages;
  }

  handleExportTrichXuatThongTin() {
    if (this.mode === Mode.LDP) {
      return this.toastrService.info('Vui lòng đăng nhập để sử dụng tính năng này!');
    }

    if (
      this.templateType.functionType === FunctionType.TrichXuatThongTin ||
      this.templateType.functionType === FunctionType.GoiYXuLyVanBan
    )
      return this.exportService.exportXlsxTemplateOcr(this.ocrResult, this.fileName);
  }

  handleExportSoHoaCoBan(exportOption?: 'currentPage' | 'currentRange') {
    if (this.mode === Mode.LDP) {
      return this.toastrService.info('Vui lòng đăng nhập để sử dụng tính năng này!');
    }

    if (this.templateType.value === TemplateType.SoHoaCoBan)
      return this.exportService.exportDocxScan({
        fileName: this.fileName,
        exportOption,
        categorizedPageRanges: this.categorizedPageRanges,
        currentPage: this.currentPage,
        pagesDimension: this.pagesDimension,
        ocrResult: this.ocrResult
      });
  }

  openScanTableExportModal() {
    if (this.mode === Mode.LDP) {
      return this.toastrService.info('Vui lòng đăng nhập để sử dụng tính năng này!');
    }

    if (!this.numPages)
      return this.toastrService.warning('Vui lòng đợi cho file được load thành công');

    if (this.templateType.value === TemplateType.SoHoaNangCao)
      this.modal.create({
        nzTitle: null,
        nzFooter: null,
        nzContent: ScanTableExportModalComponent,
        nzMaskClosable: false,
        nzClosable: false,
        nzData: {
          fileName: this.fileName,
          fileLink: this.fileLink,
          ocrResult: this.ocrResult,
          numPages: this.numPages
        },
        nzBodyStyle: { padding: '0' },
        nzStyle: { width: '500px' },
        nzClassName: 'modal-ocr-experience-result-export'
      });
  }

  getValueText(field) {
    // console.log(field);

    switch (typeof field) {
      case 'string':
        return field;
      case 'object': {
        const text = get(field, 'text');
        return typeof text === 'string' ? text : JSON.stringify(field);
      }
      default:
        return '';
    }
  }

  getNumberOfLine(text: string) {
    return text.split('\n').length || 1;
  }

  originalOrder = (a: KeyValue<number, string>, b: KeyValue<number, string>): number => {
    return 0;
  };

  private processTableFields() {
    const tableFields = {};
    for (const field in this.ocrResult) {
      if (this.ocrResult[field].type !== 'Table') continue;
      const columns = this.getTableColumns(this.ocrResult[field], field);
      const rows = this.getTableRows(this.ocrResult[field]);
      tableFields[field.toLowerCase()] = { columns, rows };
    }
    this.tableFields = tableFields;
  }

  // 2 cases, if fieldName is specified => map column names label, else => return array of columns only
  private getTableColumns(field, fieldName?) {
    if (field.type !== 'Table') return [];
    const columns = [];
    field.columns.forEach((col) => {
      let column = col.name;
      const fieldOrder = FIELD_ORDER[this.templateType.value];
      if (fieldName && fieldOrder) {
        const fieldLabel = fieldOrder.find(
          (field) => field[fieldName] && field['table_columns']
        );
        // console.log(fieldLabel, column);
        if (fieldLabel) {
          column = fieldLabel['table_columns'].find((item) => item[column])[column];
        }
      }
      columns.push(column);
    });
    return columns;
  }

  private getTableRows(field) {
    if (field.type !== 'Table') return [];
    const columns = this.getTableColumns(field);
    let rows = [];
    field.columns.forEach((col) => {
      col.cells.forEach((cell, index) => {
        if (rows[index]) {
          rows[index][cell.name] = cell.text;
        } else {
          rows.push({ [cell.name]: cell.text });
        }
      });
    });
    rows = rows.map((row) => columns.map((col) => row[col]));
    return rows;
  }

  getCurrentPageCategory(title?: boolean) {
    if (title)
      return get(
        this.pageCategories,
        `${this.currentPage - 1}.1.title`,
        'Không xác định'
      );
    else {
      const templateValue = get(this.pageCategories, `${this.currentPage - 1}.0`);
      const templateType = TemplateTypes.find((item) => item.value === templateValue);
      return !!templateType;
    }
  }

  async handleOcrCurrentCategorizedPageRange() {
    // check xem cái category của template có thuộc list template của trích xuất văn bản không
    const templateValue = get(this.pageCategories, `${this.currentPage - 1}.0`);
    const templateType = TemplateTypes.find((item) => item.value === templateValue);
    if (!templateType) return;

    // exclude this feature in landing page mode
    if (this.mode === Mode.LDP)
      return this.toastrService.info('Vui lòng đăng nhập để sử dụng tính năng này!');

    /* split pdf with [start, end] of the range for given current page */
    if (this.file.type === 'application/pdf' && this.pdfDocument) {
      const splittedPdfDocument = await PDFDocument.create();

      /* calculate page indexes in new pdf */
      const copiedPageIndexes = [];
      const currentPageRange = this.categorizedPageRanges.find(({ start, end }) => {
        return start <= this.currentPage - 1 && this.currentPage - 1 <= end;
      });
      let start = currentPageRange.start;
      while (start <= currentPageRange.end) {
        copiedPageIndexes.push(start);
        start++;
      }
      // console.log(currentPageRange, start, copiedPageIndexes);

      /* copy pages from current file to new pdf */
      const copiedPages = await splittedPdfDocument.copyPages(
        this.pdfDocument,
        copiedPageIndexes
      );
      copiedPages.forEach((page) => {
        splittedPdfDocument.addPage(page);
      });

      /* construct new pdf */
      const splittedPdfFile = new File(
        [await splittedPdfDocument.save()],
        `${this.fileName}-pages-${copiedPageIndexes.map((i) => i + 1).join('-')}.pdf`,
        {
          type: 'application/pdf'
        }
      );

      /* call addFile with new pdf */
      const formData = new FormData();
      formData.append('file', splittedPdfFile);
      formData.append('title', '');
      formData.append('description', '');
      this.addFile(formData, templateType, splittedPdfFile);
    } else {
      const fileHash = this.fileHash.pop();
      this.reload(fileHash, templateType, this.file);
    }
  }

  private async getCaptchaV3(): Promise<string> {
    // clean script to make sure siteKey is set correctly (because previous script could be incorrect)
    this.scriptService.cleanup();

    return await this.reCaptchaV3Service.executeAsPromise(this.siteKey, 'ocr_request', {
      useGlobalDomain: false
    });
  }

  private reload(fileHash, templateType, file) {
    // navigate to this same route
    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };
    this.router.onSameUrlNavigation = 'reload';
    this.router.navigate([this.navigateInput, 'result'], {
      state: {
        fileHash,
        templateType,
        file,
        fileType: this.fileType,
        isUsingSampleFile: false,
        isSplitting: false
      }
    });
  }

  private categorizePages() {
    const templateCategoryList = {
      [TemplateType.DangKyKinhDoanh]: {
        title: 'Giấy chứng nhận đăng ký doanh nghiệp',
        keywords: [
          'đăng ký doanh nghiệp',
          'đăng ký doành nghiệp',
          'đăng ký hoạt động chi nhánh',
          'đăng ký địa điểm kinh doanh'
        ]
      },
      [TemplateType.HoaDonBanHang]: {
        title: 'Hóa đơn bán hàng',
        keywords: ['hóa đơn bán hàng']
      },
      unc: { title: 'Ủy nhiệm chi', keywords: ['ủy nhiệm chi'] },
      ban_an: {
        title: 'Văn bản pháp luật',
        keywords: ['tòa án nhân dân', 'bản án số', 'hội đồng xét xử']
      },
      bao_cao_tai_chinh: {
        title: 'Báo cáo tài chính',
        keywords: [
          'báo cáo tài chính',
          'bảng cân đối kế toán',
          'báo cáo kết quả kinh doanh'
        ]
      }
    };

    let pageCategories = [];
    const phrasesTextByPage = get(this.ocrResult, 'phrases', []).map((item) =>
      get(item, 'cells', [])
        .map((item) => item.text.trim())
        .join(' ')
        .toLowerCase()
    );
    phrasesTextByPage.forEach((phrasesText, index) => {
      pageCategories[index] = Object.entries(templateCategoryList).find(([_, template]) =>
        template.keywords.some((keyword) => phrasesText.includes(keyword.toLowerCase()))
      );
    });

    if (!pageCategories[0])
      // set first page category regardless, other right handside pages of first page will follow
      pageCategories[0] = [null, { title: 'Không xác định' }];

    pageCategories = pageCategories.map((value, index, list) => {
      if (value) return value;

      let category = null;
      while (category === null && index >= 0) {
        if (list[index]) {
          category = list[index];
          break;
        } else index--;
      }
      return category;
    });

    // find range for categorized pages
    const categorizedPageRanges: {
      template: string;
      start: number;
      end: number;
    }[] = [];
    pageCategories.forEach((page, index) => {
      const template = get(page, '0');
      if (
        get(categorizedPageRanges, `${categorizedPageRanges.length - 1}.template`) !==
        template
      )
        categorizedPageRanges.push({
          template,
          start: index,
          end: index
        });
      else categorizedPageRanges[categorizedPageRanges.length - 1].end = index;
    });
    this.categorizedPageRanges = categorizedPageRanges;
    // console.log(/* phrasesTextByPage, */ pageCategories, categorizedPageRanges);

    this.pageCategories = pageCategories;
  }

  selectTab(tab: 'ocr-result' | 'doc-suggestion') {
    this.tab = tab;
  }

  deleteSuggestion(code) {
    this.suggestionName = this.suggestionName.filter((item) => item.code !== code);
  }

  openSubmitSuccessModal() {
    this.modal.create({
      nzContent: this.submitSuccessModal,
      nzFooter: null,
      nzBodyStyle: { padding: '0px' },
      nzStyle: { width: '300px' }
    });
  }

  ngOnDestroy(): void {
    URL.revokeObjectURL(this.fileLink);
    URL.revokeObjectURL(this.originalFileLink);
  }
}
