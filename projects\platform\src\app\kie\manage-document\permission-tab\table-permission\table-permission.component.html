<div class="flex flex-col gap-4 mt-4">
  <div
    *ngIf="setOfCheckedId.size > 0"
    class="bg-brand-3/10 p-[10px] rounded items-center gap-6 transition-all duration-500 flex"
  >
    <div class="flex gap-2 text-text-1 text-sm font-medium border-r border-icon-1 pr-4">
      <img
        (click)="uncheckAll()"
        class="cursor-pointer"
        src="assets/kie/header-table/cancel.svg"
        alt="cancel-icon"
      />
      <p>
        <PERSON><PERSON> chọn (<span class="text-brand-1">{{ setOfCheckedId.size }}</span
        >)
      </p>
    </div>
    <div
      class="flex gap-2 text-text-1 text-sm font-medium"
      (click)="changeAssigneeRoleMany('viewer')"
    >
      <img
        class="cursor-pointer"
        src="assets/kie/header-table/viewer.svg"
        alt="viewer-icon"
      />
      <p>Ngư<PERSON>i xem</p>
    </div>
    <div
      class="flex gap-2 text-text-1 text-sm font-medium"
      (click)="changeAssigneeRoleMany('editor')"
    >
      <img
        class="cursor-pointer"
        src="assets/kie/header-table/editor.svg"
        alt="editor-icon"
      />
      <p>Người chỉnh sửa</p>
    </div>
    <div class="flex gap-2 text-text-1 text-sm font-medium" (click)="deleteAccessMany()">
      <img
        class="cursor-pointer"
        src="assets/kie/header-table/block-access.svg"
        alt="block-icon"
      />
      <p>Xóa quyền truy cập</p>
    </div>
  </div>
</div>

<nz-table
  class="mt-4"
  #rowSelectionTable
  [nzTemplateMode]="true"
  nzShowPagination="false"
  nzShowSizeChanger
  [nzFrontPagination]="false"
  [nzData]="listPermission.permissionList"
  (nzCurrentPageDataChange)="onCurrentPageDataChange($event)"
  [nzTableLayout]="'fixed'"
>
  <thead>
    <tr>
      <th
        [nzDisabled]="!rowSelectionTable.data?.length"
        [nzChecked]="checked"
        [nzIndeterminate]="indeterminate"
        (nzCheckedChange)="onAllChecked($event)"
        nzWidth="100px"
      ></th>
      <th nzWidth="100px">STT</th>
      <th>Người dùng</th>
      <th nzWidth="150px">Vai trò</th>
      <th nzWidth="100px"></th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let data of rowSelectionTable.data; let id = index">
      <td
        [nzChecked]="setOfCheckedId.has(data.assigneeId)"
        [nzDisabled]="data.disabled"
        (nzCheckedChange)="onItemChecked(data.assigneeId, $event)"
      ></td>
      <td>
        <div class="text-text-1 text-sm font-normal">
          {{ (listPermission.page - 1) * listPermission.limit + id + 1 }}
        </div>
      </td>
      <td>
        <div class="flex items-center gap-2">
          <nz-avatar
            class="shrink-0 capitalize"
            [nzText]="data.assignee.name.charAt(0)"
          ></nz-avatar>
          <div class="text-text-1 text-sm font-semibold truncate break-words">
            {{ data.assignee.name }}
          </div>
        </div>
      </td>
      <td>
        <p class="text-sm font-normal text-[#16161D]">{{ viewRole(data?.role) }}</p>
      </td>
      <td>
        <div
          nz-popover
          [nzPopoverTitle]="null"
          nzPopoverTrigger="click"
          [nzPopoverContent]="actionsTemplate"
        >
          <img
            class="cursor-pointer"
            src="assets/kie/header-table/threedot.svg"
            alt="icon-more-action"
          />
        </div>
      </td>
      <!-- actions template -->
      <ng-template #actionsTemplate>
        <div
          class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden"
        >
          <div
            class="flex gap-2 items-center hover:bg-bg-1 p-4"
            [ngClass]="{
              'bg-sky-200': data.role === 'viewer',
              'cursor-pointer': data.role !== 'viewer'
            }"
            (click)="changeAssigneeRole([data], 'viewer')"
          >
            <img src="assets/kie/roles/viewer.svg" alt="icon" />
            <div class="flex flex-col gap-1">
              <p>Người xem</p>
              <p class="text-text-3 text-xs font-normal">Xem và tải kết quả bóc tách</p>
            </div>
          </div>
          <div
            class="flex gap-2 items-center hover:bg-bg-1 p-4"
            [ngClass]="{
              'bg-sky-200': data.role === 'editor',
              'cursor-pointer': data.role !== 'editor'
            }"
            (click)="changeAssigneeRole([data], 'editor')"
          >
            <img src="assets/kie/roles/editor.svg" alt="icon" />
            <div class="flex flex-col gap-1">
              <p>Người chỉnh sửa</p>
              <p class="text-text-3 text-xs font-normal">
                Upload, cấu hình và hiệu chỉnh văn bản
              </p>
            </div>
          </div>
          <!-- <div
            (click)="showModalTransferOwnership()"
            class="flex gap-2 items-center cursor-pointer hover:bg-bg-1 p-4"
          >
            <img src="assets/kie/roles/permission.svg" alt="icon" />
            <p>Chuyển quyền sở hữu</p>
          </div> -->
          <div
            (click)="showModalDeleteAccess([data.assignee])"
            class="flex gap-2 items-center cursor-pointer hover:bg-bg-1 p-4"
          >
            <img src="assets/kie/roles/delete-access.svg" alt="icon" />
            <p>Xóa quyền truy cập</p>
          </div>
        </div>
      </ng-template>
    </tr>
  </tbody>
</nz-table>
<div
  *ngIf="!listPermission.permissionList.length"
  class="w-full min-h-[500px] flex flex-col gap-5 items-center justify-center bg-[linear-gradient(0deg,rgba(255,255,255,1)50%,rgba(251,251,252,1)100%)]"
>
  <img src="assets/kie/document/empty-table.svg" />
</div>

<nz-pagination
  class="mt-4"
  [nzShowTotal]="rangeTemplate"
  [nzTotal]="listPermission.total"
  [nzPageSize]="listPermission.limit"
  [nzPageIndex]="listPermission.page"
  (nzPageIndexChange)="handleChangePage($event)"
  [nzItemRender]="renderItemTemplate"
>
</nz-pagination>
<ng-template #rangeTemplate let-range="range" let-total>
  <div>
    Số bản ghi mỗi trang
    <nz-select
      class="w-[65px]"
      [ngModel]="listPermission.limit || 10"
      (ngModelChange)="handleChangeLimit($event)"
    >
      <nz-option [nzValue]="10" nzLabel="10"></nz-option>
      <nz-option [nzValue]="20" nzLabel="20"></nz-option>
      <nz-option [nzValue]="30" nzLabel="30"></nz-option>
    </nz-select>
  </div>
</ng-template>
<ng-template #renderItemTemplate let-type let-page="page">
  <ng-container [ngSwitch]="type">
    <a *ngSwitchCase="'page'">{{ page }}</a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'prev'">
      <img src="assets/kie/header-table/previous_page.svg" alt="prev-icon" />
    </a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'next'">
      <img src="assets/kie/header-table/next_page.svg" alt="next-icon" />
    </a>
    <a class="btn-dot" *ngSwitchCase="'prev_5'">...</a>
    <a class="btn-dot" *ngSwitchCase="'next_5'">...</a>
  </ng-container>
</ng-template>
