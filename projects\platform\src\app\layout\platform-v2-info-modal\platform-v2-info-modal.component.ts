import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  QueryList,
  ViewChildren
} from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-platform-v2-info-modal',
  templateUrl: './platform-v2-info-modal.component.html',
  styleUrls: ['./platform-v2-info-modal.component.scss']
})
export class PlatformV2InfoModalComponent {
  @ViewChildren('slide') listSlide!: QueryList<ElementRef>;
  activeSlide: ElementRef;
  activeId = 0;
  platformV2Understood = false;

  constructor(
    private modalService: NzModalService,
    private toastr: ToastrService,
    private cd: ChangeDetectorRef
  ) {
    this.platformV2Understood = !!localStorage.getItem('platformV2Understood');
    this.toastr.warning(
      `
        <PERSON><PERSON> <PERSON>hi hệ thống được nâng cấp vào ngày <b>09/09/2024</b>, người dùng sẽ <b>không</b> còn sử dụng tính năng <b>OCR platform</b> hiện tại. <br />
        Toàn bộ dữ liệu của người dùng sẽ <b>được đảm bảo</b> tại hệ thống sau khi nâng cấp.
      `,
      'Cảnh báo',
      {
        enableHtml: true,
        disableTimeOut: true,
        closeButton: true
      }
    );
  }

  ngAfterViewInit() {
    if (!this.listSlide.length) return;
    this.activeSlide = this.listSlide.get(this.activeId);
    this.cd.detectChanges();
  }

  changePlatformV2Understood() {
    if (this.platformV2Understood) localStorage.setItem('platformV2Understood', 'true');
    else localStorage.removeItem('platformV2Understood');
  }

  next() {
    if (!this.listSlide.length) return;
    if (this.activeId === this.listSlide.length - 1) this.activeId = 0;
    else this.activeId++;
    this.activeSlide = this.listSlide.get(this.activeId);
  }

  prev() {
    if (!this.listSlide.length) return;
    if (this.activeId === 0) this.activeId = this.listSlide.length - 1;
    else this.activeId--;
    this.activeSlide = this.listSlide.get(this.activeId);
  }

  close() {
    this.modalService.closeAll();
  }
}
