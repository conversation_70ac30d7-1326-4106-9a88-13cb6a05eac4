import { Component, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { DomSanitizer } from '@angular/platform-browser';
import { NzInputModule } from 'ng-zorro-antd/input';
import { FormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { take, tap } from 'rxjs';
import { get } from 'lodash';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-demo-hau-kiem-ocr',
  standalone: true,
  imports: [
    CommonModule,
    NzButtonModule,
    NzModalModule,
    NzInputModule,
    NzToolTipModule,
    FormsModule
  ],
  templateUrl: './demo-hau-kiem-ocr.component.html',
  styleUrls: ['./demo-hau-kiem-ocr.component.scss']
})
export class DemoHauKiemOcrComponent {
  @ViewChild('hauKiemOcrModuleTmpl')
  hauKiemOcrModuleTmpl;
  moduleHauKiemOcrIframe: HTMLIFrameElement;
  hauKiemOcrModuleUrl = 'https://hau-kiem-ocr.icenter.ai';
  sourceKnowledgeId: string;
  botId: string;
  token: string;

  constructor(
    private modalService: NzModalService,
    private sanitizer: DomSanitizer,
    private toastr: ToastrService
  ) {
    window.onmessage = (event) => {
      console.log(`event: ${this.hauKiemOcrModuleUrl} => host`, event);
      switch (get(event, 'data.message')) {
        case 'hau-kiem-ocr-module-initialized': {
          this.moduleHauKiemOcrIframe?.contentWindow.postMessage(
            {
              message: 'hau-kiem-ocr-module-inputs',
              source_knowledge_id: this.sourceKnowledgeId,
              bot_id: this.botId,
              token: this.token
            },
            this.hauKiemOcrModuleUrl
          );
          break;
        }
        case 'hau-kiem-ocr-module-saved-successfully': {
          /* TODO: close the modal containing iframe */
          break;
        }
      }
    };
  }

  showModalWithHauKiemModule() {
    if (!this.token) this.toastr.warning('token không được bỏ trống');
    if (!this.botId) this.toastr.warning('bot_id không được bỏ trống');
    if (!this.sourceKnowledgeId)
      this.toastr.warning('source_knowledge_id không được bỏ trống');
    if (!this.hauKiemOcrModuleUrl)
      this.toastr.warning('URL module hậu kiểm OCR không được bỏ trống');
    if (
      !this.hauKiemOcrModuleUrl ||
      !this.sourceKnowledgeId ||
      !this.botId ||
      !this.token
    )
      return;

    const modalRef = this.modalService.create({
      nzContent: this.hauKiemOcrModuleTmpl,
      nzData: {
        src: this.sanitizer.bypassSecurityTrustResourceUrl(`${this.hauKiemOcrModuleUrl}`)
      },
      nzClassName: 'hau-kiem-ocr-module-custom-styles',
      nzWrapClassName: 'hau-kiem-ocr-module-wrapper-custom-styles',
      nzMaskClosable: false,
      nzFooter: null
    });
    modalRef.afterClose.asObservable().pipe(
      take(1),
      tap(() => (this.moduleHauKiemOcrIframe = null)) // remove the ref to closed iframe
    );

    this.moduleHauKiemOcrIframe =
      modalRef.containerInstance.modalElementRef.nativeElement.querySelector(
        'iframe#hau-kiem-ocr-module-iframe'
      );
  }
}
