import { fabric } from 'fabric';

export default function patchFabricLib() {
  /* add promisified ver of static method fabric.Image.fromURL() */
  fabric.Image['fromURLAsync'] = function (
    imageURL: string,
    imgOptions?: fabric.IImageOptions
  ) {
    /* wrapper promise for fabric.Image.fromURL */
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(
        imageURL,
        (img) => {
          // console.log(img);
          /* there is no way to handle error for this func yet */
          if (!img) reject('image NOT OK');
          resolve(img);
        },
        imgOptions
      );
    });
  };

  /* add promisified ver of instance method fabric.Object clone() */
  fabric.Object.prototype['cloneAsync'] = function (propertiesToInclude?: string[]) {
    return new Promise((resolve, reject) => this.clone(resolve, propertiesToInclude));
  };

  /* add promisified ver of instance method fabric.util.enlivenObjects */

  fabric.util['enlivenObjectsAsync'] = function (
    objects: any[],
    namespace: string,
    reviver?: Function
  ) {
    return new Promise((resolve, reject) =>
      fabric.util.enlivenObjects(objects, resolve, namespace, reviver)
    );
  };

  /* TODO: animate */
}
