import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@platform/environment/environment';

@Injectable({
  providedIn: 'root',
})
export class ResetPasswordService {
  constructor(private http: HttpClient) {}

  createForgotPassword(email) {
    return this.http.post(
      environment.backendUrl + 'idg-api/create-forgot-password',
      { email, channelCode: 'SMART_RPA' }
    );
  }

  confirmForgotPassword(
    email,
    token,
    body: { password: string; passwordConfirm: string; username: string }
  ) {
    return this.http.post(
      environment.backendUrl +
        `idg-api/confirm-forgot-password?email=${email}&token=${token}`,
      body
    );
  }

  checkExpireToken(email, token) {
    return this.http.get(
      environment.backendUrl +
        `idg-api/check-expire-reset-pass-email?email=${email}&token=${token}`
    );
  }
}
