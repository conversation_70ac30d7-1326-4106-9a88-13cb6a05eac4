import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PaymentResponseType } from './types';

@Component({
  selector: 'app-response-payment',
  templateUrl: './response-payment.component.html',
  styleUrls: ['./response-payment.component.scss'],
})
export class ResponsePaymentComponent implements OnInit {
  countdownSeconds = 100;
  readonly PaymentResponseType = PaymentResponseType;
  paymentResponseType: PaymentResponseType = 2;
  planName = '';
  interval = null;

  constructor(private activeRoute: ActivatedRoute, private route: Router) {}

  ngOnInit(): void {
    // this.interval = null;

    // paying via wallet case
    if (
      Object.values(this.PaymentResponseType).includes(
        history.state?.paymentResponseType
      )
    ) {
      this.planName = history.state?.planName || '';
      this.paymentResponseType = history.state?.paymentResponseType;
    }
    // paying via vnpt money
    else
      this.activeRoute.queryParams.subscribe((params) => {
        if (params) {
          this.paymentResponseType =
            params['vnptpayResponseCode'] === '00'
              ? PaymentResponseType.success
              : PaymentResponseType.failed;
        }
      });
  }

  startCountdown() {
    this.interval = setInterval(() => {
      if (this.countdownSeconds === 0) {
        return this.returnUrl();
      } else this.countdownSeconds--;
    }, 1000);
  }

  returnUrl(e?: Event) {
    if (e) {
      e.preventDefault();
    }
    clearInterval(this.interval);
    this.route.navigate(['subscription']);
  }
}
