:host {
  ::ng-deep {

    .ant-select .ant-select-selector {
      border-radius: 8px;
    }

    nz-pagination {
      ul {
        display: flex;

        li.ant-pagination-total-text {
          flex: 1;
        }
      }
    }

    .ant-pagination-item {
      border-radius: 8px;

      &:hover {
        a {
          color: #ffffff;
        }

        border-color: #0667e1;
        background-color: #0667e1;
      }
    }

    .ant-table-tbody>tr:nth-child(odd) {
      background-color: #ffffff;
      /* Light grey for odd rows */
    }

    .ant-table-tbody>tr:nth-child(even) {
      background-color: #f0f1f4;
      /* White for even rows */
    }

    .ant-table-thead {
      .ant-table-cell {
        padding: 12px;
        border-bottom: 1px solid #e2e2e9;
        font-size: 14px;
        font-weight: 600;
        color: #2b2d3b;
      }
    }

    .btn-dot {
      display: block;
      min-width: 32px;
      height: 32px;
      text-align: center;
      list-style: none;
      background-color: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      outline: 0;
      cursor: pointer;

      &:hover {
        background-color: #0667e1;
        color: #ffffff;
      }
    }
  }
}

::ng-deep {
  .modal-alert {
    width: 400px !important;

    .ant-modal-content {
      border-radius: 16px;
    }

    .ant-modal-header {
      border-radius: 16px 16px 0 0;
    }

    .ant-modal-title {
      font-size: 14px;
      font-weight: 600;
    }
  }
}