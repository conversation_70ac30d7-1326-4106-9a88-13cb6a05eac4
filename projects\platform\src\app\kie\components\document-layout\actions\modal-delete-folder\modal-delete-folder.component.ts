import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-delete-folder',
  templateUrl: './modal-delete-folder.component.html',
  styleUrls: ['./modal-delete-folder.component.scss']
})
export class ModalDeleteFolderComponent implements OnInit {
  readonly nzModalData: { folder: Folder } = inject(NZ_MODAL_DATA);
  folder = this.nzModalData.folder;

  constructor(
    private kieService: KIEService,
    private toastr: ToastrService,
    private modalRef: NzModalRef
  ) {}

  ngOnInit(): void {}

  cancel() {
    this.modalRef.close();
  }

  delete() {
    this.kieService
      .deleteFolder(this.folder.id)
      .pipe(
        tap(() => {
          this.toastr.success('Xóa thư mục thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
