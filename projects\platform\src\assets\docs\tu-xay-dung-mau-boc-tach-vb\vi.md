**Phạm vi bóc tách**: position & preword sufword (li<PERSON><PERSON> hệ nếu muốn bóc tách thêm nhiều mẫu)

---

# Hướng dẫn tạo và sử dụng template để bóc tách thông tin văn bản

## Tạ<PERSON> và quản lý thư mục, v<PERSON><PERSON>, tà<PERSON> liệu

_Tạo và quản lý thư mục_

**Bước 1:** Click vào nút Tạo hoặc Tạo mới thư mục

**Bước 2:** Điền tên thư mục

**Bước 3:** Click Tạo mới thư mục

![img-1.png](img-1.png)
**Bước 4:** Thư mục được tạo hiển thị tại thanh điều hướng bên trá<PERSON> màn hình, user có thể:

\- Sắp xếp hiển thị thư mục theo tên/ thời gian t<PERSON>/ thời gian chỉnh sửa tăng dần hoặc gi<PERSON>m dần

![img-2.png](img-2.png)

\- Xem thời gian tạo & sửa thư mục, thực hiện Đổi tên hoặc Xóa thư mục

![img-3.png](img-3.png)

_Tạo và quản lý văn bản_

**Bước 1:** Click vào nút Tạo hoặc Tạo mới văn bản

![img-4.png](img-4.png)

**Bước 2:** Chọn 1 trong 2 lựa chọn: Tạo theo các mẫu giấy tờ có sẵn hoặc Tự tạo mẫu giấy tờ

**Bước 3:** Điền các trường thông tin để tạo mới văn bản:

\- Tạo theo các mẫu giấy tờ có sẵn

- Chọn mẫu giấy tờ
- Điền tên văn bản
- Chọn thư mục chứa văn bản đang tạo

![img-5.png](img-5.png)

\- Tự tạo mẫu giấy tờ

- Điền tên văn bản
- Chọn thư mục chứa văn bản đang tạo
- Chọn cách nhận dạng: theo vùng văn bản hoặc theo keyword + vùng văn bản
- Upload file mẫu

![img-6.png](img-6.png)

**Bước 4:** Click nút Tạo mới, văn bản được lưu trữ trong thư mục đã chọn và có thể tìm lại được trong thanh điều hướng bên trái:

\- Tạo và quản lý tài liệu

- **Bước 1:** Vào thư mục bất kỳ chứa văn bản đã tạo, click vào nút Upload file để tạo tài liệu mới

  ![img-7.png](img-7.png)

- **Bước 2:** Tài liệu sau khi được upload sẽ trong trạng thái Chờ xác nhận, trong trạng thái này tài liệu vẫn có thể được chỉnh sửa. Nếu user ấn Xác nhận thì tài liệu sẽ không thể chỉnh sửa được nữa

## Xem kết quả KIE, hiệu chỉnh kết quả KIE

**Bước 1:** Tại mục Tài liệu, click vào tài liệu muốn xem kết quả KIE

![img-8.png](img-8.png)

**Bước 2:** Xem kết quả KIE tại vùng bên phải màn hình, từng trường thông tin tương ứng với vùng khoanh trên văn bản

- Xem tỉ lệ chính xác của từng trường thông tin được bóc tách
- Doubleclick vào vùng khoanh trên văn bản để chỉnh sửa nội dung nếu kết quả bóc tách chưa chính xác

![img-9.png](img-9.png)

**Bước 3:** Sau khi chỉnh sửa kết quả bóc tách, click nút Lưu để lưu hiệu chỉnh kết quả bóc tách. Nếu click vào nút Xác nhận thì sau đó sẽ không được hiệu chỉnh nữa.

## Tự cấu hình các trường thông tin KIE

Tính năng chỉ áp dụng cho mẫu văn bản tự tạo

**Bước 1:** Truy cập thư mục chứa văn bản tự tạo, vào mục Cấu hình, click vào nút Cấu hình

![img-10.png](img-10.png)

**Bước 2:** Tại màn hình Cấu hình, thêm các trường thông tin muốn bóc tách

![img-11.png](img-11.png)

**Bước 3:** Sau khi điền đầy đủ các thông tin, click Lưu lại để lưu cấu hình. Với các trường thông tin đã thêm, có thể xoá hoặc bật/tắt xử lý bóc tách

![img-12.png](img-12.png)

# API Template Create

`Domain name:` [https://api.idg.vnpt.vn](https://api.idg.vnpt.vn)

**- Chức năng**: Tạo thông tin config của template trong database

**- Chi tiết API**:

**Endpoint:** `<domain-name>/template/config`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>action</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thao tác với template trong database (ở đây là tạo template => action=create)</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td></td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn.<br>
Định dạng: <br>
&lt;IOS/ANDROID&gt;_&lt;model name&gt;_&lt;OS/API&gt;_&lt;Device/Simulator&gt;_&lt;SDK version&gt;_&lt;Device id&gt;_&lt;Time stamp&gt;
</td>
</tr>
<tr>
<td></td>
<td>id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của template trong database</td>
</tr>
<tr>
<td></td>
<td>document_id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của văn bản lưu trữ trong database</td>
</tr>
<tr>
<td></td>
<td>file_link</td>
<td>String</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>config</td>
<td>Object</td>
<td>x</td>
<td></td>
<td>Thông tin config của template sẽ được lưu lại trong database</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "action": "create",
  "id": "",
  "name": "tc-Smart Reader-01",
  "document_id": "test-123",
  "file_link": "https://test-link",
  "config": {
    "ocr_model": "dkkd",
    "image_type": "ocr_dkkd",
    "content_from_pdf": false,
    "fields": {
      "MA_SO_DOANH_NGHIEP": {
        "name": "MA_SO_DOANH_NGHIEP",
        "prewords": ["Mã số chi nhánh :", "Mã số doanh nghiệp:"],
        "sufwords": ["Đăng ký lần đầu:"],
        "is_title_or_uppercase": false,
        "is_number": false,
        "is_date": false,
        "table": {},
        "multilines": false,
        "location": null,
        "paragraph": null,
        "cer_threshold": 0.9,
        "is_visible": true
      }
    }
  }
}
```

Response success

Http code: 200 - thành công

```json
{
	"dataSign": "GaQbJHyGekeD4fyeGQsnfoFa7LUhYnIcyozMPwnpfwJyhpYxm89qa6hKJ7iekgbqMfsbWrEb7soNfNNRHmlk2g==",
	"dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwMDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsInN0YXR1cyI6Ik9LIiwic3RhdHVzQ29kZSI6MjAwLCJvYmplY3QiOnsibmFtZSI6InRjLX
    JwYS0wMSIsImZpbGVfbGluayI6Imh0dHBzOi8vdGVzdC1saW5rIiwiaWQiOiJEQkM0NUVEMDExRTFBQUQyRTA1MzZDMUI5RjBBODhGRSIsImRvY3VtZW50X2lkIjpudWxsLCJjb25maWciOnsiY29udGVudF9mcm9tX3BkZiI6ZmFsc2UsImZpZWxkcyI6eyJNQV9TT19ET0FOSF9OR0hJRVAiOnsiY2VyX3RocmVzaG9sZCI6MC45LCJwYXJhZ3JhcGgiOm51bGwsInByZXdvcmRzIjpbIk3DoyBz4buRIGNoaSBuaMOhbmggOiIsIk3DoyBz4buRIGRvYW5oIG5naGnhu4dwOiJdLCJpc192aXNpYmxlIjp0cnVlLCJpc19kYXRlIjpmYWxzZSwibmFtZSI6Ik1BX1NPX0RPQU5IX05HSElFUCIsInN1ZndvcmRzIjpbIsSQxINuZyBrw70gbOG6p24gxJHhuqd1OiJdLCJsb2NhdGlvbiI6bnVsbCwibXVsdGlsaW5lcyI6ZmFsc2UsImlzX3RpdGxlX29yX3VwcGVyY2FzZSI6ZmFsc2UsInRhYmxlIjp7fSwiaXNfbnVtYmVyIjpmYWxzZX19LCJvY3JfbW9kZWwiOiJka2tkIiwiaW1hZ2VfdHlwZSI6Im9jcl9ka2tkIn19LCJjaGFsbGVuZ2VDb2RlIjoiMTExMTEifQ==",
	"logID": "bb8306b3-b373-11ec-9216-ab9c7a9d721b-998d1a2f-Zuulserver",
	"message": "IDG-00000000",
	"server_version": "1.1.5",
	"status": "OK",
	"statusCode": 200,
	"object": {
    	"name": "tc-Smart Reader-01",
    	"file_link": "https://test-link",
    	"id": "DBC45ED011E1AAD2E0536C1B9F0A88FE",
    	"document_id": null,
    	"config": {
        	"content_from_pdf": false,
        	"fields": {
            	"MA_SO_DOANH_NGHIEP": {
                	"cer_threshold": 0.9,
                	"paragraph": null,
                	"prewords": [
                    	"Mã số chi nhánh :",
                    	"Mã số doanh nghiệp:"
                	],
                	"is_visible": true,
                	"is_date": false,
                	"name": "MA_SO_DOANH_NGHIEP",
                	"sufwords": [
                    	"Đăng ký lần đầu:"
                	],
                	"location": null,
                	"multilines": false,
                	"is_title_or_uppercase": false,
                	"table": {},
                	"is_number": false
            	}
        	},
        	"ocr_model": "dkkd",
        	"image_type": "ocr_dkkd"
    	}
	},
	"challengeCode": "11111"
}
```

Http code: 400 - Tạo config với name đã tồn tại trong database

```json
{
  "dataSign": "b1XZXWBaLwxiZlGLU/StYOpdlF7xgacfOvVnd4h5gjLjKsSScEEptkox59TttouQ2IQ2kqFv+7yBYtvCHq7VFA==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwNDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsImVycm9ycyI6WyJEYXRhYmFzZSBRdWVyeSBpcyBmYWlsZWQiXSwic3RhdHVzIjoiQkFEIFJFUVVFU1QiLCJzdGF0dXNDb2RlIjo0MDAsImNoYWxsZW5nZUNvZGUiOiIxMTExMSJ9",
  "logID": "d57af9b4-b373-11ec-9216-f9978bf56e52-ff3c1ee1-Zuulserver",
  "message": "IDG-00000400",
  "server_version": "1.1.5",
  "errors": ["Database Query is failed"],
  "status": "BAD REQUEST",
  "statusCode": 400,
  "challengeCode": "11111"
}
```

# API Template Read

`Domain name:` [https://api.idg.vnpt.vn](https://api.idg.vnpt.vn)

**- Chức năng**: Đọc thông tin config của template trong database

**- Chi tiết API**:

**Endpoint:** `<domain-name>/template/config`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>action</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thao tác với template trong database (ở đây là tạo template => action=read)</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn.<br>
Định dạng: <br>
&lt;IOS/ANDROID&gt;_&lt;model name&gt;_&lt;OS/API&gt;_&lt;Device/Simulator&gt;_&lt;SDK version&gt;_&lt;Device id&gt;_&lt;Time stamp&gt;
</td>
</tr>
<tr>
<td></td>
<td>template_id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của template trong database</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "action": "read",
  "id": "DA7AA8914973CA0EE0536C1B9F0A34E3"
}
```

Response success

Http code: 200 - thành công

```json
{
  "dataSign": "J3BKmms/F9xf40rGqiKSlcY1BKQW5nVsHJtDQmj3a1oa5D4D9klZGTVv146sXDL2qzbdCXHlFLuJVqvhKZkVIA==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwMDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsInN0YXR1cyI6Ik9LIiwic3RhdHVzQ29kZSI6MjAwLCJvYmplY3QiOnsibmFtZSI6ImJpel9yZWdfY2VydCIsImZpbGVfbGluayI6bnVsbCwiaWQiOiJEQTdBQTg5MTQ5NzNDQTBFRTA1MzZDMUI5RjBBMzRFMyIsImRvY3VtZW50X2lkIjoiNzRmNWU1MWUtNDhmMi00MjhkLWE2M2UtYjFmMTRmNzZlYWUzIiwiY29uZmlnIjp7ImNvbnRlbnRfZnJvbV9wZGYiOmZhbHNlLCJmaWVsZHMiOnsiVEVOX0NPTkdfVFlfTlVPQ19OR09BSSI6eyJjZXJfdGhyZXNob2xkIjowLjksInBhcmFncmFwaCI6bnVsbCwicHJld29yZHMiOlsiVMOqbiBkb2FuaCBuZ2hp4buHcCB2aeG6v3QgYuG6sW5nIHRp4bq/bmcgbsaw4bubYyBuZ2/DoGk6IiwiVMOqbiBjw7RuZyB0eSB2aeG6v3QgYuG6sW5nIHRp4bq/bmcgbsaw4bubYyBuZ2/DoGk6IiwiVMOqbiBjaGkgbmjDoW5oIHZp4bq/dCBi4bqxbmcgdGnhur9uZyBuxrDhu5tjIG5nb8OgaToiXSwiaXNfdmlzaWJsZSI6dHJ1ZSwiaXNfZGF0ZSI6ZmFsc2UsIm5hbWUiOiJURuIjpudWxsLCJtdWx0aWxpbmVzIjp0cnVlLCJpc190aXRsZV9vcl91cHBlcmNhc2UiOmZhbHNlLCJ0YWJsZSI6e30sImlzX251bWJlciI6ZmFsc2V9fSwib2NyX21vZGVsIjoiZGtrZCIsImltYWdlX3R5cGUiOiJvY3JfZGtrZCJ9fSwiY2hhbGxlbmdlQ29kZSI6IjExMTExIn0=",
  "logID": "2e87fb40-b372-11ec-9216-d9bf998c8ee8-226990ef-Zuulserver",
  "message": "IDG-00000000",
  "server_version": "1.1.5",
  "status": "OK",
  "statusCode": 200,
  "object": {
    "name": "biz_reg_cert",
    "file_link": null,
    "id": "DA7AA8914973CA0EE0536C1B9F0A34E3",
    "document_id": "74f5e51e-48f2-428d-a63e-b1f14f76eae3",
    "config": {
      "content_from_pdf": false,
      "fields": {
        "TEN_CONG_TY_NUOC_NGOAI": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Tên doanh nghiệp viết bằng tiếng nước ngoài:",
            "Tên công ty viết bằng tiếng nước ngoài:",
            "Tên chi nhánh viết bằng tiếng nước ngoài:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "TEN_CONG_TY_NUOC_NGOAI",
          "sufwords": [
            "Tên doanh nghiệp viết tắt:",
            "Tên công ty viết tắt:",
            "Tên chi nhánh viết tắt:"
          ],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": true,
          "table": {},
          "is_number": false
        },
        "HO_KHAU_THUONG_TRU": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Nơi đăng ký hộ khẩu thường trú:",
            "Nơi đăng ký HKTT:",
            "Địa chỉ thường trú:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "HO_KHAU_THUONG_TRU",
          "sufwords": ["Địa chỉ liên lạc:", "Chỗ ở hiện tại:"],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "TEN_CONG_TY_VIET_TAT": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Tên doanh nghiệp viết tắt:",
            "Tên công ty viết tắt:",
            "Tên chi nhánh viết tắt:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "TEN_CONG_TY_VIET_TAT",
          "sufwords": ["Địa chỉ trụ sở chính", "Địa chỉ trụ sở", "Địa chỉ:", "Địa chỉ"],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": true,
          "table": {},
          "is_number": false
        },
        "DANG_KY_THAY_DOI": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Đăng ký thay đổi"],
          "is_visible": true,
          "is_date": false,
          "name": "DANG_KY_THAY_DOI",
          "sufwords": ["Tên chi nhánh", "Tên doanh nghiệp", "Tên công ty"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "DANG_KY_LAN_DAU": {
          "cer_threshold": 0.8,
          "paragraph": null,
          "prewords": ["Đăng ký lần đầu:"],
          "is_visible": true,
          "is_date": true,
          "name": "DANG_KY_LAN_DAU",
          "sufwords": [
            "Đăng ký thay đổi lần thứ",
            "Tên chi nhánh",
            "Tên doanh nghiệp",
            "Tên công ty"
          ],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "TEN_CONG_TY_TIENG_VIET": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Tên doanh nghiệp viết bằng tiếng Việt:",
            "Tên công ty viết bằng tiếng Việt:",
            "Tên chi nhánh:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "TEN_CONG_TY_TIENG_VIET",
          "sufwords": [
            "Tên doanh nghiệp viết bằng tiếng nước ngoài:",
            "Tên công ty viết bằng tiếng nước ngoài:",
            "Tên chi nhánh viết bằng tiếng nước ngoài:",
            "Tên công ty viết tắt:"
          ],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": true,
          "table": {},
          "is_number": false
        },
        "VON_DIEU_LE": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Vốn điều lệ:", "Vốn đầu tư:", "Vốn điều lệ", "Vốn đầu tư"],
          "is_visible": true,
          "is_date": false,
          "name": "VON_DIEU_LE",
          "sufwords": ["Bằng chữ:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "HO_VA_TEN": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Họ và tên:"],
          "is_visible": true,
          "is_date": false,
          "name": "HO_VA_TEN",
          "sufwords": ["Giới tính:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": true,
          "table": {},
          "is_number": false
        },
        "SO_GIAY_CHUNG_THUC_CA_NHAN": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Số giấy tờ pháp lý của cá nhân:",
            "Số giấy chứng thực cá nhân:",
            "Số:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "SO_GIAY_CHUNG_THUC_CA_NHAN",
          "sufwords": ["Ngày cấp:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "MA_SO_DOANH_NGHIEP": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Mã số chi nhánh:", "Mã số doanh nghiệp:", "Số:"],
          "is_visible": true,
          "is_date": false,
          "name": "MA_SO_DOANH_NGHIEP",
          "sufwords": ["Đăng ký lần đầu:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "QUOC_TICH": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Quốc tịch:"],
          "is_visible": true,
          "is_date": false,
          "name": "QUOC_TICH",
          "sufwords": [
            "Loại giấy tờ chứng thực cá nhân:",
            "Loại giấy chứng thực cá nhân:"
          ],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": true,
          "table": {},
          "is_number": false
        },
        "VON_DIEU_LE_BANG_CHU": {
          "cer_threshold": 0.88,
          "paragraph": null,
          "prewords": ["Bằng chữ:"],
          "is_visible": true,
          "is_date": false,
          "name": "VON_DIEU_LE_BANG_CHU",
          "sufwords": [
            "Mệnh giá cổ phần:",
            "Thông tin về chủ sở hữu",
            "Danh sách thành viên",
            "Người đại diện",
            "Vốn pháp định",
            "Tương đương"
          ],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "DIA_CHI_TRU_SO_CHINH": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Địa chỉ trụ sở chính", "Địa chỉ trụ sở", "Địa chỉ:", "Địa chỉ"],
          "is_visible": true,
          "is_date": false,
          "name": "DIA_CHI_TRU_SO_CHINH",
          "sufwords": ["Điện thoại:", "Email:"],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "NGAY_CAP": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Ngày cấp:"],
          "is_visible": true,
          "is_date": true,
          "name": "NGAY_CAP",
          "sufwords": ["Nơi cấp:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": [
            "Loại giấy tờ chứng thực cá nhân:",
            "Loại giấy chứng thực cá nhân:"
          ],
          "is_visible": true,
          "is_date": false,
          "name": "LOAI_GIAY_TO_CHUNG_THUC_CA_NHAN",
          "sufwords": ["Số giấy chứng thực cá nhân:", "Số:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "CHO_O_HIEN_TAI": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Địa chỉ liên lạc:", "Chỗ ở hiện tại:"],
          "is_visible": true,
          "is_date": false,
          "name": "CHO_O_HIEN_TAI",
          "sufwords": [
            "Người đại diện theo pháp luật",
            "Hoạt động theo ủy quyền của doanh nghiệp",
            "Thông tin về chí nhánh.",
            "Thông tin về chi nhánh",
            "TRƯỞNG PHÒNG",
            "Trưởng phòng",
            "Chữ ký:",
            "Chữ ký"
          ],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "NGAY_SINH": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Sinh ngày:", "Ngày sinh:"],
          "is_visible": true,
          "is_date": true,
          "name": "NGAY_SINH",
          "sufwords": ["Dân tộc:"],
          "location": null,
          "multilines": false,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        },
        "NOI_CAP": {
          "cer_threshold": 0.9,
          "paragraph": null,
          "prewords": ["Nơi cấp:"],
          "is_visible": true,
          "is_date": false,
          "name": "NOI_CAP",
          "sufwords": [
            "Nơi đăng ký hộ khẩu thường trú:",
            "Nơi đăng ký HKTT:",
            "Địa chỉ trụ sở chính:",
            "Địa chỉ thường trú:"
          ],
          "location": null,
          "multilines": true,
          "is_title_or_uppercase": false,
          "table": {},
          "is_number": false
        }
      },
      "ocr_model": "dkkd",
      "image_type": "ocr_dkkd"
    }
  },
  "challengeCode": "11111"
}
```

Http code: 404 – Template_id đầu vào không hợp lệ

```json
{
  "dataSign": "DpiExoMJXKpqpC4XzU1Q7WeVMWzPqsdesnhIf/NrUOY+S+XLjiC8Q/kkxX8pfNrIMssHPiznyps3STqo3Un+wg==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwNDA0Iiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsImVycm9ycyI6WyJEb2N1bWVudCBjb25maWcgbm90IGZvdW5kIl0sInN0YXR1cyI6Ik5PVCBGT1VORCIsInN0YXR1c0NvZGUiOjQwNCwiY2hhbGxlbmdlQ29kZSI6IjExMTExIn0=",
  "logID": "fa62fb7f-b371-11ec-9216-1f5dd20a8585-66b6658a-Zuulserver",
  "message": "IDG-00000404",
  "server_version": "1.1.5",
  "errors": ["Document config not found"],
  "status": "NOT FOUND",
  "statusCode": 404,
  "challengeCode": "11111"
}
```

# API Template Update

`Domain name:` [https://api.idg.vnpt.vn](https://api.idg.vnpt.vn)

**- Chức năng**: Cập nhật thông tin config của template trong database

**- Chi tiết API**:

**Endpoint:** `<domain-name>/template/config`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>action</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thao tác với template trong database (ở đây là tạo template => action=update)</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn.<br>
Định dạng: <br>
&lt;IOS/ANDROID&gt;_&lt;model name&gt;_&lt;OS/API&gt;_&lt;Device/Simulator&gt;_&lt;SDK version&gt;_&lt;Device id&gt;_&lt;Time stamp&gt;
</td>
</tr>
<tr>
<td></td>
<td>id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của template trong database</td>
</tr>
<tr>
<td></td>
<td>document_id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của văn bản lưu trữ trong database</td>
</tr>
<tr>
<td></td>
<td>file_link</td>
<td>String</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>config</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông tin config của template sẽ được lưu lại trong database</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "action": "update",
  "id": "DB535A43E96DEA6CE0536C1B9F0A8E3C",
  "name": "test-28-3191",
  "document_id": "",
  "file_link": "https://test-link",
  "config": {
    "ocr_model": "dkkd",
    "image_type": "ocr_dkkd",
    "content_from_pdf": false,
    "fields": {
      "MA_SO_DOANH_NGHIEP": {
        "name": "MA_SO_DOANH_NGHIEP",
        "prewords": ["Mã số chi nhánh :", "Mã số doanh nghiệp:"],
        "sufwords": ["Đăng ký lần đầu:"],
        "is_title_or_uppercase": false,
        "is_number": false,
        "is_date": false,
        "table": {},
        "multilines": false,
        "location": null,
        "paragraph": null,
        "cer_threshold": 0.9,
        "is_visible": true
      }
    }
  }
}
```

Response success

Http code: 200 - thành công

```json
{
  "dataSign": "Zvd1f3ImWHdj5Hp8uCdoVrvPL3ogqZ++syxa23dvuVV9KEUCaLaNwkWDTZKeeYKEcSmart ReaderNIk0Tgr4i9yJuJ4Eyg==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwMDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsInN0YXR1cyI6Ik9LIiwic3RhdHVzQ29kZSI6MjAwLCJjaGFsbGVuZ2VDb2RlIjoiMTExMTEifQ==",
  "logID": "ca0338fb-b3a4-11ec-9216-2fcb7dad999a-a9b58a04-Zuulserver",
  "message": "IDG-00000000",
  "server_version": "1.1.5",
  "status": "OK",
  "statusCode": 200,
  "challengeCode": "11111"
}
```

Http code: 400 - Template_id không hợp lệ

```json
{
  "dataSign": "CGVioDdQJ+dpbVnAvZ2SHkFwcFoQDcWvHEPTPuwGlKYsKMsrQUIf6r2ooYJ0HlB3NrUhI2FYlSWorCmOdzPt4Q==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwNDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsImVycm9ycyI6WyJUZW1wbGF0ZSBJRCBtdXN0IGJlIDMyIGxlbmd0aCJdLCJzdGF0dXMiOiJCQUQgUkVRVUVTVCIsInN0YXR1c0NvZGUiOjQwMCwiY2hhbGxlbmdlQ29kZSI6IjExMTExIn0=",
  "logID": "68665d58-b374-11ec-9216-b93dde4a3067-88422442-Zuulserver",
  "message": "IDG-00000400",
  "server_version": "1.1.5",
  "errors": ["Template ID must be 32 length"],
  "status": "BAD REQUEST",
  "statusCode": 400,
  "challengeCode": "11111"
}
```

# API Template Delete

`Domain name:` [https://api.idg.vnpt.vn](https://api.idg.vnpt.vn)

**- Chức năng**: Xóa thông tin template trong database

**- Chi tiết API**:

**Endpoint:** `<domain-name>/template/config`

**Method**: POST

**Request:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>HEADER</td>
<td>Content-Type</td>
<td>application/json</td>
<td>x</td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>Authorization</td>
<td>Access token</td>
<td>x</td>
<td></td>
<td>Access_token lấy từ service oauth/token</td>
</tr>
<tr>
<td></td>
<td>Token-id</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
<tr>
<td></td>
<td>Token-key</td>
<td>String</td>
<td>x</td>
<td>50</td>
<td>Chuỗi ký tự bảo mật</td>
</tr>
</tbody></table>

**Request body:**

<table>
<colgroup>
<col style="width: 10%;">
<col style="width: 20%;">
<col style="width: 20%">
<col style="width: 8%;">
<col style="width: 8%;">
<col>
</colgroup>
<thead>
<tr>
<th></th>
<th><strong>Tên trường</strong></th>
<th><strong>Giá trị</strong></th>
<th><strong>Bắt buộc</strong></th>
<th><strong>Max length</strong></th>
<th><strong>Mô tả</strong></th>
</tr>
</thead>
<tbody><tr>
<td>BODY</td>
<td>action</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thao tác với template trong database (ở đây là tạo template => action=delete)</td>
</tr>
<tr>
<td></td>
<td>token</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Một chuỗi bất kỳ bên app truyền vào để phục vụ cho việc tra log phía server</td>
</tr>
<tr>
<td></td>
<td>client_session</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông tin Session của khách hàng theo cú pháp cho sẵn.<br>
Định dạng: <br>
&lt;IOS/ANDROID&gt;_&lt;model name&gt;_&lt;OS/API&gt;_&lt;Device/Simulator&gt;_&lt;SDK version&gt;_&lt;Device id&gt;_&lt;Time stamp&gt;
</td>
</tr>
<tr>
<td></td>
<td>id</td>
<td>String</td>
<td>x</td>
<td></td>
<td>ID của template trong database</td>
</tr>
<tr>
<td></td>
<td>document_id</td>
<td>String</td>
<td></td>
<td></td>
<td>ID của văn bản lưu trữ trong database</td>
</tr>
<tr>
<td></td>
<td>file_link</td>
<td>String</td>
<td></td>
<td></td>
<td></td>
</tr>
<tr>
<td></td>
<td>config</td>
<td>String</td>
<td>x</td>
<td></td>
<td>Thông tin config của template sẽ được lưu lại trong database</td>
</tr>
</tbody></table>

**Example:**

Request body

```json
{
  "token": "8928skjhfa89298jahga1771vbvb",
  "client_session": "00-14-22-01-23-45-1548211589291",
  "action": "delete",
  "id": "DBC45ED011E1AAD2E0536C1B9F0A88FE"
}
```

Response success

Http code: 200 - thành công

```json
{
  "dataSign": "Zvd1f3ImWHdj5Hp8uCdoVrvPL3ogqZ++syxa23dvuVV9KEUCaLaNwkWDTZKeeYKEcSmart ReaderNIk0Tgr4i9yJuJ4Eyg==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwMDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsInN0YXR1cyI6Ik9LIiwic3RhdHVzQ29kZSI6MjAwLCJjaGFsbGVuZ2VDb2RlIjoiMTExMTEifQ==",
  "logID": "8536a76c-b3a5-11ec-9216-317e10692def-b515ec6b-Zuulserver",
  "message": "IDG-00000000",
  "server_version": "1.1.5",
  "status": "OK",
  "statusCode": 200,
  "challengeCode": "11111"
}
```

Http code: 400 - Template_id không hợp lệ

```json
{
  "dataSign": "CGVioDdQJ+dpbVnAvZ2SHkFwcFoQDcWvHEPTPuwGlKYsKMsrQUIf6r2ooYJ0HlB3NrUhI2FYlSWorCmOdzPt4Q==",
  "dataBase64": "eyJtZXNzYWdlIjoiSURHLTAwMDAwNDAwIiwic2VydmVyX3ZlcnNpb24iOiIxLjEuNSIsImVycm9ycyI6WyJUZW1wbGF0ZSBJRCBtdXN0IGJlIDMyIGxlbmd0aCJdLCJzdGF0dXMiOiJCQUQgUkVRVUVTVCIsInN0YXR1c0NvZGUiOjQwMCwiY2hhbGxlbmdlQ29kZSI6IjExMTExIn0=",
  "logID": "aadd536d-b3a5-11ec-9216-7151352611d0-bf000013-Zuulserver",
  "message": "IDG-00000400",
  "server_version": "1.1.5",
  "errors": ["Template ID must be 32 length"],
  "status": "BAD REQUEST",
  "statusCode": 400,
  "challengeCode": "11111"
}
```
