import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DocsComponent } from './docs.component';
import { docsConfigList } from './docs';

const routes: Routes = [
  {
    path: ':docs-key',
    component: DocsComponent
  },
  { path: '**', redirectTo: docsConfigList[0].key }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DocsRoutingModule {}
