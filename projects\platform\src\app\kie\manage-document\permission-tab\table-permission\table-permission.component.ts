import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ModalDeleteAccessComponent } from './modal-delete-access/modal-delete-access.component';
import { ModalTransferOwnershipComponent } from './modal-transfer-ownership/modal-transfer-ownership.component';
import { DocumentPermission, ListDocumentPermission, RolePermisson } from '@platform/app/kie/kie';
import { filter, take, tap } from 'rxjs';

@Component({
  selector: 'app-table-permission',
  templateUrl: './table-permission.component.html',
  styleUrls: ['./table-permission.component.scss']
})
export class TablePermissionComponent implements OnInit, OnChanges {
  _documentId: string;
  @Input()
  set documentId(id: string) {
    this._documentId = id;
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }
  get documentId(): string {
    return this._documentId;
  }
  @Input() listPermission: ListDocumentPermission;
  @Output() onPageChanged: EventEmitter<number> = new EventEmitter<number>();
  @Output() onAssigneeRoleChanged: EventEmitter<{}> = new EventEmitter<number>();
  @Output() onLimitChanged: EventEmitter<number> = new EventEmitter<number>();

  checked = false;
  indeterminate = false;
  listOfCurrentPageData: readonly DocumentPermission[] = [];
  setOfCheckedId = new Set<string>();

  constructor(private modalService: NzModalService) {}

  ngOnInit(): void {}

  ngOnChanges(): void {}

  // handler pagination
  handleChangePage(pageIndex: number): void {
    this.onPageChanged.emit(pageIndex);
  }

  handleChangeLimit(limit): void {
    this.onLimitChanged.emit(limit);
  }

  // handler active list checkbox
  updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  onCurrentPageDataChange(listOfCurrentPageData: DocumentPermission[]): void {
    this.listOfCurrentPageData = listOfCurrentPageData;
    this.refreshCheckedStatus();
  }

  refreshCheckedStatus(): void {
    const listOfEnabledData = this.listOfCurrentPageData; /* .filter(
      ({ disabled }) => !disabled
    ); */
    if (!listOfEnabledData.length) {
      this.checked = false;
      this.indeterminate = false;
      return;
    }
    this.checked = listOfEnabledData.every(({ assigneeId }) =>
      this.setOfCheckedId.has(assigneeId)
    );
    this.indeterminate =
      listOfEnabledData.some(({ assigneeId }) => this.setOfCheckedId.has(assigneeId)) &&
      !this.checked;
  }

  onItemChecked(id: string, checked: boolean): void {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onAllChecked(checked: boolean): void {
    this.listOfCurrentPageData
      // .filter(({ disabled }) => !disabled)
      .forEach(({ assigneeId }) => this.updateCheckedSet(assigneeId, checked));
    this.refreshCheckedStatus();
  }

  uncheckAll() {
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }

  viewRole(role: RolePermisson): string {
    switch (role) {
      case 'creator':
        return 'Chủ sở hữu';
      case 'editor':
        return 'Người chỉnh sửa';
      case 'viewer':
        return 'Người xem';
    }
  }

  changeAssigneeRole(listDp: Partial<DocumentPermission>[], role: 'viewer' | 'editor') {
    if (listDp.length === 0) return;
    if (listDp.length === 1 && listDp[0].role && listDp[0].role === role) return;

    const warningText = `Chuyển vai trò của <b>${listDp.length === 1 && listDp[0].assignee?.name ? listDp[0].assignee.name : listDp.length + ' người dùng'}</b> thành <b>${this.viewRole(role)}<b/>`;
    const modal = this.modalService.confirm({
      nzTitle: '<b>Lưu ý</b>',
      nzContent: warningText,
      nzCancelText: 'Hủy',
      nzOkText: 'OK',
      nzOnOk: () => {
        this.onAssigneeRoleChanged.emit({
          assigneeIds: listDp.map((dp) => dp.assigneeId),
          role
        });
        return true;
      }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => {
          listDp.forEach((dp) => this.updateCheckedSet(dp.assigneeId, false));
          this.refreshCheckedStatus();
          console.log(this.setOfCheckedId, this.indeterminate, this.checked);
        })
      )
      .subscribe();
  }

  changeAssigneeRoleMany(role: 'viewer' | 'editor') {
    const selectedAssigneeIds = Array.from(this.setOfCheckedId);
    this.changeAssigneeRole(
      selectedAssigneeIds.map((assigneeId) => ({ assigneeId })),
      role
    );
  }

  showModalDeleteAccess(assignees: { id: string; name?: string }[]): void {
    if (!assignees.length) return;
    const modal = this.modalService.create({
      nzWidth: 335,
      nzTitle: null,
      nzContent: ModalDeleteAccessComponent,
      nzData: { documentId: this.documentId, assignees },
      nzMaskClosable: true,
      nzClosable: true,
      nzFooter: null,
      nzCloseIcon: null,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '20px' },
      nzStyle: { width: '335px' }
    });
    modal.afterClose
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => {
          this.onPageChanged.emit(this.listPermission.page); // reload items for current page
          assignees.forEach((user) => this.updateCheckedSet(user.id, false));
          this.refreshCheckedStatus();
        })
      )
      .subscribe();
  }

  deleteAccessMany() {
    const selectedAssigneeIds = Array.from(this.setOfCheckedId);
    this.showModalDeleteAccess(selectedAssigneeIds.map((id) => ({ id })));
  }

  showModalTransferOwnership(): void {
    this.modalService.create({
      nzWidth: 335,
      nzTitle: null,
      nzContent: ModalTransferOwnershipComponent,
      nzMaskClosable: true,
      nzClosable: true,
      nzFooter: null,
      nzCloseIcon: null,
      nzClassName: 'custom-ant-modal-common-styles',
      nzBodyStyle: { padding: '20px' },
      nzStyle: { width: '335px' }
    });
  }
}
