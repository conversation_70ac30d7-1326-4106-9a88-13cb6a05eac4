import {
  AfterViewInit,
  Component,
  Do<PERSON>heck,
  ElementRef,
  EventEmitter,
  Input,
  NgZone,
  OnDestroy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild
} from '@angular/core';
import {
  BehaviorSubject,
  EMPTY,
  Subject,
  catchError,
  distinctUntilChanged,
  filter,
  finalize,
  from,
  fromEvent,
  iif,
  map,
  of,
  switchMap,
  take,
  takeUntil,
  tap
} from 'rxjs';
import { fabric } from 'fabric';
// import { Canvas, Group, Image } from 'fabric/fabric-impl';
import { NodeCanvasFactory } from '@platform/app/core/factories/canvas.factory';
import {
  Bbox,
  BboxChange,
  DocumentField,
  DrawCommand,
  FieldOcrResultChange,
  File,
  excludedKeyFields
} from '@platform/app/kie/kie';
import { get, isEqual, pick, uniq } from 'lodash';
import { NgxSpinnerModule, NgxSpinnerService } from 'ngx-spinner';
import UtilsService from '@platform/app/core/services/utils.service';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { CommonModule } from '@angular/common';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { FormsModule } from '@angular/forms';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { ImageEditorComponent } from '../image-editor/image-editor.component';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { DocumentCompareComponent } from '@platform/app/components/document-compare/document-compare.component';

enum BasicColor {
  Green = '#009B4E',
  Red = '#FF3355',
  Yellow = '#FFA100'
}

type BboxData = {
  bboxId: string; // unique
  fieldId: string;

  valueTypeListPath?: number; // cell index in type = List
  valueTypeTablePath?: {
    // column + cell index in type = Table
    columnId: number;
    cellId: number;
  };

  name: string;
  text: string;
  bbox: Bbox;
  color?: string /* TODO: MAKE color required */;
};

const DEFAULT_FEATURE_FLAGS = {
  DisplayOcrResult: false,
  ViewerFitPageWidth: false,
  EditOcrResult: false,
  Zooming: true,
  PdfLazyLoading: true,
  OnlyDisplaySpecifiedPages: false,
  EditPageImage: false,
  CompareDocument: false
};

@Component({
  selector: 'app-document-viewer',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzSwitchModule,
    NzToolTipModule,
    NzPopoverModule,
    NzModalModule,
    NgxSpinnerModule,
    DocumentCompareComponent
  ],
  templateUrl: './document-viewer.component.html',
  styleUrls: ['./document-viewer.component.scss']
})
export class DocumentViewerComponent
  implements OnInit, OnDestroy, DoCheck, AfterViewInit
{
  @Input()
  bboxChangeSubject: BehaviorSubject<BboxChange>;

  loadingSubject = new BehaviorSubject<boolean>(false);

  @Input()
  fileName?: string;

  // fileLinkChangeCount = 0;
  private _fileLink: string;
  @Input()
  set fileLink(fileLink: string) {
    const current = this.fileLink,
      updated = fileLink;
    this._fileLink = fileLink;
    // this.fileLinkChangeCount++;
    try {
      new URL(this.fileLink); // trigger throwing exception if this.fileLink value is null / undefined / invalid link

      /* only doing reset and fetch actual new this.fileLink (not the same file but with different presigned link) */
      if (this.checkFileLinkIsReallyChanged(current, updated)) {
        this.resetOnFileLinkChanged();
        this.loadingSubject.next(true);
        this.utils
          .fetchFile(fileLink)
          .pipe(
            take(1),
            catchError((error) => {
              console.log('Fetch file failed', error);
              return EMPTY;
            }),
            switchMap((blob) => from(this.handleFetchedFile(blob))),
            tap(() => {
              this.initCanvas();
              this.handleCurrentPageChange();
              this.handleCurrentModeChange();
              this.handleKeyboardEvents();
              this.handleDrawCommand();
              this.handleSelectedBboxSubject();
            })
          )
          .subscribe();
      }
    } catch (error) {
      /* this.file value: null or undefined or invalid link will go here (cleanup) */
      this.resetOnFileLinkChanged();
    }
  }
  get fileLink(): string {
    return this._fileLink;
  }

  // ocrResultChangeCount = 0;
  private _ocrResult: File['ocrResult'];
  @Input()
  set ocrResult(ocrResult: File['ocrResult']) {
    if (!this.featureFlags['DisplayOcrResult']) {
      this._ocrResult = null;
      return;
    }
    // console.log(++this.ocrResultChangeCount, { old: this.ocrResult, new: ocrResult });
    this._ocrResult = ocrResult;

    /* check renderedPages.length > 0 => file is already loaded successfully then apply drawing bboxes logic */
    if (this.renderedPages.size === 0) return;

    this.renderedPages.forEach((page) =>
      page.getObjects('rect').forEach((rect) => {
        page.remove(rect);
      })
    );
    this.renderBboxes(
      this.currentPageIndexSubject.value,
      this.renderedPages.get(this.currentPageIndexSubject.value)
    );
  }
  get ocrResult(): File['ocrResult'] {
    return this._ocrResult;
  }

  canvas: fabric.Canvas;

  renderedPages: Map<number, fabric.Group> = new Map();

  currentPageIndexSubject = new BehaviorSubject(0); // zero-based index

  @Output() onChangePageIndex = new EventEmitter<number>();

  zoomLevel = '100%';

  numPages = 0;

  pdfDocument: PDFDocumentProxy = null;

  currentModeSubject = new BehaviorSubject<'normal' | 'zoom'>('normal');

  // isDrawingBbox = false;

  @Input()
  drawCommandSubject: BehaviorSubject<DrawCommand>;

  selectedBboxSubject = new BehaviorSubject<BboxData | null>(null);

  keyList = [
    'ShiftLeft',
    'Space',
    'ArrowRight',
    'ArrowLeft'
  ] as const; /* list of pressing keys being watched */
  pressingKey: (typeof this.keyList)[number] | null = null;

  @ViewChild('popoverAnchor')
  popoverAnchor: ElementRef;

  @ViewChild('tooltipAnchor')
  tooltipAnchor: ElementRef;

  private _cleanup = new Subject<void>();

  // editFieldPopoverVisible = false;
  editingFieldValue = '';
  private editOcrResultSubject = new Subject<FieldOcrResultChange>();
  @Output()
  onEditOcrResult = this.editOcrResultSubject.pipe(
    distinctUntilChanged((prev, curr) => isEqual(prev, curr)),
    /* debounce changes here if editOcrResultSubject is hooked to rapidly onChange of editingFieldValue */
    filter(
      (change) =>
        this.featureFlags['EditOcrResult'] &&
        this.selectedBboxSubject.value?.text !== change.text.trim()
    ) // filter if there is no change in field value
  );

  @ViewChild('documentWrapper') documentWrapper!: ElementRef<HTMLDivElement>;
  documentWrapperWidth: number = 200;
  resizeObserver: ResizeObserver;

  // Why?: document-viewer is reusable. feature flags allow parent component to have control over actual functionalities of document-viewer.component. different parents have its own requirements from document-viewer.component
  _featureFlags: {
    [key in
      | 'DisplayOcrResult' // render interactive bbox for field [x]
      | 'EditOcrResult' // allow edit ocr result, equivalent to 'extract-file' as before [x]
      | 'ViewerFitPageWidth' // allow viewer be rendered with all the needed height rather than being confined to allocated space remaining on the screen [WIP]
      | 'Zooming' // allow zooming [x]
      | 'PdfLazyLoading' // lazy loading each page / load all pages at initialization [x]
      | 'OnlyDisplaySpecifiedPages' // display pdf/image with specified pages only
      | 'EditPageImage'
      | 'CompareDocument']: boolean;
  }; // _featureFlags is declared without intial value
  @Input()
  set featureFlags(featureFlags) {
    /* once setter is executed, _featureFlags is set (with fallback value from DEFAULT_FEATURE_FLAGS) */
    /* TODO: expect @Input() featureFlags to be set by parent once, but in the case of featureFlags can be changed then... */
    if (this._featureFlags && isEqual(featureFlags, this._featureFlags)) return;

    const basedFF = Object.assign({}, DEFAULT_FEATURE_FLAGS);
    this._featureFlags = Object.assign(
      basedFF,
      pick(featureFlags, [
        'DisplayOcrResult',
        'EditOcrResult',
        'ViewerFitPageWidth',
        'Zooming',
        'PdfLazyLoading',
        'OnlyDisplaySpecifiedPages',
        'EditPageImage',
        'CompareDocument'
      ])
    );

    /* close unused subjects, skip initialization and setup */
    // if (!this._featureFlags['DisplayOcrResult']) this.ocrResult = null; // FIXME: ??

    if (!this._featureFlags['EditOcrResult']) this.editOcrResultSubject.complete();

    if (!this._featureFlags['ViewerFitPageWidth']) {
      this.resizeObserver?.disconnect(); // quite unnecessary since resizeObserver is yet to initialized
    }

    if (!this._featureFlags['Zooming']) {
      if (this.currentModeSubject.value === 'zoom')
        this.currentModeSubject.next('normal');
      this.currentModeSubject.complete();
    }

    if (!this._featureFlags['OnlyDisplaySpecifiedPages'])
      this.onSpecifiedPagesValidation.complete();

    if (!this._featureFlags['EditPageImage']) this.onEditPageImage.complete();
  }
  get featureFlags() {
    return this._featureFlags || DEFAULT_FEATURE_FLAGS; // default value is used while _featureFlags hasn't been set
  }

  @Output() afterFileLinkLoaded = new EventEmitter<{ numPages: number }>();

  _specifiedPages: number[] = null;
  @Input()
  set specifiedPages(specifiedPages: number[]) {
    if (!this.featureFlags['OnlyDisplaySpecifiedPages']) return;

    if (!specifiedPages?.length) {
      this._specifiedPages = null;
      return;
    }

    of(1) // source observable
      .pipe(
        switchMap(() =>
          iif(
            () => this.numPages === 0, // check whether handleFetchedFile is executed (numPages is set) at this point
            this.afterFileLinkLoaded.asObservable(), // delay the below operation until handleFetchedFile is executed (numPages is set)
            of(1) // run the below operation immediately
          )
        ),
        take(1),
        tap(() => {
          specifiedPages = uniq(specifiedPages).sort((a, b) => a - b);
          let validation: any = { valid: true };
          /* validates */
          if (specifiedPages.every((pageIndex) => pageIndex < this.numPages)) {
            this._specifiedPages = specifiedPages;
            this.goToPage(this.specifiedPages[0]);
          } else {
            validation = {
              valid: false,
              errorMsg: `Giới hạn trang không hợp lệ, lựa chọn trong khoảng từ 1 tới ${this.numPages || 1}`
            };
            this._specifiedPages = null; // reset current _specifiedPages if valid = false
          }
          this.onSpecifiedPagesValidation.emit(validation);
        })
      )
      .subscribe();
  }
  get specifiedPages() {
    return this._specifiedPages;
  }

  @Output() onSpecifiedPagesValidation = new EventEmitter<{
    valid: boolean;
    errorMsg?: string;
  }>();

  @Output() onEditPageImage = new EventEmitter<{
    pageIndex: number;
    editedPageImageLink: string;
  }>();

  readonly pageProps: fabric.IGroupOptions = {
    selectable: false /* set to false in order for drawing on top of canvas to work */,
    // hasRotatingPoint: false,
    hoverCursor: 'default',
    hasControls: false,
    lockMovementX: true,
    lockMovementY: true,
    lockRotation: true,
    lockScalingFlip: true,
    lockScalingX: true,
    lockScalingY: true,
    lockSkewingX: true,
    lockSkewingY: true,
    lockUniScaling: true,
    subTargetCheck: true
  };

  @ViewChild('infoModal')
  infoModal: TemplateRef<any>;

  @Input() fileLinkToCompareWith: string;

  @ViewChild('documentCompareModal')
  documentCompareModal: TemplateRef<any>;

  constructor(
    private zone: NgZone,
    private spinner: NgxSpinnerService,
    private utils: UtilsService,
    private pdfService: PdfService,
    private modalService: NzModalService
  ) {
    fabric.Object.prototype.objectCaching = false;
  }

  ngOnInit(): void {
    /*
      At this point of execution, if _featureFlags value is not set yet, meaning featureFlags setter never executed, then run the featureFlags setter with default value
      This ensures _featureFlags is always set with default value even if parent component does not provided and the other logic in featureFlags setter is executed
    */
    if (!this._featureFlags) this.featureFlags = Object.assign({}, DEFAULT_FEATURE_FLAGS);

    this.loadingSubject
      .pipe(
        distinctUntilChanged(),
        tap((isLoading) => {
          const name = 'document-viewer-loading-spinner';
          isLoading ? this.spinner.show(name) : this.spinner.hide(name);
        })
      )
      .subscribe();
  }

  ngAfterViewInit() {
    if (this.featureFlags['ViewerFitPageWidth']) {
      this.documentWrapper.nativeElement.style.overflow = 'hidden'; // FIXME: temporary fix and to be removed until better solution.
      /* 
          TODO:
          the documentWrapper.nativeElement.clientWidth before init canvas is slightly longer than documentWrapper.nativeElement.clientWidth after the canvas is init
          this is because after the canvas is init, its height expand beyond current window.innerHeight => ~11px scroll bar is shown causing the documentWrapper.nativeElement.clientWidth is shrink slightly

          SOLUTION:
          calculate the the canvasHeight + its parent offsetTop to see if they are exceed the window.innerHeight => 
        */
      /* WIP SOLUTION */
      /* this.resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          if (entry.target.id !== 'document-wrapper') continue;
          const width = entry.contentRect.width;
          const height = entry.contentRect.height;
          console.log('resizeObserver', `Width: ${width}px, Height: ${height}px`);
          this.documentWrapperWidth = width;
        }
      });
      this.resizeObserver.observe(this.documentWrapper.nativeElement); */
    }
  }

  // check = 0;
  ngDoCheck(): void {
    // console.log('document-viewer', ++this.check);
  }

  private async handleFetchedFile(blob: Blob) {
    const fileObjUrl = URL.createObjectURL(blob); // FIXME: @Input fileLink is already a link, why need to fetch the file itself and get returned blob and then created a link out of it => need optimization
    switch (blob.type) {
      case 'application/pdf': {
        this.pdfDocument = await this.pdfService.pdfjsDist.getDocument({
          url: fileObjUrl
          // cMapUrl: 'node_modules/pdfjs-dist/cmaps',
          // cMapPacked: true,
          // standardFontDataUrl: 'node_modules/pdfjs-dist/standard_fonts/'
          // canvasFactory,
        }).promise;
        this.numPages = this.pdfDocument.numPages;

        /* lazy-loading approach: pre-load page 0 and pre-load 3 pages forward and 3 pages backward of page 0 */
        const pageIndexList = uniq(
          [-3, -2, -1, 0, 1, 2, 3]
            .map((i) => {
              if (i >= 0) return i;
              else return this.numPages + i;
            })
            .filter((i) => i >= 0 && i < this.numPages)
        ).sort((a, b) => a - b);
        await this.loadPagesFromPdfDocument(
          // new Array(1).fill(undefined).map((_, i) => i) // load only first page
          this.featureFlags['PdfLazyLoading']
            ? pageIndexList // load first 7 pages maximum
            : new Array(this.numPages).fill(undefined).map((_, i) => i) // load all pages
        );

        break;
      }

      case 'image/png':
      case 'image/jpeg': {
        // const base64Image = await this.blobToBase64(blob);
        // console.log(base64Image);
        // console.log(fileObjUrl);

        const image = await fabric.Image['fromURLAsync'](fileObjUrl, {
          originX: 'left',
          originY: 'top'
        });
        this.numPages = 1;
        this.renderedPages.set(
          0,
          new fabric.Group([image], {
            data: { id: 0, originalPageImageLink: fileObjUrl, pageImageLink: fileObjUrl },
            ...this.pageProps
          })
        );
        break;
      }

      default:
        break;
    }
    this.afterFileLinkLoaded.emit({ numPages: this.numPages });
    this.loadingSubject.next(false);
  }

  private initCanvas() {
    /* all fabric canvas related works are run outside angular change detection for better performance */
    this.zone.runOutsideAngular(() => {
      try {
        let canvasHeight, canvasWidth;
        canvasHeight = this.documentWrapper.nativeElement.clientHeight;
        canvasWidth = this.documentWrapper.nativeElement.clientWidth;
        /* with ViewerFitPageWidth = true, canvas.height should be calculated based off documentWrapper.width and current page ratio */
        if (this.featureFlags['ViewerFitPageWidth']) {
          this.canvas && (canvasWidth = this.canvas.width); // reuse canvasWidth if possible
          const { height: currentPageHeight, width: currentPageWidth } =
            this.renderedPages.get(this.currentPageIndexSubject.value);
          canvasHeight = canvasWidth * (currentPageHeight / currentPageWidth);
        }

        /* from 2nd initCanvas onward, clear and dispose the current canvas first */
        if (this.canvas) {
          this.canvas?.clear()?.dispose();
          this.canvas = null;
        }

        this.canvas = new fabric.Canvas('document-viewer', {
          selection: false,
          // backgroundColor: 'lightblue',
          // match canvas dimension to its component wrapper
          height: canvasHeight,
          width: canvasWidth
        });
      } catch (error) {
        console.log('initCanvas failed:', error);
      }
    });
  }

  private handleCurrentPageChange() {
    this.currentPageIndexSubject
      .pipe(
        distinctUntilChanged(),
        takeUntil(this._cleanup),
        tap(
          () => this.featureFlags['ViewerFitPageWidth'] && this.reInitCanvasIfNecessary()
        ),
        tap((pageIndex) => {
          this.renderPage(pageIndex);
          this.onChangePageIndex.emit(pageIndex);
        })
        // finalize(() => console.log('cleanup currentPage subject'))
      )
      .subscribe();
  }

  private handleCurrentModeChange() {
    this.zone.runOutsideAngular(() => {
      this.currentModeSubject
        .pipe(
          distinctUntilChanged(),
          takeUntil(this._cleanup),
          tap((mode) => {
            const page = this.canvas.getObjects('group')[0] as fabric.Group;
            // console.log('mode', mode, this.canvas.getObjects('group'));
            this.setupPageHoverCursor(page, mode);

            this.canvas
              ?.off('mouse:wheel')
              ?.off('mouse:down')
              ?.off('mouse:move')
              ?.off('mouse:up');
            switch (mode) {
              case 'zoom': {
                let isDragging = false,
                  lastPosX: number,
                  lastPosY: number,
                  vpt: any;

                // register event at top level canvas
                this.canvas
                  .on('mouse:wheel', (opt: any) => {
                    // console.log(opt.e.deltaY);
                    // const delta = opt.e.deltaY;
                    // let zoom = this.canvas.getZoom();
                    // zoom *= 0.999 ** delta;

                    let zoom = this.canvas.getZoom();
                    if (opt.e.deltaY < 0) zoom += 0.1;
                    if (opt.e.deltaY > 0) zoom -= 0.1;

                    if (zoom > 2.5) zoom = 2.5;
                    if (zoom < 1) zoom = 1;
                    this.canvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
                    opt.e.preventDefault();
                    opt.e.stopPropagation();

                    vpt = this.canvas.viewportTransform;
                    const panXMax =
                      this.canvas.getWidth() * this.canvas.getZoom() -
                      this.canvas.getWidth();
                    if (vpt[4] > 0) vpt[4] = 0;
                    if (Math.abs(vpt[4]) > panXMax) vpt[4] = -panXMax;
                    const panYMax =
                      this.canvas.getHeight() * this.canvas.getZoom() -
                      this.canvas.getHeight();
                    if (vpt[5] > 0) vpt[5] = 0;
                    if (Math.abs(vpt[5]) > panYMax) vpt[5] = -panYMax;

                    this.zone.run(() => {
                      this.zoomLevel = `${(this.canvas.getZoom() * 100).toFixed(0)}%`;
                    });

                    // console.log(
                    //   'viewporttransofrm',
                    //   this.canvas.viewportTransform,
                    //   'zoom',
                    //   zoom
                    // );
                  })
                  .on('mouse:down', (opt: any) => {
                    // if (!opt.e.altKey) return;
                    isDragging = true;
                    lastPosX = opt.e.clientX;
                    lastPosY = opt.e.clientY;
                  })
                  .on('mouse:move', (opt: any) => {
                    if (!isDragging) return;
                    vpt = this.canvas.viewportTransform;
                    vpt[4] += opt.e.clientX - lastPosX;
                    vpt[5] += opt.e.clientY - lastPosY;

                    const panXMax =
                      this.canvas.getWidth() * this.canvas.getZoom() -
                      this.canvas.getWidth();
                    if (vpt[4] > 0) vpt[4] = 0;
                    if (Math.abs(vpt[4]) > panXMax) vpt[4] = -panXMax;
                    const panYMax =
                      this.canvas.getHeight() * this.canvas.getZoom() -
                      this.canvas.getHeight();
                    if (vpt[5] > 0) vpt[5] = 0;
                    if (Math.abs(vpt[5]) > panYMax) vpt[5] = -panYMax;

                    this.canvas.requestRenderAll();
                    lastPosX = opt.e.clientX;
                    lastPosY = opt.e.clientY;
                  })
                  .on('mouse:up', () => {
                    // on mouse up we want to recalculate new interaction
                    // for all objects, so we call setViewportTransform
                    this.canvas.setViewportTransform(this.canvas.viewportTransform);
                    isDragging = false;
                  });
                break;
              }
              case 'normal': {
                this.canvas.on('mouse:wheel', (opt) => {
                  // console.log(opt.e.deltaY);
                  const panXMax =
                    this.canvas.getWidth() * this.canvas.getZoom() -
                    this.canvas.getWidth();
                  const panYMax =
                    this.canvas.getHeight() * this.canvas.getZoom() -
                    this.canvas.getHeight();
                  // console.log(this.canvas.viewportTransform, panYMax);

                  /* 
                    in viewportTransform array, with index of:
                      4: xAxis,
                      5: yAxis
                  */
                  enum ScrollAxis {
                    xAxis = 4,
                    yAxis = 5
                  }
                  const isPressingShift = this.pressingKey === 'ShiftLeft';
                  const scrollAxis: ScrollAxis = isPressingShift
                    ? ScrollAxis.xAxis
                    : ScrollAxis.yAxis;
                  const panMax = isPressingShift ? panXMax : panYMax;
                  /* scroll: > 0 === down | < 0 === up */
                  let absScroll = 0;
                  if (opt.e.deltaY > 0) {
                    absScroll = Math.abs(this.canvas.viewportTransform[scrollAxis]) + 20;
                    absScroll = absScroll > panMax ? panMax : absScroll;
                  } else {
                    absScroll = Math.abs(this.canvas.viewportTransform[scrollAxis]) - 20;
                    absScroll = absScroll < 0 ? 0 : absScroll;
                  }
                  this.canvas.viewportTransform[scrollAxis] = -absScroll;
                  this.canvas.requestRenderAll();
                });
                break;
              }

              default:
                break;
            }
          })
          // finalize(() => console.log('cleanup currentMode subject'))
        )
        .subscribe();
    });
  }

  private setupPageHoverCursor(page: fabric.Group, pageMode: 'normal' | 'zoom') {
    page.off('mouseover').off('mousedown').off('mouseup');
    if (pageMode === 'normal') page.set('hoverCursor', 'default');
    else
      page
        .on('mouseover', () => page.set('hoverCursor', 'grab'))
        .on('mousedown', () => page.set('hoverCursor', 'grabbing'))
        .on('mouseup', () => page.set('hoverCursor', 'grab'));
  }

  private handleKeyboardEvents() {
    fromEvent(document, 'keydown')
      .pipe(
        takeUntil(this._cleanup),
        filter(
          (evt: KeyboardEvent) =>
            /* only accept event of listed keys AND not while typing on any <input /> */
            this.keyList.includes(evt.code as any) && evt.target === document.body
        ),
        tap((evt: KeyboardEvent) => {
          // console.log('keydown', evt.target === document.body);
          this.pressingKey = evt.code as any;

          if (evt.code === 'Space') this.switchCurrentMode('zoom');
        })
        // finalize(() => console.log('cleanup keyboarEvent obs'))
      )
      .subscribe();

    fromEvent(document, 'keyup')
      .pipe(
        takeUntil(this._cleanup),
        filter(
          (evt: KeyboardEvent) =>
            /* only accept event of listed keys AND not while typing on any <input /> */
            this.keyList.includes(evt.code as any) && evt.target === document.body
        ),
        tap((evt: KeyboardEvent) => {
          // console.log('keyup', evt.code);
          this.pressingKey = null;

          switch (evt.code) {
            case 'Space': {
              this.switchCurrentMode('normal');
              break;
            }
            case 'ArrowRight': {
              this.nextPage();
              break;
            }
            case 'ArrowLeft': {
              this.prevPage();
              break;
            }
          }
        })
        // finalize(() => console.log('cleanup keyboarEvent obs'))
      )
      .subscribe();
  }

  private handleDrawCommand() {
    if (!this.drawCommandSubject) return;
    this.zone.runOutsideAngular(() => {
      const setVisiblePageBboxRects = (visible: boolean, excludedBboxIds?) => {
        const page = this.canvas.getObjects('group')[0] as fabric.Group;
        if (!page) return;
        page.forEachObject((obj) => {
          if (obj.type === 'rect')
            obj.set(
              'visible',
              excludedBboxIds?.includes(obj.data['fieldId']) ? !visible : visible
            );
        });
        this.canvas.requestRenderAll();
      };
      const setupDrawBboxEvent = (command: DrawCommand) => {
        let tmpBbox: fabric.Rect | null;
        let isDragging = false;
        const page = this.canvas.getObjects('group')[0] as fabric.Group;
        if (!page) return;
        setVisiblePageBboxRects(false, [this.drawCommandSubject.value?.fieldId]);
        // console.log(page);
        page.set('hoverCursor', 'crosshair');
        this.canvas.requestRenderAll();
        this.canvas
          ?.off('mouse:down')
          ?.off('mouse:move')
          ?.off('mouse:up')
          .on('mouse:down', (opt: fabric.IEvent<MouseEvent>) => {
            /* only start drawing on page/group */
            /* not allow drawing outside page/group OR draw while an obj in active */
            if (opt.target !== page || this.canvas.getActiveObject()) {
              this.drawCommandSubject.next(null);
              return;
            }
            isDragging = true;
            tmpBbox = new fabric.Rect({
              left: opt.absolutePointer.x, // x1,
              top: opt.absolutePointer.y, // y1,
              width: 1,
              height: 1,
              fill: 'hsla(120, 100%, 75%, 0.15)',
              stroke: command.color || BasicColor.Green,
              strokeWidth: 1,
              selectable: false,
              hoverCursor: 'default',
              originX: 'left',
              originY: 'top'
            });
            setVisiblePageBboxRects(false);
            this.canvas.add(tmpBbox);
          })
          .on('mouse:move', (opt: fabric.IEvent<MouseEvent>) => {
            if (!isDragging) return;
            /* not allow drawing when tmpBbox is not created in mouse:down OR draw while an obj in active */
            if (!tmpBbox || this.canvas.getActiveObject()) {
              this.drawCommandSubject.next(null);
              return;
            }
            const x1 = tmpBbox?.left as number,
              y1 = tmpBbox?.top as number;
            let x2 = opt.absolutePointer.x,
              y2 = opt.absolutePointer.y;

            // not allowing to draw outside the page/group, 2 is the bbox stroke width
            const minX2 = page.left + 2,
              minY2 = page.top + 2,
              maxX2 = page.left + page.getScaledWidth() - 2,
              maxY2 = page.top + page.getScaledHeight() - 2;
            if (x2 < minX2) x2 = minX2;
            else if (x2 > maxX2) x2 = maxX2;
            if (y2 < minY2) y2 = minY2;
            else if (y2 > maxY2) y2 = maxY2;

            if (x1 > x2) tmpBbox?.set({ originX: 'right' });
            else tmpBbox?.set({ originX: 'left' });

            if (y1 > y2) tmpBbox?.set({ originY: 'bottom' });
            else tmpBbox?.set({ originY: 'top' });

            const width = Math.abs(x2 - x1);
            const height = Math.abs(y2 - y1);

            tmpBbox?.set({ width: width, height: height });

            this.canvas.requestRenderAll();
          })
          .on('mouse:up', (opt: fabric.IEvent<MouseEvent>) => {
            if (
              !this.canvas.getActiveObject() &&
              tmpBbox?.width! > 10 &&
              tmpBbox?.height! > 10
            ) {
              tmpBbox.setCoords();
              const { tl, br } = tmpBbox.aCoords;
              const bbox = this.convertAbsoluteToRelativeBbox(
                /* bbox in relation to page/group, NOT canvas itself */
                [tl.x - page.left, tl.y - page.top, br.x - page.left, br.y - page.top],
                page.width,
                page.height
              );

              this.bboxChangeSubject.next({
                fieldId: command.fieldId,
                page: this.currentPageIndexSubject.value + 1,
                value: bbox
              });
            }
            this.canvas.remove(tmpBbox);
            tmpBbox = null;
            isDragging = false;
            this.drawCommandSubject.next(null);
            this.zone.run(() => {}); // manually run change detection
          });
      };
      const removeSetupDrawBboxEvent = () => {
        this.canvas?.off('mouse:down')?.off('mouse:move')?.off('mouse:up');
        const page = this.canvas.getObjects('group')[0] as fabric.Group;
        if (!page) return;
        setVisiblePageBboxRects(true);
        page.set('hoverCursor', 'default');
        /* this.zone.run(() => {
          this.isDrawingBbox = false;
        }); */
      };
      this.drawCommandSubject
        .pipe(
          takeUntil(this._cleanup),
          tap(() => this.switchCurrentMode('normal')),
          filter(() => this.currentModeSubject.value === 'normal'),
          tap((command) => {
            if (!command) return removeSetupDrawBboxEvent();
            setupDrawBboxEvent(command);
          })
          // finalize(() => console.log('cleanup draw command subject'))
        )
        .subscribe();
    });
  }

  private handleSelectedBboxSubject() {
    this.zone.runOutsideAngular(() => {
      this.selectedBboxSubject
        .pipe(
          distinctUntilChanged(),
          takeUntil(this._cleanup),
          tap((selection) => {
            // console.log(selection);
            const page = this.canvas.getObjects('group')[0] as fabric.Group;
            if (!page) return;
            page.getObjects('rect').forEach((rect) => {
              rect.set('stroke', get(rect, 'data.color', BasicColor.Green));
              rect.set('strokeWidth', 1.5);
              rect.set('fill', 'transparent');
            });
            if (selection) {
              this.editingFieldValue = selection.text;
              const rect = page
                .getObjects('rect')
                .find((rect) => rect.data['id'] === selection.fieldId);
              if (rect) rect.set('stroke', BasicColor.Yellow);
            } else this.editingFieldValue = '';
            this.canvas.requestRenderAll();
          })
          // finalize(() => console.log('cleanup selected bbox subject'))
        )
        .subscribe();
    });
  }

  private renderPage(pageIndex: number) {
    this.zone.runOutsideAngular(() => {
      const page = this.renderedPages.get(pageIndex);
      const scaleX = this.canvas.width / page.width,
        scaleY = this.canvas.height / page.height;
      // if the canvas width and height is fixed. to make the page fits inside canvas. we must choose between scaleToHeight or scaleToWidth, either one side takes up all the length and other side will follow
      // if ViewerFitPageWidth = true, the width and height of canvas are already calculated based off page width and height => scale both scaleToWidth and scaleToHeight is acceptable
      if (
        page.height * scaleX <= this.canvas.height ||
        this.featureFlags['ViewerFitPageWidth']
      )
        page.scaleToWidth(this.canvas.width); // FIXME: after scaleToWidth() operation success => the next if statemant using page.width is the new width that is already applied scaled
      if (
        page.width * scaleY <= this.canvas.width ||
        this.featureFlags['ViewerFitPageWidth']
      )
        page.scaleToHeight(this.canvas.height);
      this.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]); /* reset canvas viewport */
      this.zone.run(() => (this.zoomLevel = '100%')); // this line accidentally trigger change detection
      this.canvas.clear();
      this.canvas.add(page);
      this.featureFlags['ViewerFitPageWidth']
        ? this.canvas.centerObjectH(page)
        : this.canvas.centerObject(page);
      this.setupPageHoverCursor(page, this.currentModeSubject.value);
      page.addWithUpdate(); // manually recalculate group dimension after scaled <- TODO: find alternative
      // console.log('page', pageIndex, pick(page, ['left', 'top', 'width', 'height']));

      /* draw result bboxes if provided */
      this.featureFlags['DisplayOcrResult'] && this.renderBboxes(pageIndex, page);
      this.setTooltip(); // clear any displaying tooltip
    });
  }

  private renderBboxes(pageIndex: number, page: fabric.Group) {
    this.zone.runOutsideAngular(() => {
      for (const fieldId in this.ocrResult) {
        const field: DocumentField = this.ocrResult[fieldId];
        if (excludedKeyFields.includes(fieldId) || !field) continue;

        /* skip showing field with is_visible = false */
        if (
          this.featureFlags['EditOcrResult'] &&
          !get(field, 'config.extraConfig.is_visible', true)
        )
          continue;

        switch (field.type) {
          case 'Field': {
            if (
              /* field has no bbox to display, eg.: new field added, field bbox is cleared */
              !get(field, `bboxes.${pageIndex + 1}`) ||
              /* field bbox is already created in page */
              page.getObjects('rect').some((obj) => obj?.data['fieldId'] === fieldId)
            )
              break;
            const rectData: BboxData = {
              bboxId: fieldId,
              fieldId,
              text: get(field, 'text', null),
              bbox: get(field, `bboxes.${pageIndex + 1}`, null),
              name: get(
                field,
                'config.name',
                get(field, `config.extraConfig.name`, fieldId)
              ),
              color: get(field, `config.extraConfig.color`, BasicColor.Green)
            };

            const rect = this.createBboxRect(field.bboxes[pageIndex + 1], page, rectData);
            if (rect) {
              const rectVisible = this.drawCommandSubject?.value
                ? this.drawCommandSubject?.value?.fieldId === fieldId
                : true;
              rect.set('visible', rectVisible);
              page.addWithUpdate(rect);
            }
            break;
          }
          case 'List': {
            field.cells.forEach((cell, cellId) => {
              const bboxId = `${fieldId}.cells.${cellId}`;
              if (
                !get(cell, `bboxes.${pageIndex + 1}`) ||
                page.getObjects('rect').some((obj) => obj?.data['bboxId'] === bboxId)
              ) {
                return;
              }
              const rectData: BboxData = {
                bboxId,
                fieldId,
                valueTypeListPath: cellId,
                bbox: get(cell, `bboxes.${pageIndex + 1}`, null),
                text: get(cell, 'text', 'N/A'),
                name: get(
                  field,
                  'config.name',
                  get(field, `config.extraConfig.name`, fieldId)
                ),
                color: get(field, `config.extraConfig.color`, BasicColor.Green)
              };
              const rect = this.createBboxRect(
                cell.bboxes[pageIndex + 1],
                page,
                rectData
              );
              if (rect) {
                const rectVisible = !this.drawCommandSubject?.value;
                rect.set('visible', rectVisible);
                page.addWithUpdate(rect);
              }
            });
            break;
          }
          case 'Table': {
            /* render by rows */
            // field.rows.forEach((row, rowId) => {
            //   row.cells.forEach((cell, cellId) => {
            //     const rect = this.createBboxRect(
            //       cell.bboxes[pageIndex + 1],
            //       page,
            //       `${keyword}.${row.name}.${rowId}.${cell.name}.${cellId}`
            //     );
            //     if (rect) page.addWithUpdate(rect);
            //   });
            // });

            /* render by cols */
            field.columns.forEach((col, colId) => {
              col.cells.forEach((cell, cellId) => {
                const bboxId = `${fieldId}.columns.${colId}.cells.${cellId}`;
                if (
                  !get(cell, `bboxes.${pageIndex + 1}`) ||
                  page.getObjects('rect').some((obj) => obj?.data['bboxId'] === bboxId)
                ) {
                  return;
                }
                const rectData: BboxData = {
                  bboxId,
                  fieldId,
                  valueTypeTablePath: {
                    cellId,
                    columnId: colId
                  },
                  bbox: get(cell, `bboxes.${pageIndex + 1}`, null),
                  text: get(cell, 'text', 'N/A'),
                  name: get(
                    field,
                    'config.name',
                    get(field, `config.extraConfig.name`, fieldId)
                  ),
                  color: get(field, `config.extraConfig.color`, BasicColor.Green)
                };
                const rect = this.createBboxRect(
                  cell.bboxes[pageIndex + 1],
                  page,
                  rectData
                );
                if (rect) {
                  const rectVisible = !this.drawCommandSubject?.value;
                  rect.set('visible', rectVisible);
                  page.addWithUpdate(rect);
                }
              });
            });
            break;
          }

          default:
            break;
        }
      }
      this.canvas.renderAll();
    });
  }

  private createBboxRect(relativeBbox, pageObj: fabric.Group, bboxData: BboxData) {
    if (!relativeBbox || !relativeBbox.length) return;

    const [x1, y1, width, height] = this.getDrawingBoundingBox(
      relativeBbox,
      pageObj.width,
      pageObj.height
    );

    const strokeColor = bboxData?.color || BasicColor.Green;
    bboxData['color'] = strokeColor;

    const rect = new fabric.Rect({
      left: pageObj.left + x1,
      top: pageObj.top + y1,
      width: width,
      height: height,
      stroke: strokeColor,
      strokeWidth: 1.5,
      fill: 'transparent',
      selectable: false,
      hoverCursor: 'default',
      originX: 'left',
      originY: 'top',
      data: bboxData
    });
    this.registerBboxEvents(rect);

    return rect;
  }

  private registerBboxEvents(rect: fabric.Rect) {
    const bboxData = rect.data;
    rect
      .off('mouseover')
      .off('mousemove')
      .off('mouseout')
      .off('mousedblclick')
      .on('mouseover', () => {
        if (bboxData.id === this.selectedBboxSubject.value?.fieldId) return;
        rect.set('stroke', BasicColor.Red);
        rect.set('strokeWidth', 2);
        rect.set('fill', 'hsla(120, 100%, 75%, 0.15)');
        this.canvas.requestRenderAll();
      })
      .on('mousemove', (evt) => {
        this.setTooltip({
          padding: 4,
          left: evt.pointer.x,
          top: evt.pointer.y,
          text: bboxData.name
        });
      })
      .on('mouseout', () => {
        this.setTooltip();
        if (bboxData.id === this.selectedBboxSubject.value?.fieldId) return;
        rect.set('stroke', get(bboxData, 'color', BasicColor.Green));
        rect.set('strokeWidth', 1.5);
        rect.set('fill', 'transparent');
        this.canvas.requestRenderAll();
      })
      .on('mousedblclick', (evt) => {
        /* note: target refers to page(group), subtarget is actual rect */
        /* show modal/popover for editing at given evt.pointer */

        /* kinda workaround */
        /* set top left of anchor to be near the pointer click (rect) on canvas */
        this.popoverAnchor.nativeElement.style['left'] = `${evt.pointer.x}px`;
        this.popoverAnchor.nativeElement.style['top'] = `${evt.pointer.y}px`;
        /* manually dispatch click event so for popover to be display */
        this.popoverAnchor.nativeElement.dispatchEvent(
          new Event('click', { bubbles: true })
        );

        this.selectedBboxSubject.next(bboxData);
      });
  }

  private setTooltip(tooltipOptions?: {
    text: string;
    left: number;
    top: number;
    padding?: number;
  }) {
    if (!tooltipOptions) {
      this.tooltipAnchor.nativeElement.style['padding'] = '0';
      this.tooltipAnchor.nativeElement.style['left'] = '0px';
      this.tooltipAnchor.nativeElement.style['top'] = '0px';
      this.tooltipAnchor.nativeElement.innerHTML = '';
    } else {
      this.tooltipAnchor.nativeElement.style['padding'] =
        `${tooltipOptions.padding && 4}px`;
      this.tooltipAnchor.nativeElement.style['left'] = `${tooltipOptions.left + 20}px`;
      this.tooltipAnchor.nativeElement.style['top'] = `${tooltipOptions.top + 20}px`;
      this.tooltipAnchor.nativeElement.innerHTML = tooltipOptions.text;
    }
  }

  private blobToBase64(blob) {
    return new Promise((resolve, _) => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onloadend = () => resolve(reader.result);
    });
  }

  async nextPage() {
    if (this.loadingSubject.value) return;
    let currentPageIndex = this.currentPageIndexSubject.value;
    if (
      this.featureFlags['OnlyDisplaySpecifiedPages'] === false ||
      !this.specifiedPages?.length
    ) {
      if (currentPageIndex < this.numPages - 1) currentPageIndex++;
      else currentPageIndex = 0;
    } else {
      if (!this.specifiedPages.includes(currentPageIndex))
        currentPageIndex = this.specifiedPages[0];
      else {
        const foundIndex = this.specifiedPages.findIndex((i) => i === currentPageIndex);
        if (foundIndex === this.specifiedPages.length - 1)
          currentPageIndex = this.specifiedPages[0];
        else currentPageIndex = this.specifiedPages[foundIndex + 1];
      }
    }
    this.goToPage(currentPageIndex);
  }

  async prevPage() {
    if (this.loadingSubject.value) return;
    let currentPageIndex = this.currentPageIndexSubject.value;
    if (
      this.featureFlags['OnlyDisplaySpecifiedPages'] === false ||
      !this.specifiedPages?.length
    ) {
      if (currentPageIndex === 0) currentPageIndex = this.numPages - 1;
      else currentPageIndex--;
    } else {
      if (!this.specifiedPages.includes(currentPageIndex))
        currentPageIndex = this.specifiedPages[0];
      else {
        const foundIndex = this.specifiedPages.findIndex((i) => i === currentPageIndex);
        if (foundIndex === 0)
          currentPageIndex = this.specifiedPages[this.specifiedPages.length - 1];
        else currentPageIndex = this.specifiedPages[foundIndex - 1];
      }
    }
    this.goToPage(currentPageIndex);
  }

  async goToPage(pageIndex: number) {
    if (this.loadingSubject.value) return;
    if (pageIndex < 0 || pageIndex >= this.numPages)
      throw new Error('Attempt to access page out of bound');
    this.pdfDocument && (await this.loadPagesFromPdfDocument([pageIndex]));
    this.currentPageIndexSubject.next(pageIndex);
  }

  switchCurrentMode(forceMode?: 'normal' | 'zoom') {
    /* either given force mode or the opposite of current mode value */
    const currentMode = forceMode
      ? forceMode
      : this.currentModeSubject.value === 'normal'
        ? 'zoom'
        : 'normal';
    this.currentModeSubject.next(currentMode);
  }

  showInfoModal() {
    this.modalService.create({
      nzContent: this.infoModal,
      nzClosable: false,
      nzFooter: null,
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  showImageEditorModal() {
    if (!this.featureFlags['EditPageImage']) return;
    if (this.loadingSubject.value) return;
    const originalPageImageLink = get(
      this.renderedPages.get(this.currentPageIndexSubject.value),
      'data.originalPageImageLink'
    );
    const pageImageLink = get(
      this.renderedPages.get(this.currentPageIndexSubject.value),
      'data.pageImageLink'
    );

    if (!pageImageLink || !originalPageImageLink) return;

    const pageImage = this.renderedPages
      .get(this.currentPageIndexSubject.value)
      .getObjects('image')[0];
    if (!pageImage) return;

    const editorRef = this.modalService.create<any, any, { editedPageImageLink: string }>(
      {
        nzContent: ImageEditorComponent,
        nzClosable: false,
        nzMaskClosable: false,
        nzFooter: null,
        nzCentered: true,
        nzClassName: 'custom-ant-modal-common-styles',
        nzBodyStyle: { padding: '0px' },
        nzStyle: { width: 'fit-content' },
        nzData: {
          imgTitle: `trang ${this.currentPageIndexSubject.value + 1} - ${this.fileName}`,
          imgSrc: pageImageLink
        }
      }
    );
    editorRef.afterClose
      .asObservable()
      .pipe(
        take(1),
        filter((result) => !!result),
        tap(() => this.loadingSubject.next(true)),
        switchMap(({ editedPageImageLink }) =>
          from(
            (async (editedPageImageLink) => {
              let image: fabric.Image;
              if (!image && editedPageImageLink)
                image = await fabric.Image['fromURLAsync'](editedPageImageLink, {
                  originX: 'left',
                  originY: 'top'
                });

              if (!image) throw new Error('image-editor does not return valid result');
              /* replace current page with edited page */
              const currentPageIndex = this.currentPageIndexSubject.value;
              const oldPage = this.renderedPages.get(currentPageIndex);
              const updatedPage = new fabric.Group([image], {
                data: { ...oldPage.get('data'), pageImageLink: editedPageImageLink },
                ...this.pageProps
              });
              this.renderedPages.set(currentPageIndex, updatedPage);
              this.renderPage(currentPageIndex);
              this.onEditPageImage.emit({
                pageIndex: currentPageIndex,
                editedPageImageLink
              });
            })(editedPageImageLink)
          )
        ),
        tap(() => this.loadingSubject.next(false))
      )
      .subscribe();
  }

  showDocumentCompareModal() {
    if (!this.featureFlags['CompareDocument']) return;
    if (this.loadingSubject.value) return;
    if (!this.fileLinkToCompareWith) return;
    const modalRef = this.modalService.create({
      nzContent: this.documentCompareModal,
      nzCentered: true,
      nzClosable: false,
      nzMaskClosable: false,
      nzFooter: null,
      nzWidth: 1200,
      nzBodyStyle: { padding: '0' },
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  closeAllModal() {
    this.modalService.closeAll();
  }

  private getDrawingBoundingBox(
    relativeBboxes = [0, 0, 0, 0],
    canvasWidth = 0,
    canvasHeight = 0
  ) {
    const [x1, y1, x2, y2] = relativeBboxes;
    return [
      x1 * canvasWidth,
      y1 * canvasHeight,
      (x2 - x1) * canvasWidth,
      (y2 - y1) * canvasHeight
    ];
  }

  /* drawBbox(drawing) {
    if (this.currentModeSubject.value === 'zoom') return;
    if (drawing) {
      const randomColor = BasicColor.Green; // this.generateRandomColor();
      this.drawCommandSubject.next({
        color: randomColor,
        fieldName: randomColor
      });
    } else this.drawCommandSubject.next(null);
  } */

  private generateRandomColor(fixed?: BasicColor) {
    if (fixed) return fixed;
    return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
  }

  private convertAbsoluteToRelativeBbox(
    absoluteBbox: [number, number, number, number],
    canvasWidth: number,
    canvasHeight: number
  ): [number, number, number, number] {
    const x1 = absoluteBbox[0] / canvasWidth;
    const y1 = absoluteBbox[1] / canvasHeight;
    const x2 = absoluteBbox[2] / canvasWidth;
    const y2 = absoluteBbox[3] / canvasHeight;
    return [x1, y1, x2, y2];
  }

  handleEditFieldPopoverVisibleChange(visible) {
    if (!visible) this.selectedBboxSubject.next(null);
  }

  handleEditFieldPopoverOk() {
    const fieldOcrResultChange: FieldOcrResultChange = {
      text: this.editingFieldValue,
      ...pick(this.selectedBboxSubject.value, [
        'fieldId',
        'valueTypeListPath',
        'valueTypeTablePath'
      ])
    };

    this.editOcrResultSubject.next(fieldOcrResultChange);
    this.selectedBboxSubject.next(null);
  }

  handleEditFieldPopoverCancel() {
    this.handleEditFieldPopoverVisibleChange(false);
  }

  private checkFileLinkIsReallyChanged(fileLinkOld: string, fileLinkNew: string) {
    /* presigned fileLink is changed on every time call getDocumentDetail() => must compare URL(fileLink).pathname to solve issue of redundantly re-fetching the same file */
    if (typeof fileLinkOld === 'string' && typeof fileLinkNew === 'string') {
      if (fileLinkOld === fileLinkNew) return false;
      try {
        const oldPathname = new URL(fileLinkOld).pathname;
        const newPathname = new URL(fileLinkNew).pathname;
        // console.log(
        //   { oldPathname, newPathname },
        //   { changed: oldPathname !== newPathname }
        // );
        return oldPathname !== newPathname;
      } catch (error) {
        return true;
      }
    }
    return true;
  }

  private resetOnFileLinkChanged() {
    /* close all current subscriptions, new subscriptions will be re-setup */
    this._cleanup.next();
    /* clear current canvas */
    if (this.canvas) {
      this.canvas.clear().dispose();
      this.canvas = null;
    }
    /* reset list of rendered page from previous pdf/image */
    this.renderedPages.clear();
    /* release resource allocated to current pdfDocument */
    this.pdfDocument?.cleanup();
    this.pdfDocument?.destroy();
    this.pdfDocument = null;
    /* reset current page index, once handleFetchedFile is executed, the viewer starts rendering at page 0 */
    this.currentPageIndexSubject.next(0);
    /* numPages will be set once handleFetchedFile is executed, meanwhile numPages is set to 0 */
    this.numPages = 0;
    /* reset specifiedPages, cannot assume new file from fileLink will work with current specifiedPages */
    this.featureFlags['OnlyDisplaySpecifiedPages'] && (this.specifiedPages = null);
  }

  private async loadPagesFromPdfDocument(pageIndexList: number[]) {
    /* return if all pages are loaded already */
    if (this.renderedPages.size === this.numPages) return;

    const renderedPageIndexList = Array.from(this.renderedPages.keys());
    pageIndexList = pageIndexList.filter(
      (pageIndex) => !renderedPageIndexList.includes(pageIndex)
    );

    const canvasFactory = new NodeCanvasFactory(); // create each canvas and its context for every page

    this.loadingSubject.next(true);
    for (const pageIndex of pageIndexList) {
      const page = await this.pdfDocument.getPage(pageIndex + 1);
      const viewport = page.getViewport({ scale: 1.0 });

      const renderScale = 3;

      const canvasAndContext = canvasFactory.create(
        viewport.width * renderScale,
        viewport.height * renderScale
      );
      const renderContext = {
        canvasContext: canvasAndContext.context,
        viewport: viewport.clone({ scale: renderScale })
      };
      const renderTask = page.render(renderContext as any);
      await renderTask.promise;

      const pageImageDataURL = canvasAndContext.canvas.toDataURL('image/png');
      const image = await fabric.Image['fromURLAsync'](pageImageDataURL, {
        originX: 'left',
        originY: 'top'
      });
      const pageImageLink = URL.createObjectURL(
        this.utils.createBlobFromDataUrl(pageImageDataURL, 'image/png')
      );
      this.renderedPages.set(
        pageIndex,
        new fabric.Group([image], {
          data: {
            id: pageIndex,
            originalPageImageLink: pageImageLink,
            pageImageLink
          },
          ...this.pageProps
        })
      );
      // Release page resources.
      canvasFactory.destroy(canvasAndContext);
      page.cleanup();
    }
    this.loadingSubject.next(false);

    if (this.renderedPages.size === this.numPages) {
      // release resource once all pages are loaded, reduce memory consumption
      this.pdfDocument?.cleanup();
      this.pdfDocument?.destroy();
      this.pdfDocument = null;
    }
  }

  private reInitCanvasIfNecessary() {
    if (!this.featureFlags['ViewerFitPageWidth']) return;
    const page = this.renderedPages.get(this.currentPageIndexSubject.value);
    const calcuatedNextCanvasHeight = this.canvas.width * (page.height / page.width);

    // must use Math.floor because ratio as a float would produced slightly different answer each calculation
    if (Math.floor(calcuatedNextCanvasHeight) !== Math.floor(this.canvas.height))
      this.initCanvas();
  }

  ngOnDestroy(): void {
    this.loadingSubject.complete();
    this.currentPageIndexSubject.complete();
    this.currentModeSubject.complete();
    this.drawCommandSubject?.complete();
    this.selectedBboxSubject.complete();
    this.editOcrResultSubject.complete();
    this._cleanup.next();
    this._cleanup.complete();
    this.canvas?.dispose();
    this.resizeObserver?.disconnect();
    this.pdfDocument?.cleanup();
    this.pdfDocument?.destroy();
  }
}
