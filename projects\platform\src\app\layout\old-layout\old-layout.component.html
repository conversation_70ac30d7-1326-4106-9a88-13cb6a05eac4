<!-- <main class="h-100">
  <nav class="navbar navbar-expand-lg position-fixed w-100">
    <div class="container-fluid">
      <a class="navbar-brand rpa-cus-logo" href="#">
        <img src="assets/img/rpa/logo_ngang.svg" alt="">
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarTogglerDemo02"
        aria-controls="navbarTogglerDemo02" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarTogglerDemo02">
        <div class="d-flex align-items-center ms-auto">
          <div class="d-flex align-items-center">
            <img src="assets/img/home.svg" class="me-2" alt="">
            <span>Introduction RPA</span>
            <img src="assets/img/line.svg" class="ms-3" alt="">
          </div>
          <div class="ms-3">
            <div class="dropdown">
              <a class="dropdown-toggle text-decoration-none text-dark" href="#" role="button" id="dropdownMenuLink"
                data-bs-toggle="dropdown" aria-expanded="false">
                {{userInfo.name}}
              </a>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
                <li><p class="dropdown-item">Information</p></li>
                <li><p class="dropdown-item" (click)="logout()">Logout</p></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
  <div class="container-fluid h-100 pt-57">
    <div class="row flex-nowrap h-100">
      <div class="col-auto col-md-3 col-xl-2 px-0 menu-left">
        <div class="d-flex flex-column align-items-center align-items-sm-start pt-2 text-white">
          <ul class="nav nav-pills flex-column mb-sm-auto mb-0 align-items-center align-items-sm-start w-100" id="menu">
            <li *ngFor="let menu of menus" routerLinkActive="router-active">
              <div class="border position-absolute"></div>
              <a [routerLink]="menu.path" class="nav-link align-middle px-3 text-center">
                <img width="24" height="24" [src]="router.isActive(menu.path, false) ? menu.iconActive : menu.icon" alt="">
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div class="col bg-content overflow-auto">
        <router-outlet></router-outlet>
      </div>
    </div>
  </div>
</main> -->


<body class="sidebar-mini layout-navbar-fixed" id="mainAdminLte">
  <div class="wrapper">
    <nav class="main-header navbar navbar-expand border-bottom menu-bar">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" (click)="toggleSidebar()">
            <i style="color: #0f67ce; cursor: pointer;" class="fa fa-bars" aria-hidden="true"></i>
          </a>
        </li>
      </ul>
      <ul class="navbar-nav ml-auto">
        <li class="nav-item">
          <div class="d-flex align-items-center" style="gap: 1rem">
            <a target="_blank" href="/demo/ocr-experience"
              style="text-decoration: none; display: flex; align-items: center; justify-content: center; gap: 4px; width: 150px; height: 36px; border-radius: 4px; background: rgba(15, 103, 206, 0.10); font-weight: 600; color: #0F67CE">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M10.3866 7.99995C10.3866 9.31995 9.31995 10.3866 7.99995 10.3866C6.67995 10.3866 5.61328 9.31995 5.61328 7.99995C5.61328 6.67995 6.67995 5.61328 7.99995 5.61328C9.31995 5.61328 10.3866 6.67995 10.3866 7.99995Z"
                  stroke="#0F67CE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                <path
                  d="M7.9999 13.5138C10.3532 13.5138 12.5466 12.1271 14.0732 9.72714C14.6732 8.78714 14.6732 7.20714 14.0732 6.26714C12.5466 3.86714 10.3532 2.48047 7.9999 2.48047C5.64656 2.48047 3.45323 3.86714 1.92656 6.26714C1.32656 7.20714 1.32656 8.78714 1.92656 9.72714C3.45323 12.1271 5.64656 13.5138 7.9999 13.5138Z"
                  stroke="#0F67CE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              Demo Preview
            </a>
            <a [href]="urlAdmin" class="cursor-pointer text-decoration-none text-dark">
              <img src="assets/img/home.svg" alt="">
            </a>
            <img src="assets/img/line.svg" alt="">
          </div>
        </li>
        <li class="collapse navbar-collapse" id="navbarTogglerDemo02">
          <div class="ml-3">
            <div class="dropdown">
              <a class="dropdown-toggle text-decoration-none text-dark" href="#" role="button" id="dropdownMenuLink"
                data-bs-toggle="dropdown" aria-expanded="false">
                {{userInfo.username}}
              </a>
              <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink">
                <li><a class="dropdown-item cursor-pointer" href="#" (click)="showUserInfo($event)">Thông tin tài
                    khoản</a></li>
                <li><a class="dropdown-item cursor-pointer" href="#" (click)="showChangePassword($event)">Đổi mật
                    khẩu</a></li>
                <li><a class="dropdown-item cursor-pointer" href="#" (click)="showRevokeConsentModal($event)">
                    Hạn chế, phản đối, rút lại sự đồng ý xử lý dữ liệu</a></li>
                <li><a class="dropdown-item cursor-pointer" href="#" (click)="showRequestRemoveDataModal($event)">
                    Yêu cầu xóa dữ liệu cá nhân</a></li>
                <li><a class="dropdown-item cursor-pointer" href="#" (click)="logout($event)">Thoát</a></li>
              </ul>
            </div>
          </div>
        </li>
      </ul>
    </nav>
    <aside class="main-sidebar"
      style="box-shadow: inset -1px 0px 0px #E2E2EA; background-color: #fff; position: fixed; margin-top: 3.5rem">
      <a class="brand-link border-bottom d-flex align-items-center"
        style=" height: 50px; box-shadow: inset 0px -1px 0px #e2e2ea; background-color: #fff;" href="#">
        <img src="assets/img/rpa/vnpt-smart-reader-logo.svg" alt="" class="brand-image-xs logo-xl"
          style="background-color: #fff; left: 22px; top: 10px">
        <img src="assets/img/rpa/vnpt-smart-reader-collapse-logo.svg" alt="" class="brand-image-xl logo-xs"
          style="height:33px; background-color: #fff; top: 9px; left: 22px">
      </a>
      <div class="sidebar" style="padding: 0 1px 0 0">
        <nav class="mt-2" style="box-shadow: none;">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false"
            data-toggle="push-menu">
            <li *ngFor="let menu of menus" routerLinkActive="router-active" class="nav-item menu-item py-4">
              <a [routerLink]="menu.path" class="nav-link px-4"
                style="border-radius: 0; padding-left: calc(1.5rem - 4px) !important;"
                [ngStyle]="{ 'border-left': router.isActive(menu.path, false) ? '3px solid #0f67ce': '3px solid transparent' }">
                <img style="width: 24px; height: 24px;"
                  [src]="router.isActive(menu.path, false) ? menu.iconActive : menu.icon" alt="">
                <p class="ml-3 flex-grow-1" [style]="router.isActive(menu.path, false) ? 'color: #0d6efd' : ''">
                  {{menu.name}}
                </p>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>
    <div class="content-wrapper bg-content"
      style="min-width: fit-content; min-height: 100vh; margin-top: 0; padding-top: calc(3.5rem + 1px)">
      <router-outlet></router-outlet>
    </div>
  </div>
</body>