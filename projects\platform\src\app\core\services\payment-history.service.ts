import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import UtilsService from '@platform/app/core/services/utils.service';
import { environment } from '@platform/environment/environment';

@Injectable({
  providedIn: 'root'
})
export class PaymentHistoryService {
  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  fetchPaymentHistory(params: {
    orderStatus: number;
    startDate: string;
    endDate: string;
    page: number;
    sort: 'ASC' | 'DESC';
    propertiesSort: 'orderDate' | string;
    maxSize: number;
  }) {
    return this.http.get<{
      object: {
        data: any[];
        maxSize: number;
        page: number;
        propertiesSort: string;
        sort: string;
        totalElement: number;
        totalPages: number;
      };
    }>(environment.backendUrl + 'idg-api/payment-history', {
      headers: this.utilsService.headers,
      params: {
        ...params,
        channelCode: 'SMART_RPA'
      }
    });
  }
}
