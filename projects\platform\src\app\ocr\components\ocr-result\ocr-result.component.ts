import { Component, Input } from '@angular/core';
import { OcrResult } from '@platform/app/core/services/export.service.type';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize, tap } from 'rxjs';
import { ajax } from 'rxjs/ajax';

@Component({
  selector: 'app-ocr-result',
  templateUrl: './ocr-result.component.html',
  styleUrls: ['./ocr-result.component.scss']
})
export class OcrResultComponent {
  ocrResult: OcrResult['object'];
  @Input()
  set exportedInJSONLink(link) {
    this.loading = true;
    this.ocrResult = null;
    ajax<OcrResult>({ url: link, responseType: 'json' })
      .pipe(
        tap((resp) => (this.ocrResult = resp.response.object)),
        finalize(() => (this.loading = false))
      )
      .subscribe();
  }

  file: File;
  @Input()
  set fileLink(link) {
    this.loading = true;
    this.file = null;
    ajax<Blob>({ url: link, responseType: 'blob' })
      .pipe(
        tap((resp) => {
          this.file = new File(
            [resp.response],
            this.fileName || 'document-to-be-reproduced',
            { type: resp.response.type }
          );
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe();
  }

  @Input()
  fileName;

  numPages = 0;
  currentPage = 1;
  isShowingPhraseBbox = false;
  isShowingLineBbox = false;
  isShowingParagraphBbox = true;

  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('ocr-result-viewer');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('ocr-result-viewer');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }

  constructor(private spinner: NgxSpinnerService) {}

  nextPage() {
    if (this.numPages === 0) return; // document-reproduction not initialized yet
    if (this.currentPage >= this.numPages) this.currentPage = 1;
    else this.currentPage++;
  }

  prevPage() {
    if (this.numPages === 0) return; // document-reproduction not initialized yet
    if (this.currentPage <= 1) this.currentPage = this.numPages;
    else this.currentPage--;
  }
}
