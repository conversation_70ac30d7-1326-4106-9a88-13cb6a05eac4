import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { OcrService } from '@platform/app/core/services/ocr.service';
import { isFinite } from 'lodash';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, Subject, takeUntil, tap } from 'rxjs';

@Component({
  selector: 'app-ocr-in-progress',
  templateUrl: './ocr-in-progress.component.html',
  styleUrls: ['./ocr-in-progress.component.scss']
})
export class OcrInProgressComponent implements OnInit, OnDestroy {
  static readonly confirmLeaveMessage;

  /* feature flags */
  static documentViewerFF = {
    DisplayOcrResult: false,
    ViewerFitPageWidth: false,
    EditOcrResult: false,
    Zooming: true,
    PdfLazyLoading: true,
    OnlyDisplaySpecifiedPages: false
  };

  @Output() fileChange = new EventEmitter<{
    fileId: string;
    fileLink: string;
    fileName: string;
  }>();

  fileList: {
    id: string;
    sessionId: string;
    name: string;
    file?: File;
    link: string;
    lastUpdated: Date;

    errorAttemptList?: any[];
    progress?: {
      processedPages: number;
      remainingPages: number;
      warningMessages: string[];
      warnings: string[];
    };
  }[] = [];
  activeFile: (typeof this.fileList)[number];
  MaxErrorAttemptCount = this.ocrService.MaxErrorAttemptCount;
  setOfCheckedId = new Set<string>();
  destroy$ = new Subject<void>();

  constructor(
    private ocrService: OcrService,
    private spinner: NgxSpinnerService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.ocrService.inProgressFileList$
      .pipe(
        takeUntil(this.destroy$),
        tap((inProgressFileList) => {
          this.fileList = inProgressFileList;
          Array.from(this.setOfCheckedId)
            .filter(
              (checkedFileId) =>
                this.fileList.findIndex((file) => checkedFileId === file.id) === -1
            )
            .forEach((fileId) => this.onItemChecked(fileId, false));
        })
      )
      .subscribe();
  }

  selectFile(file?: (typeof this.fileList)[number]) {
    if (!file) this.fileChange.emit(null);
    this.fileChange.emit({
      fileId: file.id,
      fileLink: file.link,
      fileName: file.name
    });
    this.activeFile = file;
  }

  onItemChecked(id: string, checked: boolean) {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  uncheckAll() {
    this.setOfCheckedId.clear();
  }

  /* cancelScanTableAsync may fail, resulting in the in-progress file in cache is NOT removed from cache */
  cancelInProgressFile(file: (typeof this.fileList)[number]) {
    this.ocrService
      .cancelScanTableAsyncAndDeleteInProgressFilesFromCache([file])
      .subscribe();
  }

  /* cancelScanTableAsync may fail, but the in-progress file in cache is removed from cache regardless */
  deleteInProgressFile(file: (typeof this.fileList)[number]) {
    this.ocrService
      .cancelScanTableAsyncAndDeleteInProgressFilesFromCache([file], true)
      .subscribe({ complete: () => this.onItemChecked(file.id, false) });
  }

  /* cancelScanTableAsync may fail, but the in-progress file in cache is removed from cache regardless */
  deleteSelectedFiles() {
    this.ocrService
      .cancelScanTableAsyncAndDeleteInProgressFilesFromCache(
        this.fileList.filter((file) => Array.from(this.setOfCheckedId).includes(file.id)),
        true
      )
      .subscribe({ complete: () => this.uncheckAll() });
  }

  getFileProgressText(file: (typeof this.fileList)[number]) {
    if (file.errorAttemptList)
      return file.errorAttemptList.length > this.MaxErrorAttemptCount
        ? 'Lỗi File'
        : file.errorAttemptList.length === 0
          ? `Bắt đầu thử lại...`
          : `Lỗi File (đang thử lại ${file.errorAttemptList.length}/${this.MaxErrorAttemptCount})`;
    if (
      !file.progress ||
      !isFinite(file.progress.processedPages) ||
      !isFinite(file.progress.remainingPages)
    )
      return 'N/A';
    return `${file.progress.processedPages}/${file.progress.processedPages + file.progress.remainingPages}`;
  }

  getFileProgressPercent(file: (typeof this.fileList)[number]) {
    const percent =
      Math.ceil(
        (file.progress.processedPages /
          (file.progress.processedPages + file.progress.remainingPages)) *
          100
      ) || 0;
    return percent;
  }

  retryFailedFile(file: (typeof this.fileList)[number]) {
    this.ocrService.resetErrorAttemptList([file.id]);
  }

  retrySelectedFailedFiles() {
    this.ocrService.resetErrorAttemptList(Array.from(this.setOfCheckedId));
  }

  getFileStatus(file: (typeof this.fileList)[number]): 'failed' | 'in-progress' | null {
    if (file.errorAttemptList) return 'failed';
    else if (file.progress) return 'in-progress';
    return null;
  }

  ngOnDestroy(): void {
    // console.log('ocr-in-progress destroyed');
    this.destroy$.next();
    this.destroy$.complete();
  }
}
