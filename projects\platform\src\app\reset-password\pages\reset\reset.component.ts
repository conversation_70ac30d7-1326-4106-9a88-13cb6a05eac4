import { Component, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { get, isEmpty } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { ResetPasswordService } from '@platform/app/core/services/reset-password.service';
import { environment } from '@platform/environment/environment';

@Component({
  selector: 'app-reset',
  templateUrl: './reset.component.html',
  styleUrls: ['../../reset-password.component.scss'],
})
export class ResetComponent implements OnInit {
  readonly landingPageUrl = environment.landingPageUrl;
  form: UntypedFormGroup;
  token;
  email;
  passwordType: 'password' | 'text' = 'password';
  passwordConfirmType: 'password' | 'text' = 'password';

  constructor(
    private resetPasswordService: ResetPasswordService,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService
  ) {
    this.setupForm();
  }

  ngOnInit(): void {
    this.token = this.route.snapshot.queryParams['token'] || '';
    this.email = this.route.snapshot.queryParams['email'] || '';
    if (!this.token || !this.email) {
      this.router.navigate(['login']);
      return;
    }
    this.resetPasswordService
      .checkExpireToken(this.email, this.token)
      .subscribe({
        error: () => {
          this.toastr.error(
            'Link đổi mật khẩu đã hết hạn, vui lòng gửi lại yêu cầu đổi mật khẩu!'
          );
          return this.router.navigate(['reset-password/forgot']);
        },
      });
  }

  private setupForm() {
    this.form = new UntypedFormGroup(
      {
        password: new UntypedFormControl(null, [
          Validators.required,
          Validators.minLength(8),
          Validators.pattern(
            /^.*(?=.{3,})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[\d\x])(?=.*[!@$#%]).*$/
          ),
        ]),
        passwordConfirm: new UntypedFormControl(null, [Validators.required]),
      },
      [
        (formGroup: UntypedFormGroup) => {
          const newPass = formGroup.get('password');
          const verifiedPass = formGroup.get('passwordConfirm');

          const error = {
            ...verifiedPass.errors,
          };
          if (newPass.value !== verifiedPass.value) {
            error['passwordConfirmFailed'] = true;
            verifiedPass.setErrors(error);
          }

          return error;
        },
      ]
    );
  }

  handleSubmit() {
    if (this.form.invalid) {
      for (const field in this.form.controls) {
        this.form.controls[field].markAsDirty({ onlySelf: true });
        this.form.controls[field].updateValueAndValidity();
      }
      return;
    }
    this.resetPasswordService
      .confirmForgotPassword(this.email, this.token, {
        ...this.form.value,
        username: this.email,
      })
      .subscribe({
        next: () => {
          this.toastr.success('Đổi mật khẩu thành công');
          return this.router.navigate(['login']);
        },
        error: () => {
          return this.toastr.error('Đổi mật khẩu thất bại');
        },
      });
  }

  showHidePassword(item: 'password' | 'confirm') {
    if (item === 'confirm') {
      if (this.passwordConfirmType === 'password')
        this.passwordConfirmType = 'text';
      else this.passwordConfirmType = 'password';
    } else {
      if (this.passwordType === 'password') this.passwordType = 'text';
      else this.passwordType = 'password';
    }
  }
}
