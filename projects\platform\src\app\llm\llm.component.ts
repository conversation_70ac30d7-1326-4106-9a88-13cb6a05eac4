import { Component } from '@angular/core';
import { FormB<PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { LLMService } from '@platform/app/core/services/llm.service';
import { OcrExperienceService } from '@platform/app/core/services/ocr-experience.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { get } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { catchError, delay, EMPTY, of, switchMap, tap, timer } from 'rxjs';

@Component({
  selector: 'app-llm',
  templateUrl: './llm.component.html',
  styleUrls: ['./llm.component.scss']
})
export class LLMComponent {
  readonly RULE_ACCEPT = {
    multiple: false,
    mimetypes: ['application/pdf', 'image/jpeg', 'image/png'],
    accept: '.jpeg, .jpg, .png, .pdf',
    typeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    extensions: ['pdf', 'jpeg', 'jpg', 'png'],
    size: 10 * 1024 * 1024, // 10MB
    getSizeStr() {
      return this.size / (1024 * 1024) + 'MB';
    }
  };

  convertBytesToMB = this.utilsService.convertBytesToMB;
  selectedFile: File;
  selectedFileLink: string;
  selectedFileHash: string;
  selectedFileType: string;
  form: FormGroup<{ prompt: FormControl<string> }>;
  readonly maxPromptCount = 2000;
  answer;
  loading = false;
  promptHistory: {
    prompt: string;
    answer: string;
    timestamp: Date;
  }[] = [];

  constructor(
    private toastrService: ToastrService,
    private utilsService: UtilsService,
    private ocrExperienceService: OcrExperienceService,
    private fb: FormBuilder,
    private llmService: LLMService
  ) {
    this.form = fb.group({
      prompt: fb.control(null, {
        validators: [
          Validators.required,
          Validators.minLength(1),
          Validators.maxLength(this.maxPromptCount)
        ]
      })
    });
    this.form.disable();
  }

  async handleFileInputChange(e, inputFileElem: HTMLInputElement) {
    /* TODO: each time use handleFileInputChange, only allow n files, prevent user add too many files at once, potential performance issue */
    let files: File[] = Array.from(get(e, 'target.files', []));
    if (files.length !== 1) return;
    const selectedFile = files[0];

    // Extracting the file extension from the file name
    const extension = selectedFile.name.split('.').pop().toLowerCase();

    // Check if the MIME type is in the accepted list and the file extension is in the accepted list
    if (
      !this.RULE_ACCEPT.mimetypes.includes(selectedFile.type) ||
      !this.RULE_ACCEPT.extensions.includes(extension)
    ) {
      this.toastrService.error(
        `File ${selectedFile.name} không đúng định dạng cho phép (Hỗ trợ file ${this.RULE_ACCEPT.extensions.join(', ')})`
      );
      return;
    }

    // Check file size
    if (selectedFile.size > this.RULE_ACCEPT.size) {
      this.toastrService.error(
        `File ${selectedFile.name} vượt quá dung lượng cho phép (Dung lượng tối đa cho phép ${this.RULE_ACCEPT.getSizeStr()})`
      );
      return;
    }

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('title', selectedFile.name);
    formData.append('description', 'add file for demo LLM from platform');

    this.selectedFileLink = URL.createObjectURL(selectedFile);
    this.selectedFile = selectedFile;
    // reset input file element
    if (inputFileElem) inputFileElem.value = null;

    this.ocrExperienceService
      .addFile({ body: formData, isLandingPageMode: false })
      .pipe(
        tap((resp) => {
          this.selectedFileHash = resp.object['hash'];
          this.selectedFileType = resp.object['fileType'];
          this.form.enable();
        }),
        catchError((err) => {
          this.toastrService.error('Đã có lỗi xảy ra, vui lòng thử lại!');
          this.removeFile();
          return EMPTY;
        })
      )
      .subscribe();
  }

  extractAnswer() {
    if (!this.selectedFileType || !this.selectedFileHash)
      return this.toastrService.error('Không có tài liệu để xử lý');
    if (
      this.form.controls.prompt.getError(Validators.required.name.toLowerCase()) ||
      !this.form.value.prompt?.trim()
    )
      return this.toastrService.error('Mô tả prompt không được để trống');
    if (this.form.controls.prompt.getError(Validators.maxLength.name.toLowerCase()))
      // maxLength to maxlength. see issue https://github.com/angular/angular/issues/7407
      return this.toastrService.error(
        `Mô tả prompt không được vượt quá ${this.maxPromptCount} ký tự`
      );

    of(1)
      .pipe(
        tap(() => {
          this.answer = null;
          this.loading = true;
        }),
        delay(400), // show fake loading for 400ms first
        switchMap(() =>
          this.llmService.llmExtraction({
            details: false,
            file_hash: this.selectedFileHash,
            file_type: this.selectedFileType,
            prompt: this.form.value.prompt
          })
        ),
        delay(400), // keep show fake loading for 400ms
        tap(({ object }) => {
          this.loading = false;
          this.answer = object.result;
        }),
        catchError((err) => {
          this.toastrService.error('Đã có lỗi xảy ra, vui lòng thử lại!');
          this.answer = null;
          this.loading = false;
          return EMPTY;
        })
      )
      .subscribe();
  }

  removeFile() {
    this.clearPromptAndAnswer();
    this.form.disable();
    this.selectedFileLink = null;
    this.selectedFile = null;
    this.selectedFileHash = null;
    this.selectedFileType = null;
  }

  clearPromptAndAnswer() {
    this.form.reset();
    this.answer = null;
    this.loading = false;
  }
}
