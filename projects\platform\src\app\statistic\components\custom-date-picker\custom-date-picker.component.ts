import { Component, Input, OnInit } from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { FilterOption } from '@platform/app/statistic/statistic.interface';
import { differenceInCalendarDays, format } from 'date-fns';
import { vi_VN } from 'ng-zorro-antd/i18n';

@Component({
  selector: 'app-custom-date-picker',
  templateUrl: './custom-date-picker.component.html',
  styleUrls: ['./custom-date-picker.component.scss']
})
export class CustomDatePickerComponent implements OnInit {
  readonly NzLocaleDatePicker = vi_VN.DatePicker;
  readonly today = new Date();
  readonly mode = {
    day: 1,
    month: 2
  };
  currentMode: number = 1;
  selectedDate;
  selectedMonth;

  formatDate = { title: 'dd/MM/yyyy', value: 'yyyy-MM-dd' };
  formatMonth = { title: 'MM/yyyy', value: 'yyyy-MM' };
  formatYear = { title: 'yyyy', value: 'yyyy' };

  constructor(public modal: NzModalRef) {}

  ngOnInit(): void {}

  disabledDate = (current: Date): boolean => {
    // current - today > 0 // only allow days from today to the past
    return differenceInCalendarDays(current, this.today) > 0;
  };

  apply() {
    if (this.currentMode === this.mode.day && this.selectedDate) {
      return this.modal.close({
        title: format(this.selectedDate, this.formatDate.title),
        value: format(this.selectedDate, this.formatDate.value),
        xAxisTitleForChart: 'Giờ'
      } as FilterOption);
    } else if (this.currentMode === this.mode.month && this.selectedMonth) {
      return this.modal.close({
        title: format(this.selectedMonth, this.formatMonth.title),
        value: format(this.selectedMonth, this.formatMonth.value),
        xAxisTitleForChart: 'Ngày'
      } as FilterOption);
    }
  }
}
