<div class="p-4">
  <div class="flex items-center gap-4" [formGroup]="filterForm">
    <div class="flex-1">
      <nz-select
        formControlName="assigneeIds"
        class="w-full"
        nzPlaceHolder="Người dùng đã mời"
        nzShowSearch
        nzSize="large"
        nzMode="multiple"
        nzOptionHeightPx="36"
        (nzOnSearch)="handleSearchUser($event)"
      >
        <nz-option
          *ngFor="let user of listUser"
          nzCustomContent
          [nzLabel]="user.name"
          [nzValue]="user.id"
        >
          <nz-avatar
            [nzSize]="24"
            [nzText]="user.name.charAt(0)"
            class="text-white bg-[#ff3355] uppercase text-center"
            >D</nz-avatar
          >
          {{ user.name }}
        </nz-option>
      </nz-select>
    </div>

    <div class="w-[15%]">
      <nz-select
        formControlName="roleId"
        class="w-full"
        nzPlaceHolder="Vai trò đ<PERSON> phân quyền"
        nzShowSearch
        nzSize="large"
      >
        <nz-option
          *ngFor="let role of listRoles"
          [nzLabel]="role.name"
          [nzValue]="role.id"
        >
        </nz-option>
      </nz-select>
    </div>
    <div class="ml-auto shrink-0">
      <button
        (click)="showModalInviteMember()"
        class="rounded-lg flex items-baseline px-8 gap-2 py-[10px] bg-brand-1 text-white text-sm font-medium"
      >
        <i
          nz-icon
          nzType="user-add"
          nzTheme="outline"
          class="text-md translate-y-[-2px]"
        ></i>
        Mời
      </button>
    </div>
  </div>
  <app-table-permission
    [documentId]="documentId"
    [listPermission]="listPermission"
    (onPageChanged)="handleChangePage($event)"
    (onAssigneeRoleChanged)="handleChangeAssigneeRole($event)"
    (onLimitChanged)="handleChangeLimit($event)"
  ></app-table-permission>
</div>
