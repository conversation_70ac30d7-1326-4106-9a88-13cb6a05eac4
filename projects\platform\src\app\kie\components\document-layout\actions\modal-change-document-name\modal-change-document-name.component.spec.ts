import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalChangeDocumentNameComponent } from './modal-change-document-name.component';

describe('ModalChangeDocumentNameComponent', () => {
  let component: ModalChangeDocumentNameComponent;
  let fixture: ComponentFixture<ModalChangeDocumentNameComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalChangeDocumentNameComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalChangeDocumentNameComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
