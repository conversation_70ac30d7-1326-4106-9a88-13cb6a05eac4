<h5 class="text-xl font-bold">Thông tin tài khoản</h5>
<div *ngIf="isEditing">
	<form class="mt-5" nz-form [formGroup]="accountForm" (ngSubmit)="handleSubmit()">
		<nz-form-item>
			<nz-form-label [nzSpan]="8" [nzNoColon]="true" nzRequired nzFor="fullName">
				Họ tên
			</nz-form-label>
			<nz-form-control [nzSpan]="16" nzErrorTip="Họ tên không được để trống">
				<input nz-input formControlName="fullName" id="fullName" />
			</nz-form-control>
		</nz-form-item>
		<nz-form-item>
			<nz-form-label [nzSpan]="8" [nzNoColon]="true" nzRequired nzFor="phoneNumber">
				Số điện thoại
			</nz-form-label>
			<nz-form-control [nzSpan]="16" nzErrorTip="Số điện thoại không hợp lệ">
				<input nz-input formControlName="phoneNumber" id="phoneNumber" />
			</nz-form-control>
		</nz-form-item>
		<div class="flex justify-end gap-3 col-span-full">
			<button type="button" (click)="cancelEditing()"
				class="bg-icon-2 text-white p-2 rounded-[4px] font-semibold uppercase" style="font-weight: 600;">HỦY</button>
			<button type="submit" class="bg-brand-1 text-white p-2 rounded-[4px] font-semibold uppercase">Chỉnh sửa</button>
		</div>
	</form>

</div>

<div *ngIf="!isEditing">
	<div class="grid grid-cols-1r gap-y-3 mt-5">
		<div class="col-span-4 label">Họ tên</div>
		<div class="col-span-8 label flex-1">{{ account?.fullName }}</div>
		<div class="col-span-4 label">Số điện thoại</div>
		<div class="col-span-8 label flex-1">{{ account?.phoneNumber }}</div>
		<div class="col-span-12 flex justify-end">
			<button (click)="isEditing = true" class="bg-brand-1 text-white p-2 rounded-[4px] font-semibold uppercase">Chỉnh
				sửa</button>
		</div>
	</div>
</div>