import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, catchError, tap } from 'rxjs';
import { UploadFilesChange } from '../upload-files/upload-files.component';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';

@Component({
  selector: 'app-modal-create-files',
  templateUrl: './modal-create-files.component.html',
  styleUrls: ['./modal-create-files.component.scss']
})
export class ModalCreateFilesComponent implements OnInit {
  readonly nzModalData: { documentId: string } = inject(NZ_MODAL_DATA);
  documentId = this.nzModalData.documentId;

  fileList: File[] = [];

  constructor(
    private toastrService: ToastrService,
    private kieService: KIEService,
    private modalRef: NzModalRef,
    private documentLayoutService: DocumentLayoutService
  ) {}

  ngOnInit(): void {}

  handleCancel() {
    this.modalRef.close();
  }

  handleUploadFilesChange(change: UploadFilesChange) {
    switch (change.action) {
      case 'add': {
        this.fileList.push(...(change.addedFiles || []));
        break;
      }
      case 'remove': {
        this.fileList = this.fileList.filter((_, i) => i !== (change.removeIndex ?? -1));
        break;
      }
    }
  }

  saveFiles(): void {
    if (this.documentId && this.fileList.length > 0) {
      this.kieService
        .uploadFile(this.documentId, this.fileList)
        .pipe(
          tap((res) => {
            let fileUploadFailed: string[] = [];
            if (res.data) {
              res.data.forEach((f, index) => {
                if (f.success) {
                  this.toastrService.success(`Tải file ${f.fileName} thành công!`);
                } else {
                  this.toastrService.error(
                    `Tải file ${f.fileName} thất bại. Bấm "Lưu" để thử lại`
                  );
                  fileUploadFailed.push(`${index}_${f.fileName}`);
                }
              });
            }
            this.documentLayoutService.updateFileCountOfDocument(
              this.documentId,
              res.data.filter((f) => f.success).length
            );
            if (fileUploadFailed.length === 0) {
              this.fileList = [];
              this.modalRef.close(true);
            } else {
              // Filter to keep files that were uploaded but failed.
              this.fileList = this.fileList.filter((file, index) =>
                fileUploadFailed.includes(`${index}_${file.name}`)
              );
            }
          }),
          catchError((err: any) => {
            let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
            if (err instanceof HttpErrorResponse) {
              switch (err.status) {
                case 403:
                  errorMessage =
                    'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
              }
            }
            this.toastrService.error(errorMessage);
            return EMPTY;
          })
        )
        .subscribe();
    }
  }
}
