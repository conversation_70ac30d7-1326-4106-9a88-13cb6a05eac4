import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@platform/environment/environment';
import UtilsService from './utils.service';

@Injectable({
  providedIn: 'root'
})
export class LLMService {
  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  llmExtraction(body: {
    prompt: string;
    file_type: string;
    file_hash: string;
    details: boolean;
  }) {
    return this.http.post<{
      status: string;
      statusCode: number;
      object: {
        num_of_pages: number;
        result: string;
        warning_messages: string[];
        warnings: any[];
      };
    }>(`${environment.backendUrl}idg-api/llm/extraction`, body, {
      headers: this.utilsService.headers
    });
  }
}
