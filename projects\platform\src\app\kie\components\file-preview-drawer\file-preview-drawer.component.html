<ng-template #previewDrawerTmpl let-data let-drawerRef="drawerRef">
  <div class="h-full flex flex-col items-center justify-around">
    <div class="h-[90%]">
      <img class="object-contain h-full" [src]="data?.previewLink" [alt]="data?.name" />
    </div>
    <div
      class="bg-[#000000CC] rounded-[28px] px-6 py-[6px] text-center truncate max-w-full"
      *ngIf="data?.name"
    >
      {{ data?.name }}
    </div>
    <div
      class="bg-[#000000CC] rounded-[28px] px-6 py-[6px] text-center"
      *ngIf="data?.numPages"
    >
      {{ data?.numPages }} trang
    </div>
    <div
      class="bg-[#000000CC] rounded-[28px] px-6 py-[6px] text-center max-w-full"
      *ngIf="data?.warning"
    >
      {{ data?.warning }}
    </div>
  </div>
</ng-template>

<ng-container
  *ngTemplateOutlet="
    triggerTemplate ? triggerTemplate : defaultTrigger;
    context: {
      click: openDrawer.bind(this),
      mouseover: openDrawer.bind(this),
      text: fileName
    }
  "
></ng-container>

<ng-template #defaultTrigger>
  <button (click)="openTemplate()">Show Preview</button>
</ng-template>
