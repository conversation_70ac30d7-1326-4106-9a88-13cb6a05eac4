<div class="p-4">
  <div class="flex justify-between items-center">
    <div class="flex gap-4" [formGroup]="filterForm">
      <nz-input-group class="rounded-lg" nzSize="large" nzPrefixIcon="search">
        <input nz-input placeholder="Nhập tên tài liệu" formControlName="search" />
      </nz-input-group>

      <nz-range-picker
        class="rounded-lg"
        formControlName="dateRange"
        [nzPlaceHolder]="['Chọn ngày', 'Chọn ngày']"
        [nzFormat]="'dd/MM/yyyy'"
      ></nz-range-picker>
    </div>
    <ng-container *ngTemplateOutlet="uploadBtn"></ng-container>
  </div>
  <app-table-file
    [documentId]="documentId"
    [roleInDocument]="roleInDocument"
    [listFiles]="listFiles"
    (pageChange)="onPageChange($event)"
    (limitChange)="onLimitChange($event)"
    [uploadBtn]="uploadBtn"
    (fileActionSuccess)="onFileActionSuccess()"
  ></app-table-file>
</div>

<ng-template #uploadBtn>
  <button
    *ngIf="['creator', 'editor'].includes(roleInDocument)"
    (click)="openModalCreateFiles()"
    class="rounded-lg flex items-center px-4 gap-2 py-[6px] bg-brand-1 text-white text-sm font-medium"
  >
    <i nz-icon nzType="upload" class="text-lg translate-y-[-2px]"></i>
    <span>Upload file</span>
  </button>
</ng-template>
