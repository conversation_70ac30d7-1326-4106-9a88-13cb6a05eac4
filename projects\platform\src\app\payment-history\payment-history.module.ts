import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaymentHistoryRoutingModule } from './payment-history-routing.module';
import { PaymentHistoryComponent } from './payment-history.component';
import { FormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTableModule } from 'ng-zorro-antd/table';

@NgModule({
  declarations: [PaymentHistoryComponent],
  imports: [
    CommonModule,
    PaymentHistoryRoutingModule,
    FormsModule,
    NzSelectModule,
    NzDatePickerModule,
    NzTableModule,
  ],
})
export class PaymentHistoryModule {}
