import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ResetPasswordRoutingModule } from './reset-password-routing.module';
import { ResetComponent } from './pages/reset/reset.component';
import { ForgotComponent } from './pages/forgot/forgot.component';
import { ReactiveFormsModule } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';

@NgModule({
  declarations: [ResetComponent, ForgotComponent],
  imports: [
    ReactiveFormsModule,
    CommonModule,
    ResetPasswordRoutingModule,
    NzFormModule,
    NzInputModule,
  ],
})
export class ResetPasswordModule {}
