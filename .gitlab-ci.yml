# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
before_script:
  - echo ************ hub.vnpt.vn  >> /etc/hosts
  - echo *********** scm.devops.vnpt.vn >> /etc/hosts
variables:
  CD_CHART_REPO_SANDBOX: helm-chart-vnpt/rpa/sandbox/rpa-platform
  CD_CHART_REPO_SANDBOX_2: helm-chart-vnpt/rpa/sandbox/rpa-platform-2
  CD_CHART_REPO_PROD: helm-charts/rpa-platform
  CD_VALUES_FILE: values.yaml

stages:
  - build
  - deploy

build_sandbox:
  stage: build
  image: crelease.devops.vnpt.vn:10121/image-base/rpa-nodejs:latest
  script:
    # - docker login icr.icenter.ai -u $USER -p $PASS
    - npm install && ng build platform --configuration=sandbox
    - docker build -t crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform:$CI_PIPELINE_IID ./projects/platform/deploy
    - docker push crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform:$CI_PIPELINE_IID
  only:
    - sandbox
  tags:
    - runner-gpu87

build_vnptai_io_sandbox:
  stage: build
  image: crelease.devops.vnpt.vn:10121/image-base/rpa-nodejs:latest
  script:
    # - docker login icr.icenter.ai -u $USER -p $PASS
    - npm install && ng build platform --configuration="vnptai.io.sandbox"
    - docker build -t crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-vnptai-io:$CI_PIPELINE_IID ./projects/platform/deploy
    - docker push crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-vnptai-io:$CI_PIPELINE_IID
  only:
    - vnptai.io.sandbox
  tags:
    - runner-gpu87

build_2.0:
  stage: build
  image: crelease.devops.vnpt.vn:10121/image-base/rpa-nodejs:latest
  script:
    # - docker login icr.icenter.ai -u $USER -p $PASS
    - npm install && ng build platform --configuration=sandbox
    - docker build -t crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-$CI_COMMIT_REF_NAME:$CI_PIPELINE_IID ./projects/platform/deploy
    - docker push crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-$CI_COMMIT_REF_NAME:$CI_PIPELINE_IID
  only:
    - platform-2.0
  tags:
    - runner-gpu87

build_prod:
  stage: build
  image: crelease.devops.vnpt.vn:10121/image-base/rpa-nodejs:latest
  script:
    # - docker login icr.icenter.ai -u $USER -p $PASS
    - npm install && ng build platform --configuration=production
    - docker build -t crelease.devops.vnpt.vn:10121/rpa/rpa-platform:$CI_PIPELINE_IID ./projects/platform/deploy
    - docker push crelease.devops.vnpt.vn:10121/rpa/rpa-platform:$CI_PIPELINE_IID
  only:
    - main
  tags:
    - runner-gpu87

update_manifest_sandbox:
  stage: deploy
  image: crelease.devops.vnpt.vn:10121/image-base/gitlab-ic:1.0.0
  script:
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd clone -branch master
    - cd /app/source
    - /usr/bin/yq eval -i ".image.repository = \"crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform\"" $CD_CHART_REPO_SANDBOX/$CD_VALUES_FILE
    - /usr/bin/yq eval -i ".image.tag = \"$CI_PIPELINE_IID\"" $CD_CHART_REPO_SANDBOX/$CD_VALUES_FILE
    - GITIC -cmd commit -message "Update manifest $CI_PIPELINE_IID" -branch master
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd push -branch master
  needs:
    - build_sandbox
  only:
    - sandbox
  tags:
    - k8s

update_manifest_vnptai_io_sandbox:
  stage: deploy
  image: crelease.devops.vnpt.vn:10121/image-base/gitlab-ic:1.0.0
  script:
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd clone -branch master
    - cd /app/source
    - /usr/bin/yq eval -i ".image.repository = \"crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-vnptai-io\"" helm-chart-vnpt/rpa/sandbox/rpa-platform-vnptai-io/values.yaml
    - /usr/bin/yq eval -i ".image.tag = \"$CI_PIPELINE_IID\"" helm-chart-vnpt/rpa/sandbox/rpa-platform-vnptai-io/values.yaml
    - GITIC -cmd commit -message "Update manifest $CI_PIPELINE_IID" -branch master
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd push -branch master
  only:
    - vnptai.io.sandbox
  tags:
    - k8s

update_manifest_2.0:
  stage: deploy
  image: crelease.devops.vnpt.vn:10121/image-base/gitlab-ic:1.0.0
  script:
    - |
      count=0
      while [ $count -lt 100 ]; do
        count=$((count+1))
        if GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd clone -branch master ; then
          echo "Number of loops pull: $count"
          break
        else
          continue
        fi
      done
      cd /app/source
      /usr/bin/yq eval -i ".image.repository = \"crelease.devops.vnpt.vn:10121/rpa-dev/rpa-platform-$CI_COMMIT_REF_NAME\"" $CD_CHART_REPO_SANDBOX_2/$CD_VALUES_FILE
      /usr/bin/yq eval -i ".image.tag = \"$CI_PIPELINE_IID\"" $CD_CHART_REPO_SANDBOX_2/$CD_VALUES_FILE
      GITIC -cmd commit -message "Update manifest $CI_PIPELINE_IID" -branch master
      count=0
      while [ $count -lt 100 ]; do
        count=$((count+1))
        if GITIC -url https://scm.devops.vnpt.vn/devops/devopsic.git -cmd push -branch master ; then
          echo "Number of loops push: $count"
          break
        else
          continue
        fi
      done
  needs:
    - build_2.0
  only:
    - platform-2.0
  tags:
    - k8s

update_manifest_prod:
  stage: deploy
  image: crelease.devops.vnpt.vn:10121/image-base/gitlab-ic:1.0.0
  variables:
    GIT_STRATEGY: none
  retry: 2
  script:
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsrpa.git -cmd clone -branch master
    - GITIC -cmd newbranch -branch "merge_$CI_PIPELINE_IID"
    - cd /app/source
    - /usr/bin/yq eval -i ".image.repository = \"crelease.devops.vnpt.vn:10121/rpa/rpa-platform\"" $CD_CHART_REPO_PROD/$CD_VALUES_FILE
    - /usr/bin/yq eval -i ".image.tag = \"$CI_PIPELINE_IID\"" $CD_CHART_REPO_PROD/$CD_VALUES_FILE
    - GITIC -cmd commit -message "Update manifest $CI_PIPELINE_IID"
    - GITIC -url https://scm.devops.vnpt.vn/devops/devopsrpa.git -cmd push -merge 1 -branch "master"
  needs:
    - build_prod
  only:
    - main
  tags:
    - k8s
