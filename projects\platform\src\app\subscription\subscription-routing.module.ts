import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InfoComponent } from './pages/info/info.component';
import { PaymentComponent } from './pages/payment/payment.component';
import { SubscriptionComponent } from './pages/index/subscription.component';
import { ResponsePaymentComponent } from './pages/response-payment/response-payment.component';

const routes: Routes = [
  {
    path: '',
    component: SubscriptionComponent,
  },
  {
    path: 'info',
    component: InfoComponent,
  },
  {
    path: 'payment',
    component: PaymentComponent,
  },
  {
    path: 'response-payment',
    component: ResponsePaymentComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SubscriptionRoutingModule {}
