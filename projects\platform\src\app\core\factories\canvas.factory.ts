import * as Canvas from 'canvas';

/* help rendering pdf page into actual image */
export class NodeCanvasFactory {
  create(
    width,
    height
  ): { canvas: Canvas.Canvas; context: Canvas.CanvasRenderingContext2D } {
    if (width <= 0 || height <= 0) {
      console.error('Invalid canvas size');
      return;
    }
    const canvas = Canvas.createCanvas(width, height);
    const context = canvas.getContext('2d');
    return {
      canvas,
      context
    };
  }

  reset(canvasAndContext, width, height) {
    if (!canvasAndContext.canvas) {
      console.error('Canvas is not specified');
      return;
    }
    if (width <= 0 || height <= 0) {
      console.error('Invalid canvas size');
      return;
    }

    canvasAndContext.canvas.width = width;
    canvasAndContext.canvas.height = height;
  }

  destroy(canvasAndContext) {
    if (!canvasAndContext.canvas) {
      console.error('Canvas is not specified');
      return;
    }

    // Zeroing the width and height cause Firefox to release graphics
    // resources immediately, which can greatly reduce memory consumption.
    canvasAndContext.canvas.width = 0;
    canvasAndContext.canvas.height = 0;
    canvasAndContext.canvas = null;
    canvasAndContext.context = null;
  }
}
