/* 
  be awared of styles bleeding to another component bc of the class ocr-input-tooltip is lifted to app-wide styles with ::ng-deep. 
  cannot use :host for this tooltip bc it resides inside div.cdk-overlay-container 
  however, style bleeding in this case is suppressed with class naming (.ocr-input-tooltip)
*/
::ng-deep .ocr-input-tooltip {
  max-width: 366px;

  .ant-tooltip-inner {
    border-radius: 8px;
    font-size: 14px;
    text-align: start;
    max-width: 366px;
    padding: 28px;
    background-color: #2e435a;
  }

  .ant-tooltip-arrow-content::before {
    background: #2e435a;
  }
}

:host ::ng-deep {
  .ant-select .ant-select-selector {
    border-radius: 8px;
  }
}