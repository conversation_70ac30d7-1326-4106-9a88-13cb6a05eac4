import { Component, inject, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { EMPTY, Observable, catchError, switchMap, tap } from 'rxjs';
import { UploadFilesChange } from '../upload-files/upload-files.component';

@Component({
  selector: 'app-modal-create-sample-document',
  templateUrl: './modal-create-sample-document.component.html',
  styleUrls: ['./modal-create-sample-document.component.scss']
})
export class ModalCreateSampleDocumentComponent implements OnInit {
  readonly nzModalData: { listFolders: Folder[] } = inject(NZ_MODAL_DATA);
  listFolders: Folder[] = this.nzModalData.listFolders || [];
  selectedFolderId: string;
  nameDocument: string = '';
  selectedConfig: 'location_only' | 'default' = 'location_only';
  fileList: File[] = [];
  fileType: string = '';
  RULE_ACCEPT = {
    mimetypes: ['application/pdf', 'image/jpeg', 'image/png'],
    accept: '.jpeg, .jpg, .png, .pdf',
    typeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    extensions: ['pdf', 'jpeg', 'jpg', 'png'],
    size: 10 * 1024 * 1024 // 10MB
  };

  configs = [
    { label: 'Nhận dạng theo vùng văn bản', value: 'location_only' },
    { label: 'Nhận dạng theo keyword + vùng văn bản', value: 'default' }
  ];

  constructor(
    private toastr: ToastrService,
    private kieService: KIEService,
    private modalRef: NzModalRef,
    private documentLayoutService: DocumentLayoutService,
    private router: Router
  ) {}

  ngOnInit(): void {
    if (this.listFolders.length > 0) {
      this.selectedFolderId = this.listFolders[0].id;
    }
  }

  handleUploadFilesChange(change: UploadFilesChange) {
    switch (change.action) {
      case 'add': {
        this.fileList.push(...(change.addedFiles || []));
        break;
      }
      case 'remove': {
        this.fileList = this.fileList.filter((_, i) => i !== (change.removeIndex ?? -1));
        break;
      }
    }
  }

  handleCancel() {
    this.modalRef.close();
  }

  createNewSelfDocument(): void {
    if (this.nameDocument.trim().length < 1) {
      this.toastr.error('Tên văn bản không được để trống');
      return;
    }

    if (this.nameDocument.trim().length > 255) {
      this.toastr.error('Tên văn bản không được quá 255 ký tự');
      return;
    }

    // check exist name documents
    const selectedFolder = this.listFolders.find(
      (folder) => folder.id === this.selectedFolderId
    );

    if (selectedFolder) {
      if (selectedFolder.documents.length >= 100) {
        this.toastr.error(
          'Vượt quá số lượng tối đa 100 văn bản cho phép trong 1 thư mục'
        );
        return;
      }

      // Check if any document in the selected folder has the same name as this.documentSelected.title.trim()
      const isDocumentExist = selectedFolder.documents.some(
        (doc) => doc.name === this.nameDocument.trim()
      );

      if (isDocumentExist) {
        this.toastr.error('Tên văn bản đã tồn tại!');
        return;
      }
    }

    if (this.fileList.length === 0) {
      this.toastr.error('Vui lòng upload file config!');
      return;
    }

    const payload = {
      name: this.nameDocument,
      folderId: this.selectedFolderId,
      templateOcrModel: this.selectedConfig,
      templateFile: this.fileList[0]
    };

    this.kieService
      .createNewSelfDocument(payload)
      .pipe(
        tap(() => {
          this.toastr.success('Tạo mới văn bản thành công');
          this.modalRef.close();
        }),
        catchError((error) => {
          this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
          return EMPTY;
        }),
        switchMap(
          () =>
            this.documentLayoutService.loadFolders({
              loadStrategy: 'refetch-all-current-pages',
              returnAsObservable: true
            }) as Observable<any>
        ),
        tap(() => {
          const selectedFolder = this.documentLayoutService.folders.find(
            (folder) => folder.id === this.selectedFolderId
          );
          const newestDoc = selectedFolder.documents[selectedFolder.documents.length - 1]; // last document === newest one
          if (newestDoc?.id)
            this.router.navigate([
              '/',
              'key-information-extractor',
              'document',
              newestDoc.id
            ]);
        })
      )
      .subscribe();
  }
}
