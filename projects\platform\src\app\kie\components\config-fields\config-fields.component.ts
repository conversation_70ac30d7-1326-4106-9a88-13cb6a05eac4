import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges
} from '@angular/core';
import {
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import UtilsService from '@platform/app/core/services/utils.service';
import {
  BboxChange,
  DrawCommand,
  FieldConfigChange,
  OcrModel,
  Template,
  TemplateConfigFieldModel
} from '@platform/app/kie/kie';
import { get, isEqual, isFinite, pick } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import {
  BehaviorSubject,
  Subject,
  Subscription,
  debounceTime,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  takeUntil,
  tap
} from 'rxjs';

const DataTypes = ['is_date', 'is_number', 'is_title_or_uppercase'];

@Component({
  selector: 'app-config-fields',
  templateUrl: './config-fields.component.html',
  styleUrls: ['./config-fields.component.scss']
})
export class ConfigFieldsComponent implements OnChanges, OnDestroy {
  readonly TemplateConfigFieldModel = TemplateConfigFieldModel;
  readonly OcrModel = OcrModel;

  @Input()
  bboxChangeSubject: BehaviorSubject<BboxChange>;

  bboxChangesSubscription: Subscription;

  @Input()
  templateConfig: Template['config'];

  @Output()
  onTemplateConfigSave = new EventEmitter<Template['config']>();

  @Input()
  drawCommandSubject: BehaviorSubject<DrawCommand>;

  /* prevent onFieldConfigChange as EventEmitter repeatedly emit() without new changes into parent component */
  fieldConfigChangeSubject = new Subject<FieldConfigChange>();
  @Output()
  onFieldConfigChange = this.fieldConfigChangeSubject.pipe(
    distinctUntilChanged((prev, curr) => isEqual(prev, curr))
    // tap((value) => console.log('value', value))
  );

  /* TODO: validate unique across all field name */
  /* TODO: render form fields based on type of template */
  form: UntypedFormGroup;

  someFormFieldsChangesSubscription: Subscription;

  /* check if there is any ongoing drawing command */
  get isUpdatingBbox() {
    return get(this.drawCommandSubject, 'value.fieldId');
  }

  fieldOrderCount = 0;

  destroy$ = new Subject<void>();

  constructor(
    private formBuilder: UntypedFormBuilder,
    private cdr: ChangeDetectorRef,
    private utils: UtilsService,
    private toastr: ToastrService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.drawCommandSubject) console.warn('no drawCommandSubject');

    const tmp = Object.values(this.templateConfig?.fields || {});
    this.form = this.formBuilder.group({
      fieldConfigList: this.formBuilder.array(
        tmp
          .sort((a, b) => a.extraConfig.order - b.extraConfig.order)
          .map((field, fieldIndex) => {
            let bboxPage: number, bboxValue: [number, number, number, number];
            if (field.location) {
              const fieldLocation = Object.entries(field.location).pop();
              bboxPage = parseInt(fieldLocation[0]);
              bboxValue = fieldLocation[1];
            }

            const controls = {
              /* extra config */
              id: this.formBuilder.control(field.extraConfig.id, [Validators.required]),
              is_visible: this.formBuilder.control(field.extraConfig.is_visible, [
                Validators.required
              ]),
              color: this.formBuilder.control(field.extraConfig.color, [
                Validators.required
              ]),
              order: this.formBuilder.control(field.extraConfig.order || fieldIndex, [
                Validators.required
              ]),
              /* field config */
              name: this.formBuilder.control(field.name, [Validators.required]),
              model: this.formBuilder.control(
                field.model || TemplateConfigFieldModel.Default,
                [Validators.required]
              ),
              bbox: this.formBuilder.control(
                field.location && isFinite(bboxPage) && bboxValue.length === 4
                  ? { page: bboxPage, value: bboxValue }
                  : null,
                this.templateConfig.ocr_model === OcrModel.LocationOnly
                  ? [Validators.required]
                  : undefined
              )
            };
            if (this.templateConfig.ocr_model === OcrModel.Default) {
              controls['prewords'] = this.formBuilder.array(
                field.prewords.map((preword) =>
                  this.formBuilder.control(preword, [Validators.required])
                ),
                [Validators.required]
              );
              controls['sufwords'] = this.formBuilder.array(
                field.sufwords.map((sufword) =>
                  this.formBuilder.control(sufword, [Validators.required])
                ),
                [Validators.required]
              );
              controls['multilines'] = this.formBuilder.control(
                field.multilines || false,
                [Validators.required]
              );
              /* FIXME:
                BE defined is_address, is_date, is_number, is_title_or_uppercase as boolean to be toggled
                BE supports only one of those flag at a time
                FE convert those flags into a dropdown/radio button
              */
              let datatypeValue = null;
              DataTypes.forEach((type) => field[type] && (datatypeValue = type));
              controls['datatype'] = this.formBuilder.control(datatypeValue);
            }
            return this.formBuilder.group(controls);
          }),
        [
          /* (arr: FormArray) => {
            if (!arr.controls.length) return { error: 'Must config at least one field' };
            return null;
          } */
        ]
      )
      /* TODO: declare extra common template config here */
    });

    this.handleBboxChanges();
    this.handleSomeFormFieldsChanges();
  }

  get fieldConfigListFormArray() {
    return this.form.controls['fieldConfigList'] as UntypedFormArray;
  }

  handleBboxChanges() {
    this.bboxChangesSubscription?.unsubscribe();
    if (this.bboxChangeSubject)
      this.bboxChangesSubscription = this.bboxChangeSubject
        .asObservable()
        .pipe(
          takeUntil(this.destroy$),
          filter((bboxChange) => !!bboxChange),
          tap(({ fieldId, page, value }) => {
            const updateFieldId = fieldId;
            const updateFieldIndex = this.fieldConfigListFormArray.value.findIndex(
              (field) => field.id === updateFieldId
            );
            if (updateFieldIndex === -1) return;
            const bboxValue =
              isFinite(page) && value.length === 4 ? { page, value } : null;
            const bboxControl: UntypedFormControl = get(
              this.fieldConfigListFormArray.at(updateFieldIndex),
              'controls.bbox'
            );
            if (!bboxControl) return;
            bboxControl.setValue(bboxValue);
            bboxControl.markAsTouched({ onlySelf: true });
            bboxControl.updateValueAndValidity();
            this.cdr.detectChanges();
          })
        )
        .subscribe();
  }

  /* subscribe changes của form (some fields are picked like is_visible,...) để emit change cần hiện ngay lên document-viewer + ocr-result */
  private handleSomeFormFieldsChanges() {
    this.someFormFieldsChangesSubscription?.unsubscribe();
    this.someFormFieldsChangesSubscription = new Subscription(() => {
      console.log('initial teardown');
    });
    if (this.fieldConfigListFormArray) {
      this.fieldConfigListFormArray.controls.forEach((formGroup: UntypedFormGroup) =>
        this.handleOneFormFieldChanges(formGroup)
      );
      // console.log(this.someFormFieldsChangesSubscription);
    }
  }

  private handleOneFormFieldChanges(fieldFormGroup) {
    const formGroupId = fieldFormGroup.controls['id'].value;
    const listOfFormFieldBeingHandleWhenChanging = ['is_visible', 'color', 'name'];
    Object.entries(fieldFormGroup.controls)
      .filter(([key]) => listOfFormFieldBeingHandleWhenChanging.includes(key))
      .forEach(([key, formControl]: [string, UntypedFormControl]) => {
        this.someFormFieldsChangesSubscription.add(
          formControl.valueChanges
            .pipe(
              takeUntil(this.destroy$),
              // tap(console.log),
              debounceTime(300), // debounce first, for input that can rapidly changes like color select or text input
              distinctUntilChanged(), // check change after debounced, default === operator is ok for primitive changed value checking
              tap(() => {
                /* 
                  - publish change into fieldConfigChangeSubject
                  - note that many individual fields (is_visible, color, name) could SIMULTANEOUSLY publish the same change (under one fieldFormGroup.value) due to validateAllFormFields() recursive nature
                  - fieldConfigChangeSubject have additional pipe logic to catch and discard same changes to prevent parent and other sibling component being overloading because of extensively re-rendering
                */
                this.fieldConfigChangeSubject.next({
                  type: 'update',
                  content: fieldFormGroup.value
                });
              }),
              finalize(() => {
                console.log(`unsubscribed ${key}_${formGroupId}`);
              })
            )
            .subscribe()
        );
      });
  }

  handleClearBbox(formGroupFieldId) {
    this.bboxChangeSubject.next({ fieldId: formGroupFieldId });
  }

  handleRemoveField(event: Event, id) {
    event.stopPropagation();
    const fieldFormControl = this.fieldConfigListFormArray.at(id);
    this.fieldConfigListFormArray.removeAt(id);
    /* cancel removing field's drawing command still in place */
    if (this.isUpdatingBbox === fieldFormControl.value['id'])
      this.drawCommandSubject.next(null);
    /* FIXME: remove field should also remove its added subscriptions to someFormFieldsChangesSubscription */
    this.fieldConfigChangeSubject.next({
      type: 'remove',
      content: fieldFormControl.value
    });
  }

  handleAddField() {
    const tmpName = `NEW_FIELD_${this.fieldConfigListFormArray['controls'].length + 1}`;
    const id = `${this.utils.removeAccents(tmpName.toLowerCase().split(' ').join('_'))}_${this.utils.makeId(6)}`;
    const controls = {
      id: this.formBuilder.control(id, [Validators.required]),
      name: this.formBuilder.control(tmpName, [Validators.required]),
      model: this.formBuilder.control(TemplateConfigFieldModel.Default, [
        Validators.required
      ]),
      bbox: this.formBuilder.control(
        null,
        this.templateConfig.ocr_model === OcrModel.LocationOnly
          ? [Validators.required]
          : undefined
      ),
      color: this.formBuilder.control('#009B4E', [Validators.required]),
      is_visible: this.formBuilder.control(false, [Validators.required]),
      order: this.formBuilder.control(--this.fieldOrderCount, [Validators.required]),
      isNew: this.formBuilder.control(true)
    };

    if (this.templateConfig.ocr_model === OcrModel.Default) {
      controls['prewords'] = this.formBuilder.array(
        [this.formBuilder.control('', [Validators.required])],
        [Validators.required]
      );
      controls['sufwords'] = this.formBuilder.array(
        [this.formBuilder.control('', [Validators.required])],
        [Validators.required]
      );
      controls['multilines'] = this.formBuilder.control(false, [Validators.required]);
      controls['datatype'] = this.formBuilder.control(null);
    }

    const newFieldFormGroup = this.formBuilder.group(controls);
    this.fieldConfigListFormArray.insert(0, newFieldFormGroup);

    this.handleOneFormFieldChanges(newFieldFormGroup);

    this.fieldConfigChangeSubject.next({
      type: 'create',
      content: newFieldFormGroup.value
    });
  }

  handleSave() {
    if (this.form.invalid) {
      console.log('form not valid', this.fieldConfigListFormArray.value);
      this.toastr.warning(
        'Template config chưa hợp lệ, hãy kiểm tra lại các trường thông tin!'
      );
      this.fieldConfigListFormArray['controls'].forEach(this.validateAllFormFields);
      return;
    }
    this.onTemplateConfigSave.emit({
      ...this.templateConfig,
      fields: Object.fromEntries(
        this.form.value['fieldConfigList'].map((field) => {
          let location = {};
          if (field.bbox && isFinite(field.bbox.page) && field.bbox.value.length === 4)
            location = {
              [field.bbox.page]: field.bbox.value
            };

          const extraConfig = pick(field, [
            /* not save 'id' bc id is gen and used by FE only */
            /* 'id', */
            'name',
            'is_visible',
            'color',
            'order'
          ]);
          const savedData = {
            // name: get(field, 'name'), // unnecessary in save template request
            location,
            is_visible: get(field, 'is_visible'), // not required
            extraConfig
          };

          if (this.templateConfig.ocr_model === OcrModel.Default) {
            const ocrModelDefaultData = pick(field, [
              'model',
              'multilines',
              'prewords',
              'sufwords'
            ]);
            DataTypes.forEach(
              (prop) => (ocrModelDefaultData[prop] = prop === get(field, 'datatype'))
            );
            Object.assign(savedData, ocrModelDefaultData);
          } else {
            Object.assign(savedData, pick(field, ['model']));
          }

          // console.log(savedData);

          return [get(field, 'name'), savedData];
        })
      )
    });
  }

  updateFieldBbox(formGroup: UntypedFormGroup) {
    const command: DrawCommand =
      this.drawCommandSubject.value &&
      this.drawCommandSubject.value.fieldId === formGroup.controls['id'].value
        ? null
        : {
            fieldId: formGroup.controls['id'].value,
            fieldName: formGroup.controls['name'].value,
            color: formGroup.controls['color'].value
          };
    // console.log('draw command', command);

    this.drawCommandSubject.next(command);
  }

  addWord(wordsFormArray: UntypedFormArray) {
    wordsFormArray.push(this.formBuilder.control('', [Validators.required]));
  }

  removeWord(wordsFormArray: UntypedFormArray, index) {
    if (wordsFormArray.controls.length === 1) return;
    wordsFormArray.removeAt(index);
  }

  /* recursively loop through all control in FormArray and its FormGroup validate */
  private validateAllFormFields(formGroup: UntypedFormGroup) {
    Object.keys(formGroup.controls).forEach((field) => {
      const control = formGroup.get(field);
      if (control instanceof UntypedFormControl) {
        control.markAsTouched({ onlySelf: true });
        control.updateValueAndValidity({ emitEvent: true });
      } else if (control instanceof UntypedFormGroup) {
        this.validateAllFormFields(control);
      } else if (control instanceof UntypedFormArray) {
        control.controls.forEach((control) => {
          control.markAsTouched({ onlySelf: true });
          control.updateValueAndValidity({ emitEvent: true });
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    /* manually unsubscribe for someFormFieldsChangesSubscription is necessary, since it is a combined of many subscriptions, destroy$ won't do it */
    this.someFormFieldsChangesSubscription?.unsubscribe();

    /* manually unsubscribe for bboxChangesSubscription is optional, since this component and it parent are destroyed at the same time, then bboxChangesSubject will be completed and cause the subscription to be closed. however, bboxChangesSubscription is necessary and must be declared then, here we still do it anyway */
    this.bboxChangesSubscription?.unsubscribe();
  }
}
