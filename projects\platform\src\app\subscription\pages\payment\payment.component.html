<div class="dashboard-rpa">
	<div class="font-bold text-xl">
		<PERSON><PERSON>
	</div>
	<div class="mt-5 grid grid-cols-12 py-[20px] px-[120px]">
		<div class="col-span-full xl:col-span-7 p-[60px]">
			<h5 class="font-semibold text-xl">Thông tin gói cước</h5>
			<div class="my-[10px]">
				<nz-table nzBordered nzTemplateMode>
					<thead>
						<tr>
							<th class="!font-semibold !bg-[#e2e2e2]">Tên gói</th>
							<th class="!font-semibold !bg-[#e2e2e2]" nzAlign="right">Trang văn bản bóc tách</th>
							<th class="!font-semibold !bg-[#e2e2e2]" nzAlign="right">Thời gian sử dụng</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td class="font-bold text-[#0f67ce]">{{selectedSubscription?.name}}</td>
							<td nzAlign="right" class="font-bold">{{selectedSubscription?.quota}} trang
							</td>
							<td nzAlign="right" class="font-bold">{{selectedSubscription?.time}} tháng
							</td>
						</tr>
					</tbody>
				</nz-table>
			</div>
			<div class="my-3 flex gap-2">
				<input id="exportBill" type="checkbox" [(ngModel)]="exportBill ">
				<label for="exportBill">Xuất hóa đơn</label>
			</div>
			<div *ngIf="exportBill">
				<h5 class="font-bold text-xl mb-4">Thông tin hóa đơn</h5>
				<form [formGroup]="billForm" nz-form>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon nzRequired>
							Họ tên
						</nz-form-label>
						<nz-form-control [nzSpan]="20" nzErrorTip="Họ tên không được để trống">
							<input nz-input type="text" formControlName="fullName">
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon nzRequired>
							Tên đơn vị
						</nz-form-label>
						<nz-form-control [nzSpan]="20" nzErrorTip="Tên đơn vị không được để trống">
							<input nz-input type="text" formControlName="companyName">
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label [nzSpan]="4" nzNoColon></nz-form-label>
						<nz-form-control [nzSpan]="20">
							<div class="flex gap-2 items-center">
								<input type="radio" id="business" name="isIndividual" checked (click)="handleIndividualChange(false)">
								<label for="business">
									Doanh nghiệp
								</label>
								<input type="radio" id="individual" name="isIndividual" class="custom-control-input"
									(click)="handleIndividualChange(true)">
								<label for="individual">Cá nhân</label>
							</div>
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon [nzRequired]="!isIndividual">
							Mã số thuế
						</nz-form-label>
						<nz-form-control [nzSpan]="20" nzErrorTip="Mã số thuế không được để trống">
							<input nz-input type="text" formControlName="taxCode">
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon nzRequired>
							Địa chỉ
						</nz-form-label>
						<nz-form-control [nzSpan]="20" nzErrorTip="Địa chỉ không được để trống">
							<input nz-input type="text" formControlName="address">
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon nzRequired>
							Số điện thoại
						</nz-form-label>
						<nz-form-control [nzSpan]="20" [nzErrorTip]="phoneErrorTpl">
							<input nz-input type="text" formControlName="phone">
							<ng-template #phoneErrorTpl let-control>
								<ng-container *ngIf="control.hasError('required')">
									Số điện thoại không được để trống
								</ng-container>
								<ng-container *ngIf="control.hasError('pattern')">
									Số điện thoại trống không hợp lệ
								</ng-container>
							</ng-template>
						</nz-form-control>
					</nz-form-item>
					<nz-form-item>
						<nz-form-label class="text-left" [nzSpan]="4" nzNoColon nzRequired>
							Email
						</nz-form-label>
						<nz-form-control [nzSpan]="20" [nzErrorTip]="emailErrorTpl">
							<input nz-input type="text" formControlName="email">
							<ng-template #emailErrorTpl let-control>
								<ng-container *ngIf="control.hasError('required')">
									Email không được để trống
								</ng-container>
								<ng-container *ngIf="control.hasError('email')">
									Email không hợp lệ
								</ng-container>
							</ng-template>
						</nz-form-control>
					</nz-form-item>
				</form>
			</div>
		</div>
		<div class="col-span-full xl:col-span-5 p-[60px]">
			<h5 class="font-semibold text-xl mb-[10px]">Đơn hàng</h5>
			<div class="flex justify-between">
				<div>Đơn giá</div>
				<div>{{formatAmount(selectedSubscription?.price)}} VNĐ</div>
			</div>
			<hr class="my-3 border border-[#cbcbcb] border-dashed">
			<div class="flex justify-between">
				<div>Số lượng (Tháng)</div>
				<div>{{selectedSubscription?.time}}</div>
			</div>
			<hr class="my-3 border border-[#cbcbcb] border-dashed">
			<div class="flex justify-between">
				<div>Giá dịch vụ</div>
				<div>{{formatAmount(selectedSubscription?.price)}} VNĐ</div>
			</div>
			<hr class="my-3 border border-[#cbcbcb] border-dashed">
			<div class="flex justify-between">
				<div>Thuế VAT (10%)</div>
				<div>{{formatAmount(selectedSubscription?.price * 0.1)}} VNĐ</div>
			</div>
			<hr class="my-3 border border-[#cbcbcb] border-dashed">
			<div class="flex justify-between" style="font-weight: bolder;">
				<div>Thành tiền</div>
				<div>{{formatAmount(selectedSubscription?.price * 1.1)}} VNĐ</div>
			</div>
			<!-- <div style="font-size: 20px; font-weight: 700; margin: 18px 0;">Chọn phương thức thanh toán:</div>
			<div style="display: flex; flex-direction: column; gap: 8px;">
				<div class="form-check">
					<input [(ngModel)]="paymentType" class="form-check-input" type="radio" name="paymentType" id="vnptMoney"
						[value]="1">
					<label class="form-check-label" for="vnptMoney">
						Cổng thanh toán VNPT Money
					</label>
				</div>
				<div class="form-check">
					<input [disabled]="(selectedSubscription?.price * selectedSubscription?.time * 1.1) > walletDetail?.amount"
						[(ngModel)]="paymentType" class="form-check-input" type="radio" name="paymentType" id="wallet" [value]="2">
					<label class="form-check-label" for="wallet">
						Ví điện tử ({{formatAmount(walletDetail?.amount)}} VNĐ)
					</label>
				</div>
			</div> -->
			<a class="btn-pay" (click)="showNotice()">Thanh Toán</a>
		</div>
	</div>
</div>

<ng-template #notice>
	<div class="bg-white p-6 rounded-lg shadow-[0px_4px_12px_0px_rgba(64,75,68,0.05)]">
		<div class="flex items-center mb-5">
			<span class="text-center flex-1 text-[#141414] text-xl font-semibold leading-[26px]">Thoả
				thuận về bảo vệ dữ liệu cá nhân</span>
			<svg class="cursor-pointer" (click)="modalService.closeAll()" width="24" height="24" viewBox="0 0 24 24"
				fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd"
					d="M13.5022 12L17.68 16.1778C18.0743 16.5721 18.1087 17.1573 17.7582 17.5078L17.5079 17.7582C17.1574 18.1087 16.5721 18.0742 16.1778 17.6799L12 13.5021L7.82216 17.68C7.42785 18.0743 6.84264 18.1087 6.49214 17.7582L6.24178 17.5079C5.89128 17.1574 5.92571 16.5722 6.32002 16.1778L10.4979 12L6.32002 7.82215C5.92571 7.42784 5.89128 6.84264 6.24178 6.49214L6.49214 6.24178C6.84264 5.89128 7.42785 5.92571 7.82216 6.32002L12 10.4979L16.1778 6.32008C16.5721 5.92577 17.1574 5.89134 17.5079 6.24184L17.7582 6.4922C18.1087 6.84269 18.0743 7.4279 17.68 7.82221L13.5022 12Z"
					fill="#757575" />
			</svg>
		</div>
		<div class="flex gap-[10px]">
			<div class="flex-1">
				<div class="rounded-lg border border-[#E2E2E2] bg-white max-h-[390px] overflow-auto py-3 px-5">
					<div class="grid	grid-cols-[160px_1fr] gap-x-[10px] gap-y-[4px] mb-3">
						<div>Tên tôi là:</div>
						<div class="font-semibold">{{ billForm?.controls['fullName'].value }}</div>
						<div>Số điện thoại:</div>
						<div class="font-semibold">{{ billForm?.controls['phone'].value }}</div>
						<div>Địa chỉ email của tôi là:</div>
						<div class="font-semibold">{{ billForm?.controls['email'].value }}</div>
						<div>Tôi đang làm việc tại:</div>
						<div class="font-semibold">{{ billForm?.controls['companyName'].value }}</div>
						<div class="col-span-full">
							Vào lúc <span class="font-semibold">{{ noticeTime | date : 'HH:mm, dd/MM/yyyy'}}</span>, tôi đã đọc và
							đồng ý
							với thỏa thuận về bảo vệ
							dữ
							liệu cá nhân do Công ty Công nghệ thông tin VNPT - Chi nhánh Tập đoàn Bưu chính Viễn thông Việt Nam
							(VNPT-IT)
							ban hành.
						</div>
					</div>
					<b>Nội dung thỏa thuận về bảo vệ dữ liệu cá nhân như sau:</b>
					<div id="1" class="mb-3">
						<div class="font-semibold">Điều 1. Định nghĩa và giải thích</div>
						Trừ khi được định nghĩa theo một cách khác trong Thoả Thuận này, các thuật ngữ được viết hoa và các cách
						diễn
						đạt được sử dụng trong Thỏa Thuận này sẽ có nghĩa như sau:
						<br>
						<span class="font-semibold">1.1. “Thỏa Thuận”</span> có nghĩa là Thỏa Thuận Xử lý dữ liệu cá nhân này và
						tất cả các bản chỉnh sửa, bổ sung kèm theo (nếu có).
						<br>
						<span class="font-semibold">1.2. “Dữ Liệu Cá Nhân”</span> có nghĩa là bất kỳ Dữ Liệu Cá Nhân nào được xử
						lý bởi Bên Kiểm soát dữ liệu cá nhân
						theo Thỏa Thuận Chính hoặc liên quan đến Thỏa Thuận Chính.
						<br>
						<span class="font-semibold">1.3</span>. Các thuật ngữ <span class="font-semibold">“Bên Kiểm Soát Dữ
							Liệu”, “Chủ Thể Dữ Liệu”,
							“Dữ Liệu Cá Nhân”, “Xử lý dữ liệu cá nhân”</span>
						được
						định nghĩa tại Nghị định 13/2023/NĐ-CP do Chính phủ ban hành ngày 17/04/2023 về bảo vệ dữ liệu cá nhân và
						các
						bản sửa đổi, bổ sung kèm theo (nếu có).
					</div>
					<div class="mb-3">
						<div class="font-semibold">Điều 2: Mục đích xử lý dữ liệu cá nhân và thời gian lưu trữ xử lý dữ liệu cá
							nhân</div>
						<span class="font-semibold">2.1.</span> Mục đích xử lý dữ liệu cá nhân:
						<ul class="list-disc list-inside ml-2">
							<li>Xác minh tính chính xác, đầy đủ của các thông tin được Khách hàng cung cấp; xác định hoặc xác thực
								danh
								tính của Khách hàng và thực hiện quy trình xác thực Khách hàng</li>
							<li>
								Quản lý và đánh giá các hoạt động kinh doanh bao gồm thiết kế, cải tiến và nâng cao chất lượng các sản
								phẩm, dịch vụ của VNPT hoặc thực hiện các hoạt động truyền thông tiếp thị; Thực hiện nghiên cứu thị
								trường, khảo sát và phân tích dữ liệu liên quan đến sản phẩm, dịch vụ của VNPT nhằm đáp ứng nhu cầu của
								Khách hàng
							</li>
							<li>Lưu trữ các log giao dịch trong quá trình cung cấp dịch vụ VNPT Smart Reader, có thể tra cứu lại được
								các thông tin dữ liệu xử lý nhằm phục vụ tra cứu lỗi và xử lý khiếu nại khi có yêu cầu</li>
							<li>Ngăn chặn gian lận hoặc giảm thiểu mối đe dọa đối với tính mạng, sức khỏe của người khác và lợi ích
								công cộng: VNPT có thể sử dụng thông tin cá nhân của Khách hàng để ngăn chặn và phát hiện gian lận, lạm
								dụng nhằm bảo vệ Khách hàng, VNPT/Công ty con của VNPT và các chủ thể liên quan</li>
							<li>Lập các báo cáo liên quan nếu Pháp luật quy định/yêu cầu</li>
							<li>Các mục đích khác có liên quan đến các mục đích nêu trên</li>
						</ul>
						<span class="font-semibold">2.2.</span> Thời gian lưu trữ dữ liệu xử lý dữ liệu cá nhân:
						VNPT Smart Reader chỉ thực hiện lưu trữ Dữ liệu cá nhân của Khách hàng trong khoảng thời gian cần thiết và
						hợp
						lý để hoàn thành các Mục Đích quy định tại Thỏa thuận này. Tuy nhiên, trường hợp pháp luật hiện hành có quy
						định khác về thời hạn lưu trữ Dữ liệu cá nhân, VNPT Smart Reader có nghĩa vụ tuân thủ quy định của pháp
						luật.

					</div>
					<div class="mb-3">
						<div class="font-semibold">Điều 3: Loại dữ liệu xử lý và cách thức xử lý dữ liệu cá nhân</div>
						<span class="font-semibold">3.1.</span> Các dữ liệu cá nhân được xử lý bao gồm:
						<ul class="list-disc list-inside ml-2">
							<li>Dữ liệu cá nhân cơ bản: thông tin trên Giấy tờ tùy thân, email, số điện thoại, công ty, lĩnh vực, hình
								ảnh chân dung, mã số thuế, thông tin về tài khoản số của cá nhân</li>
						</ul>
						<span class="font-semibold">3.2.</span> Cách thức xử lý dữ liệu cá nhân:
						<ul class="list-disc list-inside ml-2">
							<li>Chỉ thực hiện xử lý dữ liệu cá nhân trong quá trình cung cấp dịch vụ VNPT Smart Reader khi có sự đồng
								ý
								của chủ thể dữ liệu</li>
							<li>Trường hợp chủ thể dữ liệu yêu cầu xóa dữ liệu cá nhân, VNPT Smart Reader sẽ phải xác minh thông tin
								và
								thực hiện yêu cầu xóa dữ liệu trong vòng 48 giờ kể từ khi nhận được yêu cầu</li>
							<li>VNPT Smart Reader với vai trò là Bên kiểm soát dữ liệu cá nhân cam kết thực hiện các trách nhiệm được
								quy định tại Điều 38 - Nghị định 13/2023/NĐ-CP</li>
						</ul>
					</div>
					<div class="mb-3">
						<div class="font-semibold">Điều 4. Bảo vệ dữ liệu</div>
						<ul class="list-disc list-inside ml-2">
							<li>VNPT Smart Reader sẽ thực hiện các biện pháp kỹ thuật và có cách thức triển khai xử lý dữ liệu cá nhân
								phù hợp đảm bảo việc kiểm soát và xử lý dữ liệu cá nhân</li>
							<li>Tuân thủ quy định của Pháp Luật Việt Nam về bảo vệ
								dữ liệu cá nhân</li>
							<li>Hạn chế tối đa khả năng xảy ra hành vi vi phạm và giảm thiểu mức độ nghiêm trọng của hành vi vi phạm
								các quy định về bảo vệ dữ liệu cá nhân</li>
						</ul>
					</div>
				</div>
				<div class="mt-[14px] flex gap-2 items-baseline">
					<input [(ngModel)]="confirm" class="form-check-input" type="checkbox" id="confirm">
					<label class="form-check-label" for="confirm">Tôi đã đọc và đồng ý với <a target="_blank"
							class="font-bold text-brand-1" href="https://vnptai.io/ldp/smartreader/vi/term-of-use">Điều khoản sử dụng</a> và
						<a target="_blank" class="font-bold text-brand-1" href="https://vnptai.io/ldp/smartreader/vi/policy">Chính sách
							bảo mật</a> của VNPT</label>
				</div>
				<div class="flex justify-center">
					<button (click)="pay()" [disabled]="!confirm" class="btn btn-primary text-center"
						class="w-[224px] h-[42px] mt-2 text-white bg-brand-1 rounded-md" [ngClass]="{'opacity-60': !confirm}">Xác
						Nhận</button>
				</div>
			</div>
			<div class="w-[130px] text-[#757575] flex flex-col gap-[12px]">
				<div><a><b>Điều 1</b>: Định nghĩa và giải thích</a></div>
				<div>
					<a><b>Điều 2</b>: Mục đích xử lý dữ liệu cá nhân và thời gian lưu trữ xử lý dữ liệu cá nhân</a>
				</div>
				<div><a><b>Điều 3</b>: Loại dữ liệu xử lý và cách thức xử lý dữ liệu cá nhân</a></div>
				<div><a><b>Điều 4</b>: Bảo vệ dữ liệu</a></div>
			</div>
		</div>
	</div>
</ng-template>