<nz-table
  class="mt-4"
  #rowSelectionTable
  [nzTemplateMode]="true"
  nzShowPagination="false"
  nzShowSizeChanger
  [nzFrontPagination]="false"
  [nzData]="listSharedDocuments.documents"
  [nzTableLayout]="'fixed'"
>
  <thead>
    <tr>
      <th>Tên văn bản</th>
      <th>Chủ sở hữu</th>
      <th nzWidth="200px">Vai trò của bạn</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let item of rowSelectionTable.data">
      <td>
        <div
          [routerLink]="['/', 'key-information-extractor', 'document', item.id]"
          class="text-text-1 font-medium truncate break-words hover:text-brand-2 hover:cursor-pointer"
        >
          <img
            *ngIf="item.isSelfConfig"
            src="assets/kie/document/is-self-config-badge.svg"
            class="inline-block mr-1"
            alt=""
          />{{ item.name }}
        </div>
      </td>
      <td>
        <div class="flex items-center gap-2">
          <nz-avatar
            [nzText]="item.creator.name.charAt(0)"
            class="shrink-0 text-white bg-[#ff3355] capitalize"
          ></nz-avatar>
          <div class="text-brand-2 text-sm font-medium truncate break-words">
            {{ item.creator.name }}
          </div>
        </div>
      </td>
      <td>
        <p>{{ viewRole(item.role) }}</p>
      </td>
    </tr>
  </tbody>
</nz-table>
<div
  *ngIf="!listSharedDocuments.documents.length"
  class="w-full min-h-[500px] flex flex-col gap-5 items-center justify-center bg-[linear-gradient(0deg,rgba(255,255,255,1)50%,rgba(251,251,252,1)100%)]"
>
  <img src="assets/kie/document/empty-table.svg" />
</div>

<nz-pagination
  class="mt-4"
  [nzShowTotal]="rangeTemplate"
  [(nzTotal)]="listSharedDocuments.total"
  [(nzPageSize)]="listSharedDocuments.limit"
  [(nzPageIndex)]="listSharedDocuments.page"
  (nzPageIndexChange)="handleChangePage($event)"
  [nzItemRender]="renderItemTemplate"
>
</nz-pagination>
<ng-template #rangeTemplate let-range="range" let-total>
  <div>
    Số bản ghi mỗi trang
    <nz-select
      class="w-[65px]"
      [ngModel]="listSharedDocuments.limit || 10"
      (ngModelChange)="handleChangeLimit($event)"
    >
      <nz-option [nzValue]="10" nzLabel="10"></nz-option>
      <nz-option [nzValue]="20" nzLabel="20"></nz-option>
      <nz-option [nzValue]="30" nzLabel="30"></nz-option>
    </nz-select>
  </div>
</ng-template>
<ng-template #renderItemTemplate let-type let-page="page">
  <ng-container [ngSwitch]="type">
    <a *ngSwitchCase="'page'">{{ page }}</a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'prev'">
      <img src="assets/kie/header-table/previous_page.svg" alt="prev-icon" />
    </a>
    <a class="grid h-full place-content-center" *ngSwitchCase="'next'">
      <img src="assets/kie/header-table/next_page.svg" alt="next-icon" />
    </a>
    <a class="btn-dot" *ngSwitchCase="'prev_5'">...</a>
    <a class="btn-dot" *ngSwitchCase="'next_5'">...</a>
  </ng-container>
</ng-template>
