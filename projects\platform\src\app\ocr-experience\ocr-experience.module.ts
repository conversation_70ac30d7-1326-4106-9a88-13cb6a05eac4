import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { OcrExperienceRoutingModule } from './ocr-experience-routing.module';
import { OcrInputComponent } from './ocr-input/ocr-input.component';
import { OcrPreviewComponent } from './components/ocr-preview/ocr-preview.component';
import { OcrResultComponent } from './ocr-result/ocr-result.component';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { FileInputComponent } from './components/file-input/file-input.component';
import { NgxCaptchaModule } from 'ngx-captcha';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TranslocoModule } from '@ngneat/transloco';
import { DongHoNuocComponent } from './dong-ho-nuoc/dong-ho-nuoc.component';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { ScanTableExportModalComponent } from './components/scan-table-export-modal/scan-table-export-modal.component';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { DocumentViewerComponent } from '@platform/app/components/document-viewer/document-viewer.component';
import { ImagesIntoPdfComponent } from '../components/images-into-pdf/images-into-pdf.component';
import { DocumentCompareComponent } from '../components/document-compare/document-compare.component';
import { DocumentReproductionComponent } from '../components/document-reproduction/document-reproduction.component';

@NgModule({
  declarations: [
    OcrInputComponent,
    OcrPreviewComponent,
    OcrResultComponent,
    FileInputComponent,
    DongHoNuocComponent,
    ScanTableExportModalComponent
  ],
  imports: [
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    OcrExperienceRoutingModule,
    PdfViewerModule,
    NgxCaptchaModule,
    NzToolTipModule,
    TranslocoModule,
    NzSelectModule,
    NzDropDownModule,
    NzButtonModule,
    NzSwitchModule,
    NzInputModule,
    NzPopoverModule,
    NzCheckboxModule,
    DocumentViewerComponent,
    ImagesIntoPdfComponent,
    DocumentCompareComponent,
    DocumentReproductionComponent
  ],
  exports: [OcrPreviewComponent]
})
export class OcrExperienceModule {}
