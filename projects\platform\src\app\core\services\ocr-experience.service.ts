import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import UtilsService from '@platform/app/core/services/utils.service';
import { environment as env } from '@platform/environment/environment';
import { FunctionType, Template } from '../../ocr-experience/ocr-experience';
import { map } from 'rxjs';

type AddFileResult = {
  message: string;
  object: {
    hash: string;
    fileType: string;
    [key: string]: any;
  };
};

type AddFileSplitResult = {
  message: string;
  object: AddFileResult[];
};

interface OcrResult {
  object: {
    [key: string]: any;
  };
}

@Injectable({
  providedIn: 'root'
})
export class OcrExperienceService {
  private baseUrl = env.backendUrl + 'idg-api';

  constructor(
    private http: HttpClient,
    private utilsService: UtilsService
  ) {}

  addFile({
    body,
    isSplitting,
    isLandingPageMode,
    captcha,
    captchaV3,
    skipAppLoadingSpinner
  }: {
    body: FormData;
    isSplitting?: boolean;
    isLandingPageMode: boolean;
    captcha?: string;
    captchaV3?: string;
    skipAppLoadingSpinner?: boolean;
  }) {
    let headers = new HttpHeaders();
    let api =
      this.baseUrl +
      (isLandingPageMode ? '/ldp' : '') +
      (isSplitting ? '/add-file-split' : '/file-service/addFile');

    if (!isLandingPageMode) {
      headers = skipAppLoadingSpinner
        ? this.utilsService.headersWithSkipAppLoadingSpinner
        : this.utilsService.headers;
    } else {
      if (captcha) headers = headers.set('captcha', captcha ?? '');
      else if (captchaV3) headers = headers.set('captcha-v3', captchaV3 ?? '');
    }

    return this.http.post<AddFileResult | AddFileSplitResult>(api, body, {
      headers
    });
  }

  ocr({
    fileHash,
    fileType,
    templateType,
    devModeEnabled,
    count = 2,
    isLandingPageMode,
    captcha = ''
  }: {
    fileHash: string | string[];
    fileType: 'pdf' | 'png' | 'jpg' | 'jpeg' | 'heic' | 'pdf-captured' | string; // TODO: future support for heic
    templateType: Template;
    devModeEnabled?: boolean;
    count: number;
    isLandingPageMode: boolean;
    captcha?: string;
  }) {
    let headers;
    let api: string = '';
    const body: any = {
      token: '',
      client_session: this.utilsService.encryptMessage(
        JSON.stringify({ time: +new Date(), count })
      ),
      file_type: fileType,
      details: true
    };
    templateType.allowDevMode && (body.dev = devModeEnabled);

    if (!isLandingPageMode) {
      headers = this.utilsService.headers;
    } else {
      headers = new HttpHeaders({ 'captcha-v3': captcha });
      api = 'ldp/';
    }

    switch (templateType.functionType) {
      case FunctionType.SoHoaVanBan: {
        const isOcrMultipleHashes = !(typeof fileHash === 'string');
        api += `${isOcrMultipleHashes ? 'ocr-multiple' : 'ocr'}/${templateType.value}`;
        body.file_hashes = fileHash;
        break;
      }
      case FunctionType.TrichXuatThongTin: {
        api += `ocr/${templateType.value}`;
        body.file_hash = fileHash;
        break;
      }
      case FunctionType.GoiYXuLyVanBan: {
        api += `ocr/${templateType.value}`;
        body.file_hash = fileHash;
        body.additional_info = {
          m_origin: '',
          m_number: '',
          left_m_cited: ''
        };
        break;
      }
    }

    return this.http.post<OcrResult>(`${this.baseUrl}/${api}`, body, {
      headers
    });
  }

  fetchFile(url) {
    return this.http.get(url, { responseType: 'blob' });
  }

  ocrDongHoNuoc({
    fileHash,
    fileType
  }: {
    fileHash: string | string[];
    fileType: string;
    // templateType: Template;
    // count: number;
    // isLandingPageMode: boolean;
    // captcha?: string;
  }) {
    const api: string = '/ocr/dong-ho-nuoc';
    const body: any = {
      details: true,
      file_type: fileType,
      file_hash: fileHash
    };

    return this.http.post<OcrResult>(this.baseUrl + api, body, {
      headers: this.utilsService.headers
    });
  }
}
