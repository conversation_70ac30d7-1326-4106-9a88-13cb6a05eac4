<div class="dashboard-rpa">
	<div class="font-bold text-xl">
		<PERSON><PERSON><PERSON> sử thanh toán
	</div>
	<div class="text-center p-5 mt-5 bg-white rounded-lg" style="background-color: #fff; border-radius: 8px;">
		<div class="flex items-center gap-[30px]">
			<div class="flex items-center gap-[10px]">
				<span>Thời gian thanh toán</span>
				<nz-date-picker [nzLocale]="NzLocaleDatePicker" nzFormat="dd/MM/yyyy" [(ngModel)]="startDate"></nz-date-picker>
				<nz-date-picker [nzLocale]="NzLocaleDatePicker" nzFormat="dd/MM/yyyy" [(ngModel)]="endDate"></nz-date-picker>
			</div>
			<div class="flex items-center gap-[10px]">
				<span>Trạng thái</span>
				<nz-select class="min-w-[180px]" [(ngModel)]="status">
					<nz-option *ngFor="let option of statuses" [nzValue]="option" [nzLabel]="option.label"></nz-option>
				</nz-select>
			</div>
			<button class="bg-brand-1 rounded-md text-white p-2" type="button" (click)="handleFilterBtnClick()">Tìm
				kiếm</button>
		</div>
		<div class="my-5">
			<nz-table (nzQueryParams)="onQueryParamsChange($event)" [nzTotal]="countPayment" [nzPageSize]="pageSize"
				[(nzPageIndex)]="pageIndex" [nzFrontPagination]="false" nzBordered [nzData]="listPayment" #listPaymentTable>
				<thead>
					<tr>
						<th class="!bg-[#005197] !text-white !font-bold">No.</th>
						<th class="!bg-[#005197] !text-white !font-bold">Mã thanh toán</th>
						<th class="!bg-[#005197] !text-white !font-bold">Tên gói</th>
						<th class="!bg-[#005197] !text-white !font-bold">Thời gian thanh toán</th>
						<th class="!bg-[#005197] !text-white !font-bold">Loại</th>
						<th class="!bg-[#005197] !text-white !font-bold">Trạng thái TT</th>
						<th class="!bg-[#005197] !text-white !font-bold">Tiền tệ</th>
						<th class="!bg-[#005197] !text-white !font-bold">VAT (%)</th>
						<th class="!bg-[#005197] !text-white !font-bold">Trước thuế</th>
						<th class="!bg-[#005197] !text-white !font-bold">Thuế</th>
						<th class="!bg-[#005197] !text-white !font-bold">Sau thuế</th>
						<th class="!bg-[#005197] !text-white !font-bold">Lấy hóa đơn</th>
					</tr>
				</thead>
				<tbody>
					<!-- <tr *ngIf="!listPaymentTable.data.length">
						<td colspan="12">Không có lịch sử thanh toán</td>
					</tr> -->
					<tr *ngFor="let payment of listPaymentTable.data; let i = index">
						<td>{{i + 1}}</td>
						<td>{{payment?.uuidOrderTransacion}}</td>
						<td>{{payment?.planName}}</td>
						<td>{{payment?.orderDate}}</td>
						<td>{{getOrderType(payment?.orderType)}}</td>
						<td>{{getOrderStatus(payment?.orderStatus)}}</td>
						<td>{{payment?.currencyCode}}</td>
						<td>{{payment?.vatRate}}%</td>
						<td>{{formatAmount(payment?.total)}}</td>
						<td>{{formatAmount(payment?.vatAmount)}}</td>
						<td>{{formatAmount(payment?.totalAmount)}}</td>
						<td>{{payment?.exportInvoice === 1 ? 'Có' : 'Không'}}</td>
					</tr>
				</tbody>
			</nz-table>
			<app-pagination *ngIf="listPayment.length" [itemsPerPage]="pageSize" [totalItems]="countPayment"
				(change)="page = $event"></app-pagination>
		</div>
	</div>
</div>