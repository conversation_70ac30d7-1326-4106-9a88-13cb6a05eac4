<div class="flex-shrink-0 border-r border-line w-[300px] overflow-auto">
  <ng-container *ngFor="let docs of docsConfigList">
    <ng-template
      *ngTemplateOutlet="pageItem; context: { key: docs.key, title: docs.title }"
    >
    </ng-template>
  </ng-container>
</div>
<div class="flex-auto h-full overflow-auto flex scroll-smooth" #docsCtn>
  <div id="docs-content" class="docs-content flex-auto h-fit p-6 overflow-auto">
    <div class="text-[40px] font-bold mb-2">{{ docsConfig.title }}</div>
    <markdown
      (ready)="handleMarkdownReady()"
      (error)="(null)"
      [srcRelativeLink]="true"
      [src]="docsConfig?.src"
    />
  </div>
  <ul
    id="docs-toc"
    #docsToc
    class="docs-toc flex-shrink-0 border-l border-line w-[280px] sticky top-0 overflow-auto"
  >
    <div class="text-xs text-icon-2 p-4 pb-2">{{ docsConfig.title }}</div>
    <li *ngFor="let header of extractedHeaders">
      <a
        [href]="header.href"
        (click)="scrollTo(header.id)"
        class="block py-1 pr-4 border-l-[3px] border-transparent text-xs font-medium text-text-1"
        [ngClass]="{
          'pl-4': header.level === 1,
          'pl-8': header.level === 2,
          'pl-12': header.level === 3,
          'pl-16': header.level === 4,
          'pl-20': header.level === 5,
          'pl-24': header.level === 6
        }"
      >
        {{ header.text }}
      </a>
    </li>
  </ul>
</div>

<ng-template #pageItem let-key="key" let-title="title">
  <a
    [ngClass]="{
      '!border-r-brand-1 bg-[#E6F1FE] !font-semibold': key === docsConfig?.key
    }"
    class="block mt-2 text-base font-medium px-6 py-2 border-r-[3px] border-r-transparent hover:bg-[#E6F1FE]"
    [routerLink]="['/', 'docs', key]"
  >
    {{ title }}
  </a>
</ng-template>
