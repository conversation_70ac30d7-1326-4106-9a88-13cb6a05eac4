:host {
	--document-viewer-bg-color: #202225;
	--document-viewer-text-color: #fff;
	--document-viewer-header-bg-color: #414346;
	--document-viewer-header-text-color: #fff;

	@media (prefers-color-scheme: light) {
		background-size: contain;
		background-color: white;
		background-image: linear-gradient(rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.024) 5.26%,
				rgba(255, 255, 255, 0.055) 9.79%,
				rgba(255, 255, 255, 0.098) 13.69%,
				rgba(255, 255, 255, 0.145) 17.08%,
				rgba(255, 255, 255, 0.2) 20.04%,
				rgba(255, 255, 255, 0.267) 22.7%,
				rgba(255, 255, 255, 0.333) 25.16%,
				rgba(255, 255, 255, 0.408) 27.51%,
				rgba(255, 255, 255, 0.486) 29.87%,
				rgba(255, 255, 255, 0.57) 32.34%,
				rgba(255, 255, 255, 0.65) 35.02%,
				rgba(255, 255, 255, 0.737) 38.02%,
				rgba(255, 255, 255, 0.824) 41.45%,
				rgba(255, 255, 255, 0.914) 45.41%,
				rgb(255, 255, 255) 50%),
			url('/assets/kie/document/bg.png');
	}

	::ng-deep {
		/* document-layout takes all remaining space */
		// display: flex;
		// flex: auto;

		// /* fix height with remaining space, make overflow children scroll inside document-layout */
		// height: 1px;
		@apply grid grid-cols-2 flex-auto gap-5 py-4 px-6;

	}
}

:host .ocr-tabs-container ::ng-deep>.ant-tabs-default {
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	// background-color: #fff;

	::-webkit-scrollbar-thumb {
		border-radius: 10px;
		background-color: #cbcbcb;
	}

	::-webkit-scrollbar {
		width: 8px;
		background-color: transparent;
	}

	>.ant-tabs-nav {
		// padding: 12px 24px 12px 24px;
		margin-bottom: 0;

		&::before {
			display: none;
		}

		>.ant-tabs-nav-wrap {
			margin-bottom: 12px;

			>.ant-tabs-nav-list {
				flex: auto;
				justify-content: space-between;
				padding: 6px 12px 6px 12px;
				background-color: #E7EBEF;
				border-radius: 46px;

				>.ant-tabs-ink-bar {
					display: none;
				}

				>.ant-tabs-tab {
					min-width: 195px;
					justify-content: center;
					padding: 10px 0 10px 0;
					font-size: 12px;
					font-weight: 600;
					color: #2B2D3B;

					+.ant-tabs-tab {
						margin: 0;
					}

					.ant-tabs-tab-btn {
						font-weight: 700;
						color: #111127;
					}

					&.ant-tabs-tab-active {
						background-color: #fff;
						border-radius: 26px;
						box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.10);
						color: #0667E1;

						.ant-tabs-tab-btn {
							color: #0F67CE;
						}
					}
				}
			}
		}
	}

	.ant-tabs-content {
		height: 100%;
	}
}