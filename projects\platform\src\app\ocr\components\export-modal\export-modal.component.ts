import { Component, inject, OnDestroy } from '@angular/core';
import { ExportService } from '@platform/app/core/services/export.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import {
  catchError,
  concatMap,
  defer,
  EMPTY,
  finalize,
  from,
  of,
  switchMap,
  takeUntil,
  tap,
  toArray
} from 'rxjs';
import { ajax, AjaxError, AjaxResponse } from 'rxjs/ajax';

@Component({
  selector: 'app-export-modal',
  templateUrl: './export-modal.component.html',
  styleUrls: ['./export-modal.component.scss']
})
export class ExportModalComponent implements OnDestroy {
  readonly nzModalData: {
    files: { exportedInJSONLink: string; name: string; file: File; link: string }[];
  } = inject(NZ_MODAL_DATA);
  files = this.nzModalData.files;
  exportType: 'docx' | 'xlsx' | 'json';
  xlsxMapPageToSheet = false;
  progressFileIndex = 0;

  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('export-modal-loading');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('export-modal-loading');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }
  destroy$;

  constructor(
    private modal: NzModalRef,
    private exportService: ExportService,
    private toastr: ToastrService,
    private spinner: NgxSpinnerService
  ) {}

  closeExportModal() {
    this.modal.close();
  }

  selectExportType(type: typeof this.exportType) {
    this.exportType = type;
  }

  async export() {
    if (!this.exportType) return this.toastr.error('Lựa chọn 1 kiểu tải về');
    if (this.exportType === ('jsonx' as any)) {
      /* let file-saver lib to download file directly from url */
      /* pros: download directly json file from server minio, easier on the performance since no actual file content must be downloaded and parsed */
      /* cons: popup, popup prevention, if popup are allowed, new tab are created and being switched to, only in order to download file, quite bad UX */
      for (const file of this.files)
        await this.exportService.exportJSON(file.exportedInJSONLink);
    } else
    /* fetch the data from exportedInJSONLink */
      from(this.files)
        .pipe(
          takeUntil(this.modal.afterClose),
          concatMap((file, index) => {
            this.loading = true;
            this.progressFileIndex = index + 1;
            return ajax({
              url: file.exportedInJSONLink,
              responseType: this.exportType === 'json' ? 'blob' : 'json'
            }).pipe(
              catchError((error) => of(error)),
              switchMap((resp: AjaxResponse<any> | AjaxError) => {
                if (resp instanceof AjaxError) {
                  this.toastr.error(
                    `Xuất file ${file.name} theo định dạng ${this.exportType} thất bại`
                  );
                  return EMPTY;
                }

                switch (this.exportType) {
                  case 'docx':
                    return defer(async () => {
                      return this.exportService.exportDocxScanTable({
                        fileLink: file.link,
                        fileType: file.name.split('.').pop() as any,
                        fileName: file.name,
                        ocrResult: resp.response?.object,
                        outputType: 'file',
                        isFigureIncluded: true
                      });
                    });
                  case 'xlsx':
                    return defer(() =>
                      this.exportService.exportXlsxScanTableWithExcelJS({
                        fileLink: file.link,
                        fileType: file.name.split('.').pop() as any,
                        fileName: file.name,
                        ocrResult: resp.response?.object,
                        pageSelectionList: undefined,
                        mapPageToSheet: this.xlsxMapPageToSheet,
                        isFigureIncluded: true,
                        outputType: 'file'
                      })
                    );
                  case 'json':
                    return defer(() =>
                      this.exportService.exportJSON(resp.response, file.name)
                    );
                  default:
                    return EMPTY;
                }
              }),
              finalize(() => (this.loading = false))
            );
          }),
          finalize(() => (this.progressFileIndex = 0))
          // toArray(),
          // tap((jsonRespList) => {
          //   console.log(jsonRespList);
          // })
        )
        .subscribe();
  }

  ngOnDestroy(): void {
    // this.destroy$.next
  }
}
