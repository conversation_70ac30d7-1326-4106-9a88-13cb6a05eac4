<div class="flex flex-col items-center gap-2">
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="emails-list"
      >Email <span class="text-red-500">*</span></label
    >
    <div class="w-full">
      <nz-select
        [ngModel]="[]"
        (ngModelChange)="handleFilterAssignee($event)"
        class="rounded-lg w-full"
        nzPlaceHolder="Nhập email"
        nzShowSearch
        nzSize="large"
        nzMode="multiple"
        nzOptionHeightPx="36"
        (nzOnSearch)="searchUser($event)"
      >
        <nz-option
          *ngFor="let user of listUser"
          nzCustomContent
          [nzLabel]="user.name"
          [nzValue]="user.id"
        >
          <nz-avatar
            [nzSize]="24"
            [nzText]="user.name.charAt(0) || 'null'"
            class="text-white bg-[#ff3355] uppercase text-center"
            >D</nz-avatar
          >
          {{ user.name }}
        </nz-option>
      </nz-select>
    </div>
  </div>
  <div class="flex flex-col gap-2 w-full">
    <label class="text-sm font-medium" for="role">Vai trò</label>
    <nz-select
      id="role"
      nzSize="large"
      [(ngModel)]="selectedRole"
      nzPlaceHolder="Chọn vai trò"
    >
      <nz-option
        *ngFor="let role of roles"
        [nzValue]="role.value"
        [nzLabel]="role.label"
      ></nz-option>
    </nz-select>
  </div>
  <button
    (click)="inviteMembers()"
    class="rounded-lg flex items-center px-3 gap-2 py-2 bg-brand-1 text-white text-sm font-medium"
  >
    <i nz-icon nzType="user-add" nzTheme="outline" class="text-md translate-y-[-2px]"></i>
    Mời
  </button>
</div>
