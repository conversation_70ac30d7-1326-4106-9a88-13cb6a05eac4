<div class="col-span-full xl:col-span-1 ocr-tabs-container">
  <nz-tabset
    class="h-full"
    [nzSelectedIndex]="selectedTabIndex"
    nzLinkRouter
    (nzSelectedIndexChange)="(null)"
    (nzSelectChange)="(null)"
  >
    <nz-tab *ngFor="let tab of tabs; let i = index">
      <a *nzTabLink nz-tab-link [routerLink]="['/', 'ocr', tab.key]" class="uppercase">
        <span class="">{{ i + 1 }} - </span>
        {{ tab.title }}
        <nz-badge
          [nzOffset]="[2, -4]"
          [nzOverflowCount]="tab.badgeOverflowCount"
          nzStandalone
          [nzCount]="fileListCount[tab.key]"
        >
        </nz-badge>
      </a>
      <ng-container [ngSwitch]="tab.key">
        <ng-container *ngSwitchCase="'preprocessing'">
          <ng-template nz-tab>
            <app-ocr-preprocessing
              (fileChange)="fileSubject.next($event)"
              [afterLoadCompleteFileInfo]="afterLoadCompleteFileInfo$ | async"
              [editedPageImage]="editedPageImage$ | async"
            ></app-ocr-preprocessing>
          </ng-template>
        </ng-container>
        <ng-container *ngSwitchCase="'in-progress'">
          <ng-template nz-tab>
            <app-ocr-in-progress
              (fileChange)="fileSubject.next($event)"
              [afterLoadCompleteFileInfo]="afterLoadCompleteFileInfo$ | async"
            ></app-ocr-in-progress>
          </ng-template>
        </ng-container>
        <ng-container *ngSwitchCase="'finished'">
          <ng-template nz-tab>
            <app-ocr-finished
              (fileChange)="fileSubject.next($event)"
              [afterLoadCompleteFileInfo]="afterLoadCompleteFileInfo$ | async"
            ></app-ocr-finished>
          </ng-template>
        </ng-container>
        <ng-container *ngSwitchDefault> Invalid tab key: {{ tab.key }} </ng-container>
      </ng-container>
    </nz-tab>
  </nz-tabset>
</div>
<div class="col-span-full xl:col-span-1 flex flex-col">
  <ng-container *ngIf="fileSubject.value">
    <ng-container *ngIf="selectedTabIndex !== OcrTab.Finished">
      <ng-template *ngTemplateOutlet="nonFinishedDocumentView"></ng-template>
    </ng-container>
    <ng-container *ngIf="selectedTabIndex === OcrTab.Finished">
      <ng-template *ngTemplateOutlet="finishedDocumentView"></ng-template>
    </ng-container>
  </ng-container>

  <ng-container *ngIf="!fileSubject.value">
    <ng-template *ngTemplateOutlet="emptyView"></ng-template>
  </ng-container>
</div>

<ng-template #tabLeaveModal let-params>
  <div class="flex flex-col gap-2 items-center">
    <svg
      width="81"
      height="80"
      viewBox="0 0 81 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="40.5" cy="40" r="40" fill="#FFF8E7" />
      <ellipse cx="40.5" cy="40" rx="27" ry="27" fill="url(#paint0_linear_12089_39775)" />
      <rect x="37.5" y="25" width="6" height="21" rx="3" fill="white" />
      <circle cx="40.5" cy="52" r="3" fill="white" />
      <defs>
        <linearGradient
          id="paint0_linear_12089_39775"
          x1="32.7273"
          y1="15.2689"
          x2="63.6947"
          y2="64.232"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FCC22D" />
          <stop offset="1" stop-color="#FF7A00" />
        </linearGradient>
      </defs>
    </svg>
    <div class="font-semibold text-lg text-[#282D57]">Lưu ý</div>
    <div class="font-medium text-center mb-1">{{ params.message }}</div>

    <button
      nz-tooltip
      nzTooltipTitle="Thông báo này sẽ không còn được hiển thị lại trong phiên đăng nhập hiện tại"
      nzTooltipPlacement="bottom"
      class="border border-[#0667e1] bg-[#0667e1] text-white font-medium flex items-end gap-2 py-1 px-[14px] rounded-md"
      (click)="params?.ok()"
    >
      Đã hiểu
    </button>
  </div>
</ng-template>

<ng-template #nonFinishedDocumentView>
  <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
  <app-document-viewer
    class="h-full min-h-[650px]"
    [featureFlags]="documentViewerFF"
    [fileName]="fileSubject.value.fileName"
    [fileLink]="fileSubject.value.fileLink"
    [specifiedPages]="fileSubject.value.pages"
    (afterFileLinkLoaded)="afterLoadComplete($event)"
    (onSpecifiedPagesValidation)="onSpecifiedPagesValidation($event)"
    (onEditPageImage)="onEditPageImage($event)"
  ></app-document-viewer>
</ng-template>

<ng-template #finishedDocumentView>
  <nz-radio-group
    class="h-auto w-full grid grid-cols-2"
    [(ngModel)]="tabFinishedViewMode"
    nzButtonStyle="solid"
    nzSize="large"
  >
    <label
      class="!shadow-none text-center font-medium !rounded-tl-lg"
      [ngClass]="{
        'font-semibold': tabFinishedViewMode === TabFinishedViewMode.Original
      }"
      nz-radio-button
      [nzValue]="TabFinishedViewMode.Original"
    >
      Văn bản gốc
    </label>
    <label
      class="!shadow-none text-center font-medium !rounded-tr-lg"
      [ngClass]="{
        'font-semibold': tabFinishedViewMode === TabFinishedViewMode.Reproduced
      }"
      nz-radio-button
      [nzValue]="TabFinishedViewMode.Reproduced"
      >Dựng lại văn bản
    </label>
  </nz-radio-group>
  <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
  <app-document-viewer
    *ngIf="tabFinishedViewMode === TabFinishedViewMode.Original"
    class="h-full min-h-[650px]"
    [featureFlags]="documentViewerFF"
    [fileName]="fileSubject.value.fileName"
    [fileLink]="fileSubject.value.fileLink"
    [specifiedPages]="fileSubject.value.pages"
    (afterFileLinkLoaded)="afterLoadComplete($event)"
    (onSpecifiedPagesValidation)="onSpecifiedPagesValidation($event)"
    (onEditPageImage)="onEditPageImage($event)"
  ></app-document-viewer>
  <app-ocr-result
    *ngIf="tabFinishedViewMode === TabFinishedViewMode.Reproduced"
    class="flex-1"
    [fileName]="fileSubject.value.fileName"
    [fileLink]="fileSubject.value.fileLink"
    [exportedInJSONLink]="fileSubject.value.exportedInJSONLink"
  >
  </app-ocr-result>
</ng-template>

<ng-template #emptyView>
  <div
    class="h-full border border-[#E7EBEF] rounded-lg bg-bg-1 flex flex-col items-center justify-center gap-3 text-icon-2 text-sm font-medium min-h-[650px]"
  >
    <svg
      width="140"
      height="140"
      viewBox="0 0 140 140"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M133.711 131.797H122.227C118.672 131.797 115.938 129.062 115.938 125.234V111.836C115.938 108.281 118.672 105.273 122.227 105.273H133.711C137.266 105.273 140 108.281 140 111.836V125.234C140 129.062 137.266 131.797 133.711 131.797ZM122.227 109.102C120.859 109.102 119.766 110.469 119.766 111.836V125.234C119.766 126.875 120.859 127.969 122.227 127.969H133.711C135.078 127.969 136.172 126.875 136.172 125.234V111.836C136.172 110.469 135.078 109.102 133.711 109.102H122.227Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M17.7734 34.7266H6.5625C3.00781 34.7266 0 31.7188 0 28.1641V14.7656C0 11.2109 3.00781 8.20312 6.5625 8.20312H17.7734C21.3281 8.20312 24.3359 11.2109 24.3359 14.7656V28.1641C24.3359 31.7188 21.3281 34.7266 17.7734 34.7266ZM6.5625 12.0312C5.19531 12.0312 3.82812 13.3984 3.82812 14.7656V28.1641C3.82812 29.5312 5.19531 30.8984 6.5625 30.8984H17.7734C19.1406 30.8984 20.5078 29.5312 20.5078 28.1641V14.7656C20.5078 13.3984 19.1406 12.0312 17.7734 12.0312H6.5625Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M17.7734 131.797H6.5625C3.00781 131.797 0 128.789 0 125.234V111.836C0 108.281 3.00781 105.273 6.5625 105.273H17.7734C21.3281 105.273 24.3359 108.281 24.3359 111.836V125.234C24.3359 128.789 21.6016 131.797 17.7734 131.797ZM6.5625 109.102C5.19531 109.102 3.82812 110.469 3.82812 111.836V125.234C3.82812 126.875 5.19531 127.969 6.5625 127.969H17.7734C19.1406 127.969 20.5078 126.875 20.5078 125.234V111.836C20.5078 110.469 19.1406 109.102 17.7734 109.102H6.5625Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M75.7422 34.7266H64.2578C60.7031 34.7266 57.9688 31.7188 57.9688 28.1641V14.7656C57.9688 11.2109 60.7031 8.20312 64.2578 8.20312H75.7422C79.2969 8.20312 82.0312 11.2109 82.0312 14.7656V28.1641C82.0312 31.7188 79.2969 34.7266 75.7422 34.7266ZM64.2578 12.0312C62.8906 12.0312 61.7969 13.3984 61.7969 14.7656V28.1641C61.7969 29.5312 62.8906 30.8984 64.2578 30.8984H75.7422C77.1094 30.8984 78.2031 29.5312 78.2031 28.1641V14.7656C78.2031 13.3984 77.1094 12.0312 75.7422 12.0312H64.2578Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M75.7422 131.797H64.2578C60.7031 131.797 57.9688 128.789 57.9688 125.234V111.836C57.9688 108.281 60.7031 105.273 64.2578 105.273H75.7422C79.2969 105.273 82.0312 108.281 82.0312 111.836V125.234C82.0312 128.789 79.2969 131.797 75.7422 131.797ZM64.2578 109.102C62.8906 109.102 61.7969 110.469 61.7969 111.836V125.234C61.7969 126.602 62.8906 127.969 64.2578 127.969H75.7422C77.1094 127.969 78.2031 126.602 78.2031 125.234V111.836C78.2031 110.469 77.1094 109.102 75.7422 109.102H64.2578Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M133.711 83.125H122.227C118.672 83.125 115.938 80.3906 115.938 76.8359V63.1641C115.938 59.6094 118.672 56.875 122.227 56.875H133.711C137.266 56.875 140 59.6094 140 63.1641V76.8359C140 80.3906 137.266 83.125 133.711 83.125ZM122.227 60.7031C120.859 60.7031 119.766 61.7969 119.766 63.1641V76.8359C119.766 78.2031 120.859 79.2969 122.227 79.2969H133.711C135.078 79.2969 136.172 78.2031 136.172 76.8359V63.1641C136.172 61.7969 135.078 60.7031 133.711 60.7031H122.227Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M101.172 96.5234H38.5547C35.2734 96.5234 32.5391 93.7891 32.5391 90.5078V49.4922C32.5391 46.2109 35.2734 43.4766 38.5547 43.4766H101.172C104.453 43.4766 106.914 46.2109 106.914 49.4922V90.5078C106.914 93.7891 104.453 96.5234 101.172 96.5234ZM38.5547 47.3047C37.4609 47.3047 36.3672 48.3984 36.3672 49.4922V90.5078C36.3672 91.6016 37.4609 92.6953 38.5547 92.6953H101.172C102.266 92.6953 103.086 91.6016 103.086 90.5078V49.4922C103.086 48.3984 102.266 47.3047 101.172 47.3047H38.5547Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M91.0549 96.5234C90.508 96.5234 90.2346 96.25 89.9611 95.9766L57.9689 70.2734L35.5471 88.0469C34.7267 88.8672 33.633 88.5938 33.0861 87.7734C32.2658 86.9531 32.5392 85.8594 33.3596 85.3125L56.8752 66.4453C57.4221 65.8984 58.5158 65.8984 59.3361 66.4453L92.1486 93.2422C92.9689 93.7891 93.2424 94.8828 92.6955 95.7031C92.1486 96.25 91.6017 96.5234 91.0549 96.5234Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M105 83.1243C104.453 83.1243 103.907 82.8508 103.633 82.5774L92.4222 70.2727L74.3753 81.7571C73.2816 82.304 72.1878 82.0305 71.641 81.2102C71.0941 80.1165 71.3675 79.0227 72.1878 78.4758L91.6019 66.1711C92.4222 65.6243 93.516 65.8977 94.0628 66.4446L106.368 79.843C107.188 80.6633 107.188 81.7571 106.368 82.5774C105.821 82.8508 105.547 83.1243 105 83.1243Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M72.4609 69.7266C67.5391 69.7266 63.7109 65.8984 63.7109 60.9766C63.7109 56.0547 67.5391 52.2266 72.4609 52.2266C77.1094 52.2266 80.9375 56.0547 80.9375 60.9766C80.9375 65.8984 77.1094 69.7266 72.4609 69.7266ZM72.4609 56.0547C69.7266 56.0547 67.5391 58.2422 67.5391 60.9766C67.5391 63.7109 69.7266 65.8984 72.4609 65.8984C74.9219 65.8984 77.1094 63.7109 77.1094 60.9766C77.1094 58.2422 74.9219 56.0547 72.4609 56.0547Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M117.852 120.586H80.1172C79.0234 120.586 78.2031 119.766 78.2031 118.672C78.2031 117.578 79.0234 116.758 80.1172 116.758H117.852C118.945 116.758 119.766 117.578 119.766 118.672C119.766 119.766 118.945 120.586 117.852 120.586ZM59.8828 120.586H22.4219C21.3281 120.586 20.5078 119.766 20.5078 118.672C20.5078 117.578 21.3281 116.758 22.4219 116.758H59.8828C60.9766 116.758 61.7969 117.578 61.7969 118.672C61.7969 119.766 60.9766 120.586 59.8828 120.586ZM128.516 109.102C127.422 109.102 126.602 108.281 126.602 107.188V81.2109C126.602 80.1172 127.422 79.2969 128.516 79.2969C129.609 79.2969 130.43 80.1172 130.43 81.2109V107.188C130.43 108.281 129.609 109.102 128.516 109.102ZM12.0312 109.102C10.9375 109.102 10.1172 108.281 10.1172 107.188V32.8125C10.1172 31.7188 10.9375 30.8984 12.0312 30.8984C13.125 30.8984 13.9453 31.7188 13.9453 32.8125V107.188C13.9453 108.281 13.125 109.102 12.0312 109.102ZM128.516 60.7031C127.422 60.7031 126.602 59.8828 126.602 58.7891V23.2422H80.1172C79.0234 23.2422 78.2031 22.4219 78.2031 21.3281C78.2031 20.2344 79.0234 19.4141 80.1172 19.4141H128.516C129.609 19.4141 130.43 20.2344 130.43 21.3281V58.7891C130.43 59.8828 129.609 60.7031 128.516 60.7031ZM59.8828 23.2422H22.4219C21.3281 23.2422 20.5078 22.4219 20.5078 21.3281C20.5078 20.2344 21.3281 19.4141 22.4219 19.4141H59.8828C60.9766 19.4141 61.7969 20.2344 61.7969 21.3281C61.7969 22.4219 60.9766 23.2422 59.8828 23.2422Z"
        fill="#6C7093"
      />
    </svg>
    <div>Chọn tài liệu</div>
  </div>
</ng-template>
