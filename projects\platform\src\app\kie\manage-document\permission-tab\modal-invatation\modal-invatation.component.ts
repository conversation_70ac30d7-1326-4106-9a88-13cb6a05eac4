import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { KIEService } from '@platform/app/core/services/kie.service';
import { UserService } from '@platform/app/core/services/user.service';
import { Role, User } from '@platform/app/kie/kie';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import {
  EMPTY,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';

@Component({
  selector: 'app-modal-invatation',
  templateUrl: './modal-invatation.component.html',
  styleUrls: ['./modal-invatation.component.scss']
})
export class ModalInvitationComponent implements OnInit {
  readonly nzModalData: { documentId: string } = inject(NZ_MODAL_DATA);
  documentId = this.nzModalData.documentId;
  selectedRole: Role.Viewer | Role.Editor;
  assigneeIds: string[] = [];
  roles = [
    { label: 'Người chỉnh sửa', value: Role.Editor },
    { label: 'Người xem', value: Role.Viewer }
  ];

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  listUser: User[] = [];

  constructor(
    private modalRef: NzModalRef,
    private userService: UserService,
    private toastr: ToastrService,
    private kieService: KIEService,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.searchSubject
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300), // Wait for 300ms pause in events
        distinctUntilChanged(),
        filter((text) => !!text.trim()),
        switchMap((text) =>
          this.userService.searchUser(text).pipe(
            tap((res) => {
              this.listUser = res.users;
            }),
            catchError((err) => {
              this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
              return EMPTY;
            })
          )
        )
      )
      .subscribe();
  }

  searchUser(text: string): void {
    this.searchSubject.next(text);
  }

  inviteMembers() {
    if (this.assigneeIds.length === 0)
      return this.toastr.error('Vui lòng chọn ít nhất một email!');
    if (!this.selectedRole) return this.toastr.error('Vui lòng chọn vai trò!');

    this.kieService
      .inviteMember(this.documentId, {
        role: this.selectedRole,
        assigneeIds: this.assigneeIds
      })
      .pipe(
        tap(() => {
          this.toastr.success('Mời người dùng thành công!');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }

  handleFilterAssignee(assigneeIds: string[]) {
    this.assigneeIds = assigneeIds;
  }
}
