import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  standalone: true,
  selector: 'app-subscription-plan-warning',
  templateUrl: './subscription-plan-warning.component.html',
  styleUrls: ['./subscription-plan-warning.component.scss']
})
export class SubscriptionPlanWarningComponent {
  constructor(
    private modalService: NzModalService,
    public router: Router
  ) {}

  closeAll(): void {
    this.modalService.closeAll();
  }
}
