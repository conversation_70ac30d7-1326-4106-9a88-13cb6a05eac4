import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '@platform/app/kie/kie';
import { DocumentLayoutService } from '@platform/app/kie/services/document-layout.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-change-folder-name',
  templateUrl: './modal-change-folder-name.component.html',
  styleUrls: ['./modal-change-folder-name.component.scss']
})
export class ModalChangeFolderNameComponent implements OnInit {
  readonly nzModalData: { folder: Folder } = inject(NZ_MODAL_DATA);
  folder = this.nzModalData.folder;
  newFolderName: string;

  constructor(
    private kieService: KIEService,
    private toastr: ToastrService,
    private modalRef: NzModalRef,
    private documentLayoutService: DocumentLayoutService
  ) {}

  ngOnInit(): void {
    this.newFolderName = this.folder.name;
  }

  cancel() {
    this.modalRef.close();
  }

  save() {
    if (this.newFolderName.trim().length < 1)
      return this.toastr.error('Tên thư mục không được để trống');

    if (this.newFolderName.trim().length > 255)
      return this.toastr.error('Tên thư mục không được quá 255 ký tự');

    if (
      this.documentLayoutService.folders
        .filter((folder) => folder.id !== this.folder.id)
        .map((folder) => folder.name.trim())
        .includes(this.newFolderName.trim())
    )
      return this.toastr.error('Tên thư mục đã tồn tại!');

    this.kieService
      .updateFolderDetail(this.folder.id, {
        name: this.newFolderName
      })
      .pipe(
        tap(() => {
          this.toastr.success('Đổi tên thư mục thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
