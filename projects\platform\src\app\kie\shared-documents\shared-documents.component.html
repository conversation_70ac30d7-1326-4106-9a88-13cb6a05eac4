<div class="p-6">
  <div>
    <img
      src="assets/kie/document/shared-documents.svg"
      class="inline-block mb-1 mr-2 text-red"
    />
    <span class="text-base font-semibold inline-block">V<PERSON><PERSON> bản đư<PERSON> chia sẻ</span>
  </div>

  <div class="flex gap-4 mt-4" [formGroup]="filterForm">
    <div class="w-[35%]">
      <nz-select
        class="w-full"
        nzPlaceHolder="Tìm loại văn bản"
        nzShowSearch
        nzSize="large"
        [nzAllowClear]="true"
        formControlName="templateTypes"
      >
        <nz-option
          *ngFor="let doc of listSystemTemplate"
          [nzLabel]="doc.title"
          [nzValue]="[doc.id]"
        ></nz-option>
      </nz-select>
    </div>

    <div class="w-[35%]">
      <nz-select
        formControlName="creatorIds"
        class="w-full"
        nzPlaceHolder="Chủ sở hữu"
        nzShowSearch
        nzSize="large"
        nzMode="multiple"
        nzOptionHeightPx="36"
        (nzOnSearch)="searchUser($event)"
      >
        <nz-option
          *ngFor="let user of listUser"
          nzCustomContent
          [nzLabel]="user.name"
          [nzValue]="user.id"
        >
          <nz-avatar
            [nzSize]="24"
            [nzText]="user.name.charAt(0)"
            class="text-white bg-[#ff3355] uppercase text-center"
            >D</nz-avatar
          >
          {{ user.name }}
        </nz-option>
      </nz-select>
    </div>

    <div class="flex-1">
      <nz-select
        formControlName="roleId"
        class="w-full"
        nzPlaceHolder="Vai trò được phân quyền"
        nzShowSearch
        nzSize="large"
      >
        <nz-option
          *ngFor="let role of listRoles"
          [nzLabel]="role.name"
          [nzValue]="role.id"
        >
        </nz-option>
      </nz-select>
    </div>
  </div>

  <div>
    <app-table-shared-document
      [listSharedDocuments]="dataSharedDocuments"
      (onPageChanged)="handleChangePage($event)"
      (onLimitChanged)="handleChangeLimit($event)"
    ></app-table-shared-document>
  </div>
</div>
