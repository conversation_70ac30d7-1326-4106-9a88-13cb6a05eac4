<div *ngIf="!!restriction; else previewContent">
  <div class="h-[1px] min-h-[750px] bg-white rounded-lg p-[1rem]" [ngSwitch]="restriction">
    <!-- Restricted Access -->
    <ng-template *ngSwitchCase="RestrictionType.LDP" [ngTemplateOutlet]="ldpRestriction">
    </ng-template>
    <ng-template *ngSwitchCase="RestrictionType.WIP" [ngTemplateOutlet]="wipRestriction">
    </ng-template>
    <ng-template *ngSwitchDefault>
      <div>Restricted Access</div>
    </ng-template>
  </div>
</div>

<ng-template #previewContent>
  <div [ngSwitch]="getOcrFileType()">
    <div id="document-container" *ngSwitchDefault
      [ngStyle]="{'background-color': demoDongHoNuoc ? 'rgba(161, 165, 186, 0.15)' : '#fff'}"
      class="h-[1px] min-h-[750px] bg-white rounded-lg p-[1rem]">
      <ng-template *ngTemplateOutlet="previewInfo"></ng-template>
    </div>
    <ng-container *ngSwitchCase="'image'">
      <div
        class="bg-[#E2E2E2] h-12 rounded-tl-lg rounded-tr-lg flex items-center pl-[18px] text-[#111127] text-base font-semibold">
        {{ocrFile?.name}}
      </div>
      <div [ngStyle]="{'background-color': demoDongHoNuoc ? 'rgba(161, 165, 186, 0.15)' : '#fff'}"
        class="flex flex-col justify-center items-center min-h-[750px] bg-white rounded-lg p-[1rem] rounded-tl-none rounded-tr-none">
        <div class="relative w-full" id="document-container">
          <img alt="preview" *ngIf="!!ocrFile" [src]="source" class="w-full">
          <!-- canvas#ocr-bboxes-overlay inserted here -->
        </div>
      </div>
    </ng-container>
    <ng-container *ngSwitchCase="'pdf'">
      <div
        class="bg-[#E2E2E2] min-h-[48px] rounded-tl-lg rounded-tr-lg flex items-center pl-[18px] text-[#111127] text-base font-semibold">
        {{ocrFile?.name}}
      </div>
      <div id="document-container" class="relative">
        <div class="flex justify-between absolute w-full" [ngStyle]="{'z-index': numPages - 1}">
          <button class="text-white bg-status-error rounded-md p-2 rounded-tl-none min-w-[60px]" type="button"
            (click)="previous()">Trước</button>
          <span class="font-bold self-center">{{ currentPage }}/{{ numPages }}</span>
          <button class="text-white bg-status-error rounded-md p-2 rounded-tr-none min-w-[60px]" type="button"
            (click)="next()">Sau</button>
        </div>
        <pdf-viewer
          [src]="source"
          [render-text]="false"
          class="min-h-[750px]"
          (after-load-complete)="afterLoadComplete($event)"
          [fit-to-page]="false"
          [original-size]="false"
          [autoresize]="true"
          [show-all]="false"
          [page]="currentPage">
        </pdf-viewer>
        <!-- canvas#ocr-bboxes-overlay inserted here -->
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #ldpRestriction>
  <div class="flex justify-center items-center flex-col h-full gap-8">
    <img src="assets/img/rpa/ocr-experience/ldp/restriction.svg">
    <div class="text-center text-[#273266] max-w-[414px]">
      Để bóc tách thông tin giấy tờ và trải nghiệm sản phẩm đầy đủ, bạn vui lòng <a class="font-bold text-[#2140D2]"
        routerLink="/login">Đăng nhập</a>
    </div>
  </div>
</ng-template>

<ng-template #wipRestriction>
  <div class="flex justify-center items-center flex-col h-full gap-8">
    <img src="assets/img/rpa/ocr-experience/wip.svg">
    <div class="text-center text-[#273266] font-semibold max-w-[250px]">
      Tính năng đang phát triển. <br> Vui lòng <a href="https://vnptai.io/ldp/smartreader/vi/contact" target="_blank">liên
        hệ</a>
      để được hỗ trợ.
    </div>
  </div>
</ng-template>

<ng-template #previewInfo>
  <ng-container [ngSwitch]="template?.functionType">
    <ng-container *ngSwitchDefault>
      <div class="flex items-center justify-center flex-col h-full">
        <img class="mb-3" src="assets/img/rpa/ocr-experience/empty.svg">
        <div class="text-[#A1A5BA] text-center">No image</div>
      </div>
    </ng-container>
    <ng-container *ngSwitchCase="FunctionType.SoHoaVanBan">
      <div class="flex items-center justify-center flex-col h-full text-xs text-[#666C8A]">
        <img class="mb-9" src="assets/img/rpa/ocr-experience/empty.svg">
        <div class="mb-3 font-bold uppercase leading-[18px]">
          Để tối ưu chất lượng OCR, quý khách vui lòng lưu ý:</div>
        <div class="leading-[22px]">
          <div>1. Văn bản đầu vào dạng thuần chữ in, không có chữ viết tay.</div>
          <div>2. Dòng chữ trong văn bản không nghiêng quá 10 độ so với chiều ngang của File PDF/ảnh.</div>
          <div>3. Đối với văn bản có bảng, yêu cầu bảng phải có đủ hàng, đủ cột, đủ dòng kẻ và nằm trong 1 trang.</div>
          <div>4. Văn bản đầu vào dạng PDF được scan với tối thiểu 150 dpi.</div>
          <div>5. Văn bản PDF/ảnh không bị mờ nhòe, không bị mất góc, không bị lóa sáng, không bị quá tối. </div>
          <div>6. Văn bản dạng ảnh không được chụp gián tiếp từ một thiết bị khác.</div>
        </div>
      </div>
    </ng-container>
  </ng-container>
</ng-template>