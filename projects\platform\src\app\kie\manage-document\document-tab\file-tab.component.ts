import { HttpParams } from '@angular/common/http';
import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormBuilder, Validators } from '@angular/forms';
import { KIEService } from '@platform/app/core/services/kie.service';
import { ModalCreateFilesComponent } from '@platform/app/kie/components/modal-create-files/modal-create-files.component';
import { ListFile, ParamQueryListFile } from '@platform/app/kie/kie';
import { isArray } from 'lodash';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import {
  BehaviorSubject,
  EMPTY,
  Subject,
  catchError,
  debounceTime,
  distinctUntilChanged,
  filter,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';

@Component({
  selector: 'app-file-tab',
  templateUrl: './file-tab.component.html',
  styleUrls: ['./file-tab.component.scss']
})
export class FileTabComponent implements OnInit, OnDestroy {
  @Input() roleInDocument: 'creator' | 'editor' | 'viewer';

  _documentId: string;
  @Input()
  set documentId(id: string) {
    this._documentId = id;
    this.filterForm.setValue({ page: 1, limit: 10, search: null, dateRange: null }); // fresh start
  }
  get documentId(): string {
    return this._documentId;
  }

  listFiles: ListFile = {
    files: [],
    limit: 10,
    page: 1,
    total: 0
  };
  filterForm = this.formBuilder.group({
    limit: this.formBuilder.control(10, [
      Validators.required,
      Validators.max(30),
      Validators.min(10)
    ]),
    page: this.formBuilder.control(1, [Validators.required, Validators.min(1)]),
    search: this.formBuilder.control(null),
    dateRange: this.formBuilder.control(null)
  });
  destroy$ = new Subject<void>();

  constructor(
    private toastr: ToastrService,
    private kieService: KIEService,
    private modalService: NzModalService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit(): void {
    this.fetchListFiles$().subscribe();

    this.filterForm.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(300),
        distinctUntilChanged(),
        switchMap(this.fetchListFiles$.bind(this))
      )
      .subscribe();
  }

  fetchListFiles$() {
    if (this.filterForm.invalid) {
      console.log('invalid filterForm', this.filterForm);
      return EMPTY;
    }

    const filters = Object.assign({}, this.filterForm.value);
    if (
      filters.dateRange?.length === 2 &&
      filters.dateRange[0] instanceof Date &&
      filters.dateRange[1] instanceof Date
    ) {
      filters['startDate'] = (filters.dateRange[0] as Date).toISOString();
      filters['endDate'] = (filters.dateRange[1] as Date).toISOString();
    }
    delete filters.dateRange;

    let params = new HttpParams();
    for (const key in filters) {
      if (!filters[key]) continue;
      if (isArray(filters[key]))
        filters[key].forEach((value) => {
          params = params.append(`${key}[]`, value);
        });
      else params = params.append(key, filters[key]);
    }
    return this.kieService.getListFileByDocumentId(this.documentId, params).pipe(
      tap((res) => {
        this.listFiles = res.data;
      }),
      catchError((err) => {
        this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
        return EMPTY;
      })
    );
  }

  // handler pagination
  onPageChange(page): void {
    this.filterForm.patchValue({ page });
  }

  onLimitChange(limit: number): void {
    this.filterForm.patchValue({
      limit,
      page: 1
    });
  }

  openModalCreateFiles() {
    const modalRef = this.modalService.create({
      nzContent: ModalCreateFilesComponent,
      nzClosable: false,
      nzMaskClosable: false,
      nzFooter: null,
      nzClassName: 'modal-create-files',
      nzBodyStyle: { padding: '0' },
      nzStyle: { width: '500px' },
      nzData: { documentId: this.documentId }
    });
    modalRef.afterClose
      .pipe(
        filter((result) => result),
        switchMap(this.fetchListFiles$.bind(this))
      )
      .subscribe();
  }

  onFileActionSuccess() {
    this.fetchListFiles$().subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
