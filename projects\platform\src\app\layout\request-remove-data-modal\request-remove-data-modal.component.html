<div class="wrapper">
	<button type="button" class="close-btn" (click)="modal.close()">
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M3.81227 3.81325C4.04668 3.57891 4.36457 3.44727 4.69602 3.44727C5.02748 3.44727 5.34536 3.57891 5.57977 3.81325L9.99977 8.23325L14.4198 3.81325C14.6555 3.58555 14.9713 3.45956 15.299 3.46241C15.6268 3.46525 15.9403 3.59672 16.172 3.82848C16.4038 4.06024 16.5353 4.37375 16.5381 4.7015C16.541 5.02924 16.415 5.345 16.1873 5.58075L11.7673 10.0007L16.1873 14.4207C16.415 14.6565 16.541 14.9723 16.5381 15.3C16.5353 15.6277 16.4038 15.9413 16.172 16.173C15.9403 16.4048 15.6268 16.5362 15.299 16.5391C14.9713 16.5419 14.6555 16.4159 14.4198 16.1882L9.99977 11.7682L5.57977 16.1882C5.34402 16.4159 5.02827 16.5419 4.70052 16.5391C4.37277 16.5362 4.05926 16.4048 3.8275 16.173C3.59574 15.9413 3.46428 15.6277 3.46143 15.3C3.45858 14.9723 3.58457 14.6565 3.81227 14.4207L8.23227 10.0007L3.81227 5.58075C3.57793 5.34634 3.44629 5.02845 3.44629 4.697C3.44629 4.36554 3.57793 4.04766 3.81227 3.81325Z"
				fill="#757575" />
		</svg>
	</button>
	<div class="header">Yêu cầu xóa dữ liệu cá nhân</div>
	<ng-container [ngSwitch]="step">
		<ng-container *ngSwitchCase="1">
			<ng-template *ngTemplateOutlet="step1"></ng-template>
		</ng-container>
		<ng-container *ngSwitchCase="2">
			<ng-template *ngTemplateOutlet="step2"></ng-template>
		</ng-container>
	</ng-container>
</div>

<ng-template #step1>
	<div class="content">
		<div class="pb-3">
			Tài khoản của quý khách sẽ bị ngắt dịch vụ và không thể truy tập vào hệ thống VNPT Smart Reader và trang Website
			<a target="_blank" href="https://vnptai.io/ldp/smartreader">https://vnptai.io/ldp/smartreader</a>, khi quý khách xác nhận yêu
			cầu xóa dữ liệu
			cá nhân
		</div>
		<div class="pb-3">
			Do đó, VNPT khuyến nghị Khách hàng đọc thông tin chi tiết tại: Điều 8 mục 8.5 tại
			<a target="_blank" href="https://vnptai.io/ldp/smartreader/vi/personal-data-policy#8">Chính sách bảo vệ dữ liệu cá
				nhân</a> của VNPT
		</div>
		<div class="flex gap-3 items-center pt-3 pb-6 border-b border-[#E2E2E2]">
			<input class="w-[18px] h-[18px]" [(ngModel)]="confirmed" type="checkbox" id="agreed">
			<label for="agreed">Tôi đã đọc và đồng ý xóa dữ liệu cá nhân</label>
		</div>
	</div>
	<div class="confirm-btn">
		<button (click)="goToStep2()" [disabled]="!confirmed">Xác nhận</button>
	</div>
</ng-template>

<ng-template #step2>
	<div class="content">
		<div>
			Toàn bộ thông tin tài khoản và dịch vụ của Quý khách sẽ xóa khỏi hệ thống khi yêu cầu này được thực hiện. Vui lòng
			đọc thông tin chi tiết tại: <a target="_blank" href="https://vnptai.io/ldp/smartreader/vi/personal-data-policy#8">Điều
				8, mục 8.5 trong Chính sách bảo vệ dữ liệu cá nhân</a> của VNPT và điền đầy đủ
			thông
			tin yêu cầu xóa dữ liệu cá nhân dưới đây.
		</div>
		<div class="py-4">
			Chúng tôi sẽ xác minh và xử lý yêu cầu của Quý khách trong vòng <span
				class="text-[#DA1E28] font-medium">72h</span> làm việc kể từ khi nhận được yêu cầu này
		</div>
		<div class="form">
			<div>
				<label class="text-[#111127] font-semibold" for="fullName">
					Họ tên <span class="text-[#DA1E28]">*</span>
				</label>
				<input nz-input disabled id="fullName" class="" type="text" [(ngModel)]="account.fullName">
			</div>
			<div>
				<label class="text-[#111127] font-semibold" for="phoneNumber">
					Số điện thoại <span class="text-[#DA1E28]">*</span>
				</label>
				<input nz-input disabled id="phoneNumber" class="" type="text" [(ngModel)]="account.phoneNumber">
			</div>
			<div>
				<label class="text-[#111127] font-semibold" for="username">
					Email <span class="text-[#DA1E28]">*</span>
				</label>
				<input nz-input disabled id="username" class="" type="text" [(ngModel)]="account.username">
			</div>
			<div>
				<label class="text-[#111127] font-semibold" for="workUnit">
					Công ty <span class="text-[#DA1E28]">*</span>
				</label>
				<input nz-input disabled id="workUnit" class="" type="text" [(ngModel)]="account.workUnit">
			</div>
			<div>
				<label class="text-[#111127] font-semibold" for="message">
					Yêu cầu <span class="text-[#DA1E28]">*</span>
				</label>
				<input nz-input id="message" class="" type="text" placeholder="Nhập yêu cầu (bắt buộc)" [(ngModel)]="message">
			</div>
		</div>
	</div>
	<div class="confirm-btn">
		<button [disabled]="!this.confirmed || !this.message?.trim()" (click)="sendRemoveDataRequest()">Gửi yêu cầu</button>
	</div>
</ng-template>