import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { isEmpty } from 'lodash';
import { PDFDocumentProxy } from 'ng2-pdf-viewer';
import {
  excludedKeyFields,
  FunctionType,
  RestrictionType,
  Template
} from '../../ocr-experience';

interface DocumentField {
  bboxes: { [page: number]: number[] };
  confidence_score: number;
  text?: string;
  cells?: {
    bboxes: { [page: number]: number[] };
    confidence_score: number;
    text: string;
  }[];
  warnings?: any[];
  type: 'Field' | 'Table' | 'List';
}

@Component({
  selector: 'app-ocr-preview',
  templateUrl: './ocr-preview.component.html',
  styleUrls: ['./ocr-preview.component.scss']
})
export class OcrPreviewComponent {
  readonly FunctionType = FunctionType;
  readonly RestrictionType = RestrictionType;
  _ocrFile;
  @Input()
  set ocrFile(ocrFile: File) {
    this._ocrFile = ocrFile;
    this.currentPage = 1;
    this.displayFile();
  }
  get ocrFile() {
    return this._ocrFile;
  }

  _ocrData;
  @Input()
  set ocrData(ocrData) {
    this._ocrData = ocrData;
    this.displayOverlayOcrData();
  }
  get ocrData() {
    return this._ocrData;
  }
  source;

  // fields for pdf case
  numPages: number = 0;
  currentPage: number;
  @Output() currentPageChange = new EventEmitter<number>();

  @Input() restriction?: RestrictionType;
  @Input() template?: Template;
  @Input() demoDongHoNuoc?: boolean;

  constructor(private sanitizer: DomSanitizer) {}

  getOcrFileType() {
    const type = this.ocrFile?.type;
    if (['image/jpeg', 'image/png', 'image/jpg'].includes(type)) return 'image';
    if (type === 'application/pdf') return 'pdf';
    return !!type;
  }

  displayFile() {
    if (!this.ocrFile) return;
    if (this.ocrFile.type === 'application/pdf') {
      this.source = this.sanitizer.bypassSecurityTrustResourceUrl(
        URL.createObjectURL(this.ocrFile)
      )['changingThisBreaksApplicationSecurity'];
    } else {
      this.source = this.sanitizer.bypassSecurityTrustResourceUrl(
        URL.createObjectURL(this.ocrFile)
      );
    }
  }

  private getOcrDocumentAndContainer() {
    if (!this.ocrFile) return {};
    const ocrDocumentContainer = document.getElementById(
      'document-container'
    ) as HTMLDivElement;
    let ocrDocument: HTMLCanvasElement | HTMLImageElement;
    if (this.ocrFile.type === 'application/pdf')
      ocrDocument = ocrDocumentContainer.querySelector('canvas') as HTMLCanvasElement;
    else ocrDocument = ocrDocumentContainer.querySelector('img') as HTMLImageElement;

    return {
      ocrDocument,
      ocrDocumentContainer,
      isPdfDoc: this.ocrFile.type === 'application/pdf'
    };
  }

  // help with right-hand side result being display at proper height in pdf case
  private setPdfDocumentContainerHeight() {
    if (!this.ocrFile || this.ocrFile.type !== 'application/pdf') return;
    setTimeout(() => {
      const { ocrDocument: ocrDoc, ocrDocumentContainer } =
        this.getOcrDocumentAndContainer() || {};
      if (!ocrDoc) return;
      ocrDocumentContainer.style.minHeight = ocrDoc.style.height;
    }, 300); // wait for the pdf canvas got rendered on the screen
  }

  // ONLY display template document
  displayOverlayOcrData() {
    if (isEmpty(this.ocrData)) {
      this.removeOverlayOcrData();
      return;
    }

    const {
      ocrDocument: ocrDoc,
      ocrDocumentContainer: ocrDocCtn,
      isPdfDoc
    } = this.getOcrDocumentAndContainer();
    if (!ocrDoc || !ocrDocCtn) return;

    if (this.ocrData.isDrawingBboxOverlay) {
      const drawBBox = (field) => {
        if (isEmpty(field.bboxes)) return;

        const pageOfField = +Object.keys(field.bboxes).pop();
        if (pageOfField !== this.currentPage) return;

        // extract relative x, y, width height of the field on the scale 0 to 1
        const x = field.bboxes[pageOfField][0];
        const y = field.bboxes[pageOfField][1];
        const width = field.bboxes[pageOfField][2] - field.bboxes[pageOfField][0];
        const height = field.bboxes[pageOfField][3] - field.bboxes[pageOfField][1];

        // draw overlay canvas on top of the doc(image/pdf)
        let ctx: CanvasRenderingContext2D;
        let canvas: HTMLCanvasElement;
        canvas = document.getElementById('ocr-bboxes-overlay') as HTMLCanvasElement;
        if (!canvas) {
          canvas = document.createElement('canvas') as HTMLCanvasElement;
          canvas.width = isPdfDoc ? +ocrDoc.style.width.split('px')[0] : ocrDoc.width;
          canvas.height = isPdfDoc ? +ocrDoc.style.height.split('px')[0] : ocrDoc.height;
          canvas.id = 'ocr-bboxes-overlay';
          /* canvas is layered on top of the image/pdf with exact same dimension */
          canvas.style.position = 'absolute';
          canvas.style.top = '0';
          ocrDocCtn.appendChild(canvas);
          ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
        } else {
          ctx = (canvas as HTMLCanvasElement).getContext(
            '2d'
          ) as CanvasRenderingContext2D;
        }
        ctx.lineWidth = 1;
        ctx.strokeStyle = 'green';
        ctx.strokeRect(
          x * canvas.width,
          y * canvas.height,
          width * canvas.width,
          height * canvas.height
        );
      };
      for (const keyword in this.ocrData) {
        const field: DocumentField = this.ocrData[keyword];
        if (excludedKeyFields.includes(keyword) || !field) continue;

        if (field.type === 'Field') drawBBox(field);
        if (field.type === 'List') field.cells.forEach((cell) => drawBBox(cell));
      }
    }
  }

  removeOverlayOcrData() {
    const { ocrDocumentContainer: ocrDocCtn } = this.getOcrDocumentAndContainer();
    if (!ocrDocCtn) return;
    const canvas = ocrDocCtn.querySelector('canvas#ocr-bboxes-overlay');
    if (canvas) canvas.remove();
  }

  afterLoadComplete(pdf: PDFDocumentProxy) {
    this.numPages = pdf.numPages;
    this.currentPage = 1;
    this.setPdfDocumentContainerHeight();
    // this.currentPageChange.emit(1);
    // console.log('afterLoad', this.numPages, this.currentPage);
  }

  previous() {
    if (this.currentPage > 1) {
      this.removeOverlayOcrData();
      this.currentPage--;
      this.currentPageChange.emit(this.currentPage);
      this.displayOverlayOcrData();
      this.setPdfDocumentContainerHeight();
    }
    // console.log(this.currentPage, this.numPages);
  }

  next() {
    if (this.currentPage < this.numPages) {
      this.removeOverlayOcrData();
      this.currentPage++;
      this.currentPageChange.emit(this.currentPage);
      this.displayOverlayOcrData();
      this.setPdfDocumentContainerHeight();
    }
    // console.log(this.currentPage, this.numPages);
  }
}
