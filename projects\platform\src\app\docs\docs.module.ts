import { CUSTOM_ELEMENTS_SCHEMA, NgModule, SecurityContext } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DocsRoutingModule } from './docs-routing.module';
import { MarkdownModule, MarkedOptions } from 'ngx-markdown';
import { DocsComponent } from './docs.component';
import { headerPrefix } from './docs';

@NgModule({
  declarations: [DocsComponent],
  imports: [
    CommonModule,
    DocsRoutingModule,
    MarkdownModule.forRoot({
      markedOptions: {
        provide: MarkedOptions,
        useFactory: markedOptionsFactory
      },
      sanitize: SecurityContext.NONE // Safe for rendering our controlled markdown file. Be aware of all javascript added via html in markdown content will be added to the DOM (xss attack threat)
    })
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DocsModule {}

export function markedOptionsFactory(): MarkedOptions {
  return { headerIds: true, headerPrefix: headerPrefix };
}
