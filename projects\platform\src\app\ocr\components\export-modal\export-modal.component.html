<div class="flex flex-col">
  <div class="flex items-center justify-between py-3 px-4">
    <div class="font-semibold text-base">
      T<PERSON><PERSON> về {{ files.length > 1 ? files.length + ' file' : '' }}
    </div>
    <button (click)="closeExportModal()">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 15L15 5"
          stroke="#989BB3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M15 15L5 5"
          stroke="#989BB3"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
  <div class="w-full h-[1px] bg-line"></div>
  <div class="p-4">
    <div class="flex items-center justify-between">
      <button
        (click)="selectExportType('docx')"
        [ngClass]="{ '!border-brand-1': exportType === 'docx' }"
        class="w-24 flex flex-col gap-2 items-center p-4 rounded-lg border-2 border-line font-medium"
      >
        <img class="w-8" src="assets/ocr-experience/doc-icon.svg" alt="doc" />Word
      </button>
      <button
        (click)="selectExportType('xlsx')"
        [ngClass]="{ '!border-brand-1': exportType === 'xlsx' }"
        class="w-24 flex flex-col gap-2 items-center p-4 rounded-lg border-2 border-line font-medium"
      >
        <img class="w-8" src="assets/ocr-experience/xls-icon.svg" alt="xls" />Excel
      </button>
      <button
        (click)="selectExportType('json')"
        [ngClass]="{ '!border-brand-1': exportType === 'json' }"
        class="w-24 flex flex-col gap-2 items-center p-4 rounded-lg border-2 border-line font-medium"
      >
        <img class="w-8" src="assets/ocr-experience/json-icon.svg" alt="json" />JSON
      </button>
    </div>
    <div
      *ngIf="exportType === 'xlsx'"
      class="mt-4 pt-4 border-t border-dashed border-text-4"
    >
      <div class="flex gap-4 items-center font-medium">
        <div>Tài liệu</div>
        <nz-select class="flex-auto rounded-lg" [(ngModel)]="xlsxMapPageToSheet">
          <nz-option nzLabel="Nằm trong 1 Sheet" [nzValue]="false"></nz-option>
          <nz-option nzLabel="Ứng với từng Sheet" [nzValue]="true"></nz-option>
        </nz-select>
      </div>
    </div>
  </div>
  <div class="w-full h-[1px] bg-line"></div>
  <div class="py-3 px-4">
    <button
      (click)="export()"
      class="flex items-center justify-center gap-2 w-full p-[10px] rounded-lg text-white bg-[#1E5FD5] font-medium"
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M18.3334 10C18.3334 10 18.3334 11.2857 18.3334 13.6964V13.7738C18.3334 16.4345 16.7657 17.5 12.8509 17.5H7.14939C3.23455 17.5 1.66686 16.4345 1.66686 13.7738V13.6964C1.66686 11.3036 1.66675 10 1.66675 10"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M10 12.5009V3.01758"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M12.7916 10.543L9.99992 13.3346L7.20825 10.543"
          stroke="white"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Tải về
    </button>
  </div>
</div>

<ngx-spinner
  [name]="'export-modal-loading'"
  bdColor="rgba(0, 0, 0, 0.4)"
  size="medium"
  color="#fff"
  type="ball-scale-multiple"
  [fullScreen]="false"
>
  <div class="font-semibold text-text-2">
    Đang xử lý... {{ progressFileIndex }}/{{ files.length }}
  </div>
</ngx-spinner>
