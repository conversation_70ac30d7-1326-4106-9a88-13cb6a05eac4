<div class="m-5 py-6 px-8 bg-white rounded-md border border-line">
  <h4 class="text-xl font-semibold">Thông tin Token</h4>
  <div class="mt-4">
    <div class="mb-4">
      <div class="text-base font-semibold" nz-typography nzCopyable [nzCopyText]="tokens.uuidProjectServicePlan"
        nzContent="Token id">
      </div>
      <div nz-typography nzEllipsis class="py-2 rounded-md">
        <span class="bg-line p-2 rounded-md">
          {{ tokens.uuidProjectServicePlan }}
        </span>
      </div>
    </div>
    <div class="mb-4">
      <div class="text-base font-semibold" nz-typography nzCopyable [nzCopyText]="tokens.publicKey"
        nzContent="Token key">
      </div>
      <div nz-typography nzEllipsis class="py-2 rounded-md">
        <span class="bg-line p-2 rounded-md">
          {{ tokens.publicKey }}
        </span>
      </div>
    </div>
    <div class="mb-4">
      <div class="text-base font-semibold" nz-typography nzCopyable [nzCopyText]="accessToken" nzContent="Access token">
      </div>
      <div nz-typography class="bg-line p-2 rounded-md">
        {{accessToken}}
      </div>
    </div>
    <div class="italic text-xs" nz-typography>
      ** Token id, Token Key và Access token dùng để bảo mật khi thực hiện gọi api. Tham khảo hướng dẫn
      <a target="_blank" [href]="'https://vnptai.io/ldp/smartreader/vi/documents/tong-quan'"> tại đây</a>.<br />
      <span>Vui lòng không chia sẻ các keys trên vì an toàn thông tin Dự án của bạn.</span>
      **
    </div>
  </div>






</div>