<div class="wrapper">
	<div class="left">
		<div class="title">Ảnh để OCR</div>
		<div class="input-preview empty" *ngIf="!image">
			<img src="assets/img/rpa/ocr-experience/demo/input-preview.svg" alt="input-preview">
			<span>Ảnh đồng hồ</span>
		</div>
		<div class="input-preview" *ngIf="image">
			<img [src]="image?.path" alt="input-preview">
			<div>
				<span class="input-name">{{image?.name}}</span>
				<span class="input-size">{{image?.size}}</span>
			</div>
		</div>
		<div class="actions">
			<button class="refresh" (click)="refresh()">Làm mới</button>
			<button class="ocr" (click)="ocr()">OCR</button>
		</div>
		<hr class="my-3">
		<div class="title">Danh sách ảnh</div>
		<div class="upload-file">
			<input type="file" accept="image/jpeg, image/png, image/jpg" (change)="handleChangeFile($event)">
			<img src="assets/img/rpa/ocr-experience/demo/upload.svg" alt="upload">
			<div>
				Kéo thả hoặc chọn các tệp bạn muốn tải lên từ máy tính của mình
			</div>
		</div>
		<div class="sample-list">
			<ng-container *ngFor="let sample of sampleList">
				<img [src]="sample?.path" (click)="selectSampleFile(sample)">
			</ng-container>
		</div>
	</div>
	<div class="right">
		<div class="title">Kết quả OCR</div>
		<div class="output">
			<div class="preview">
				<app-ocr-preview
					[demoDongHoNuoc]="true"
					[ocrFile]="image?.file"
					[ocrData]="image?.result">
				</app-ocr-preview>
			</div>
			<div class="border"></div>
			<div class="info">
				<img class="image-snip" *ngIf="image?.result?.number?.imageSnip" [src]="image?.result?.number?.imageSnip">
				<div class="image-snip placeholder" *ngIf="!image?.result?.number?.imageSnip"></div>

				<div class="title">Số nước trên đồng hồ</div>
				<div class="water-num">
					<ng-container *ngIf="image?.result?.number?.textArr">
						<div *ngFor="let num of image?.result?.number?.textArr">
							{{num}}
						</div>
					</ng-container>
					<ng-container *ngIf="!image?.result?.number?.textArr">
						<div>-</div>
						<div>-</div>
						<div>-</div>
						<div>-</div>
						<div>-</div>
					</ng-container>
				</div>
				<div class="title">Độ tự tin</div>
				<div class="accuracy">{{image?.result?.number?.confidence_score ?
					getPercentage(image?.result?.number?.confidence_score, 3) : ''}}</div>
			</div>
		</div>
	</div>
</div>