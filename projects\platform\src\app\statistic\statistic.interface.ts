import { TemplateRef } from "@angular/core";

export interface TableConfig {
  hasTotalRow?: boolean;
  title: string;
  columns: TableColumn[];
  data: TableRow[];
}

export interface TableColumn {
  canSum?: boolean;
  title: string;
  index: string;
  tooltip?: string | TemplateRef<void>;
  render?: (
    data: string | number | boolean,
    record: any,
    index: string
  ) => string | number;
  routerLink?:
    | string[]
    | ((data: string | number | boolean, record: any, index: string) => string[]);
}

export interface TableRow {
  [index: string]: string | number | boolean;
}

export interface FilterOption {
  title: string;
  value: string;
  xAxisTitleForChart: string;
}
