<div class="w-2/5 px-5 py-4 overflow-y-auto">
  <p class="text-text-1 text-sm font-bold">Loại giấy tờ</p>
  <div
    class="flex items-center justify-between rounded-lg bg-bg-1 px-3 py-2 mt-2 mb-4 border border-line"
  >
    <p>{{ documentName }}</p>
  </div>
  <p class="text-text-1 text-sm font-bold">Thông tin API</p>
  <div class="mt-4">
    <p class="font-medium">API URL</p>
    <div
      class="flex items-center justify-between rounded-lg bg-bg-1 px-3 py-2 mt-1 border border-line"
    >
      <p>{{ apiUrl }}</p>
      <img
        (click)="copyText(apiUrl)"
        class="cursor-pointer"
        src="assets/kie/document/copy.svg"
        alt="icon-copy"
      />
    </div>
  </div>
  <div *ngIf="!!templateId" class="mt-2">
    <p class="font-medium">TEMPLATE_ID</p>
    <div
      class="flex items-center justify-between rounded-lg bg-bg-1 px-3 py-2 mt-1 border border-line"
    >
      <p>{{ templateId }}</p>
      <img
        (click)="copyText(templateId)"
        class="cursor-pointer"
        src="assets/kie/document/copy.svg"
        alt="icon-copy"
      />
    </div>
  </div>
  <div class="mt-2">
    <p>
      ** <span class="font-semibold">API URL</span> và
      <span class="font-semibold">TEMPLATE_ID</span> (Đối với mẫu văn bản người dùng cấu
      hình bóc tách thông tin) dùng để thực hiện gọi API.
      <br />
      Tham khảo Tài liệu tích hợp
      <a
        class="text-brand-2 font-semibold"
        target="_blank"
        href="https://vnptai.io/ldp/smartreader/vi/documents/tong-quan"
        >tại đây</a
      >.
      <br />
      Chi tiết Token id, Token Key và Acess Token dùng để bảo mật khi thực hiện gọi API.
      Xem Quản lý token
      <button
        class="text-brand-2 font-semibold"
        (click)="openTokenInfoModal(tokenInfoModalRef)"
      >
        tại đây.
      </button>
      <br />
      Vui lòng không chia sẻ các keys trên vì an toàn thông tin Dự án của bạn.**
    </p>
  </div>
  <div class="mt-4">
    <p class="text-text-1 text-sm font-bold">Trường thông tin bóc tách</p>
    <div
      class="field-list flex flex-col gap-2 mt-4"
      cdkDropList
      (cdkDropListDropped)="fieldExtraConfigDrop($event)"
    >
      <div
        *ngFor="let field of fieldExtraConfigList['controls']"
        cdkDrag
        class="field-item relative flex items-center rounded-lg bg-bg-1 px-5 py-3 justify-between"
        [formGroup]="field"
      >
        <div class="flex gap-3 items-center flex-1 overflow-hidden">
          <img
            cdkDragHandle
            class="hover:cursor-grab"
            src="assets/kie/document/draggable.svg"
            alt="+"
          />
          <label class="relative cursor-pointer">
            <input
              #colorPickerInput
              type="color"
              class="absolute top-0 left-0 w-full h-full z-10 text-[0px] hidden"
              formControlName="color"
            />
            <div
              class="w-4 h-4 rounded-[4px] shrink-0"
              [ngStyle]="{ 'background-color': field.controls['color'].value }"
            ></div>
          </label>
          <p
            class="font-medium break-words truncate"
            nz-tooltip
            nzTooltipColor="#000"
            [nzTooltipTitle]="field.controls['name'].value"
          >
            {{ field.controls['name'].value }}
          </p>
        </div>
        <nz-switch class="shrink-0" formControlName="is_visible"></nz-switch>
      </div>
    </div>
    <button
      *ngIf="fieldExtraConfigList['controls'].length"
      class="py-2 px-4 rounded bg-brand-1 mt-4 font-medium text-white flex items-center gap-2"
      (click)="saveExtraConfigList()"
    >
      <img class="inline-block" src="assets/kie/document/save.svg" alt="" />
      Lưu
    </button>
  </div>
</div>

<ng-container
  *ngIf="!!document?.template?.file_link; then configPreview; else noConfigPreview"
>
</ng-container>

<ng-template #configPreview>
  <div class="flex-auto w-[1px] flex flex-col">
    <div
      class="flex items-center justify-between bg-[#F5F9FECC] border border-line px-4 py-2"
    >
      <div class="text-text-1 font-semibold truncate overflow-ellipsis flex-1">
        {{ document?.name }}
      </div>
      <div *ngIf="document?.isSelfConfig" class="flex items-center gap-4">
        <button
          [routerLink]="['/', 'key-information-extractor', 'template', document?.id]"
          class="bg-brand-1 rounded-[4px] flex items-center gap-2 py-2 px-4 text-white font-medium"
        >
          <img src="assets/kie/document/setting.svg" class="inline-block" />
          Cấu hình
        </button>
        <button
          class="rounded-full w-6 h-6 items-center justify-center"
          (click)="$event.preventDefault(); $event.stopPropagation()"
          nz-popover
          nzPopoverTrigger="click"
          [nzPopoverContent]="templateFileActions"
          nzPopoverPlacement="rightTop"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7.99984 2C7.2665 2 6.6665 2.6 6.6665 3.33333C6.6665 4.06667 7.2665 4.66667 7.99984 4.66667C8.73317 4.66667 9.33317 4.06667 9.33317 3.33333C9.33317 2.6 8.73317 2 7.99984 2Z"
              fill="#6C7093"
            />
            <path
              d="M7.99984 11.3335C7.2665 11.3335 6.6665 11.9335 6.6665 12.6668C6.6665 13.4002 7.2665 14.0002 7.99984 14.0002C8.73317 14.0002 9.33317 13.4002 9.33317 12.6668C9.33317 11.9335 8.73317 11.3335 7.99984 11.3335Z"
              fill="#6C7093"
            />
            <path
              d="M7.99984 6.6665C7.2665 6.6665 6.6665 7.2665 6.6665 7.99984C6.6665 8.73317 7.2665 9.33317 7.99984 9.33317C8.73317 9.33317 9.33317 8.73317 9.33317 7.99984C9.33317 7.2665 8.73317 6.6665 7.99984 6.6665Z"
              fill="#6C7093"
            />
          </svg>
        </button>
        <ng-template #templateFileActions>
          <div
            class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden min-w-[200px]"
          >
            <button
              class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
              (click)="openChangeDocumentTemplateFile()"
            >
              <img src="assets/kie/actions/refresh.svg" alt="icon" />
              <p>Đổi mẫu giấy tờ</p>
            </button>
            <a
              class="flex gap-2 items-center hover:text-inherit hover:bg-bg-1 px-3 py-2 border-t border-line"
              (click)="(null)"
              [href]="document?.template?.file_link || '#'"
              download
              target="_blank"
            >
              <img src="assets/kie/actions/download.svg" alt="icon" />
              <p>Tải xuống</p>
            </a>
          </div>
        </ng-template>
      </div>
      <a
        *ngIf="!document?.isSelfConfig"
        [href]="document?.template?.file_link || '#'"
        download
        target="_blank"
        class="p-2 font-medium text-text-1 flex gap-1"
      >
        <img src="assets/kie/header-table/download.svg" />
        Tải xuống
      </a>
    </div>
    <!-- default document-viewer, only display image/pdf, no config -->
    <app-document-viewer [fileLink]="document?.template?.file_link"></app-document-viewer>
  </div>
</ng-template>

<ng-template #noConfigPreview>
  <div
    class="flex-auto w-[1px] flex flex-col justify-center items-center gap-2 bg-bg-1 border border-line text-icon-1 font-medium"
  >
    <svg
      width="140"
      height="140"
      viewBox="0 0 140 140"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M133.711 131.797H122.227C118.672 131.797 115.938 129.062 115.938 125.234V111.836C115.938 108.281 118.672 105.273 122.227 105.273H133.711C137.266 105.273 140 108.281 140 111.836V125.234C140 129.062 137.266 131.797 133.711 131.797ZM122.227 109.102C120.859 109.102 119.766 110.469 119.766 111.836V125.234C119.766 126.875 120.859 127.969 122.227 127.969H133.711C135.078 127.969 136.172 126.875 136.172 125.234V111.836C136.172 110.469 135.078 109.102 133.711 109.102H122.227Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M17.7734 34.7266H6.5625C3.00781 34.7266 0 31.7188 0 28.1641V14.7656C0 11.2109 3.00781 8.20312 6.5625 8.20312H17.7734C21.3281 8.20312 24.3359 11.2109 24.3359 14.7656V28.1641C24.3359 31.7188 21.3281 34.7266 17.7734 34.7266ZM6.5625 12.0312C5.19531 12.0312 3.82812 13.3984 3.82812 14.7656V28.1641C3.82812 29.5312 5.19531 30.8984 6.5625 30.8984H17.7734C19.1406 30.8984 20.5078 29.5312 20.5078 28.1641V14.7656C20.5078 13.3984 19.1406 12.0312 17.7734 12.0312H6.5625Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M17.7734 131.797H6.5625C3.00781 131.797 0 128.789 0 125.234V111.836C0 108.281 3.00781 105.273 6.5625 105.273H17.7734C21.3281 105.273 24.3359 108.281 24.3359 111.836V125.234C24.3359 128.789 21.6016 131.797 17.7734 131.797ZM6.5625 109.102C5.19531 109.102 3.82812 110.469 3.82812 111.836V125.234C3.82812 126.875 5.19531 127.969 6.5625 127.969H17.7734C19.1406 127.969 20.5078 126.875 20.5078 125.234V111.836C20.5078 110.469 19.1406 109.102 17.7734 109.102H6.5625Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M75.7422 34.7266H64.2578C60.7031 34.7266 57.9688 31.7188 57.9688 28.1641V14.7656C57.9688 11.2109 60.7031 8.20312 64.2578 8.20312H75.7422C79.2969 8.20312 82.0312 11.2109 82.0312 14.7656V28.1641C82.0312 31.7188 79.2969 34.7266 75.7422 34.7266ZM64.2578 12.0312C62.8906 12.0312 61.7969 13.3984 61.7969 14.7656V28.1641C61.7969 29.5312 62.8906 30.8984 64.2578 30.8984H75.7422C77.1094 30.8984 78.2031 29.5312 78.2031 28.1641V14.7656C78.2031 13.3984 77.1094 12.0312 75.7422 12.0312H64.2578Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M75.7422 131.797H64.2578C60.7031 131.797 57.9688 128.789 57.9688 125.234V111.836C57.9688 108.281 60.7031 105.273 64.2578 105.273H75.7422C79.2969 105.273 82.0312 108.281 82.0312 111.836V125.234C82.0312 128.789 79.2969 131.797 75.7422 131.797ZM64.2578 109.102C62.8906 109.102 61.7969 110.469 61.7969 111.836V125.234C61.7969 126.602 62.8906 127.969 64.2578 127.969H75.7422C77.1094 127.969 78.2031 126.602 78.2031 125.234V111.836C78.2031 110.469 77.1094 109.102 75.7422 109.102H64.2578Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M133.711 83.125H122.227C118.672 83.125 115.938 80.3906 115.938 76.8359V63.1641C115.938 59.6094 118.672 56.875 122.227 56.875H133.711C137.266 56.875 140 59.6094 140 63.1641V76.8359C140 80.3906 137.266 83.125 133.711 83.125ZM122.227 60.7031C120.859 60.7031 119.766 61.7969 119.766 63.1641V76.8359C119.766 78.2031 120.859 79.2969 122.227 79.2969H133.711C135.078 79.2969 136.172 78.2031 136.172 76.8359V63.1641C136.172 61.7969 135.078 60.7031 133.711 60.7031H122.227Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M101.172 96.5234H38.5547C35.2734 96.5234 32.5391 93.7891 32.5391 90.5078V49.4922C32.5391 46.2109 35.2734 43.4766 38.5547 43.4766H101.172C104.453 43.4766 106.914 46.2109 106.914 49.4922V90.5078C106.914 93.7891 104.453 96.5234 101.172 96.5234ZM38.5547 47.3047C37.4609 47.3047 36.3672 48.3984 36.3672 49.4922V90.5078C36.3672 91.6016 37.4609 92.6953 38.5547 92.6953H101.172C102.266 92.6953 103.086 91.6016 103.086 90.5078V49.4922C103.086 48.3984 102.266 47.3047 101.172 47.3047H38.5547Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M91.0549 96.5234C90.508 96.5234 90.2346 96.25 89.9611 95.9766L57.9689 70.2734L35.5471 88.0469C34.7267 88.8672 33.633 88.5938 33.0861 87.7734C32.2658 86.9531 32.5392 85.8594 33.3596 85.3125L56.8752 66.4453C57.4221 65.8984 58.5158 65.8984 59.3361 66.4453L92.1486 93.2422C92.9689 93.7891 93.2424 94.8828 92.6955 95.7031C92.1486 96.25 91.6017 96.5234 91.0549 96.5234Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M105 83.1252C104.453 83.1252 103.906 82.8518 103.633 82.5784L92.4217 70.2737L74.3748 81.7581C73.2811 82.3049 72.1873 82.0315 71.6405 81.2112C71.0936 80.1174 71.367 79.0237 72.1873 78.4768L91.6014 66.1721C92.4217 65.6252 93.5155 65.8987 94.0623 66.4456L106.367 79.844C107.187 80.6643 107.187 81.7581 106.367 82.5784C105.82 82.8518 105.547 83.1252 105 83.1252Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M72.4609 69.7266C67.5391 69.7266 63.7109 65.8984 63.7109 60.9766C63.7109 56.0547 67.5391 52.2266 72.4609 52.2266C77.1094 52.2266 80.9375 56.0547 80.9375 60.9766C80.9375 65.8984 77.1094 69.7266 72.4609 69.7266ZM72.4609 56.0547C69.7266 56.0547 67.5391 58.2422 67.5391 60.9766C67.5391 63.7109 69.7266 65.8984 72.4609 65.8984C74.9219 65.8984 77.1094 63.7109 77.1094 60.9766C77.1094 58.2422 74.9219 56.0547 72.4609 56.0547Z"
        fill="#6C7093"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M117.852 120.586H80.1172C79.0234 120.586 78.2031 119.766 78.2031 118.672C78.2031 117.578 79.0234 116.758 80.1172 116.758H117.852C118.945 116.758 119.766 117.578 119.766 118.672C119.766 119.766 118.945 120.586 117.852 120.586ZM59.8828 120.586H22.4219C21.3281 120.586 20.5078 119.766 20.5078 118.672C20.5078 117.578 21.3281 116.758 22.4219 116.758H59.8828C60.9766 116.758 61.7969 117.578 61.7969 118.672C61.7969 119.766 60.9766 120.586 59.8828 120.586ZM128.516 109.102C127.422 109.102 126.602 108.281 126.602 107.188V81.2109C126.602 80.1172 127.422 79.2969 128.516 79.2969C129.609 79.2969 130.43 80.1172 130.43 81.2109V107.188C130.43 108.281 129.609 109.102 128.516 109.102ZM12.0312 109.102C10.9375 109.102 10.1172 108.281 10.1172 107.188V32.8125C10.1172 31.7188 10.9375 30.8984 12.0312 30.8984C13.125 30.8984 13.9453 31.7188 13.9453 32.8125V107.188C13.9453 108.281 13.125 109.102 12.0312 109.102ZM128.516 60.7031C127.422 60.7031 126.602 59.8828 126.602 58.7891V23.2422H80.1172C79.0234 23.2422 78.2031 22.4219 78.2031 21.3281C78.2031 20.2344 79.0234 19.4141 80.1172 19.4141H128.516C129.609 19.4141 130.43 20.2344 130.43 21.3281V58.7891C130.43 59.8828 129.609 60.7031 128.516 60.7031ZM59.8828 23.2422H22.4219C21.3281 23.2422 20.5078 22.4219 20.5078 21.3281C20.5078 20.2344 21.3281 19.4141 22.4219 19.4141H59.8828C60.9766 19.4141 61.7969 20.2344 61.7969 21.3281C61.7969 22.4219 60.9766 23.2422 59.8828 23.2422Z"
        fill="#6C7093"
      />
    </svg>
    Không có ảnh file hệ thống mẫu cấu hình
  </div>
</ng-template>

<ng-template #tokenInfoModalRef>
  <div class="border-b border-line flex justify-between gap-4 items-center p-4">
    <span class="font-semibold">Quản lý Token</span>
    <img
      src="assets/kie/document/cancel.svg"
      class="p-2 pr-0 cursor-pointer"
      (click)="closeModal()"
    />
  </div>
  <div class="flex flex-col gap-4 p-4">
    <div>
      <div class="text-xs font-medium mb-1">Token id</div>
      <div class="bg-bg-1 p-2 rounded-lg flex gap-2 items-center justify-between">
        <div class="break-words truncate">{{ tokens.id }}</div>
        <button (click)="copyText(tokens.id)" class="shrink-0">
          <img src="assets/kie/actions/copy.svg" />
        </button>
      </div>
    </div>
    <div>
      <div class="text-xs font-medium mb-1">Token key</div>
      <div class="bg-bg-1 p-2 rounded-lg flex gap-2 items-center justify-between">
        <div class="break-words truncate">{{ tokens.key }}</div>
        <button (click)="copyText(tokens.key)" class="shrink-0">
          <img src="assets/kie/actions/copy.svg" />
        </button>
      </div>
    </div>
    <div>
      <div class="text-xs font-medium mb-1">Access token</div>
      <div class="bg-bg-1 p-2 rounded-lg flex flex-col gap-2">
        <div class="line-clamp-4">{{ tokens.token }}</div>
        <div class="flex justify-end">
          <button (click)="copyText(tokens.token)">
            <img src="assets/kie/actions/copy.svg" />
          </button>
        </div>
      </div>
    </div>
    <div class="text-sm">
      ** Token id, Token Key và Access token dùng để bảo mật khi thực hiện gọi api.
      <br />
      Tham khảo hướng dẫn
      <a
        class="underline text-brand-1"
        target="_blank"
        [href]="'https://vnptai.io/ldp/smartreader/vi/documents/tong-quan'"
        >tại đây</a
      >. Vui lòng không chia sẻ các keys trên vì an toàn thông tin Dự án của bạn. **
    </div>
  </div>
</ng-template>
