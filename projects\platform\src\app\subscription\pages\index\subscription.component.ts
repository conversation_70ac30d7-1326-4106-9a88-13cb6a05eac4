import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import UtilsService from '@platform/app/core/services/utils.service';
import { SubscriptionService } from '@platform/app/core/services/subscription.service';

@Component({
  selector: 'app-subscription',
  templateUrl: './subscription.component.html',
  styleUrls: ['./subscription.component.scss'],
})
export class SubscriptionComponent implements OnInit {
  formatAmount = this.utilsService.formatAmount;
  subscriptionInfo: any;
  subscriptionCheckRegister: any;
  canExtend = false;
  constructor(
    private subscriptionService: SubscriptionService,
    private router: Router,
    private utilsService: UtilsService
  ) {}

  ngOnInit(): void {
    this.subscriptionService
      .fetchSubscriptionStatus()
      .subscribe((subscriptionStatus) => {
        this.subscriptionInfo = subscriptionStatus.object;

        this.canExtend = ['limited', 'fix limited'].includes(
          this.subscriptionInfo?.planType?.toLowerCase()
        )
          ? this.subscriptionInfo.remainTime <= 6
          : false;
      });
  }

  navigateToExtendSubscription() {
    const uuidPlan = this.subscriptionInfo?.uuidPlan;
    if (this.canExtend && uuidPlan) {
      return this.router.navigate(['subscription', 'payment'], {
        state: { uuidPlan, action: 'extend' },
      });
    }
  }
}
