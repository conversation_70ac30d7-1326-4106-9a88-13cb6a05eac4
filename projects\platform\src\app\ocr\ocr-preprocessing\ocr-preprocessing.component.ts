import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators
} from '@angular/forms';
import { OcrService } from '@platform/app/core/services/ocr.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { get, isFinite, isString } from 'lodash';
import { ToastrService } from 'ngx-toastr';
import {
  catchError,
  concatMap,
  debounceTime,
  defer,
  delay,
  distinctUntilChanged,
  EMPTY,
  filter,
  finalize,
  firstValueFrom,
  from,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap
} from 'rxjs';
import { OcrExperienceService } from '@platform/app/core/services/ocr-experience.service';
import { PdfService } from '@platform/app/core/services/pdf.service';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-ocr-preprocessing',
  templateUrl: './ocr-preprocessing.component.html',
  styleUrls: ['./ocr-preprocessing.component.scss']
})
export class OcrPreprocessingComponent implements OnInit, OnDestroy {
  static readonly confirmLeaveMessage =
    'Dữ liệu tại tab "Tài liệu tải lên" sẽ chỉ được lưu cho tới hết phiên đăng nhập hoặc khi bạn đăng xuất. Hãy đảm bảo chuẩn hóa xong tài liệu và ấn "Số hóa tài liệu"';

  /* feature flags */
  static readonly documentViewerFF = {
    DisplayOcrResult: false,
    ViewerFitPageWidth: false,
    EditOcrResult: false,
    Zooming: true,
    PdfLazyLoading: true,
    OnlyDisplaySpecifiedPages: true,
    EditPageImage: true
  };

  /* TODO: cap by total number of file OR by total number of storage used */
  readonly RULE_ACCEPT = {
    mimetypes: ['application/pdf', 'image/jpeg', 'image/png'],
    accept: '.jpeg, .jpg, .png, .pdf',
    typeLabel: '*.pdf, *.jpeg, *.jpg, *.png',
    extensions: ['pdf', 'jpeg', 'jpg', 'png'],
    size: 50 * 1024 * 1024, // 10MB
    getSizeStr() {
      return this.size / (1024 * 1024) + 'MB';
    }
  };

  readonly maxFileCount = 100; // TODO: decide the cap, potential performance issue
  fileListFormArray: FormArray<
    FormGroup<{
      id: FormControl<string>;
      pageSelectionType: FormControl<'all' | 'custom'>;
      pageSelectionText: FormControl<string>;
    }>
  >;
  pageSelectionTextChangeSubscription: Subscription;
  fileList: {
    id: string;
    file: File;
    name: string;
    link: string;
    size: string;
    numPages?: number;
    createdAt: Date;
    pageSelectionText?: string;
    editedPageLinkObj?: { [pageIndex: number]: string };
  }[] = [];
  currentPageFileList: typeof this.fileList = []; // a subset of fileList
  tableLimit = 10;
  tablePageIndex = 1;

  @Output() fileChange = new EventEmitter<{
    fileId: string;
    fileLink: string;
    fileName: string;
    pages?: number[];
  }>();

  @Input()
  set afterLoadCompleteFileInfo(afterLoadEvent: {
    info: { numPages: number };
    fileId: string;
  }) {
    /* fileChange emit file to the parent compt => parent pass file to document-viewer compt read and disply, after the read it emit the info back to parent compt => parent compt pass the info to this compt. setup with Input(), Output() and Subject */
    if (!this.activeFile) return;
    if (this.activeFile.id !== afterLoadEvent.fileId) return;
    if (!!this.activeFile.numPages) return; // numPages is already set

    this.ocrService.updatePreprocessingFileInCache(this.activeFile.id, {
      numPages: afterLoadEvent.info.numPages
    });
    this.activeFile.numPages = afterLoadEvent.info.numPages;
    const fg = this.fileListFormArray?.controls.find(
      (item) => item.controls.id.value === this.activeFile.id
    );
    if (fg)
      fg.enable({
        /* prevent triggering valueChanges observable */
        emitEvent: false
      });
  }

  @Input()
  set editedPageImage(editedPageImageEvent: {
    data: { pageIndex: number; editedPageImageLink: string };
    fileId: string;
  }) {
    /* fileChange emit file to the parent compt => parent pass file to document-viewer compt read and disply, after the read it emit the info back to parent compt => parent compt pass the info to this compt. setup with Input(), Output() and Subject */
    if (!this.activeFile) return;
    if (this.activeFile.id !== editedPageImageEvent.fileId) return;

    /* TODO: logic to save these data to cache */

    if (!this.activeFile.editedPageLinkObj)
      this.activeFile.editedPageLinkObj = {
        [editedPageImageEvent.data.pageIndex]:
          editedPageImageEvent.data.editedPageImageLink
      };
    else
      this.activeFile.editedPageLinkObj[editedPageImageEvent.data.pageIndex] =
        editedPageImageEvent.data.editedPageImageLink;
  }

  convertBytesToMB = this.utilsService.convertBytesToMB;
  checked = false;
  indeterminate = false;
  setOfCheckedId = new Set<string>();
  activeFile: (typeof this.fileList)[number];
  exchangeTargetFile: (typeof this.fileList)[number];
  executionSubject = new Subject<{
    id: string;
    modifiedFile: File;
  }>();
  MaxPreprocessingFileListCount;

  private loadingInProgressCount = 0;
  _loading: boolean = false;
  set loading(loading) {
    if (loading) {
      this.loadingInProgressCount++;
      this._loading = true;
      this.spinner.show('ocr-preprocessing-loading');
    } else {
      this.loadingInProgressCount--;
      if (this.loadingInProgressCount <= 0) {
        this._loading = false;
        this.spinner.hide('ocr-preprocessing-loading');
        this.loadingInProgressCount = 0; // reset loadingInProgressCount to prevent negative value
      }
    }
  }
  get loading() {
    return this._loading;
  }
  destroy$ = new Subject<void>();

  constructor(
    private utilsService: UtilsService,
    private toastrService: ToastrService,
    private ocrService: OcrService,
    private ocrExperienceService: OcrExperienceService,
    private pdfService: PdfService,
    private fb: FormBuilder,
    private spinner: NgxSpinnerService
  ) {
    this.MaxPreprocessingFileListCount = this.ocrService.MaxPreprocessingFileListCount;
  }

  async ngOnInit() {
    this.ocrService.preprocessingFileList$
      .pipe(
        // skip(1),
        // take(1),
        takeUntil(this.destroy$),
        tap((preprocessingFileList) => {
          const fileList = preprocessingFileList;

          this.pageSelectionTextChangeSubscription?.unsubscribe();
          this.pageSelectionTextChangeSubscription = new Subscription();

          this.fileListFormArray?.clear();
          this.fileListFormArray = this.fb.array(
            fileList.map((file) => this.createNewFileFormGroup(file))
          );
          this.fileList = fileList;
        })
      )
      .subscribe();

    /* addFile + scanTableAsync, in sequential execution order */
    this.executionSubject
      .asObservable()
      .pipe(
        concatMap(({ id, modifiedFile }: { id: string; modifiedFile: File }) => {
          const invalid =
            this.ocrService.fileListCount['in-progress'] >=
            this.ocrService.MaxInProgressFileListCount;
          if (invalid) {
            const warningMessage = `Số lượng tài liệu đang xử lý tại cùng một thời điểm tối đa là ${this.ocrService.MaxInProgressFileListCount}. Vui lòng đợi các tài liệu xử lý xong trước khi tiếp tục`;
            const dup = this.toastrService.findDuplicate(
              undefined,
              warningMessage,
              false, // resetOnDuplicate
              false // countDuplicates
            );
            if (!dup) this.toastrService.warning(warningMessage);
            return EMPTY;
          }

          this.loading = true;
          const f = this.fileList.find((item) => item.id === id);
          const formData = new FormData();
          formData.append('title', f.name);
          formData.append('description', 'add file for platform`s OCR module');
          formData.append('file', modifiedFile, f.name);
          return this.ocrExperienceService
            .addFile({
              body: formData,
              isLandingPageMode: false,
              skipAppLoadingSpinner: true
            })
            .pipe(
              catchError((err) => {
                this.toastrService.error('addFile: Đã có lỗi xảy ra, vui lòng thử lại!');
                // return throwError(() => err); // rethrow error to stop execution at that point
                this.loading = false;
                return EMPTY;
              }),
              switchMap((addFileResp) =>
                this.ocrService.ocrScanTableAsync(addFileResp.object as any).pipe(
                  catchError((err) => {
                    this.toastrService.error(
                      'ocrScanTableAsync: Đã có lỗi xảy ra, vui lòng thử lại!'
                    );
                    // return throwError(() => err); // rethrow error to stop execution at that point
                    this.loading = false;
                    return EMPTY;
                  })
                )
              ),
              switchMap((ocrScanTableAsyncResp) =>
                defer(async () => {
                  try {
                    await this.ocrService.addInProgressFilesToCache([
                      {
                        id: f.id,
                        sessionId: ocrScanTableAsyncResp['object']['session_id'],
                        name: f.name,
                        file: modifiedFile
                      }
                    ]);
                    await this.deleteFile(f);
                  } catch (error) {
                    console.log(error);
                  }
                  this.loading = false;
                })
              )
            );
        })
      )
      .subscribe();
  }

  private createNewFileFormGroup(file: (typeof this.fileList)[number]) {
    const pageSelectionTextCtrl = this.fb.control<string>(file.pageSelectionText);
    const formGroup = this.fb.group({
      id: this.fb.control<string>(file.id, Validators.required),
      pageSelectionType: this.fb.control<'all' | 'custom'>(
        file.pageSelectionText ? 'custom' : 'all'
      ),
      pageSelectionText: pageSelectionTextCtrl
    });
    if (!file.numPages) formGroup.disable(); // disable all formGroup until it is enabled by afterLoadCompleteFileInfo

    /* any change is made to pageSelectionTextCtrl input (with throttling measures to help with performance) will be parsed, if valid, emit selected pages to document viewer to trigger re-render */
    this.pageSelectionTextChangeSubscription.add(
      pageSelectionTextCtrl.valueChanges
        .pipe(
          takeUntil(this.destroy$),
          // skip(1),
          filter((text) => isString(text)),
          filter(() => isFinite(file.numPages)),
          debounceTime(700),
          // distinctUntilChanged(),
          tap((text) => {
            const fileIndex = this.fileList.findIndex((item) => item.id === file.id);
            if (fileIndex === -1) return;

            const { valid } = this.convertTextIntoPageSelectionList(text, file.numPages);
            this.fileList[fileIndex].pageSelectionText = text;
            this.selectFile(this.fileList[fileIndex]);

            if (valid)
              /* only valid pageSelectionText change made it to cache */
              this.ocrService.updatePreprocessingFileInCache(file.id, {
                pageSelectionText: text
              });
            else {
              pageSelectionTextCtrl.setErrors({
                invalidPageSelection: file.numPages
                  ? `Lựa chọn trang không hợp lệ, lựa chọn trong khoảng từ 1 tới ${file.numPages}`
                  : 'File chưa được load thành công'
              });
              pageSelectionTextCtrl.parent.updateValueAndValidity();
            }
          })
        )
        .subscribe()
    );
    return formGroup;
  }

  private convertTextIntoPageSelectionList(
    text: string,
    numPages: number
  ): { valid: boolean; pages: number[] } {
    if (!isFinite(numPages)) return { valid: false, pages: null };
    /* validation */
    const regexPattern = /^\d+(-\d+)?(,\d+(-\d+)?)*$/;
    const regexValid = regexPattern.test(text);
    let pageSelectionValid = false;
    let pageSet = new Set<number>();

    if (regexValid) {
      const pageNumberAndPageRangeList = text.split(',').map((item) => item.trim());
      pageSelectionValid = pageNumberAndPageRangeList.every((item) => {
        if (item.includes('-')) {
          const [start, end] = item.split('-').map((item) => +item.trim());
          const rangeValid =
            0 < start && start <= numPages && 0 < end && end <= numPages && start < end;
          if (rangeValid) for (let i = start; i <= end; i++) pageSet.add(i);
          return rangeValid;
        } else {
          const page = +item;
          const pageValid = 0 < page && page <= numPages;
          if (pageValid) pageSet.add(page);
          return pageValid;
        }
      });
    }

    const valid = regexValid && pageSelectionValid;
    return {
      valid,
      pages: valid
        ? Array.from(pageSet)
            .sort((a, b) => a - b)
            .map((i) => i - 1) // 0 based page index
        : null
    };
  }

  getPageSelectionTextError(fileIndex: number, errorName: string) {
    return this.fileListFormArray.controls[fileIndex].controls.pageSelectionText.getError(
      errorName
    );
  }

  handlePageSelectionTypeChange(type: 'all' | 'custom', fileIndex: number) {
    if (type === 'all') {
      const fgControls = this.fileListFormArray.controls[fileIndex].controls;
      fgControls.pageSelectionText.setValue(null, { emitEvent: false }); // emitEvent = false => prevent triggering pageSelectionText.valueChanges observable with the value text = null => no setErrors() causing change detection warning
      const updateFile = this.fileList.find((item) => item.id === fgControls.id.value);
      updateFile.pageSelectionText = null;
      this.ocrService.updatePreprocessingFileInCache(fgControls.id.value, {
        pageSelectionText: null
      });
      this.selectFile(updateFile);
    }
  }

  async handleFileInputChange(e, inputFileElem: HTMLInputElement) {
    let files: File[] = Array.from(get(e, 'target.files', []));
    if (!files.length) return;
    if (
      files.length + this.fileList.length >
      this.ocrService.MaxPreprocessingFileListCount
    )
      return this.toastrService.warning(
        `Số lượng tải lên vượt quá mức cho phép. Tổng số tài liệu không vượt quá ${this.ocrService.MaxPreprocessingFileListCount}`
      );

    files = files
      .map((file) => {
        // Extracting the file extension from the file name
        const extension = file.name.split('.').pop().toLowerCase();

        // Check if the MIME type is in the accepted list and the file extension is in the accepted list
        if (
          !this.RULE_ACCEPT.mimetypes.includes(file.type) ||
          !this.RULE_ACCEPT.extensions.includes(extension)
        ) {
          this.toastrService.error(
            `File ${file.name} không đúng định dạng cho phép (Hỗ trợ file ${this.RULE_ACCEPT.extensions.join(', ')})`
          );
          return;
        }

        // Check file size
        if (file.size > this.RULE_ACCEPT.size) {
          this.toastrService.error(
            `File ${file.name} vượt quá dung lượng cho phép (Dung lượng tối đa cho phép ${this.RULE_ACCEPT.getSizeStr()})`
          );
          return;
        }

        return file;
      })
      .filter((file) => !!file);

    if (!files.length) return;

    let tmp = files;
    if (files.length + this.fileList.length > this.maxFileCount) {
      this.toastrService.error(`Tối đa upload ${this.maxFileCount} file`);
      if (this.maxFileCount - this.fileList.length <= 0) return;
      tmp = files.slice(0, this.maxFileCount - this.fileList.length);
    }

    const newAddedFiles: typeof this.fileList = [];
    for (const file of tmp) {
      const link = URL.createObjectURL(file);
      const size = this.convertBytesToMB(file.size);
      newAddedFiles.push({
        id: this.utilsService.makeId(7),
        file,
        name: file.name,
        link,
        size,
        numPages: null,
        createdAt: new Date(),
        pageSelectionText: null
      });
    }

    // this.onUploadFilesChange.emit({ action: 'add', addedFiles: newAddedFiles });
    /* this.fileList.push(...newAddedFiles);
    this.cdr.detectChanges();
    console.log(this.fileList); */
    /* cdf.detectChanges has no effect onto nz-table[nzData] with fileList being mutated, must update fileList the immutable way */
    let insertIndex = 0;
    if (this.exchangeTargetFile) {
      insertIndex = this.fileList.findIndex(
        (item) => item.id === this.exchangeTargetFile.id
      );
    }

    const fileList = [
      ...this.fileList.slice(0, insertIndex),
      ...newAddedFiles,
      ...this.fileList.slice(insertIndex)
    ];

    /* 1. try add to cache first (async operation) */
    try {
      await this.ocrService.addPreprocessingFilesToCache(
        newAddedFiles.map((item) => ({
          id: item.id,
          file: item.file,
          name: item.name,
          size: item.size,
          numPages: item.numPages,
          createdAt: item.createdAt,
          pageSelectionText: item.pageSelectionText
        })),
        'preprocessing',
        fileList.map((item) => item.id)
      );
    } catch (error) {
      console.log('addFileToCache failed', error);
      this.exchangeTargetFile = null;
      if (inputFileElem) inputFileElem.value = null;
      return;
    }
    /* 2. add new formGroup to fileListFormArray */
    newAddedFiles.forEach((newFile, index) => {
      this.fileListFormArray.insert(
        insertIndex + index,
        this.createNewFileFormGroup(newFile)
      );
    });
    /* 3. update fileList to reflect changes to view */
    this.fileList = fileList;

    if (this.exchangeTargetFile) {
      await this.deleteFile(this.exchangeTargetFile);
      this.exchangeTargetFile = null;
    }
    // reset input file element
    if (inputFileElem) inputFileElem.value = null;
  }

  handleFileInputCancel(e, inputFileElem: HTMLInputElement) {
    this.exchangeTargetFile = null;
  }

  async deleteFile(file: (typeof this.fileList)[number]) {
    this.loading = true;
    /* 1. remove from cache (async operation) */
    await this.ocrService.deletePreprocessingFileFromCache(file);
    /* 2. remove from fileList, changes is reflected into view */
    this.fileList = this.fileList.filter((item) => item.id !== file.id);
    /* 3. remove formGroup from fileListFormArray */
    const index = this.fileListFormArray.controls.findIndex(
      (gr) => gr.value.id === file.id
    );
    if (index !== -1) this.fileListFormArray.removeAt(index);
    /* 4. remove checked id if the file.id in the set */
    this.onItemChecked(file.id, false);

    /* TODO: remove subscription from pageSelectionTextChangeSubscription */
    if (this.activeFile?.id === file.id) {
      this.activeFile = null;
      this.fileChange.emit(null);
    }
    this.loading = false;
  }

  async deleteCheckedItems() {
    const setOfCheckedId = Array.from(this.setOfCheckedId);
    if (!setOfCheckedId.length) return;

    for (const fileId of setOfCheckedId) {
      const file = this.fileList.find((item) => item.id === fileId);
      if (!file) continue;
      await this.deleteFile(file);
    }
  }

  async exchangeFile(
    file: (typeof this.fileList)[number],
    inputFileElem: HTMLInputElement
  ) {
    this.exchangeTargetFile = file;
    inputFileElem.click();
  }

  selectFile(file?: (typeof this.fileList)[number]) {
    if (!file) this.fileChange.emit(null);
    let pages;
    if (file.pageSelectionText && file.numPages) {
      const { valid, pages: tmpPages } = this.convertTextIntoPageSelectionList(
        file.pageSelectionText,
        file.numPages
      );
      if (valid) pages = tmpPages;
    }
    this.fileChange.emit({
      fileId: file.id,
      fileLink: file.link,
      fileName: file.name,
      pages
    });
    this.activeFile = file;
  }

  onAllChecked(checked: boolean): void {
    this.currentPageFileList.forEach(({ id }) => this.updateCheckedSet(id, checked));
    this.refreshCheckedStatus();
  }

  uncheckAll() {
    this.setOfCheckedId.clear();
    this.refreshCheckedStatus();
  }

  onCurrentPageDataChange(currentPageFileList: any[]): void {
    this.currentPageFileList = currentPageFileList;
    this.refreshCheckedStatus();
  }

  private updateCheckedSet(id: string, checked: boolean): void {
    if (checked) {
      this.setOfCheckedId.add(id);
    } else {
      this.setOfCheckedId.delete(id);
    }
  }

  private refreshCheckedStatus(): void {
    if (!this.currentPageFileList.length) {
      this.checked = false;
      this.indeterminate = false;
      return;
    }
    this.checked = this.currentPageFileList.every(({ id }) =>
      this.setOfCheckedId.has(id)
    );
    this.indeterminate =
      this.currentPageFileList.some(({ id }) => this.setOfCheckedId.has(id)) &&
      !this.checked;
  }

  onItemChecked(id: string, checked: boolean) {
    this.updateCheckedSet(id, checked);
    this.refreshCheckedStatus();
  }

  onLimitChange(limit) {
    this.tablePageIndex = 1;
    this.tableLimit = limit;
  }

  showPageSelectionTextInput(fileIndex: number) {
    if (fileIndex >= this.fileListFormArray.controls.length || fileIndex < 0)
      return false;
    return (
      this.fileListFormArray.controls[fileIndex].value.pageSelectionType === 'custom'
    );
  }

  async executeMany() {
    const files = this.fileList.filter((file) =>
      Array.from(this.setOfCheckedId.values()).includes(file.id)
    );
    const executeErrorList = [];
    for (const file of files) {
      try {
        await this.execute(file, true);
      } catch (error) {
        executeErrorList.push(`${file.name}`);
      }
    }

    executeErrorList.length &&
      this.toastrService.error(
        `<div>Kiểm tra lại các file không hợp lệ sau để tiếp tục xử lý:</div>
          <ul class="list-disc list-inside">
            ${executeErrorList.map((error) => '<li>' + error + '</li>').join('')}
          </ul>
        `,
        null,
        { closeButton: true, disableTimeOut: true, enableHtml: true }
      );
  }

  async execute(file: (typeof this.fileList)[number], rethrowOnError?: boolean) {
    try {
      const fg = this.fileListFormArray?.controls.find(
        (item) => item.controls.id.value === file.id
      );

      /* validation */
      if (!fg) throw new Error('Unreachable error');
      if (fg.invalid) throw new Error('Lựa chọn các trang không hợp lệ');
      if (
        (fg.value.pageSelectionType === 'custom' ||
          Object.values(file.editedPageLinkObj || {}).length !== 0) &&
        !file.numPages
      )
        // wait til the file is finished loading to proceed,
        // quite unreachable, pageSelectionType can only be set to custom once numPages is loaded, the same to editedPageLinkObj
        throw new Error(
          'File chưa được xử lý hoàn chỉnh, chọn lại file và đợi cho tới khi file được xử lý xong'
        );

      let pageSelectionList: number[];
      if (fg.value.pageSelectionType === 'custom') {
        if (file.pageSelectionText === null) {
          // type custom but never enter pageSelection, set value to '' to trigger a failed validation
          fg.controls['pageSelectionText'].setValue('');
          throw new Error('Hãy lựa chọn các trang trong phạm vi OCR');
        }

        const { valid, pages } = this.convertTextIntoPageSelectionList(
          file.pageSelectionText,
          file.numPages
        );
        /* quite unreachable in normal usage */
        if (!valid) throw new Error('Lựa chọn các trang không hợp lệ.');
        pageSelectionList = pages;
      }

      this.loading = true;
      if (file.file.type !== 'application/pdf') {
        this.executionSubject.next({
          id: file.id,
          modifiedFile:
            file.editedPageLinkObj && file.editedPageLinkObj[0]
              ? new File(
                  [await (await fetch(file.editedPageLinkObj[0])).arrayBuffer()],
                  file.name,
                  { type: file.file.type }
                )
              : file.file
        });
      } else {
        const modifiedPdfFileLink = await this.pdfService.createModifiedPdfFile({
          originalPdfLink: file.link,
          pageIndexSelectionList: pageSelectionList,
          editedPageLinkObj: file.editedPageLinkObj
        });

        this.executionSubject.next({
          id: file.id,
          modifiedFile: new File(
            [await (await fetch(modifiedPdfFileLink)).arrayBuffer()],
            file.name,
            { type: file.file.type }
          )
        });
      }
    } catch (error) {
      if (rethrowOnError) throw error;
      this.toastrService.error(error instanceof Error ? error.message : error);
    } finally {
      this.loading = false;
    }
  }

  ngOnDestroy(): void {
    // console.log('ocr preprocessing destroyed');
    this.destroy$.next();
    this.destroy$.complete();
    this.pageSelectionTextChangeSubscription?.unsubscribe();
  }
}
