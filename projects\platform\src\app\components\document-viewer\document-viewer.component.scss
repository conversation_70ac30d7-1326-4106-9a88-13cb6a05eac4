:host {
  --bg-color: var(--document-viewer-bg-color, #F0F1F4);
  --text-color: var(--document-viewer-text-color, #2b2d3b);
  --header-bg-color: var(--document-viewer-header-bg-color, #fff);
  --header-text-color: var(--document-viewer-header-text-color, #2b2d3b);

  flex: auto;
  width: 100%;
  min-width: 500px;
  position: relative;
  display: flex;
  flex-direction: column;

  @apply text-[--text-color];
  @apply bg-[--bg-color];

  .document-viewer-header {
    @apply py-3 px-6 w-full flex-shrink-0 flex gap-4 justify-between items-center;
    @apply text-[--header-text-color] bg-[--header-bg-color];
  }


  .controls {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    z-index: 100;
    font-size: 12px;

    &.extra {
      right: 0;
      top: 100%;
      transform: translate(0, -100%);
      // background-color: black;
    }

    &.info {
      @apply bg-[--bg-color] p-[2px] bottom-0 left-0;
    }
  }

  ::ng-deep {
    button[type="button"].ant-switch {
      background: #272836;

      &.ant-switch-checked {
        background: #009B4E;
      }
    }
  }
}