import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  FunctionType,
  TemplateTypes,
  FunctionTypes,
  Template,
  RestrictionType,
  Mode,
  TemplateGroups
} from '../ocr-experience';
import { environment } from '@platform/environment/environment';

@Component({
  selector: 'app-ocr-input',
  templateUrl: './ocr-input.component.html',
  styleUrls: ['./ocr-input.component.scss']
})
export class OcrInputComponent implements OnInit {
  readonly Mode = Mode;
  mode: Mode = Mode.Platform; // default
  readonly FunctionType = FunctionType;
  readonly TemplateTypes = TemplateTypes;
  readonly RestrictionType = RestrictionType;

  selectedFunction: (typeof FunctionTypes)[number] = FunctionTypes[0];

  selectedTemplate: Template;
  templateOptions: Template[] = [];
  groupedTemplateOptions: { label: string; templates: Template[] }[] = [];
  functionOptions: (typeof FunctionTypes)[number][];

  ocrInputFile: File & { pdfCreatedFromImages?: boolean };

  restriction: RestrictionType;

  constructor(private router: Router) {
    this.mode = this.router.url.includes('/ldp/ocr-experience')
      ? Mode.LDP
      : this.router.url.includes('/demo/ocr-experience')
        ? Mode.Demo
        : Mode.Platform;

    this.functionOptions = FunctionTypes.filter(
      (item) =>
        /* FIXME: temporary hide FunctionType.GoiYXuLyVanBan */
        (this.mode === Mode.Platform && !item.WIP[environment.envName]) || item.showInLDP
    );
  }

  ngOnInit(): void {
    const selectedTemplateValue = history.state.selectedTemplate;
    // handle naviagtion back from ocr-result
    const selectedTemplate = this.TemplateTypes.find(
      (item) => item.value === selectedTemplateValue
    );

    if (selectedTemplate) {
      this.selectedTemplate = selectedTemplate;
      this.selectedFunction = this.functionOptions.find(
        (item) => item.value === this.selectedTemplate.functionType
      );
    } else {
      this.selectedFunction = this.functionOptions[0];
      if (!this.selectedFunction) return;
      this.selectedTemplate = this.TemplateTypes.find(
        (item) => item.value === this.selectedFunction.defaultTemplate
      );
    }

    const templateOptions = this.TemplateTypes.filter(
      (item) => item.functionType === this.selectedTemplate.functionType
    );
    if (this.selectedFunction?.allowTemplateGroup) {
      this.groupedTemplateOptions = this.getGroupedTemplateOptions(templateOptions);
      this.templateOptions = [];
    } else {
      this.groupedTemplateOptions = [];
      this.templateOptions = templateOptions;
    }

    this.checkRestriction();
  }

  handleSelectFunction(selectedFunction: string) {
    if (selectedFunction === this.selectedFunction.value) return;
    this.ocrInputFile = null;
    this.selectedFunction = this.functionOptions.find(
      (item) => item.value === selectedFunction
    );

    const templateOptions = this.TemplateTypes.filter(
      (item) => item.functionType === this.selectedFunction.value
    );
    if (this.selectedFunction?.allowTemplateGroup) {
      this.groupedTemplateOptions = this.getGroupedTemplateOptions(templateOptions);
      this.templateOptions = [];
    } else {
      this.groupedTemplateOptions = [];
      this.templateOptions = templateOptions;
    }

    this.selectedTemplate = this.TemplateTypes.find(
      (item) =>
        item.value === this.selectedFunction.defaultTemplate &&
        item.functionType === this.selectedFunction.value
    );

    this.checkRestriction();
  }

  handleSelectTemplate(selectedTemplate) {
    if (selectedTemplate.value === this.selectedTemplate.value) return;
    this.ocrInputFile = null;
    this.selectedTemplate = selectedTemplate;

    this.checkRestriction();
  }

  setOcrInputFile(file: File & { pdfCreatedFromImages?: boolean }) {
    this.ocrInputFile = file;
  }

  getGroupedTemplateOptions(templates: Template[]) {
    const groupedTemplatesObj = templates.reduce<{
      [prop: Template['templateGroup']]: { label: string; templates: Template[] };
    }>((acc, template) => {
      let groupObj: { label: string; templates: Template[] } =
        acc[template.templateGroup];
      if (!groupObj) {
        groupObj = {
          label:
            TemplateGroups.find((gr) => gr.value === template.templateGroup)?.label ||
            'N/A',
          templates: []
        };
        acc[template.templateGroup] = groupObj;
      }
      groupObj.templates.push(template);
      return acc;
    }, {});
    return Object.values(groupedTemplatesObj);
  }

  private checkRestriction() {
    if (!this.selectedFunction) return;
    this.restriction = null;
    // check function type
    if (this.selectedFunction.WIP[environment.envName]) {
      this.restriction = RestrictionType.WIP;
      return;
    }
    // check template
    if (this.mode === Mode.LDP && this.selectedTemplate.restrictedInLDP) {
      this.restriction = RestrictionType.LDP;
    }
  }
}
