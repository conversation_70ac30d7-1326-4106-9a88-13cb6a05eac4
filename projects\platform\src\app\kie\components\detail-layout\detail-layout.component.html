<div
  class="h-14 flex items-center justify-between px-6 bg-[linear-gradient(90deg,#0A83FF_-36.13%,#3634A2_103.47%)]"
>
  <div class="flex items-center gap-4">
    <button (click)="goBack()">
      <svg
        width="20"
        height="20"
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.97533 4.94141L2.91699 9.99974L7.97533 15.0581"
          stroke="white"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M17.0836 10H3.05859"
          stroke="white"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
    <div class="text-lg font-semibold">{{ title || 'Title' }}</div>
  </div>
  <div class="flex items-center gap-4">
    <ng-container [ngTemplateOutlet]="actionsTemplate"></ng-container>
  </div>
</div>
<div class="flex-auto h-[1px] flex justify-between">
  <ng-container [ngTemplateOutlet]="contentTemplate"></ng-container>
</div>
