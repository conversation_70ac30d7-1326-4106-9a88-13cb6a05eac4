import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SubscriptionRoutingModule } from './subscription-routing.module';
import { SubscriptionComponent } from './pages/index/subscription.component';
import { InfoComponent } from './pages/info/info.component';
import { PaymentComponent } from './pages/payment/payment.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ContactModalComponent } from './components/contact-modal/contact-modal.component';
import { ResponsePaymentComponent } from './pages/response-payment/response-payment.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';

@NgModule({
  declarations: [
    SubscriptionComponent,
    InfoComponent,
    PaymentComponent,
    ContactModalComponent,
    ResponsePaymentComponent,
  ],
  imports: [
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    SubscriptionRoutingModule,
    NzTableModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
  ],
})
export class SubscriptionModule {}
