import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import UtilsService from '@platform/app/core/services/utils.service';

@Component({
  selector: 'app-token-info',
  templateUrl: './token-info.component.html',
  styleUrls: ['./token-info.component.scss']
})
export class TokenInfoComponent implements OnInit {
  tokens = {
    publicKey: '',
    uuidProjectServicePlan: ''
  };
  accessToken: string = '';

  constructor(
    private toastrService: ToastrService,
    private utilsService: UtilsService
  ) {}

  async ngOnInit() {
    const decodedAccessToken = this.utilsService.getDecodedAccessTokenPayload();
    this.tokens.publicKey = decodedAccessToken?.idg_token_key;
    this.tokens.uuidProjectServicePlan = decodedAccessToken?.idg_token_id;
    this.accessToken = 'Bearer ' + decodedAccessToken?.idg_access_token;
  }

  copy(selector: any) {
    navigator.clipboard.writeText(selector.innerHTML);
    this.toastrService.success('Copied');
  }
}
