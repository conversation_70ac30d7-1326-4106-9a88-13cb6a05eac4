<div>
  <div class="mb-4 flex items-baseline gap-4">
    <input
      id="userFileInput"
      type="radio"
      [checked]="!isUsingSampleFile"
      (click)="handleChangeMethod()"
      [disabled]="!!restriction"
    />
    <div class="flex flex-col flex-auto">
      <label
        for="userFileInput"
        [class]="
          'flex-1 rounded-lg border py-4 px-3 ' +
          (!isUsingSampleFile ? 'border-brand-1' : 'border-line')
        "
      >
        <div
          class="flex relative items-center justify-left gap-3 bg-[#F0F1F4] rounded-[4px] p-3 border border-transparent"
          #inputFileCtn
          [ngClass]="{ 'hover:border-line': !isUsingSampleFile }"
          (dragover)="!isUsingSampleFile && inputFileCtn?.classList?.add('!border-line')"
          (dragleave)="
            !isUsingSampleFile && inputFileCtn?.classList?.remove('!border-line')
          "
          (drop)="!isUsingSampleFile && inputFileCtn?.classList?.remove('!border-line')"
        >
          <input
            class="absolute top-0 left-0 w-full h-full z-10 opacity-0 text-[0px] cursor-pointer"
            [ngClass]="{ '!cursor-not-allowed': !!restriction || isUsingSampleFile }"
            type="file"
            id="customFile"
            #inputFileElem
            [accept]="selectedTemplate.acceptType"
            (change)="handleChangeFile($event, inputFileElem)"
            [disabled]="!!restriction || isUsingSampleFile"
          />
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M18.3332 10C18.3332 10 18.3332 11.2857 18.3332 13.6964V13.7738C18.3332 16.4345 16.7655 17.5 12.8506 17.5H7.14915C3.2343 17.5 1.66661 16.4345 1.66661 13.7738V13.6964C1.66661 11.3036 1.6665 10 1.6665 10"
              stroke="#6C7093"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M10 12.4999V3.0166"
              stroke="#6C7093"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12.7918 4.87467L10.0002 2.08301L7.2085 4.87467"
              stroke="#6C7093"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <div>
            <div class="font-medium text-text-1">
              <span class="text-brand-1">Chọn</span> hoặc kéo thả file tại đây
            </div>
            <div class="text-[10px] text-text-3">
              File dạng *.pdf, *.jpeq, *.jpg, *.png
            </div>
          </div>
        </div>
        <div
          *ngIf="file && !isUsingSampleFile"
          class="flex items-center mt-3 gap-2 text-text-1"
        >
          <img src="assets/img/rpa/icon-pdf.svg" alt="icon-pdf" />
          <div>
            <span style="overflow-wrap: anywhere" class="font-medium">{{
              file.name
            }}</span>
            ({{ (file.size / (1024 * 1024)).toFixed(2) + 'Mb' }})
          </div>
          <button class="ml-auto" (click)="clearSelectedFile()">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14 3.98665C11.78 3.76665 9.54667 3.65332 7.32 3.65332C6 3.65332 4.68 3.71999 3.36 3.85332L2 3.98665"
                stroke="#6C7093"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M5.6665 3.31301L5.81317 2.43967C5.91984 1.80634 5.99984 1.33301 7.1265 1.33301H8.87317C9.99984 1.33301 10.0865 1.83301 10.1865 2.44634L10.3332 3.31301"
                stroke="#6C7093"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12.5664 6.09375L12.1331 12.8071C12.0598 13.8537 11.9998 14.6671 10.1398 14.6671H5.85977C3.99977 14.6671 3.93977 13.8537 3.86644 12.8071L3.43311 6.09375"
                stroke="#6C7093"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </label>
      <div
        *ngIf="selectedTemplate && selectedTemplate.allowMergeImagesIntoPdfOption"
        class="mt-3"
      >
        <b>Lưu ý:</b> Nếu tài liệu của bạn dạng <b>ảnh chụp</b> (một hoặc nhiều
        <b>ảnh rời rạc</b>) từ các thiết bị di động<!--  và có thể bị nghiêng -->. Click
        <button
          (click)="showImagesIntoPdfModal()"
          class="font-semibold text-brand-1 hover:underline"
          [ngClass]="{ 'cursor-not-allowed hover:no-underline': isUsingSampleFile }"
        >
          vào đây
        </button>
        để tạo file duy nhất
      </div>
    </div>
  </div>
  <ng-container *ngIf="mode === Mode.Platform">
    <div class="font-semibold" *ngIf="selectedTemplate.sampleFiles?.length">File mẫu</div>
    <div
      class="flex items-center gap-4 mt-4"
      *ngFor="let file of selectedTemplate.sampleFiles"
    >
      <input
        [disabled]="!!restriction"
        class=""
        type="radio"
        [id]="file.path"
        [checked]="isUsingSampleFile === file.path"
        (click)="handleChangeMethod(file.path)"
      />
      <label [for]="file.path" class="flex-1">
        <div
          [class]="
            'flex items-center p-4 gap-2 font-semibold border rounded-lg ' +
            (isUsingSampleFile === file.path ? 'border-brand-1' : 'border-line')
          "
          [ngStyle]="{ opacity: isUsingSampleFile ? 1 : 0.75 }"
        >
          <img src="assets/img/rpa/icon-pdf.svg" alt="icon-pdf" />
          <a class="text-[#333333]" [href]="file.path" target="_blank">{{ file.name }}</a>
          <a class="ml-auto shrink-0" [href]="file.path" download
            ><img src="assets/img/rpa/download.svg" alt="download" />
          </a>
        </div>
      </label>
    </div>
  </ng-container>
  <div *ngIf="mode === Mode.LDP">
    <ngx-recaptcha2
      #captchaElem
      [siteKey]="siteKey"
      (reset)="handleCaptchaReset()"
      (expire)="handleCaptchaExpire()"
      (error)="handleCaptchaError()"
      (load)="handleCaptchaLoad()"
      (success)="handleCaptchaSuccess($event)"
      [useGlobalDomain]="false"
      size="normal"
      hl="en"
      theme="light"
    >
    </ngx-recaptcha2>
  </div>
  <div
    *ngIf="envName === 'sandbox' && selectedTemplate?.allowDevMode"
    class="flex items-center gap-2 mt-4"
  >
    <nz-switch nzSize="small" [(ngModel)]="devModeEnabled"></nz-switch>
    <label class="font-medium">Dev Mode</label>
  </div>
  <button
    [disabled]="!!restriction"
    type="button"
    [class]="
      'flex items-center justify-center gap-2 w-full rounded-lg h-10 bg-brand-1 mt-8 text-white' +
      (!!restriction ? ' opacity-75 cursor-not-allowed ' : '')
    "
    (click)="handleSubmit()"
  >
    <svg
      width="17"
      height="19"
      viewBox="0 0 17 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask id="path-1-inside-1_9281_1576" fill="white">
        <path
          d="M9.50016 0.666992V5.66699C9.50016 6.10902 9.67576 6.53294 9.98832 6.8455C10.3009 7.15806 10.7248 7.33366 11.1668 7.33366H16.1668V15.667C16.1668 16.109 15.9912 16.5329 15.6787 16.8455C15.3661 17.1581 14.9422 17.3337 14.5002 17.3337H8.82516C9.68768 16.4358 10.2146 15.2683 10.3172 14.0275C10.4198 12.7867 10.0918 11.5485 9.38846 10.5211C8.68512 9.49382 7.64936 8.74015 6.4555 8.38697C5.26163 8.03379 3.98254 8.10266 2.8335 8.58199V2.33366C2.8335 1.89163 3.00909 1.46771 3.32165 1.15515C3.63421 0.842587 4.05814 0.666992 4.50016 0.666992H9.50016ZM10.7502 1.08366V5.66699C10.7502 5.7775 10.7941 5.88348 10.8722 5.96162C10.9503 6.03976 11.0563 6.08366 11.1668 6.08366H15.7502L10.7502 1.08366ZM9.50016 13.5837C9.50016 14.1856 9.38161 14.7815 9.15128 15.3376C8.92094 15.8937 8.58334 16.399 8.15774 16.8246C7.73213 17.2502 7.22687 17.5878 6.6708 17.8181C6.11472 18.0484 5.51872 18.167 4.91683 18.167C4.31494 18.167 3.71894 18.0484 3.16286 17.8181C2.60679 17.5878 2.10153 17.2502 1.67592 16.8246C1.25032 16.399 0.912716 15.8937 0.682382 15.3376C0.452047 14.7815 0.333496 14.1856 0.333496 13.5837C0.333496 12.3681 0.816382 11.2023 1.67592 10.3428C2.53547 9.48321 3.70125 9.00033 4.91683 9.00033C6.1324 9.00033 7.29819 9.48321 8.15774 10.3428C9.01728 11.2023 9.50016 12.3681 9.50016 13.5837ZM7.71183 11.622C7.67312 11.5832 7.62714 11.5524 7.57652 11.5314C7.5259 11.5104 7.47164 11.4996 7.41683 11.4996C7.36202 11.4996 7.30776 11.5104 7.25713 11.5314C7.20651 11.5524 7.16053 11.5832 7.12183 11.622L4.0835 14.6612L2.71183 13.2887C2.63359 13.2104 2.52748 13.1665 2.41683 13.1665C2.30618 13.1665 2.20007 13.2104 2.12183 13.2887C2.04359 13.3669 1.99964 13.473 1.99964 13.5837C1.99964 13.6943 2.04359 13.8004 2.12183 13.8787L3.7885 15.5453C3.8272 15.5841 3.87318 15.6149 3.9238 15.6359C3.97442 15.6569 4.02869 15.6677 4.0835 15.6677C4.1383 15.6677 4.19257 15.6569 4.24319 15.6359C4.29381 15.6149 4.33979 15.5841 4.3785 15.5453L7.71183 12.212C7.75063 12.1733 7.78142 12.1273 7.80242 12.0767C7.82343 12.0261 7.83424 11.9718 7.83424 11.917C7.83424 11.8622 7.82343 11.8079 7.80242 11.7573C7.78142 11.7067 7.75063 11.6607 7.71183 11.622Z"
        />
      </mask>
      <path
        d="M9.50016 0.666992V5.66699C9.50016 6.10902 9.67576 6.53294 9.98832 6.8455C10.3009 7.15806 10.7248 7.33366 11.1668 7.33366H16.1668V15.667C16.1668 16.109 15.9912 16.5329 15.6787 16.8455C15.3661 17.1581 14.9422 17.3337 14.5002 17.3337H8.82516C9.68768 16.4358 10.2146 15.2683 10.3172 14.0275C10.4198 12.7867 10.0918 11.5485 9.38846 10.5211C8.68512 9.49382 7.64936 8.74015 6.4555 8.38697C5.26163 8.03379 3.98254 8.10266 2.8335 8.58199V2.33366C2.8335 1.89163 3.00909 1.46771 3.32165 1.15515C3.63421 0.842587 4.05814 0.666992 4.50016 0.666992H9.50016ZM10.7502 1.08366V5.66699C10.7502 5.7775 10.7941 5.88348 10.8722 5.96162C10.9503 6.03976 11.0563 6.08366 11.1668 6.08366H15.7502L10.7502 1.08366ZM9.50016 13.5837C9.50016 14.1856 9.38161 14.7815 9.15128 15.3376C8.92094 15.8937 8.58334 16.399 8.15774 16.8246C7.73213 17.2502 7.22687 17.5878 6.6708 17.8181C6.11472 18.0484 5.51872 18.167 4.91683 18.167C4.31494 18.167 3.71894 18.0484 3.16286 17.8181C2.60679 17.5878 2.10153 17.2502 1.67592 16.8246C1.25032 16.399 0.912716 15.8937 0.682382 15.3376C0.452047 14.7815 0.333496 14.1856 0.333496 13.5837C0.333496 12.3681 0.816382 11.2023 1.67592 10.3428C2.53547 9.48321 3.70125 9.00033 4.91683 9.00033C6.1324 9.00033 7.29819 9.48321 8.15774 10.3428C9.01728 11.2023 9.50016 12.3681 9.50016 13.5837ZM7.71183 11.622C7.67312 11.5832 7.62714 11.5524 7.57652 11.5314C7.5259 11.5104 7.47164 11.4996 7.41683 11.4996C7.36202 11.4996 7.30776 11.5104 7.25713 11.5314C7.20651 11.5524 7.16053 11.5832 7.12183 11.622L4.0835 14.6612L2.71183 13.2887C2.63359 13.2104 2.52748 13.1665 2.41683 13.1665C2.30618 13.1665 2.20007 13.2104 2.12183 13.2887C2.04359 13.3669 1.99964 13.473 1.99964 13.5837C1.99964 13.6943 2.04359 13.8004 2.12183 13.8787L3.7885 15.5453C3.8272 15.5841 3.87318 15.6149 3.9238 15.6359C3.97442 15.6569 4.02869 15.6677 4.0835 15.6677C4.1383 15.6677 4.19257 15.6569 4.24319 15.6359C4.29381 15.6149 4.33979 15.5841 4.3785 15.5453L7.71183 12.212C7.75063 12.1733 7.78142 12.1273 7.80242 12.0767C7.82343 12.0261 7.83424 11.9718 7.83424 11.917C7.83424 11.8622 7.82343 11.8079 7.80242 11.7573C7.78142 11.7067 7.75063 11.6607 7.71183 11.622Z"
        fill="white"
      />
      <path
        d="M9.50016 0.666992H10.5002V-0.333008H9.50016V0.666992ZM16.1668 7.33366H17.1668V6.33366H16.1668V7.33366ZM15.6787 16.8455L16.3858 17.5526L15.6787 16.8455ZM8.82516 17.3337L8.10401 16.6409L6.47784 18.3337H8.82516V17.3337ZM2.8335 8.58199H1.8335V10.0827L3.2185 9.50491L2.8335 8.58199ZM4.50016 0.666992V-0.333008V0.666992ZM10.7502 1.08366L11.4573 0.376552L9.75016 -1.33055V1.08366H10.7502ZM15.7502 6.08366V7.08366H18.1644L16.4573 5.37655L15.7502 6.08366ZM4.91683 18.167V19.167V18.167ZM0.333496 13.5837H-0.666504H0.333496ZM7.12183 11.622L7.82903 12.329L7.82983 12.3282L7.12183 11.622ZM4.0835 14.6612L3.37617 15.368L4.08338 16.0757L4.7907 15.3682L4.0835 14.6612ZM2.71183 13.2887L3.41915 12.5818L3.41894 12.5816L2.71183 13.2887ZM2.41683 13.1665V12.1665V13.1665ZM1.99964 13.5837H2.99964H1.99964ZM2.12183 13.8787L1.41472 14.5858L2.12183 13.8787ZM3.7885 15.5453L4.4965 14.8391L4.4956 14.8382L3.7885 15.5453ZM4.3785 15.5453L3.67139 14.8382L3.6705 14.8391L4.3785 15.5453ZM7.71183 12.212L7.00562 11.504L7.00472 11.5049L7.71183 12.212ZM7.71183 11.622L7.00383 12.3282L7.00562 12.33L7.71183 11.622ZM8.50016 0.666992V5.66699H10.5002V0.666992H8.50016ZM8.50016 5.66699C8.50016 6.37424 8.78111 7.05251 9.28121 7.55261L10.6954 6.1384C10.5704 6.01337 10.5002 5.8438 10.5002 5.66699H8.50016ZM9.28121 7.55261C9.78131 8.05271 10.4596 8.33366 11.1668 8.33366V6.33366C10.99 6.33366 10.8204 6.26342 10.6954 6.1384L9.28121 7.55261ZM11.1668 8.33366H16.1668V6.33366H11.1668V8.33366ZM15.1668 7.33366V15.667H17.1668V7.33366H15.1668ZM15.1668 15.667C15.1668 15.8438 15.0966 16.0134 14.9716 16.1384L16.3858 17.5526C16.8859 17.0525 17.1668 16.3742 17.1668 15.667H15.1668ZM14.9716 16.1384C14.8465 16.2634 14.677 16.3337 14.5002 16.3337V18.3337C15.2074 18.3337 15.8857 18.0527 16.3858 17.5526L14.9716 16.1384ZM14.5002 16.3337H8.82516V18.3337H14.5002V16.3337ZM9.54632 18.0264C10.5681 16.9628 11.1922 15.5797 11.3138 14.1099L9.32057 13.9451C9.23691 14.9568 8.80729 15.9088 8.10401 16.6409L9.54632 18.0264ZM11.3138 14.1099C11.4353 12.64 11.0468 11.1732 10.2136 9.95621L8.56331 11.0861C9.1368 11.9237 9.40422 12.9334 9.32057 13.9451L11.3138 14.1099ZM10.2136 9.95621C9.38042 8.73924 8.15345 7.84643 6.73917 7.42805L6.17182 9.34589C7.14528 9.63387 7.98982 10.2484 8.56331 11.0861L10.2136 9.95621ZM6.73917 7.42805C5.3249 7.00967 3.80967 7.09125 2.44849 7.65908L3.2185 9.50491C4.15541 9.11407 5.19836 9.05791 6.17182 9.34589L6.73917 7.42805ZM3.8335 8.58199V2.33366H1.8335V8.58199H3.8335ZM3.8335 2.33366C3.8335 2.15685 3.90373 1.98728 4.02876 1.86225L2.61454 0.448041C2.11445 0.948138 1.8335 1.62642 1.8335 2.33366H3.8335ZM4.02876 1.86225C4.15378 1.73723 4.32335 1.66699 4.50016 1.66699V-0.333008C3.79292 -0.333008 3.11464 -0.0520564 2.61454 0.448041L4.02876 1.86225ZM4.50016 1.66699H9.50016V-0.333008H4.50016V1.66699ZM9.75016 1.08366V5.66699H11.7502V1.08366H9.75016ZM9.75016 5.66699C9.75016 6.04271 9.89942 6.40305 10.1651 6.66873L11.5793 5.25451C11.6887 5.36391 11.7502 5.51229 11.7502 5.66699H9.75016ZM10.1651 6.66873C10.4308 6.93441 10.7911 7.08366 11.1668 7.08366V5.08366C11.3215 5.08366 11.4699 5.14512 11.5793 5.25451L10.1651 6.66873ZM11.1668 7.08366H15.7502V5.08366H11.1668V7.08366ZM16.4573 5.37655L11.4573 0.376552L10.0431 1.79077L15.0431 6.79077L16.4573 5.37655ZM8.50016 13.5837C8.50016 14.0542 8.40748 14.5202 8.2274 14.9549L10.0752 15.7203C10.3557 15.0429 10.5002 14.3169 10.5002 13.5837H8.50016ZM8.2274 14.9549C8.04732 15.3897 7.78337 15.7847 7.45063 16.1175L8.86484 17.5317C9.3833 17.0132 9.79457 16.3977 10.0752 15.7203L8.2274 14.9549ZM7.45063 16.1175C7.11789 16.4502 6.72286 16.7141 6.28811 16.8942L7.05348 18.742C7.73088 18.4614 8.34638 18.0501 8.86484 17.5317L7.45063 16.1175ZM6.28811 16.8942C5.85336 17.0743 5.3874 17.167 4.91683 17.167V19.167C5.65004 19.167 6.37608 19.0226 7.05348 18.742L6.28811 16.8942ZM4.91683 17.167C4.44626 17.167 3.9803 17.0743 3.54555 16.8942L2.78018 18.742C3.45758 19.0226 4.18362 19.167 4.91683 19.167V17.167ZM3.54555 16.8942C3.1108 16.7141 2.71577 16.4502 2.38303 16.1175L0.968816 17.5317C1.48728 18.0501 2.10278 18.4614 2.78018 18.742L3.54555 16.8942ZM2.38303 16.1175C2.05029 15.7847 1.78634 15.3897 1.60626 14.9549L-0.241498 15.7203C0.0390907 16.3977 0.450356 17.0132 0.968816 17.5317L2.38303 16.1175ZM1.60626 14.9549C1.42618 14.5202 1.3335 14.0542 1.3335 13.5837H-0.666504C-0.666504 14.3169 -0.522087 15.0429 -0.241498 15.7203L1.60626 14.9549ZM1.3335 13.5837C1.3335 12.6333 1.71102 11.7219 2.38303 11.0499L0.968817 9.63565C-0.0782615 10.6827 -0.666504 12.1029 -0.666504 13.5837H1.3335ZM2.38303 11.0499C3.05504 10.3779 3.96647 10.0003 4.91683 10.0003V8.00033C3.43604 8.00033 2.01589 8.58857 0.968817 9.63565L2.38303 11.0499ZM4.91683 10.0003C5.86719 10.0003 6.77862 10.3779 7.45063 11.0499L8.86484 9.63565C7.81776 8.58857 6.39762 8.00033 4.91683 8.00033V10.0003ZM7.45063 11.0499C8.12263 11.7219 8.50016 12.6333 8.50016 13.5837H10.5002C10.5002 12.1029 9.91192 10.6827 8.86484 9.63565L7.45063 11.0499ZM8.41983 10.9158C8.28823 10.7838 8.1319 10.6792 7.95979 10.6078L7.19326 12.455C7.12239 12.4256 7.05802 12.3825 7.00383 12.3282L8.41983 10.9158ZM7.95979 10.6078C7.78768 10.5363 7.60317 10.4996 7.41683 10.4996V12.4996C7.3401 12.4996 7.26412 12.4844 7.19326 12.455L7.95979 10.6078ZM7.41683 10.4996C7.23049 10.4996 7.04598 10.5363 6.87387 10.6078L7.6404 12.455C7.56953 12.4844 7.49356 12.4996 7.41683 12.4996V10.4996ZM6.87387 10.6078C6.70176 10.6792 6.54543 10.7838 6.41383 10.9158L7.82983 12.3282C7.77564 12.3825 7.71127 12.4256 7.6404 12.455L6.87387 10.6078ZM6.41463 10.915L3.37629 13.9541L4.7907 15.3682L7.82903 12.329L6.41463 10.915ZM4.79082 13.9543L3.41915 12.5818L2.00451 13.9956L3.37617 15.368L4.79082 13.9543ZM3.41894 12.5816C3.15316 12.3158 2.79269 12.1665 2.41683 12.1665V14.1665C2.26226 14.1665 2.11402 14.1051 2.00472 13.9958L3.41894 12.5816ZM2.41683 12.1665C2.04097 12.1665 1.6805 12.3158 1.41472 12.5816L2.82894 13.9958C2.71964 14.1051 2.5714 14.1665 2.41683 14.1665V12.1665ZM1.41472 12.5816C1.14895 12.8473 0.999636 13.2078 0.999636 13.5837H2.99964C2.99964 13.7382 2.93823 13.8865 2.82894 13.9958L1.41472 12.5816ZM0.999636 13.5837C0.999636 13.9595 1.14895 14.32 1.41472 14.5858L2.82894 13.1716C2.93823 13.2808 2.99964 13.4291 2.99964 13.5837H0.999636ZM1.41472 14.5858L3.08139 16.2524L4.4956 14.8382L2.82894 13.1716L1.41472 14.5858ZM3.0805 16.2515C3.21209 16.3835 3.36842 16.4881 3.54053 16.5596L4.30707 14.7123C4.37794 14.7417 4.44231 14.7848 4.4965 14.8391L3.0805 16.2515ZM3.54053 16.5596C3.71264 16.631 3.89716 16.6677 4.0835 16.6677V14.6677C4.16022 14.6677 4.2362 14.6829 4.30707 14.7123L3.54053 16.5596ZM4.0835 16.6677C4.26984 16.6677 4.45435 16.631 4.62646 16.5596L3.85992 14.7123C3.93079 14.6829 4.00677 14.6677 4.0835 14.6677V16.6677ZM4.62646 16.5596C4.79857 16.4881 4.9549 16.3835 5.0865 16.2515L3.6705 14.8391C3.72468 14.7848 3.78905 14.7417 3.85992 14.7123L4.62646 16.5596ZM5.0856 16.2524L8.41894 12.9191L7.00472 11.5049L3.67139 14.8382L5.0856 16.2524ZM8.41804 12.92C8.54998 12.7884 8.65464 12.6321 8.72606 12.46L6.87879 11.6934C6.90819 11.6226 6.95129 11.5582 7.00562 11.504L8.41804 12.92ZM8.72606 12.46C8.79748 12.2878 8.83424 12.1033 8.83424 11.917H6.83424C6.83424 11.8403 6.84938 11.7643 6.87879 11.6934L8.72606 12.46ZM8.83424 11.917C8.83424 11.7306 8.79748 11.5461 8.72606 11.374L6.87879 12.1406C6.84938 12.0697 6.83424 11.9937 6.83424 11.917H8.83424ZM8.72606 11.374C8.65464 11.2019 8.54998 11.0456 8.41804 10.914L7.00562 12.33C6.95129 12.2758 6.90819 12.2114 6.87879 12.1406L8.72606 11.374Z"
        fill="white"
        mask="url(#path-1-inside-1_9281_1576)"
      />
    </svg>
    <span class="font-medium">Bóc tách</span>
  </button>
</div>

<ng-template #imagesIntoPdfModal>
  <div class="flex flex-col">
    <div class="flex py-[10px] px-4 items-center justify-between border-b border-line">
      <span class="text-text-1 text-sm font-semibold">Ghép các ảnh thành một file</span>
      <button (click)="closeAllModal(null)">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 12L12 4"
            stroke="#989BB3"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M12 12L4 4"
            stroke="#989BB3"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
    <app-images-into-pdf (onPdfCreated)="handlePdfCreated($event)" />
  </div>
</ng-template>
