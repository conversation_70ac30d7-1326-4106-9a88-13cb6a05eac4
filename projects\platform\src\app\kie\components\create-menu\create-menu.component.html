<ul nz-menu class="text-text-1">
  <div
    (click)="showModalCreateFolder()"
    class="flex items-center gap-2 cursor-pointer hover:bg-hover-item border-l-[3px] border-solid border-transparent transition-colors duration-200 hover:border-l-brand-2 hover:bg-blue-3 px-4 py-[7px]"
  >
    <img src="assets/kie/document/new-folder.svg" alt="icon-new-folder" />
    <span class="font-medium">Thư mục mới</span>
  </div>
  <div class="border-b border-line w-[90%] mx-auto"></div>
  <p class="text-xs font-bold text-text-3 p-2 pl-4">GỢI Ý MẪU GIẤY TỜ</p>
  <ng-container *ngFor="let item of menuItems">
    <div
      class="pl-4 py-[7px]"
      *ngIf="!item.submenu"
      nz-menu-item
      (click)="showModalCreateDocument(item)"
    >
      <div class="flex items-center gap-2 font-medium text-text-1">
        <img [src]="item.icon" alt="icon-new-document" />
        {{ item.title }}
      </div>
    </div>
    <div
      *ngIf="item.submenu"
      class="flex items-center gap-2 pl-4 hover:bg-hover-item border-l-[3px] border-solid border-transparent transition-colors duration-200 hover:border-l-brand-2 hover:bg-blue-3 px-4 py-[2px]"
    >
      <img [src]="item.icon" alt="icon-other-template" />
      <li
        class="flex-1"
        nz-submenu
        [nzTitle]="title_submenu"
        [nzDisabled]="item.disabled"
        [nzMenuClassName]="'bg-bg-3 rounded-2xl border-line overflow-hidden'"
      >
        <ng-template #title_submenu>
          <span class="font-medium">{{ item.title }}</span>
        </ng-template>
        <ul>
          <ng-container *ngFor="let subItem of item.submenu">
            <div
              class="hover:bg-hover-item border-l-[3px] border-solid border-transparent transition-colors duration-200 hover:border-l-brand-2 hover:bg-blue-3 first:mt-2 last:mb-2 py-[7px]"
              nz-menu-item
              (click)="showModalCreateDocument(subItem)"
            >
              <div class="flex items-center gap-2 font-medium text-text-1">
                <img [src]="subItem.icon" alt="icon-new-document" />
                {{ subItem.title }}
              </div>
            </div>
          </ng-container>
        </ul>
      </li>
    </div>
  </ng-container>
  <div class="border-b border-line w-[90%] mx-auto"></div>
  <div
    (click)="showModalCreateSampleDocument()"
    class="flex items-center hover:bg-hover-item border-l-[3px] border-solid border-transparent transition-colors duration-200 hover:border-l-brand-2 hover:bg-blue-3 cursor-pointer gap-2 px-4 py-[7px]"
  >
    <img src="assets/kie/document/self-created.svg" alt="icon-self-created" />
    <span class="font-medium">Tự tạo mẫu giấy tờ</span>
  </div>
</ul>
