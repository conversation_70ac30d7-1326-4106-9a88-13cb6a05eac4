<ng-template [ngIf]="shouldContinueRendering">
  <div class="py-5 px-6" *transloco="let t; read: 'ocrExperience'">
    <ng-container [ngSwitch]="mode">
      <div class="font-semibold text-xl mb-5" *ngSwitchCase="Mode.Platform">
        TRẢI NGHIỆM DỊCH VỤ
      </div>
      <div
        class="text-center font-bold text-[40px] text-[#273266] mb-3"
        *ngSwitchCase="Mode.LDP"
      >
        {{ t('experience') }}
      </div>
      <div class="font-bold text-[32px] text-[#273266] mb-3" *ngSwitchCase="Mode.Demo">
        Trợ lý AI xử lý văn bản
      </div>
    </ng-container>
    <div class="grid grid-cols-2 gap-6">
      <div class="col-span-full xl:col-span-1 bg-white rounded-lg">
        <!-- order of attribute matter here: declare featureFlags first (in order to trigger setter featureFlag logic, then comes ocrResult (check to see if featureFlags DisplayOcrResult is allowed)  -->
        <app-document-viewer
          class="min-h-[750px]"
          [featureFlags]="{
            DisplayOcrResult: true,
            EditOcrResult: false,
            ViewerFitPageWidth: true,
            Zooming: false,
            PdfLazyLoading: true,
            CompareDocument: true
          }"
          [fileName]="file?.name"
          [fileLink]="fileLink"
          [ocrResult]="ocrResult"
          (onChangePageIndex)="handleCurrentPageChange($event + 1)"
          (afterFileLinkLoaded)="afterLoadComplete($event)"
          [fileLinkToCompareWith]="originalFileLink"
        ></app-document-viewer>
      </div>
      <div class="col-span-full xl:col-span-1 rounded-lg bg-white p-4 flex flex-col">
        <ng-container
          *ngIf="templateType.functionType === FunctionType.TrichXuatThongTin"
        >
          <ng-template *ngTemplateOutlet="TrichXuatThongTin"></ng-template>
        </ng-container>

        <ng-container *ngIf="templateType.functionType === FunctionType.SoHoaVanBan">
          <ng-template *ngTemplateOutlet="SoHoaVanBan"></ng-template>
        </ng-container>

        <ng-container *ngIf="templateType.functionType === FunctionType.GoiYXuLyVanBan">
          <ng-template *ngTemplateOutlet="GoiYXuLyVanBan"></ng-template>
        </ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #TrichXuatThongTin>
  <div class="flex flex-col gap-4 h-full">
    <div class="flex justify-between items-center">
      <span class="font-bold text-base">Kết quả OCR</span>
      <div class="flex gap-4">
        <ng-template *ngTemplateOutlet="retryBtn"></ng-template>
        <ng-template *ngTemplateOutlet="exportBtns"></ng-template>
      </div>
    </div>
    <ng-template *ngTemplateOutlet="TrichXuatThongTinContent"></ng-template>
  </div>
</ng-template>

<ng-template #TrichXuatThongTinContent>
  <div
    class="relative flex-1 overflow-auto template-result-wrapper"
    id="template-result-wrapper"
  >
    <div class="absolute top-0 left-0 w-full pr-2">
      <ng-container *ngFor="let item of ocrResult | keyvalue: originalOrder">
        <div
          *ngIf="!excludedKeyFields.includes(item.key) && item.value.type === 'Table'"
          class="flex flex-col gap-2 mb-3"
        >
          <div class="text-[#545454] font-semibold">
            {{ item.value?.label || item.key }}
          </div>
          <div class="rpa-table-wrapper">
            <table>
              <thead>
                <th
                  class="whitespace-nowrap"
                  *ngFor="let col of tableFields[item.key.toLowerCase()].columns"
                >
                  {{ col }}
                </th>
              </thead>
              <tr *ngFor="let row of tableFields[item.key.toLowerCase()].rows">
                <td class="align-middle" *ngFor="let cell of row">{{ cell }}</td>
              </tr>
              <tr *ngIf="!tableFields[item.key.toLowerCase()].rows.length">
                <td
                  class="align-middle text-center"
                  [colSpan]="tableFields[item.key.toLowerCase()].columns.length"
                >
                  Không có dữ liệu
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div
          class="flex flex-col gap-2 mb-3"
          *ngIf="!excludedKeyFields.includes(item.key) && item.value.type === 'Field'"
        >
          <div class="text-[#545454] font-semibold" [for]="item.key">
            {{ item.value?.label || item.key }}
          </div>
          <ng-template
            *ngTemplateOutlet="FieldValue; context: { value: item.value }"
          ></ng-template>
        </div>
        <div
          class="flex flex-col gap-2 mb-3"
          *ngIf="!excludedKeyFields.includes(item.key) && item.value.type === 'List'"
        >
          <div class="text-[#545454] font-semibold" [for]="item.key">
            {{ item.value?.label || item.key }}
          </div>
          <ng-container *ngFor="let cell of item.value.cells">
            <ng-template
              *ngTemplateOutlet="FieldValue; context: { value: cell.text }"
            ></ng-template>
          </ng-container>
          <div
            class="min-h-[32px] px-[10px] py-[5px] rounded-[4px] bg-[#e9ecef] border border-[#ced4da]"
            *ngIf="!item.value.cells.length"
            type="text"
            readonly
          ></div>
        </div>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #FieldValue let-value="value">
  <div
    *ngIf="getNumberOfLine(getValueText(value)) === 1; then singleLine; else multiLine"
  ></div>
  <ng-template #singleLine>
    <div
      class="min-h-[32px] px-[10px] py-[5px] rounded-[4px] bg-[#e9ecef] border border-[#ced4da]"
    >
      {{ getValueText(value) }}
    </div>
  </ng-template>
  <ng-template #multiLine>
    <textarea
      [rows]="getNumberOfLine(getValueText(value))"
      class="resize-none rounded-[4px] px-[10px] py-[5px] bg-[#e9ecef] border border-[#ced4da] outline-none"
      readonly
      >{{ getValueText(value) }}</textarea
    >
  </ng-template>
</ng-template>

<ng-template #SoHoaVanBan>
  <div class="flex justify-between items-center">
    <span class="font-bold text-base">Kết quả OCR</span>
    <div class="flex gap-4 items-center">
      <div
        class="flex gap-2 items-center rounded-[4px] py-1 px-[15px] border border-brand-1 text-brand-1 font-medium"
        *ngIf="templateType.value === TemplateType.SoHoaNangCao"
      >
        <nz-switch
          class="inline-flex"
          [ngModel]="isShowingParagraphBbox || isShowingLineBbox || isShowingPhraseBbox"
          (click)="
            isShowingParagraphBbox =
              isShowingLineBbox =
              isShowingPhraseBbox =
                !(isShowingParagraphBbox || isShowingLineBbox || isShowingPhraseBbox)
          "
          nzSize="small"
          [nzControl]="true"
        ></nz-switch>
        <label
          class="font-medium cursor-pointer hover:text-brand-2 inline-flex items-end gap-[2px] group"
          nz-popover
          [nzPopoverContent]="showBboxesMenu"
          nzPopoverTrigger="click"
          [nzPopoverPlacement]="'rightBottom'"
        >
          Vùng bounding
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="fill-brand-1 group-hover:fill-brand-2"
          >
            <path
              d="M4.56555 6.68477C4.67261 6.68472 4.77864 6.70577 4.87758 6.7467C4.97651 6.78764 5.06641 6.84766 5.14215 6.92334L10.0001 11.7818L14.858 6.92334C15.0109 6.77042 15.2183 6.68451 15.4346 6.68451C15.6508 6.68451 15.8582 6.77042 16.0112 6.92334C16.1641 7.07627 16.25 7.28368 16.25 7.49995C16.25 7.71621 16.1641 7.92362 16.0112 8.07655L10.5767 13.5111C10.501 13.5868 10.4111 13.6469 10.3121 13.6879C10.2132 13.7289 10.1071 13.75 10.0001 13.75C9.89296 13.75 9.78691 13.7289 9.68798 13.6879C9.58904 13.6469 9.49916 13.5868 9.42345 13.5111L3.98894 8.07655C3.87487 7.96254 3.79717 7.81726 3.76568 7.65908C3.7342 7.5009 3.75034 7.33694 3.81207 7.18794C3.8738 7.03894 3.97834 6.9116 4.11246 6.82203C4.24659 6.73247 4.40427 6.6847 4.56555 6.68477Z"
            />
          </svg>
        </label>
        <ng-template #showBboxesMenu>
          <div
            class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden"
          >
            <div class="hover:bg-bg-1 px-3 py-2 border-line">
              <label [(ngModel)]="isShowingParagraphBbox" nz-checkbox class="text-[red]"
                >Paragraph
              </label>
            </div>
            <div class="hover:bg-bg-1 px-3 py-2 border-line">
              <label [(ngModel)]="isShowingLineBbox" nz-checkbox class="text-[green]"
                >Line
              </label>
            </div>
            <div class="hover:bg-bg-1 px-3 py-2 border-line">
              <label [(ngModel)]="isShowingPhraseBbox" nz-checkbox class="text-[blue]"
                >Phrase
              </label>
            </div>
          </div>
        </ng-template>
      </div>
      <ng-template *ngTemplateOutlet="retryBtn"></ng-template>
      <ng-template *ngTemplateOutlet="exportBtns"></ng-template>
    </div>
  </div>
  <div class="mb-3 mt-2">
    Phân loại văn bản trong trang:
    <b
      (click)="handleOcrCurrentCategorizedPageRange()"
      [ngClass]="{ 'cursor-pointer underline': !!getCurrentPageCategory() }"
      >{{ getCurrentPageCategory(true) }}</b
    >
  </div>
  <app-document-reproduction
    class="flex-1"
    [documentReproductionInputs]="{ file: file, ocrResult: ocrResult }"
    [isShowingBbox]="{
      paragraph: isShowingParagraphBbox,
      line: isShowingLineBbox,
      phrase: isShowingPhraseBbox
    }"
    [currentPage]="currentPage"
  ></app-document-reproduction>
</ng-template>

<ng-template #GoiYXuLyVanBan>
  <div class="flex flex-col gap-4 h-full">
    <div class="flex justify-between items-center border-b border-[#CBCBCB] pb-2">
      <div class="flex items-center gap-3">
        <div
          (click)="selectTab('ocr-result')"
          class="font-semibold cursor-pointer mb-[-14px] px-[10px] pb-3"
          [ngStyle]="
            tab === 'ocr-result'
              ? { 'border-bottom': '2px solid #0F67CE', color: '#0F67CE' }
              : { 'border-bottom': '2px solid transparent', color: '#757575' }
          "
        >
          Kết quả OCR
        </div>
        <div
          (click)="selectTab('doc-suggestion')"
          class="font-semibold cursor-pointer mb-[-14px] px-[10px] pb-3"
          [ngStyle]="
            tab === 'doc-suggestion'
              ? { 'border-bottom': '2px solid #0F67CE', color: '#0F67CE' }
              : { 'border-bottom': '2px solid transparent', color: '#757575' }
          "
        >
          Gợi ý xử lý văn bản
        </div>
      </div>
      <div class="flex gap-4">
        <ng-template *ngTemplateOutlet="retryBtn"></ng-template>
        <ng-template *ngTemplateOutlet="exportBtns"></ng-template>
      </div>
    </div>
    <ng-container *ngIf="tab === 'ocr-result'">
      <ng-template *ngTemplateOutlet="TrichXuatThongTinContent"></ng-template>
    </ng-container>
    <ng-container *ngIf="tab === 'doc-suggestion'">
      <div class="relative flex-1 overflow-auto template-result-wrapper">
        <div class="absolute top-0 left-0 w-full pr-2">
          <div class="font-semibold mb-4">Danh sách phòng ban, cá nhân xử lý văn bản</div>
          <div
            class="grid grid-cols-[minmax(200px,_1fr)_minmax(135px,_1fr)_155px_48px] gap-y-2"
          >
            <div
              class="bg-[#EEE] col-span-1 font-semibold flex items-center h-[42px] p-3 justify-start rounded-bl-lg rounded-tl-lg"
            >
              Tên phòng ban/cá nhân
            </div>
            <div
              class="bg-[#EEE] col-span-1 font-semibold flex items-center h-[42px] p-3 justify-center"
            >
              Mã
            </div>
            <div
              class="bg-[#EEE] col-span-1 font-semibold flex items-center h-[42px] p-3 justify-start"
            >
              Vai trò
            </div>
            <div
              class="bg-[#EEE] col-span-1 font-semibold flex items-center h-[42px] p-3 justify-center rounded-tr-lg rounded-br-lg"
            >
              Xóa
            </div>
            <ng-container *ngFor="let suggestion of suggestionName">
              <div
                class="bg-[#EEE] col-span-1 font-semibold flex items-center p-3 min-h-[46px] justify-start rounded-bl-lg rounded-tl-lg"
              >
                {{ suggestion.name }}
              </div>
              <div
                class="bg-[#EEE] col-span-1 font-semibold flex items-center p-3 min-h-[46px] justify-center"
              >
                {{ suggestion.code }}
              </div>
              <div
                class="bg-[#EEE] col-span-1 font-semibold flex items-center p-3 min-h-[46px] justify-start"
              >
                <select class="w-full">
                  <option value="2">Xem</option>
                  <option value="3">Xử lý</option>
                </select>
              </div>
              <div
                class="bg-[#EEE] col-span-1 font-semibold flex items-center p-3 min-h-[46px] justify-center rounded-tr-lg rounded-br-lg"
              >
                <img
                  class="cursor-pointer"
                  (click)="deleteSuggestion(suggestion.code)"
                  src="assets/img/rpa/delete.svg"
                  alt="del"
                />
              </div>
            </ng-container>
            <ng-container *ngIf="!suggestionName.length">
              <div
                class="col-span-full font-semibold p-3 bg-[#EEE] h-[42px] rounded-lg text-center"
              >
                Không có gợi ý
              </div>
            </ng-container>
          </div>
          <div class="text-center mt-4">
            <button
              nz-button
              nzType="primary"
              (click)="openSubmitSuccessModal()"
              class="h-10 inline-flex items-center gap-1 bg-brand-1 hover:bg-brand-1 focus:bg-brand-1"
            >
              <img src="assets/ocr-experience/send.svg" />
              Chuyển văn bản xử lý
            </button>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>

<ng-template #retryBtn>
  <button
    nz-button
    (click)="
      router.navigate([navigateInput], {
        state: {
          selectedTemplate: templateType.value,
          devModeEnabled: devModeEnabled
        }
      })
    "
    class="!rounded-[4px] flex items-center gap-1 border-brand-1 text-brand-1 font-medium"
  >
    <img src="assets/img/rpa/ocr-experience/refresh.svg" alt="" />
    Thực hiện lại
  </button>
</ng-template>

<ng-template #exportBtns>
  <div class="flex gap-[1px]">
    <button
      [class]="
        'rounded-[4px] bg-brand-1 py-1 px-4 flex gap-2 items-center text-white font-medium border border-brand-1 ' +
        (templateType.value === TemplateType.SoHoaCoBan
          ? 'rounded-tr-none rounded-br-none'
          : '')
      "
      (click)="
        templateType.functionType === FunctionType.TrichXuatThongTin ||
        templateType.functionType === FunctionType.GoiYXuLyVanBan
          ? handleExportTrichXuatThongTin()
          : templateType.value === TemplateType.SoHoaCoBan
            ? handleExportSoHoaCoBan()
            : openScanTableExportModal()
      "
    >
      <img src="assets/ocr-experience/export.svg" />
      Tải về
    </button>
    <button
      *ngIf="templateType.value === TemplateType.SoHoaCoBan"
      class="rounded-[4px] rounded-tl-none rounded-bl-none bg-brand-1 py-1 px-2 border border-brand-1"
      nz-dropdown
      [nzDropdownMenu]="exportMenu"
      nzPlacement="bottomRight"
    >
      <img src="assets/ocr-experience/down-chevron.svg" class="inline" />
    </button>
  </div>

  <nz-dropdown-menu #exportMenu="nzDropdownMenu">
    <ul nz-menu>
      <li nz-menu-item>
        <button (click)="handleExportSoHoaCoBan('currentPage')">
          Theo trang hiện tại
        </button>
      </li>
      <li nz-menu-item>
        <button (click)="handleExportSoHoaCoBan('currentRange')">
          Theo loại văn bản
        </button>
      </li>
      <li nz-menu-item>
        <button (click)="handleExportSoHoaCoBan()">Toàn bộ file</button>
      </li>
    </ul>
  </nz-dropdown-menu>
</ng-template>

<ng-template #submitSuccessModal>
  <div
    class="flex flex-col items-center justify-between gap-5 text-center p-[32px_46px_22px_46px]"
  >
    <img src="assets/ocr-experience/check.svg" />
    <div class="text-xl font-semibold">
      Chuyển văn bản xử lý <br />
      thành công
    </div>
    <button (click)="modal.closeAll()" class="w-full h-10 bg-brand-1 text-white">
      ĐÓNG
    </button>
  </div>
</ng-template>
