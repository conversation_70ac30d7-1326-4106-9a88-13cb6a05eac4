/* TODO: zooming, currentMode */
import {
  AfterViewInit,
  Component,
  ElementRef,
  inject,
  Ng<PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  ViewChild
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { fabric } from 'fabric';
import UtilsService from '@platform/app/core/services/utils.service';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { FormsModule } from '@angular/forms';
import { NzSliderModule } from 'ng-zorro-antd/slider';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-image-editor',
  standalone: true,
  imports: [
    CommonModule,
    NzPopoverModule,
    NzSliderModule,
    NzSwitchModule,
    NzToolTipModule,
    FormsModule
  ],
  templateUrl: './image-editor.component.html',
  styleUrls: ['./image-editor.component.scss']
})
export class ImageEditorComponent implements OnDestroy, AfterViewInit {
  destroy$ = new Subject<void>();
  readonly nzModalData: {
    imgTitle?: string;
    imgSrc?: string;
    fabricImgDataJSON?: string;
  } = inject(NZ_MODAL_DATA);
  imgTitle = this.nzModalData.imgTitle;
  imgSrc = this.nzModalData.imgSrc;
  fabricImgDataJSON = this.nzModalData.fabricImgDataJSON;
  canvas: fabric.Canvas;
  image: fabric.Image;
  group: fabric.Group;
  cropRect: fabric.Rect;
  isCropping = false;
  /* TODO: mode: zooming | cropping */
  grid: fabric.Group;
  get gridEnabled() {
    if (!this.grid) return false;
    return !!this.grid.get('opacity');
  }
  zoomLevel = '100%';
  _rotationDeg = 0;
  set rotationDeg(deg) {
    this._rotationDeg = this.normalizeRotation(deg);
  }
  get rotationDeg() {
    return this._rotationDeg;
  }
  sliderValue = 0;
  readonly sliderValueMax = 45;
  readonly sliderValueMin = -45;
  readonly sliderStep = 0.5;
  @ViewChild('imageEditorWrapper') imageEditorWrapper!: ElementRef<HTMLDivElement>;

  constructor(
    private zone: NgZone,
    private utils: UtilsService,
    private modalRef: NzModalRef
  ) {
    fabric.Object.prototype.objectCaching = false;
  }

  async getInitialGroupAndImage(): Promise<{ image: fabric.Image; group: fabric.Group }> {
    let image: fabric.Image;
    const imageOption: fabric.IObjectOptions = {
      originX: 'left',
      originY: 'top',
      centeredRotation: true,
      lockMovementX: true,
      lockMovementY: true,
      lockScalingX: true,
      lockScalingY: true,
      lockRotation: true,
      selectable: false,
      hoverCursor: 'default'
    };

    if (this.fabricImgDataJSON) {
      [image] = await fabric.util['enlivenObjectsAsync']([this.fabricImgDataJSON], null);
      this.rotationDeg = image.get('angle');
      image.set(imageOption);
      image.set('angle', this.rotationDeg);
    }

    if (!image && this.imgSrc)
      image = await fabric.Image['fromURLAsync'](this.imgSrc, imageOption);

    if (!image) return;

    const group = new fabric.Group([image], {
      lockMovementX: true,
      lockMovementY: true,
      lockRotation: true,
      lockScalingX: true,
      lockScalingY: true,
      hasControls: false,
      centeredRotation: true,
      selectable: false,
      hoverCursor: 'default'
    });
    return { image, group };
  }

  async setCanvas(group: fabric.Group) {
    this.canvas.clear();
    this.canvas.setViewportTransform([1, 0, 0, 1, 0, 0]); /* reset canvas viewport */

    let scaledByWidth = false,
      scaledByHeight = false;
    const scaleX = this.canvas.width / group.width,
      scaleY = this.canvas.height / group.height,
      grHeight = group.height,
      grWidth = group.width;

    if (grHeight * scaleX <= this.canvas.height) {
      group.scaleToWidth(this.canvas.width).addWithUpdate();
      scaledByWidth = true;
    }
    if (grWidth * scaleY <= this.canvas.width) {
      group.scaleToHeight(this.canvas.height).addWithUpdate();
      scaledByHeight = true;
    }
    this.canvas.add(group);
    this.canvas.centerObject(group);
    // this.this.setupPageHoverCursor(this.page, this.currentModeSubject.value);
    group.addWithUpdate(); // manually recalculate group dimension after scaled <- TODO: find alternative
    this.canvas.renderAll();

    const grid = this.setupGrid(this.canvas);
    this.canvas.add(grid);
    this.canvas.centerObject(grid);
    grid.sendToBack();
    this.canvas.renderAll();
  }

  ngAfterViewInit() {
    /* all fabric canvas related works are run outside angular change detection for better performance */
    this.zone.runOutsideAngular(async () => {
      try {
        let canvasHeight, canvasWidth;
        canvasHeight = this.imageEditorWrapper.nativeElement.clientHeight;
        canvasWidth = this.imageEditorWrapper.nativeElement.clientWidth;
        if (this.canvas) {
          this.canvas?.clear()?.dispose();
          this.canvas = null;
        }

        this.canvas = new fabric.Canvas('image-editor', {
          selection: false,
          // match canvas dimension to its component wrapper
          height: canvasHeight,
          width: canvasWidth,
          centeredRotation: true
          // backgroundColor: 'black'
        });
        const { group, image } = await this.getInitialGroupAndImage();
        this.image = image;
        this.group = group;
        await this.setCanvas(group);
        // this.zone.run(() => (this.zoomLevel = '100%')); // this line accidentally trigger change detection
      } catch (error) {
        console.log('initCanvas failed:', error);
      }
    });
  }

  tilt(toValue) {
    if (toValue < -45 || toValue > 45) return;
    this.rotate(toValue - this.sliderValue);
    this.sliderValue = toValue;
  }

  rotate(deg: number) {
    this.removeSetupDrawBboxEvent();
    this.removeCropRect();
    this.rotationDeg += deg;
    this.image.rotate(this.rotationDeg);
    this.group.addWithUpdate();

    let scaledByWidth = false,
      scaledByHeight = false;
    const scaleX = this.canvas.width / this.group.width,
      scaleY = this.canvas.height / this.group.height,
      grHeight = this.group.height,
      grWidth = this.group.width;

    if (grHeight * scaleX <= this.canvas.height) {
      this.group.scaleToWidth(this.canvas.width).addWithUpdate();
      scaledByWidth = true;
    }
    if (grWidth * scaleY <= this.canvas.width) {
      this.group.scaleToHeight(this.canvas.height).addWithUpdate();
      scaledByHeight = true;
    }

    this.canvas.centerObject(this.group).renderAll();
  }

  private normalizeRotation(degree) {
    // Normalize to be between 0 and 360 degrees
    let normalizedDegree = degree % 360;

    // Ensure it is always positive (in case of negative degrees)
    if (normalizedDegree < 0) {
      normalizedDegree += 360;
    }

    return normalizedDegree;
  }

  flip(type: 'horizontal' | 'vertical') {
    this.image.set(
      type === 'horizontal' ? 'flipX' : 'flipY',
      !this.image[type === 'horizontal' ? 'flipX' : 'flipY']
    );
    this.canvas.renderAll();
  }

  async reset() {
    this.sliderValue = 0;
    this.rotationDeg = 0;
    this.removeSetupDrawBboxEvent();
    this.removeCropRect();
    const { group, image } = await this.getInitialGroupAndImage();
    this.image = image;
    this.group = group;
    await this.setCanvas(group);
  }

  async ok() {
    const cloneImage: fabric.Image = await this.image['cloneAsync']();

    // reset scale
    cloneImage.set({ scaleX: 1, scaleY: 1, top: 0, left: 0 });

    let link;
    if (this.cropRect) {
      /*
      the object placed inside an group, then object.left, object.top is RELATIVE TO GROUP CENTER
      https://stackoverflow.com/a/29926545/12582664 
     */
      const cropRectTrueLeft = this.group.width / 2 + this.cropRect.left;
      const cropRectTrueTop = this.group.height / 2 + this.cropRect.top;
      const cropRectRelativeBbox = this.utils.convertAbsoluteToRelativeBbox(
        [
          cropRectTrueLeft,
          cropRectTrueTop,
          cropRectTrueLeft + this.cropRect.width,
          cropRectTrueTop + this.cropRect.height
        ],
        this.group.width,
        this.group.height
      );
      const noScaledGroup = new fabric.Group([cloneImage]);
      link = URL.createObjectURL(
        this.utils.createBlobFromDataUrl(
          noScaledGroup.toDataURL({
            format: 'png',
            enableRetinaScaling: true,
            left: cropRectRelativeBbox[0] * noScaledGroup.width,
            top: cropRectRelativeBbox[1] * noScaledGroup.height,
            width:
              (cropRectRelativeBbox[2] - cropRectRelativeBbox[0]) * noScaledGroup.width,
            height:
              (cropRectRelativeBbox[3] - cropRectRelativeBbox[1]) * noScaledGroup.height
          }),
          'image/png'
        )
      );
      noScaledGroup.ungroupOnCanvas();
    } else
      link = URL.createObjectURL(
        this.utils.createBlobFromDataUrl(
          cloneImage.toDataURL({
            format: 'png',
            enableRetinaScaling: true
          }),
          'image/png'
        )
      );

    this.modalRef.close({ editedPageImageLink: link });
  }

  setupGrid(canvas: fabric.Canvas): fabric.Group {
    if (!canvas) return;
    const gridSquareWidth = 50;
    const grid = new fabric.Group([], {
      centeredRotation: true,
      top: 0,
      left: 0,
      originX: 'center',
      originY: 'center',
      width: canvas.width,
      height: canvas.height,
      hasControls: false,
      hasBorders: false,
      hasRotatingPoint: false,
      selectable: false,
      hoverCursor: 'default',
      opacity: 0
    });

    const gridLineOptions: fabric.IGroupOptions = {
      stroke: 'DarkOrange',
      strokeWidth: 1,
      strokeDashArray: [5],
      hasBorders: false,
      hasControls: false,
      hasRotatingPoint: false,
      selectable: false
    };
    for (let i = 0; i <= canvas.width / gridSquareWidth; i++) {
      /* Oy: constant x */
      let x = i * gridSquareWidth;
      grid.addWithUpdate(new fabric.Line([x, 0, x, canvas.height], gridLineOptions));
    }
    for (let i = 0; i <= canvas.height / gridSquareWidth; i++) {
      /* Ox: constant y */
      let y = i * gridSquareWidth;
      grid.addWithUpdate(new fabric.Line([0, y, canvas.width, y], gridLineOptions));
    }

    this.grid = grid;
    return grid;
  }

  async toggleGrid(showing?: 'on' | 'off') {
    if (!this.grid) return;

    let opacity = 0;
    if (showing === 'on')
      opacity = 1; // force on
    else if (showing === 'off')
      opacity = 0; // force off
    else opacity = !!this.grid.get('opacity') ? 0 : 1; // toggle logic

    if (opacity === 1) this.canvas.bringToFront(this.grid);

    await new Promise((resolve, reject) => {
      this.grid.animate(
        { opacity },
        {
          // by: 0.1,
          duration: 300,
          // from: 0.1,
          // easing: fabric.util.ease.easeInBack,
          onChange: this.canvas.renderAll.bind(this.canvas),
          onComplete: resolve
        }
      );
    });

    if (opacity === 0) this.canvas.sendToBack(this.grid);
  }

  async setupDrawBboxEvent() {
    if (this.cropRect || this.isCropping) return;

    if (!this.canvas || !this.group) return;
    await this.toggleGrid('off');
    this.canvas.bringToFront(this.group);

    let tmpBbox: fabric.Rect | null;
    let isDragging = false;
    this.isCropping = true;
    this.group.set('hoverCursor', 'crosshair');
    this.canvas.requestRenderAll();
    this.canvas
      ?.off('mouse:down')
      ?.off('mouse:move')
      ?.off('mouse:up')
      .on('mouse:down', (opt: fabric.IEvent<MouseEvent>) => {
        /* only start drawing on page/group */
        /* not allow drawing outside page/group OR draw while an obj in active */
        if (opt.target !== this.group || this.canvas.getActiveObject()) return;
        isDragging = true;
        tmpBbox = new fabric.Rect({
          left: opt.absolutePointer.x, // x1,
          top: opt.absolutePointer.y, // y1,
          width: 1,
          height: 1,
          fill: 'hsla(120, 100%, 75%, 0.15)',
          stroke: '#009B4E',
          strokeWidth: 1,
          selectable: false,
          hoverCursor: 'default',
          originX: 'left',
          originY: 'top'
        });
        this.canvas.add(tmpBbox);
      })
      .on('mouse:move', (opt: fabric.IEvent<MouseEvent>) => {
        if (!isDragging) return;
        /* not allow drawing when tmpBbox is not created in mouse:down OR draw while an obj in active */
        if (!tmpBbox || this.canvas.getActiveObject()) return;

        const x1 = tmpBbox?.left as number,
          y1 = tmpBbox?.top as number;
        let x2 = opt.absolutePointer.x,
          y2 = opt.absolutePointer.y;

        // not allowing to draw outside the page/group, 2 is the bbox stroke width
        const minX2 = this.group.left + 2,
          minY2 = this.group.top + 2,
          maxX2 = this.group.left + this.group.getScaledWidth() - 2,
          maxY2 = this.group.top + this.group.getScaledHeight() - 2;
        if (x2 < minX2) x2 = minX2;
        else if (x2 > maxX2) x2 = maxX2;
        if (y2 < minY2) y2 = minY2;
        else if (y2 > maxY2) y2 = maxY2;

        if (x1 > x2) tmpBbox?.set({ originX: 'right' });
        else tmpBbox?.set({ originX: 'left' });

        if (y1 > y2) tmpBbox?.set({ originY: 'bottom' });
        else tmpBbox?.set({ originY: 'top' });

        const width = Math.abs(x2 - x1);
        const height = Math.abs(y2 - y1);

        tmpBbox?.set({ width: width, height: height });

        this.canvas.requestRenderAll();
      })
      .on('mouse:up', async (opt: fabric.IEvent<MouseEvent>) => {
        if (
          !this.canvas.getActiveObject() &&
          tmpBbox?.width! > 10 &&
          tmpBbox?.height! > 10
        ) {
          tmpBbox.setCoords();
          this.cropRect = new fabric.Rect({
            left: tmpBbox.aCoords.tl.x,
            top: tmpBbox.aCoords.tl.y,
            width: tmpBbox.width,
            height: tmpBbox.height,
            fill: 'hsla(120, 100%, 75%, 0.15)',
            stroke: '#009B4E',
            strokeWidth: 1,
            selectable: false,
            hoverCursor: 'default',
            originX: 'left',
            originY: 'top'
          });
          this.group.addWithUpdate(this.cropRect);
          // this.canvas.add(this.cropRect);
          this.isCropping = false;
          this.removeSetupDrawBboxEvent();
        }
        this.canvas.remove(tmpBbox);
        tmpBbox = null;
        isDragging = false;
        this.zone.run(() => {}); // manually run change detection
      });
  }

  removeSetupDrawBboxEvent() {
    if (!this.canvas || !this.group) return;
    this.canvas?.off('mouse:down')?.off('mouse:move')?.off('mouse:up');
    this.group.set('hoverCursor', 'default');
    this.isCropping = false;
  }

  removeCropRect() {
    if (!this.canvas || !this.group || !this.cropRect) return;
    this.group.removeWithUpdate(this.cropRect);
    this.canvas.remove(this.cropRect).renderAll();
    this.cropRect = null;
  }

  cancelEditing() {
    this.modalRef.close();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
