import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { LoadingInterceptor } from './core/interceptor/loading.interceptor';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ToastrModule } from 'ngx-toastr';
import { TranslocoRootModule } from './transloco-root.module';
import { OAuthModule } from 'angular-oauth2-oidc';
import { NZ_DATE_LOCALE, NZ_I18N } from 'ng-zorro-antd/i18n';
import { en_US } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { LayoutModule } from './layout/layout.module';
import { vi } from 'date-fns/locale';
import * as pdfjsLib from 'pdfjs-dist';
import patchFabricLib from '../patch-fabric-lib';

registerLocaleData(en);
/* setup worker file for ng2-pdf-viewer */
(window as any).pdfWorkerSrc = `assets/lib/pdfjs-dist@${pdfjsLib.version}.worker.mjs`;
// @ts-expect-error polyfill for Promise.withResolver in Angular
if (typeof Promise.withResolvers === 'undefined' && window) {
  // @ts-expect-error polyfill for Promise.withResolver in Angular
  window.Promise.withResolvers = () => {
    let resolve;
    let reject;
    const promise = new Promise((res, rej) => {
      resolve = res;
      reject = rej;
    });
    return { promise, resolve, reject };
  };
}
patchFabricLib();

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    LayoutModule, // must import to use layout component in AppRoutingModule
    NgxSpinnerModule,
    HttpClientModule,
    ToastrModule.forRoot(),
    TranslocoRootModule,
    OAuthModule.forRoot()
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LoadingInterceptor,
      multi: true
    },
    { provide: NZ_I18N, useValue: en_US },
    {
      provide: NZ_DATE_LOCALE,
      useValue: vi
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {}
