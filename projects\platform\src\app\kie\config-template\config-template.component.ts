import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import {
  BehaviorSubject,
  EMPTY,
  catchError,
  delay,
  filter,
  forkJoin,
  from,
  of,
  switchMap,
  tap
} from 'rxjs';
import {
  BboxChange,
  DrawCommand,
  FieldConfigChange,
  FieldType,
  File,
  Template
} from '../kie';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { clone, get, isFinite, pick, set } from 'lodash';
import UtilsService from '@platform/app/core/services/utils.service';
import { HttpErrorResponse } from '@angular/common/http';

@Component({
  selector: 'app-config-template',
  templateUrl: './config-template.component.html',
  styleUrls: ['./config-template.component.scss']
})
export class ConfigTemplateComponent implements OnInit, On<PERSON><PERSON>roy {
  /* TODO: loading state watches for all children component readiness before let user interact */

  bboxChangeSubject = new BehaviorSubject<BboxChange>(null);

  /* TODO: better naming across 3 components */
  drawCommandSubject = new BehaviorSubject<DrawCommand>(null);

  constructor(
    private kieService: KIEService,
    private route: ActivatedRoute,
    private router: Router,
    private toastr: ToastrService,
    private cdr: ChangeDetectorRef,
    private utils: UtilsService
  ) {}

  file: Partial<File>;
  template: Template;
  goBackCommands: string[];

  ngOnInit(): void {
    this.goBackCommands = [
      'key-information-extractor',
      'document',
      this.route.snapshot.paramMap.get('id')
    ];
    this.load().subscribe();
    this.handleUpdateBboxFieldConfig();
  }

  private load() {
    /* flow
      1: fetch template config
      2: with config file link from step1 call ocr API
      3: create `file` object with file link from step1 and ocr result from step2
      4: pass down data to child components
    */
    return this.kieService.getDocumentDetail(this.route.snapshot.paramMap.get('id')).pipe(
      tap((resp) => {
        if (!resp.data.isSelfConfig)
          throw new Error('Cannot modify this document.template');
        const tmpTemplate = resp.data.template;
        const extraConfigObj = Object.fromEntries(
          (resp.data.extraConfig || []).map((config) => [config.name, config])
        );
        /* 
            FIXME: transform and mapping extra config here
            auto generate unique id for each field here if field not including id
            use unique 'id' as field key in config object AND not 'name'
          */
        const transformedConfig = Object.fromEntries(
          Object.values(get(tmpTemplate, `config.fields`, {})).map(
            (field: any, fieldIndex) => {
              const id = `${this.utils.removeAccents(get(field, 'name', `field_${fieldIndex}`).toLowerCase().split(' ').join('_'))}_${this.utils.makeId(6)}`;
              let extraConfig: any = {
                id,
                order: fieldIndex,
                color: '#009B4E',
                is_visible: field.is_visible,
                name: field.name
              };
              if (extraConfigObj[field.name]) {
                extraConfig = extraConfigObj[field.name];
                set(extraConfig, 'id', id);
              }

              return [id, Object.assign(field, { extraConfig })];
            }
          )
        );
        set(tmpTemplate, `config.fields`, transformedConfig);
        this.template = tmpTemplate;
        // console.log(this.template);
      }),
      catchError((err) => {
        this.router.navigate(this.goBackCommands);
        console.log(err);
        return EMPTY;
      }),
      switchMap(() =>
        this.kieService.ocrWithFileLink({
          detail: true,
          fileLink: this.template.file_link,
          template_id: this.template.id
        })
      ),
      tap((ocrResult) => {
        const fieldConfigs = get(this.template, `config.fields`, {});
        const ocrResultEntries = Object.entries(ocrResult);
        const transformedOcrResultEntries = [];

        /* map ocr result with config under the same defined unique field id above */
        for (const fieldConfigId in fieldConfigs) {
          const fieldConfig = fieldConfigs[fieldConfigId];
          const fieldResult = ocrResultEntries.find(([key]) => key === fieldConfig.name);
          if (fieldResult) {
            // set(fieldResult, `${1}.name`, fieldConfig.name);
            set(fieldResult, `${1}.config`, fieldConfig);
            transformedOcrResultEntries.push([fieldConfigId, fieldResult[1]]);
          }
        }
        ocrResult = Object.fromEntries(transformedOcrResultEntries);

        this.file = {
          fileLink: this.template.file_link,
          ocrResult: ocrResult as any
        };
      }),
      catchError((err) => {
        let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
        this.toastr.error(errorMessage);
        return EMPTY;
      })
    );
  }

  // count = 0;
  handleFieldConfigChange(change: FieldConfigChange) {
    // console.log(++this.count);
    // console.log(change);
    switch (change.type) {
      case 'create': {
        const newResult = clone(this.file.ocrResult);
        const extraConfig = pick(change.content, [
          'id',
          'name',
          'is_visible',
          'color',
          'order',
          'isNew'
        ]);
        newResult[change.content.id] = {
          bboxes: null,
          confidence_score: 0,
          type: FieldType.Field
        };
        set(newResult, `${change.content.id}.config.extraConfig`, extraConfig);
        set(newResult, `${change.content.id}.config.name`, extraConfig.name);
        this.file.ocrResult = newResult;
        break;
      }
      case 'update': {
        /* update made into doc viewer: is_visible, color */
        const newResult = clone(this.file.ocrResult);
        const extraConfig = pick(change.content, [
          'id',
          'name',
          'is_visible',
          'color',
          'order',
          'isNew'
        ]);
        set(newResult, `${change.content.id}.config.extraConfig`, extraConfig);
        set(newResult, `${change.content.id}.config.name`, extraConfig.name);
        this.file.ocrResult = newResult;
        break;
      }
      case 'remove': {
        if (this.file.ocrResult[change.content.id]) {
          const newResult = clone(this.file.ocrResult);
          delete newResult[change.content.id];
          this.file.ocrResult = newResult;
        }
        break;
      }

      default:
        break;
    }
    this.cdr.detectChanges();
  }

  private handleUpdateBboxFieldConfig() {
    this.bboxChangeSubject
      .pipe(
        filter((command) => !!command),
        tap(({ page, value, fieldId }) => {
          const newResult = clone(this.file.ocrResult);
          const bboxValue =
            isFinite(page) && value.length === 4 ? { [page]: value } : null;
          set(newResult, `${fieldId}.bboxes`, bboxValue);
          this.file.ocrResult = newResult;
          this.cdr.detectChanges(); /* manual solution for instance reflection of change made into current component and its child components which received changes (document-viewer) */
        })
      )
      .subscribe();
  }

  handleSaveConfigTemplate(config) {
    /* transform config.fields into templateBody */
    const templateBody: any = {
      templateConfigOcrModel: this.template.config.ocr_model,
      templateConfigFields: Object.entries(get(config, 'fields', {})).map(
        ([name, field]: [string, any]) => {
          const fieldLocation = Object.entries(field.location).pop();
          let location;
          if (fieldLocation) {
            const [pageNumber, bbox] = fieldLocation;
            location = {
              pageNumber: parseInt(pageNumber),
              xMin: bbox[0],
              yMin: bbox[1],
              xMax: bbox[2],
              yMax: bbox[3]
            };
          }
          delete field.extraConfig.name;
          return { ...field, name, location };
        }
      )
    };
    this.kieService
      .updateDocumentTemplateConfig(this.route.snapshot.paramMap.get('id'), templateBody)
      .pipe(
        tap(() =>
          this.toastr.success(
            `Lưu cấu hình template thành công. ${window.opener ? 'Tab này sẽ đóng lại trong 5s...' : ''}`
          )
        ),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastr.error(errorMessage);
          return EMPTY;
        }),
        switchMap(() => this.load()),
        delay(5000),
        tap(() => window.opener && window.close())
      )
      .subscribe();

    // from(of(null))
    //   .pipe(
    //     delay(700),
    //     tap(() =>
    //       this.toastr.error('Lưu template thất bại, hãy kiểm tra lại cấu hình template')
    //     ),
    //     switchMap(() => this.load())
    //   )
    //   .subscribe();
  }

  ngOnDestroy(): void {}
}
