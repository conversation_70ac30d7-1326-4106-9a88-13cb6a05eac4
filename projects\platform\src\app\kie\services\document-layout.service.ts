import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { Folder } from '../kie';
import { BehaviorSubject, EMPTY, catchError, tap } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { cloneDeep, get, set } from 'lodash';
import UtilsService from '@platform/app/core/services/utils.service';

@Injectable()
export class DocumentLayoutService implements OnDestroy {
  store$ = new BehaviorSubject<{
    folders: Folder[];
    sharedDocumentsCount: number;
    foldersCount;
  }>({
    folders: [],
    sharedDocumentsCount: 0,
    foldersCount: 0
  });

  private fetchedPages = new Set<number>();

  loadFoldersFilters$ = new BehaviorSubject<{
    orderBy: 'name' | 'createdAt' | 'updatedAt';
    orderValue: 'ASC' | 'DESC';
  }>({
    orderBy: 'createdAt',
    orderValue: 'DESC'
  });

  /* 
    for instantly access current value in store$
    DO NOT documentLayoutService.folders and assign it to component's state, change detection will not work
    instead, in component, do documentLayoutService.store$.subscribe()
  */
  get folders() {
    return this.store$.getValue().folders;
  }
  get foldersCount() {
    return this.store$.getValue().foldersCount;
  }

  selectedDocumentId$ = new BehaviorSubject<string>(null);

  constructor(
    private kieService: KIEService,
    private toastr: ToastrService,
    private utils: UtilsService
  ) {
    this.utils.onLogout$
      .asObservable()
      .pipe(tap(this.resetDocumentLayoutServiceState.bind(this)))
      .subscribe();
  }

  /* by default: depend on fetchedPages then fetch more page */
  /* reload all, from the first page if reloadFolders = true */
  loadFolders(loadOptions?: {
    loadStrategy?: 'fetch-next-page' | 'clear-and-fetch' | 'refetch-all-current-pages';
    returnAsObservable?: boolean;
    filters?: {
      orderBy: 'name' | 'createdAt' | 'updatedAt';
      orderValue: 'ASC' | 'DESC';
    };
  }) {
    const DEFAULT_LIMIT = 10,
      DEFAULT_PAGE = 1;
    let {
      loadStrategy = 'clear-and-fetch',
      returnAsObservable = false,
      filters
    } = loadOptions || {};
    let fetchPage = -1;
    let limit = -1;

    switch (loadStrategy) {
      case 'fetch-next-page': {
        fetchPage = Math.max(...Array.from(this.fetchedPages), 0) + 1;
        limit = DEFAULT_LIMIT;
        break;
      }
      case 'clear-and-fetch': {
        this.fetchedPages.clear();
        fetchPage = DEFAULT_PAGE;
        limit = DEFAULT_LIMIT;
        break;
      }
      case 'refetch-all-current-pages': {
        fetchPage = DEFAULT_PAGE;
        limit = DEFAULT_LIMIT * this.fetchedPages.size;
        break;
      }
    }

    const params = { page: fetchPage, limit };

    if (filters)
      /* preserve filters for the next request */
      this.loadFoldersFilters$.next(filters);
    else filters = this.loadFoldersFilters$.value;

    params[
      `orderBy${filters.orderBy.charAt(0).toUpperCase() + filters.orderBy.slice(1)}`
    ] = filters.orderValue;

    const obs = this.kieService.getListFolders(params).pipe(
      tap((response) => {
        if (!response.data.folders.length) return;
        this.fetchedPages.add(fetchPage);
        const folders = cloneDeep(loadStrategy === 'fetch-next-page' ? this.folders : []);
        folders.push(...response.data.folders);
        this.store$.next({
          folders,
          sharedDocumentsCount: response.data.sharedDocumentsCount,
          foldersCount: response.data.total
        });
      }),
      catchError((err) => {
        this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại sau');
        return EMPTY;
      })
    );
    if (returnAsObservable) return obs;
    else return obs.subscribe();
  }

  /* optimistic update */
  updateFileCountOfDocument(documentId: string, fileChangeCount: number) {
    const newStore = cloneDeep(this.store$.value);

    let folderIndex = -1,
      documentIndex = -1;
    newStore.folders.forEach((folder, index) => {
      if (folderIndex !== -1 && documentIndex !== -1) return;
      if (folder.documents.map((doc) => doc.id).includes(documentId)) {
        documentIndex = folder.documents.map((doc) => doc.id).indexOf(documentId);
        folderIndex = index;
      }
    });

    if (folderIndex === -1 || documentIndex === -1) return;
    const newFilesCount =
      get(newStore, `folders.${folderIndex}.documents.${documentIndex}.filesCount`, 0) +
      fileChangeCount;
    set(
      newStore,
      `folders.${folderIndex}.documents.${documentIndex}.filesCount`,
      newFilesCount
    );

    this.store$.next(newStore);
  }

  /* call reset on user logout */
  resetDocumentLayoutServiceState() {
    this.store$.next({ folders: [], foldersCount: 0, sharedDocumentsCount: 0 });
    this.fetchedPages.clear();
    this.loadFoldersFilters$.next({ orderBy: 'createdAt', orderValue: 'DESC' });
    this.selectedDocumentId$.next(null);
  }

  /* FIXME: Service does not implement OnDestroy lifecycle like component */
  ngOnDestroy(): void {
    this.store$.complete();
    this.selectedDocumentId$.complete();
    this.loadFoldersFilters$.complete();
  }
}
