<div class="py-4 text-center font-semibold text-lg">
  Demo tích hợp module hậu kiểm OCR
</div>
<div class="flex flex-col gap-3 w-[500px] mx-auto">
  <label>
    URL module hậu kiểm OCR
    <input nz-input [(ngModel)]="hauKiemOcrModuleUrl" />
  </label>
  <label>
    source_knowledge_id
    <input nz-input [(ngModel)]="sourceKnowledgeId" />
  </label>
  <label>
    bot_id
    <input nz-input [(ngModel)]="botId" />
  </label>
  <label>
    token
    <textarea
      class="whitespace-pre-line"
      nz-input
      rows="16"
      [(ngModel)]="token"
    ></textarea>
  </label>
  <button
    nz-button
    nzType="primary"
    (click)="showModalWithHauKiemModule()"
    nz-tooltip="Gửi các input sang iframe qua postMessage, sau khi trang hậu kiểm OCR này được hoàn thành khởi tạo"
  >
    <PERSON><PERSON><PERSON> kiểm
  </button>
</div>

<ng-template #hauKiemOcrModuleTmpl let-params>
  <iframe
    id="hau-kiem-ocr-module-iframe"
    class="w-full h-full"
    [src]="params.src"
    frameborder="0"
  ></iframe>
</ng-template>
