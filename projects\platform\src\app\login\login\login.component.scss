:host {

  // no option to set responsive width for ngx-recaptcha2
  // hacky solution: scaling up to fit
  // OR center it
  ::ng-deep ngx-recaptcha2>div>div {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    // transform: scale(1.195);
    // transform-origin: 0 0;
  }
}

div#wrapper {
  flex-direction: column;
  // gap: 16px;
  // padding-top: 80px;
  // padding-bottom: 32px;

  #made-by {
    display: flex;
    gap: 8px;
  }
}

// @media (max-width: 1439px) {
//   div#wrapper {
//     padding-top: 32px;
//   }
// }

@media (max-width: 460px) {
  div#wrapper {
    #tagline {
      padding: 0 !important;
    }
  }
}

@media (max-height: 650px) {
  div#wrapper {
    justify-content: center !important;

    #logo {
      display: none;
    }

    #made-by {
      display: none;
    }
  }
}

dialog {
  font-size: 14px;
  outline: none;
  color: #273266;
  border-radius: 18px;
  background-color: #FFFFFF;
  border-color: transparent;
  padding: 0;
  border: none;
}

dialog#guideline[open] {
  width: 900px;
  height: 700px;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 28px;
    border-bottom: solid 1px #E1E2E7;
  }

  .content {
    display: flex;
    flex: 1;

    .left {
      min-width: 256px;
      max-width: 256px;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      border-right: 1px solid #E1E2E7;
      padding-top: 16px;

      div {
        display: flex;
        align-items: center;
        justify-content: left;
        padding: 10px 24px 10px 32px;
        min-height: 64px;
        cursor: pointer;

        &.selected {
          font-weight: 500;
          border-left: 4px solid #2140D2;
        }
      }
    }

    .right {
      padding: 24px 32px 24px 28px;

      .step-list {
        display: flex;
        flex-direction: column;

        // gap: 12px;
        // border-left: 1px solid #A1A5BA;

        .step {
          display: flex;
          padding-left: 20px;
          border-left: 1px solid #A1A5BA;
          padding-bottom: 16px;
          position: relative;

          &:last-child {
            border-color: transparent;
          }

          .number {
            margin-top: -8px;
            min-width: 80px;
          }

          .text {
            text-align: justify;
            margin-top: -8px;
            flex: 1;
          }
        }
      }
    }
  }

}

dialog#notice[open] {
  width: 436px;
  height: 434px;
  padding: 24px 32px;

  .header {
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }

  .content {
    margin-top: 16px;
    text-align: justify;
    font-weight: 500;
    line-height: 22px;
    padding-bottom: 16px;
    border-bottom: 1px solid #E4E6F3;
  }

  a {
    color: #2140D2;
    font-weight: 500;
  }

  .login-btn {
    background-color: #A1A5BA;
    color: #FFF;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
    padding: 12px 0;
    border-radius: 10px;
    margin-bottom: 20px;
    cursor: not-allowed;

    &.agreed {
      background-color: #2140D2;
      cursor: pointer;
    }
  }

  .cancel-btn {
    span {
      cursor: pointer;
    }

    color: #2140D2;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    line-height: 24px;
  }
}