/*  @deprecated */
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '@platform/environment/environment';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ChangePasswordModalComponent } from '../change-password-modal/change-password-modal.component';
import { UserInfoModalComponent } from '../user-info-modal/user-info-modal.component';
import { RevokeConsentModalComponent } from '../revoke-consent-modal/revoke-consent-modal.component';
import { RequestRemoveDataModalComponent } from '../request-remove-data-modal/request-remove-data-modal.component';
import { SingleSignOnService } from '@platform/app/core/services/sso.service';
import { PlatformV2CountdownModalComponent } from '@platform/app/layout/platform-v2-countdown-modal/platform-v2-countdown-modal.component';

@Component({
  selector: 'app-old-layout',
  templateUrl: './old-layout.component.html',
  styleUrls: ['./old-layout.component.scss']
})
export class OldLayoutComponent implements OnInit {
  menus = [
    {
      path: 'document',
      name: 'OCR Platform',
      icon: 'assets/img/rpa/sidebar-icon/dashboard.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/dashboard-active.svg'
    },
    {
      path: 'permission',
      name: 'Quản lý phân quyền',
      icon: 'assets/img/rpa/sidebar-icon/permission.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/permission-active.svg'
    },
    {
      path: 'manage-project',
      name: 'Quản lý token',
      icon: 'assets/img/rpa/sidebar-icon/token.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/token-active.svg'
    },
    {
      path: 'statistic',
      name: 'Thống kê',
      icon: 'assets/img/rpa/sidebar-icon/statistic.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/statistic-active.svg'
    },
    {
      path: 'ocr-experience',
      name: 'Trải nghiệm',
      icon: 'assets/img/rpa/sidebar-icon/ocr-experience.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/ocr-experience-active.svg'
    },
    {
      path: 'subscription',
      name: 'Gói cước',
      icon: 'assets/img/rpa/sidebar-icon/subscription.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/subscription-active.svg'
    },
    {
      path: 'payment-history',
      name: 'Lịch sử thanh toán',
      icon: 'assets/img/rpa/sidebar-icon/payment-history.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/payment-history-active.svg'
    },
    {
      path: 'demo/dong-ho-nuoc',
      name: 'Demo',
      icon: 'assets/img/rpa/sidebar-icon/payment-history.svg',
      iconActive: 'assets/img/rpa/sidebar-icon/payment-history-active.svg',
      hidden: ['production']
    }
  ].filter((menu) => {
    if (menu.hidden) return !menu.hidden.includes(environment.envName);
    return true;
  });
  urlAdmin = '';
  userInfo: {
    id: string;
    idg_access_token: string;
    token_id: string;
    token_key: string;
    username: string;
    created_on: string;
  };
  constructor(
    public router: Router,
    private modal: NzModalService,
    private ssoService: SingleSignOnService
  ) {}

  ngOnInit(): void {
    this.userInfo = localStorage.getItem('currentUser')
      ? JSON.parse(localStorage.getItem('currentUser'))
      : {};

    if (!localStorage.getItem('platformV2Understood'))
      this.showPlatformV2CountdownModal();
  }

  showPlatformV2CountdownModal() {
    this.modal.create({
      nzContent: PlatformV2CountdownModalComponent,
      nzFooter: null
    });
  }

  logout(e: Event) {
    e.preventDefault();
    localStorage.clear();
    this.ssoService.logout();
    window.location.href = this.urlAdmin;
    this.router.navigate(['/admin-dashboard']);
  }

  showUserInfo(e: Event) {
    e.preventDefault();
    const modalRef = this.modal.create({
      nzContent: UserInfoModalComponent,
      nzFooter: null
    });
  }

  showChangePassword(e: Event) {
    e.preventDefault();
    const modalRef = this.modal.create({
      nzContent: ChangePasswordModalComponent,
      nzFooter: null
    });
  }

  showRevokeConsentModal(e: Event) {
    e.preventDefault();
    const modalRef = this.modal.create({
      nzContent: RevokeConsentModalComponent,
      nzFooter: null
    });
  }

  showRequestRemoveDataModal(e: Event) {
    e.preventDefault();
    const modalRef = this.modal.create({
      nzContent: RequestRemoveDataModalComponent,
      nzFooter: null
    });
  }

  toggleSidebar() {
    const body = document.getElementById('mainAdminLte');
    if (body.classList.contains('sidebar-collapse'))
      body.classList.remove('sidebar-collapse');
    else body.classList.add('sidebar-collapse');
  }
}
