:host {
  --document-viewer-header-bg-color: #E2E2E2;
  --document-viewer-header-text-color: #111127;

  .template-result-wrapper,
  .rpa-table-wrapper {
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: #cbcbcb;
    }

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background-color: transparent;
    }

    overflow-x: auto;

    table {
      width: 100%;
      margin-bottom: 4px;
      // border-spacing and border-collapse help bend the border
      border-spacing: 0;
      border-collapse: separate;

      thead {
        background-color: #eeeeee;

        th {
          text-align: left;
          padding: 10px;
          color: #545454;
          border-top: 2px solid #eeeeee;

          &:first-child {
            border-top-left-radius: 8px;
            border-left: 2px solid #eeeeee;
          }

          &:last-child {
            border-top-right-radius: 8px;
            border-right: 2px solid #eeeeee;
          }
        }
      }

      tr {
        td {
          padding: 10px;
          border-bottom: 2px solid #eeeeee;
          border-left: 2px solid #eeeeee;

          &:last-child {
            border-right: 2px solid #eeeeee;
          }
        }

        &:last-child {
          td {
            border-bottom: 2px solid #eeeeee;

            &:first-child {
              border-bottom-left-radius: 8px;
            }

            &:last-child {
              border-bottom-right-radius: 8px;
            }
          }
        }
      }
    }
  }

  /* re-apply antd switch component since its styled is reverted by tailwindCSS */
  ::ng-deep {
    button {
      &.ant-switch {
        background-image: linear-gradient(to right, rgba(0, 0, 0, .25), rgba(0, 0, 0, .25)), linear-gradient(to right, #fff, #fff);
      }

      &.ant-switch-checked {
        background: #009B4E;
      }
    }
  }
}

// custom class name, unique across app
::ng-deep {
  .modal-ocr-experience-result-export {
    .ant-modal-content {
      border-radius: 16px;
    }
  }
}