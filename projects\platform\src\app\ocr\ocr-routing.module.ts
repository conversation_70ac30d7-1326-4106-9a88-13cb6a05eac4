import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '@platform/app/layout/layout/layout.component';
import { OcrLayoutComponent } from './components/ocr-layout/ocr-layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: ':tabKey',
        component: OcrLayoutComponent,
        canDeactivate: [(component) => component?.canDeactivate() || true]
      },
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'preprocessing'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class OCRRoutingModule {}
