import { HttpErrorResponse } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import { UploadFilesChange } from '@platform/app/kie/components/upload-files/upload-files.component';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, tap } from 'rxjs';

@Component({
  selector: 'app-modal-change-document-template-file',
  templateUrl: './modal-change-document-template-file.component.html',
  styleUrls: ['./modal-change-document-template-file.component.scss']
})
export class ModalChangeDocumentTemplateFileComponent implements OnInit {
  readonly nzModalData: { documentId: string } = inject(NZ_MODAL_DATA);
  documentId = this.nzModalData.documentId;

  fileList: File[] = [];

  constructor(
    private toastrService: ToastrService,
    private kieService: KIEService,
    private modalRef: NzModalRef
  ) {}

  ngOnInit(): void {}

  handleUploadFilesChange(change: UploadFilesChange) {
    switch (change.action) {
      case 'add': {
        this.fileList.push(...(change.addedFiles || []));
        break;
      }
      case 'remove': {
        this.fileList = this.fileList.filter((_, i) => i !== (change.removeIndex ?? -1));
        break;
      }
    }
  }

  save() {
    if (!this.fileList.length)
      return this.toastrService.error('Vui lòng upload file config!');
    this.kieService
      .updateDocumentTemplateFile(this.documentId, this.fileList[0])
      .pipe(
        tap(() => {
          this.toastrService.success('Sửa mẫu giấy tờ thành công');
          this.modalRef.close(true);
        }),
        catchError((err) => {
          let errorMessage = 'Đã có lỗi xảy ra, vui lòng thử lại sau';
          if (err instanceof HttpErrorResponse) {
            switch (err.status) {
              case 403:
                errorMessage =
                  'Bạn không có quyền thực hiện hành động này, hãy liên hệ với chủ sở hữu văn bản để được cấp quyền';
            }
          }
          this.toastrService.error(errorMessage);
          return EMPTY;
        })
      )
      .subscribe();
  }
}
