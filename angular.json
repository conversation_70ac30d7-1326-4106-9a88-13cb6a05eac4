{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"platform": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "projects/platform", "sourceRoot": "projects/platform/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "projects/platform/deploy/dist", "index": "projects/platform/src/index.html", "main": "projects/platform/src/main.ts", "polyfills": ["projects/platform/src/polyfills.ts"], "tsConfig": "projects/platform/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/platform/src/favicon.ico", "projects/platform/src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["projects/platform/src/styles.scss"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-json.min.js"], "allowedCommonJsDependencies": ["ng2-pdf-viewer", "pdfjs-dist", "exceljs", "docx", "fabric", "lodash", "node-forge", "file-saver"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "projects/platform/src/environments/environment.ts", "with": "projects/platform/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "sandbox": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "projects/platform/src/environments/environment.ts", "with": "projects/platform/src/environments/environment.sandbox.ts"}], "outputHashing": "all"}, "vnptai.io.sandbox": {"baseHref": "/console/smartreader/", "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "150kb", "maximumError": "150kb"}], "fileReplacements": [{"replace": "projects/platform/src/environments/environment.ts", "with": "projects/platform/src/environments/environment.sandbox.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "platform:build:production"}, "development": {"browserTarget": "platform:build:development"}, "sandbox": {"browserTarget": "platform:build:sandbox"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "platform:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "projects/platform/src/polyfills.ts"], "tsConfig": "projects/platform/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/platform/src/favicon.ico", "projects/platform/src/assets"], "styles": ["projects/platform/src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "f748966a-6998-43d0-91b1-965b851d1380"}}