import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalDeleteDocumentComponent } from './modal-delete-document.component';

describe('ModalDeleteDocumentComponent', () => {
  let component: ModalDeleteDocumentComponent;
  let fixture: ComponentFixture<ModalDeleteDocumentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalDeleteDocumentComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalDeleteDocumentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
