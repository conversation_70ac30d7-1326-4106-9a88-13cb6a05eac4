import {
  Component,
  Output,
  EventEmitter,
  Input,
  OnChanges,
  SimpleChanges,
  ViewChild,
  TemplateRef
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import {
  FunctionType,
  Mode,
  RestrictionType,
  Template,
  TemplateTypes
} from '../../ocr-experience';
import { OcrExperienceService } from '@platform/app/core/services/ocr-experience.service';
import { get } from 'lodash';
import { PDFDocument } from 'pdf-lib';
import { PageOrientation } from 'docx';
import { environment } from '@platform/environment/environment';
import { ReCaptcha2Component } from 'ngx-captcha';
import { EMPTY, catchError, forkJoin, tap } from 'rxjs';
import { NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-file-input',
  templateUrl: './file-input.component.html',
  styleUrls: ['./file-input.component.scss']
})
export class FileInputComponent implements OnChanges {
  readonly RestrictionType = RestrictionType;
  readonly Mode = Mode;
  mode: Mode = Mode.Platform; // default
  sampleFiles;
  file: File & { pdfCreatedFromImages?: boolean };
  isUsingSampleFile: string = null;
  selectedTemplate: Template = null;
  @Output() inputFileEvent = new EventEmitter<typeof this.file>();
  @Input() selectedTemplateValue: string = null;
  @Input() selectedFunctionValue: string = null;
  siteKey = environment.recaptchaSiteKey;
  @ViewChild('captchaElem', { static: false }) captchaElem: ReCaptcha2Component;
  captcha: string;
  @Input() restriction?: RestrictionType;
  devModeEnabled = true;
  envName = environment.envName;
  @ViewChild('imagesIntoPdfModal') imagesIntoPdfModal: TemplateRef<any>;

  constructor(
    private toastrService: ToastrService,
    private router: Router,
    private ocrExperienceService: OcrExperienceService,
    private modal: NzModalService
  ) {
    this.devModeEnabled = history.state.devModeEnabled ?? this.devModeEnabled;
    this.mode = this.router.url.includes('/ldp/ocr-experience')
      ? Mode.LDP
      : this.router.url.includes('/demo/ocr-experience')
        ? Mode.Demo
        : Mode.Platform;
  }

  ngOnChanges(changes: SimpleChanges): void {
    // clear previous state;
    this.sampleFiles = [];
    this.file = null;
    this.selectedTemplate = null;
    this.isUsingSampleFile = null;
    this.inputFileEvent.emit(this.file);

    this.selectedTemplate = TemplateTypes.find(
      (template) =>
        template.value === this.selectedTemplateValue &&
        template.functionType === this.selectedFunctionValue
    );
    if (!this.selectedTemplate) return;

    // fetch once, when init
    if (this.mode === Mode.Platform) this.fetchSampleFiles();
  }

  fetchSampleFiles() {
    forkJoin(
      Object.fromEntries(
        this.selectedTemplate.sampleFiles.map((file) => [
          file.path,
          this.ocrExperienceService.fetchFile(file.path)
        ])
      )
    )
      .pipe(
        tap((sampleFilesResponse) => {
          for (const path in sampleFilesResponse) {
            sampleFilesResponse[path] = new File(
              [sampleFilesResponse[path]],
              path.split('/').pop(),
              {
                type: sampleFilesResponse[path].type
              }
            );
          }
          this.sampleFiles = sampleFilesResponse;
        })
      )
      .subscribe();
  }

  handleChangeFile(event, inputFileElem) {
    const fileMimetype = get(event, 'target.files.0.type', '');
    const fileExtension = get(event, 'target.files.0.name', '')
      .split('.')
      .pop()
      .toLowerCase();
    if (
      !this.selectedTemplate.acceptType
        .split(',')
        .map((item) => item.trim())
        .includes(fileMimetype) ||
      !this.selectedTemplate.acceptExtensions.includes(fileExtension)
    ) {
      return this.toastrService.error(
        'File không đúng định dạng. Vui lòng kiểm tra lại!'
      );
    }
    this.file = event.target.files[0];
    this.inputFileEvent.emit(this.file);

    // reset input file element
    if (inputFileElem) inputFileElem.value = null;
  }

  async handleSubmit() {
    /* check restriction */
    if (!!this.restriction) {
      this.toastrService.error(
        'Bạn không được phép sử dụng tính năng này, vui lòng liên hệ với chúng tôi để biết thêm thông tin.'
      );
      return;
    }
    if (this.file) {
      if (this.mode === Mode.LDP && !this.captcha)
        return this.toastrService.error('Vui lòng xác nhận reCAPTCHA');

      const formData = new FormData();
      formData.append('file', this.file);
      formData.append('title', '');
      formData.append('description', '');
      const addFileInput = {
        body: formData,
        isSplitting: this.selectedTemplate.functionType === FunctionType.SoHoaVanBan,
        isLandingPageMode: this.mode === Mode.LDP
      };
      if (this.mode === Mode.LDP) {
        addFileInput['captcha'] = this.captcha;
        this.captcha = null;
        this.captchaElem.reloadCaptcha();
      }

      this.ocrExperienceService
        .addFile(addFileInput)
        .pipe(
          tap((result) => {
            // console.log(result.object);
            const navigateResult =
              (this.mode === Mode.LDP ? 'ldp/' : this.mode === Mode.Demo ? 'demo/' : '') +
              'ocr-experience/result';
            if ('length' in result.object) {
              // calling API /add-file-split, always get a list of objects containing hash (pdf or non-pdf)
              this.router.navigate([navigateResult], {
                state: {
                  isSplitting: true,
                  fileHashes: result.object.map((item) => item.object.hash),
                  fileType: get(result, 'object.0.object.fileType'),
                  templateType: this.selectedTemplate,
                  ...(this.selectedTemplate.allowDevMode
                    ? { devModeEnabled: this.devModeEnabled }
                    : {}),
                  file: this.file,
                  isUsingSampleFile: !!this.isUsingSampleFile
                }
              });
            } else {
              // calling API file-service/addFile, get an object containing hash
              this.router.navigate([navigateResult], {
                state: {
                  isSplitting: false,
                  fileHash: result.object.hash,
                  fileType: this.file.pdfCreatedFromImages
                    ? 'pdf-captured'
                    : result.object.fileType,
                  templateType: this.selectedTemplate,
                  ...(this.selectedTemplate.allowDevMode
                    ? { devModeEnabled: this.devModeEnabled }
                    : {}),
                  file: this.file,
                  isUsingSampleFile: !!this.isUsingSampleFile
                }
              });
            }
          }),
          catchError(() => {
            this.toastrService.error('Tải file lên server thất bại!');
            return EMPTY;
          })
        )
        .subscribe();
    } else this.toastrService.error('Vui lòng chọn file cần bóc tách');
  }

  handleChangeMethod(samplePath?: string) {
    if (this.mode === Mode.LDP) return;
    this.isUsingSampleFile = samplePath || null;

    if (this.isUsingSampleFile) {
      this.file = this.sampleFiles[this.isUsingSampleFile];
      this.toastrService.clear();
      this.toastrService.info(
        'Thông tin trên File mẫu là thông tin giả lập, không có thật',
        undefined,
        { timeOut: 15 * 1000 }
      );
    } else this.file = null;

    this.inputFileEvent.emit(this.file);
  }

  clearSelectedFile() {
    this.file = null;
    this.inputFileEvent.emit(this.file);
  }

  handleCaptchaReset() {}
  handleCaptchaExpire() {}
  handleCaptchaError() {}
  handleCaptchaLoad() {}
  handleCaptchaSuccess(data) {
    this.captcha = data;
    // console.log(this.captcha);
  }

  closeAllModal() {
    this.modal.closeAll();
  }

  showImagesIntoPdfModal() {
    if (this.isUsingSampleFile || !this.selectedTemplate.allowMergeImagesIntoPdfOption)
      return;

    const modalRef = this.modal.create({
      nzContent: this.imagesIntoPdfModal,
      nzCentered: true,
      nzClosable: false,
      nzMaskClosable: false,
      nzFooter: null,
      nzBodyStyle: { padding: '0' },
      nzClassName: 'custom-ant-modal-common-styles'
    });
  }

  handlePdfCreated(createdPdf: File) {
    this.closeAllModal();
    if (!createdPdf) return; // cancel/discard merged pdf
    createdPdf['pdfCreatedFromImages'] = true;
    this.file = createdPdf;
    this.inputFileEvent.emit(this.file);
  }
}
