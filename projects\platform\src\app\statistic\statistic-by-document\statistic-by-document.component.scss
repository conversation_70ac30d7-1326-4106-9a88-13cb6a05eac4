:host ::ng-deep .ant-select .ant-select-selector {
	border-radius: 8px;
}


:host .statistic-container ::ng-deep>.ant-tabs-default {
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	background-color: #fff;

	>.ant-tabs-nav {
		padding: 12px 24px 12px 24px;
		margin-bottom: 0;

		&::before {
			display: none;
		}

		>.ant-tabs-nav-wrap {

			>.ant-tabs-nav-list {
				padding: 6px 12px 6px 12px;
				background-color: #F0F1F4;
				border-radius: 46px;

				>.ant-tabs-ink-bar {
					display: none;
				}

				>.ant-tabs-tab {
					min-width: 195px;
					justify-content: center;
					padding: 10px 0 10px 0;
					font-size: 12px;
					font-weight: 600;
					color: #2B2D3B;

					+.ant-tabs-tab {
						margin: 0;
					}

					.ant-tabs-tab-btn {
						font-weight: 700;
						color: #111127;
					}

					&.ant-tabs-tab-active {
						background-color: #fff;
						border-radius: 26px;
						box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.10);
						color: #0667E1;

						.ant-tabs-tab-btn {
							color: #0F67CE;
						}
					}
				}
			}
		}
	}
}