import {
  AfterViewInit,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
  TemplateRef,
  ViewChild
} from '@angular/core';
import { StatisticService } from '@platform/app/core/services/statistic.service';
import { TableConfig } from '../statistic.interface';
import { tap } from 'rxjs';

@Component({
  selector: 'app-statistic-by-page',
  templateUrl: './statistic-by-page.component.html',
  styleUrls: ['./statistic-by-page.component.scss']
})
export class StatisticByPageComponent implements OnChanges, AfterViewInit {
  @Input() dateFilter;

  @ViewChild('successTooltipTmpl')
  successTooltipTmpl: TemplateRef<void>;

  @ViewChild('invalidTooltipTmpl')
  invalidTooltipTmpl: TemplateRef<void>;

  pageStatisticStatus: {
    key: string;
    label: string;
    color: string;
    value: number;
    tooltip: string | TemplateRef<void>;
  }[] = [
    {
      key: 'success',
      label: 'Thành công',
      color: '#009B4E',
      value: 0,
      tooltip: 'Số trang văn bản được xử lý và trả kết quả thành công'
    },
    {
      key: 'invalid',
      label: 'Không hợp lệ',
      color: '#FF3355',
      value: 0,
      tooltip: 'Số trang văn bản lỗi do đầu vào không hợp lệ'
    }
  ];
  pageStatisticByTime: { time: string; page: number }[] = [];
  pageStatisticByAPI: TableConfig = {
    hasTotalRow: true,
    title: 'Thống kê số trang theo tần suất API',
    columns: [
      { title: 'API Path', index: 'api' },
      {
        title: 'Tổng số trang',
        index: 'total',
        canSum: true
      },
      {
        title: 'Thành công',
        index: 'success',
        canSum: true,
        tooltip: 'Số trang văn bản được xử lý và trả kết quả thành công'
      },
      {
        title: 'Không hợp lệ',
        index: 'invalid',
        canSum: true,
        tooltip: 'Số trang văn bản lỗi do đầu vào không hợp lệ'
      }
    ],
    data: []
  };

  constructor(private statisticService: StatisticService) {}

  ngOnChanges(changes: SimpleChanges): void {
    const dateFilterValue = changes['dateFilter']?.currentValue?.value!;

    this.setPageStatisticStatus();
    this.setPageStatisticByTime();
    this.setPageStatisticByAPI();
  }

  ngAfterViewInit(): void {
    this.pageStatisticStatus[0].tooltip = this.successTooltipTmpl;
    this.pageStatisticStatus[1].tooltip = this.invalidTooltipTmpl;

    this.pageStatisticByAPI.columns[2].tooltip = this.successTooltipTmpl;
    this.pageStatisticByAPI.columns[3].tooltip = this.invalidTooltipTmpl;
  }

  setPageStatisticStatus(): void {
    this.statisticService
      .fetchPageStatisticStatus(this.dateFilter?.value)
      .pipe(
        tap((result) => {
          this.pageStatisticStatus = this.pageStatisticStatus.map((item) => {
            return {
              ...item,
              value: result.object[item.key]
            };
          });
        })
      )
      .subscribe();
  }

  setPageStatisticByTime(): void {
    this.statisticService
      .fetchPageStatisticByTime(this.dateFilter?.value)
      .subscribe((result: any) => (this.pageStatisticByTime = result.object));
  }

  setPageStatisticByAPI(filters?: { search?: string }): void {
    this.statisticService
      .fetchPageStatisticByAPI(this.dateFilter?.value)
      .subscribe((result) => {
        let tmp = result.object;
        if (filters?.search)
          tmp = tmp.filter((apiStat) => apiStat.api.includes(filters?.search));
        this.pageStatisticByAPI.data = tmp;
      });
  }
}
