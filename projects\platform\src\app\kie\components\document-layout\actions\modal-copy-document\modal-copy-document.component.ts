import { HttpParams } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { KIEService } from '@platform/app/core/services/kie.service';
import UtilsService from '@platform/app/core/services/utils.service';
import { Document, Folder, OcrModel } from '@platform/app/kie/kie';
import { get, isArray, pick } from 'lodash';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ToastrService } from 'ngx-toastr';
import { catchError, EMPTY, switchMap, tap, throwError } from 'rxjs';

@Component({
  selector: 'app-modal-copy-document',
  templateUrl: './modal-copy-document.component.html',
  styleUrls: ['./modal-copy-document.component.scss']
})
export class ModalCopyDocumentComponent implements OnInit {
  readonly nzModalData: { sourceFolder: Folder; sourceDocumentId: string } =
    inject(NZ_MODAL_DATA);
  sourceFolder = this.nzModalData.sourceFolder;
  sourceDocumentId = this.nzModalData.sourceDocumentId;
  sourceDocument: Document;
  newTemplateFile: File;

  constructor(
    private kieService: KIEService,
    private utils: UtilsService,
    private toastr: ToastrService,
    private modalRef: NzModalRef
  ) {}

  ngOnInit(): void {
    this.kieService
      .getDocumentDetail(this.sourceDocumentId)
      .pipe(
        tap((result) => {
          this.sourceDocument = result.data;
        }),
        switchMap((result) => {
          if (!result.data.isSelfConfig) return EMPTY;
          return this.utils.fetchFile(result.data.template.file_link).pipe(
            tap((blob) => {
              let extension = '.';
              switch (blob.type) {
                case 'application/pdf':
                  extension += 'pdf';
                  break;
                case 'image/png':
                  extension += 'png';
                  break;
                case 'image/jpeg':
                  extension += 'jpeg';
                  break;
                default:
                  extension = '';
                  break;
              }
              this.newTemplateFile = new File([blob], `template-file${extension}`, {
                type: blob.type
              });
            }),
            this.gracefullyCatchError
          );
        }),
        this.gracefullyCatchError
      )
      .subscribe();
  }

  cancel() {
    this.modalRef.close();
  }

  copy() {
    if (this.sourceFolder.documents.length >= 100)
      return this.toastr.error(
        'Vượt quá số lượng tối đa 100 văn bản cho phép trong 1 thư mục'
      );
    const copy$ =
      this.sourceDocument.isSelfConfig && this.newTemplateFile
        ? this.kieService
            .createNewSelfDocument({
              folderId: this.sourceFolder.id,
              name: this.newDocumentName,
              templateFile: this.newTemplateFile,
              templateOcrModel: this.sourceDocument.template.config.ocr_model
            })
            .pipe(
              this.gracefullyCatchError,
              switchMap((result) => {
                const filters = {
                  limit: 1,
                  page: 1,
                  role: ['creator'],
                  templateTypes: ['isSelfConfig']
                };
                let params = new HttpParams();
                for (const key in filters) {
                  if (!filters[key]) continue;
                  if (isArray(filters[key]))
                    filters[key].forEach((value) => {
                      params = params.append(`${key}[]`, value);
                    });
                  else params = params.append(key, filters[key]);
                }
                return this.kieService.getListSharedDocuments(params).pipe(
                  switchMap((listDoc) => {
                    const newDocId = get(listDoc, 'data.documents.0.id');
                    if (!newDocId)
                      throw new Error('Get most recent created document failed');
                    const extraConfig = get(this.sourceDocument, 'extraConfig', []);
                    const templateConfigFields = Object.entries(
                      this.sourceDocument.template.config.fields
                    ).map(([fieldName, fieldConfig]) => {
                      const tmp = pick(
                        fieldConfig,
                        ['name', 'model', 'is_visible'].concat(
                          this.sourceDocument.template.config.ocr_model ===
                            OcrModel.Default
                            ? [
                                'is_date',
                                'is_number',
                                'is_title_or_uppercase',
                                'multilines',
                                'prewords',
                                'sufwords'
                              ]
                            : []
                        ) as any
                      );
                      const ec = extraConfig.find((ec) => ec.name === fieldName);
                      if (ec) {
                        tmp['extraConfig'] = pick(ec, ['color', 'is_visible', 'order']);
                      }
                      if (fieldConfig.location) {
                        const [pageNumber, bbox] =
                          Object.entries(fieldConfig.location).pop() || [];
                        if (isFinite(parseInt(pageNumber)) && bbox?.length === 4)
                          tmp['location'] = {
                            pageNumber: parseInt(pageNumber),
                            xMin: bbox[0],
                            yMin: bbox[1],
                            xMax: bbox[2],
                            yMax: bbox[3]
                          };
                      }
                      return tmp;
                    });
                    return this.kieService
                      .updateDocumentTemplateConfig(newDocId, {
                        templateConfigOcrModel:
                          this.sourceDocument.template.config.ocr_model,
                        templateConfigFields: templateConfigFields as any
                      })
                      .pipe(
                        catchError((err) => {
                          /* delete newly created document immediately */
                          this.kieService
                            .deleteDocument(newDocId)
                            .pipe(this.gracefullyCatchError)
                            .subscribe(); // side effect
                          /* rethrow error */
                          return throwError(() => err);
                        }),
                        this.gracefullyCatchError
                      );
                  }),
                  this.gracefullyCatchError
                );
              })
            )
        : this.kieService
            .createNewSystemDocument({
              folderId: this.sourceFolder.id,
              name: this.newDocumentName,
              systemTemplateName: this.sourceDocument.templateRef
            })
            .pipe(
              switchMap((result) => {
                const filters = {
                  limit: 1,
                  page: 1,
                  role: ['creator'],
                  templateTypes: [this.sourceDocument.templateRef]
                };
                let params = new HttpParams();
                for (const key in filters) {
                  if (!filters[key]) continue;
                  if (isArray(filters[key]))
                    filters[key].forEach((value) => {
                      params = params.append(`${key}[]`, value);
                    });
                  else params = params.append(key, filters[key]);
                }
                return this.kieService.getListSharedDocuments(params).pipe(
                  switchMap((listDoc) => {
                    const newDocId = get(listDoc, 'data.documents.0.id');
                    if (!newDocId)
                      throw new Error('Get most recent created document failed');
                    const extraConfig = get(this.sourceDocument, 'extraConfig', []).map(
                      (conf) => pick(conf, ['color', 'is_visible', 'name'])
                    );
                    return this.kieService
                      .updateDocumentDetail(newDocId, { extraConfig })
                      .pipe(
                        catchError((err) => {
                          /* delete newly created document immediately */
                          this.kieService
                            .deleteDocument(newDocId)
                            .pipe(this.gracefullyCatchError)
                            .subscribe(); // side effect
                          /* rethrow error */
                          return throwError(() => err);
                        }),
                        this.gracefullyCatchError
                      );
                  }),
                  this.gracefullyCatchError
                );
              }),
              this.gracefullyCatchError
            );
    copy$
      .pipe(
        tap(() => {
          this.toastr.success('Tạo bản sao văn bản thành công');
          this.modalRef.close(true);
        })
      )
      .subscribe();
  }

  private get newDocumentName() {
    let newDocumentName = '';
    let isDuplicated = false;
    let count = 0;

    do {
      isDuplicated = false;
      newDocumentName = this.sourceDocument.name.trim() + ` - Bản sao ${++count}`;
      if (
        this.sourceFolder.documents
          .map((doc) => doc.name.trim())
          .includes(newDocumentName)
      )
        isDuplicated = true;
    } while (isDuplicated);

    return newDocumentName;
  }

  private get gracefullyCatchError(): any {
    return catchError((err) => {
      console.log(err);
      this.toastr.error('Đã có lỗi xảy ra, vui lòng thử lại.');
      this.modalRef.close();
      return EMPTY;
    });
  }
}
