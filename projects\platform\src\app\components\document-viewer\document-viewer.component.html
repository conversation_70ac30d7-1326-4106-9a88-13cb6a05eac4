<div *ngIf="featureFlags['Zooming']" class="controls info">
  <div>{{ pressingKey }}</div>
  <div>Zoom: {{ zoomLevel }}</div>
</div>
<div *ngIf="featureFlags['Zooming']" class="controls extra">
  <nz-switch
    [disabled]="drawCommandSubject ? (drawCommandSubject | async) : false"
    nzSize="default"
    (ngModelChange)="switchCurrentMode()"
    [ngModel]="currentModeSubject.value === 'zoom'"
    nzCheckedChildren="Zoom"
    nzUnCheckedChildren="Normal"
    nz-tooltip
    nzTooltipTitle=""
  ></nz-switch>
  <!-- <label
    nz-checkbox
    class="text-white text-xs"
    [(ngModel)]="isDrawingBbox"
    (ngModelChange)="drawBbox($event)"
    >Draw Bbox</label
  > -->
</div>
<div class="document-viewer-header">
  <div
    nz-tooltip
    nzTooltipPlacement="topLeft"
    [nzTooltipTitle]="fileName"
    class="flex-1 text-base font-bold w-[1px] truncate break-words"
    *ngIf="fileName"
  >
    {{ fileName }}
  </div>
  <div
    class="flex-shrink-0 flex gap-6 items-center justify-between text-xs font-semibold"
    [ngStyle]="{
      flex: fileName ? 'initial' : '1'
    }"
  >
    <button (click)="prevPage()" class="flex items-center gap-2">
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.38016 3.95312L2.3335 7.99979L6.38016 12.0465"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M13.6668 8H2.44678"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Trước
    </button>
    <div class="flex items-center gap-[6px]">
      Trang
      <span
        class="p-[6px] rounded-[4px] border text-text-1 border-[#16161D] bg-bg-1 min-w-[30px] text-center"
      >
        {{ currentPageIndexSubject.value + 1 }}
      </span>
      /
      <span>{{
        featureFlags['OnlyDisplaySpecifiedPages'] && specifiedPages?.length
          ? numPages + ' (Đã chọn ' + specifiedPages.length + ' trang)'
          : numPages
      }}</span>
    </div>
    <button (click)="nextPage()" class="flex items-center gap-2">
      Sau
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.62012 3.95312L13.6668 7.99979L9.62012 12.0465"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2.3335 8H13.5535"
          stroke="var(--header-text-color, white)"
          stroke-width="1.5"
          stroke-miterlimit="10"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
  <div
    class="cursor-pointer rounded-full w-6 h-6 flex items-center justify-end"
    nz-popover
    nzPopoverTrigger="click"
    [nzPopoverContent]="moreActions"
    nzPopoverPlacement="bottomRight"
  >
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.99984 2C7.2665 2 6.6665 2.6 6.6665 3.33333C6.6665 4.06667 7.2665 4.66667 7.99984 4.66667C8.73317 4.66667 9.33317 4.06667 9.33317 3.33333C9.33317 2.6 8.73317 2 7.99984 2Z"
        fill="var(--header-text-color, white)"
      />
      <path
        d="M7.99984 11.3335C7.2665 11.3335 6.6665 11.9335 6.6665 12.6668C6.6665 13.4002 7.2665 14.0002 7.99984 14.0002C8.73317 14.0002 9.33317 13.4002 9.33317 12.6668C9.33317 11.9335 8.73317 11.3335 7.99984 11.3335Z"
        fill="var(--header-text-color, white)"
      />
      <path
        d="M7.99984 6.6665C7.2665 6.6665 6.6665 7.2665 6.6665 7.99984C6.6665 8.73317 7.2665 9.33317 7.99984 9.33317C8.73317 9.33317 9.33317 8.73317 9.33317 7.99984C9.33317 7.2665 8.73317 6.6665 7.99984 6.6665Z"
        fill="var(--header-text-color, white)"
      />
    </svg>
  </div>
</div>
<div #documentWrapper id="document-wrapper" class="flex-1 relative">
  <canvas style="width: inherit; height: inherit" id="document-viewer"
    >Your browser does not support the canvas element</canvas
  >
  <!-- <div
    class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-xl z-10"
    *ngIf="loading"
  >
    Loading...({{ renderedPages.length }})
  </div> -->

  <ngx-spinner
    name="document-viewer-loading-spinner"
    [fullScreen]="false"
    type="ball-scale-multiple"
    bdColor="rgba(0, 0, 0, 0.1)"
    ><div class="bg-[--bg-color] p-[2px] rounded-sm">
      Đang tải file...({{ renderedPages.size }}/{{ numPages }})
    </div></ngx-spinner
  >
  <div
    #popoverAnchor
    nz-popover
    [nzPopoverVisible]="!!selectedBboxSubject.value"
    (nzPopoverVisibleChange)="handleEditFieldPopoverVisibleChange($event)"
    [nzPopoverBackdrop]="true"
    [nzPopoverContent]="editFieldTemplate"
    nzPopoverTrigger="click"
    class="absolute bg-black top-0 left-0"
    nzPopoverPlacement="leftBottom"
    [nzPopoverOverlayStyle]="{ width: '300px' }"
  ></div>
  <div #tooltipAnchor class="absolute bg-black top-0 left-0 text-base text-white"></div>
  <ng-template #editFieldTemplate>
    <div
      class="bg-white max-w-[300px] px-2 py-3 rounded-lg shadow-[0px_4px_20px_0px_rgba(0,0,0,0.15)]"
    >
      <div class="font-semibold text-text-1 mb-1 break-words">
        {{ selectedBboxSubject.value?.name }}
      </div>
      <textarea
        [disabled]="!featureFlags['EditOcrResult']"
        [spellcheck]="false"
        type="text"
        class="mb-2 py-2 px-4 rounded-lg border border-line text-text-1 resize-y w-full"
        rows="4"
        [(ngModel)]="editingFieldValue"
      ></textarea>
      <div
        *ngIf="featureFlags['EditOcrResult']"
        class="flex items-center justify-center gap-2"
      >
        <button (click)="handleEditFieldPopoverOk()">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="12" cy="12" r="11.5" fill="#0667E1" stroke="#0667E1" />
            <path
              d="M16.1082 9.03155C15.8194 8.74274 15.3802 8.74274 15.0914 9.03155L10.6798 13.4432L8.90819 11.6716C8.61938 11.3827 8.18023 11.3827 7.89142 11.6716C7.6026 11.9604 7.6026 12.3995 7.89142 12.6883L10.1714 14.9683C10.2388 15.0357 10.3111 15.0912 10.3965 15.1292C10.4825 15.1674 10.5752 15.1849 10.6798 15.1849C10.7844 15.1849 10.8771 15.1674 10.9631 15.1292C11.0485 15.0912 11.1208 15.0357 11.1882 14.9683L16.1082 10.0483C16.397 9.75951 16.397 9.32037 16.1082 9.03155Z"
              fill="white"
              stroke="white"
              stroke-width="0.25"
            />
          </svg>
        </button>
        <button (click)="handleEditFieldPopoverCancel()">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 0C5.388 0 0 5.388 0 12C0 18.612 5.388 24 12 24C18.612 24 24 18.612 24 12C24 5.388 18.612 0 12 0ZM16.032 14.76C16.38 15.108 16.38 15.684 16.032 16.032C15.852 16.212 15.624 16.296 15.396 16.296C15.168 16.296 14.94 16.212 14.76 16.032L12 13.272L9.24 16.032C9.06 16.212 8.832 16.296 8.604 16.296C8.376 16.296 8.148 16.212 7.968 16.032C7.62 15.684 7.62 15.108 7.968 14.76L10.728 12L7.968 9.24C7.62 8.892 7.62 8.316 7.968 7.968C8.316 7.62 8.892 7.62 9.24 7.968L12 10.728L14.76 7.968C15.108 7.62 15.684 7.62 16.032 7.968C16.38 8.316 16.38 8.892 16.032 9.24L13.272 12L16.032 14.76Z"
              fill="#FF3355"
            />
          </svg>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #moreActions>
  <div
    class="bg-bg-3 rounded-2xl border border-line shadow-md flex flex-col font-medium text-sm overflow-hidden min-w-[150px]"
  >
    <button
      *ngIf="featureFlags['CompareDocument']"
      class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
      [ngClass]="{ 'cursor-not-allowed opacity-70': !fileLinkToCompareWith }"
      (click)="showDocumentCompareModal()"
    >
      So sánh văn bản gốc
    </button>
    <hr class="border-line" />
    <button
      *ngIf="featureFlags['EditPageImage']"
      class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
      (click)="showImageEditorModal()"
    >
      Chỉnh sửa trang
    </button>
    <hr class="border-line" />
    <button
      class="flex gap-2 items-center hover:bg-bg-1 px-3 py-2 border-line"
      (click)="showInfoModal()"
    >
      Thông tin chi tiết
    </button>
  </div>
</ng-template>

<ng-template #infoModal>
  <div class="font-semibold text-lg text-center">Document Viewer Component Info</div>
  <div class="grid grid-cols-2">
    <div>fileName:</div>
    <div>{{ fileName }}</div>
    <div>numPages:</div>
    <div>{{ numPages }}</div>
    <div>fileLink:</div>
    <div>{{ fileLink }}</div>
    <div class="col-span-2 border-b border-text-1"></div>
    <div class="col-span-2">featureFlags list:</div>
    <div>DisplayOcrResult</div>
    <div>{{ featureFlags['DisplayOcrResult'] }}</div>
    <div>EditOcrResult</div>
    <div>{{ featureFlags['EditOcrResult'] }}</div>
    <div>ViewerFitPageWidth</div>
    <div>{{ featureFlags['ViewerFitPageWidth'] }}</div>
    <div>Zooming</div>
    <div>{{ featureFlags['Zooming'] }}</div>
    <div>PdfLazyLoading</div>
    <div>{{ featureFlags['PdfLazyLoading'] }}</div>
    <div>OnlyDisplaySpecifiedPages</div>
    <div>{{ featureFlags['OnlyDisplaySpecifiedPages'] }}</div>
    <div class="col-span-2 border-b border-text-1"></div>
    <div>Is loading:</div>
    <div>{{ loadingSubject.value }}</div>
    <div>Zoom level:</div>
    <div>{{ zoomLevel }}</div>
    <div>Current mode:</div>
    <div>{{ currentModeSubject.value }}</div>
    <div class="col-span-2 border-b border-text-1"></div>
    <div>renderedPages count</div>
    <div>{{ renderedPages.size }}</div>
    <div>specifiedPages</div>
    <div>{{ specifiedPages }}</div>
    <div>specifiedPages count</div>
    <div>{{ specifiedPages?.length }}</div>
  </div>
</ng-template>

<ng-template #documentCompareModal>
  <div class="flex flex-col">
    <div class="flex py-[10px] px-4 items-center justify-between border-b border-line">
      <span class="text-text-1 text-sm font-semibold"
        >So sánh văn bản trước và sau khi qua xử lý</span
      >
      <button (click)="closeAllModal(null)">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M4 12L12 4"
            stroke="#989BB3"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M12 12L4 4"
            stroke="#989BB3"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>
    <app-document-compare
      [comparingDocumentPair]="{
        beforeDoc: fileLinkToCompareWith,
        afterDoc: fileLink
      }"
    />
  </div>
</ng-template>
